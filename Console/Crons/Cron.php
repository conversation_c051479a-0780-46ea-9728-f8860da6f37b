<?php

namespace App\Console\Crons;

use Illuminate\Console\Scheduling\CallbackEvent;

abstract class Cron
{
    private ?CallbackEvent $cron = null;

    public function setCallbackEvent(CallbackEvent $callbackEvent): void
    {
        $this->cron = $callbackEvent;
        $this->init();
    }

    private function init(): void
    {
        $this->cron = $this->schedule($this->cron);
        $this->cron->name($this->getName());
        $this->cron->onOneServer = $this->runOnOneServer();
        $withoutOverlapping = $this->withoutOverlappingMinutes();
        if (!is_null($withoutOverlapping)) {
            $this->cron->withoutOverlapping($withoutOverlapping);
        }

        $this->cron = $this->manualSetup($this->cron);
    }

    public function manualSetup(CallbackEvent $cron): CallbackEvent
    {
        return $cron;
    }

    public function __invoke()
    {
        $this->execute();
    }

    /**
     * Is cron enabled
     *
     * @return bool
     */
    abstract public function enabled(): bool;

    /**
     * Schedule cron frequency
     *
     * @param CallbackEvent $cron
     * @return CallbackEvent
     */
    abstract public function schedule(CallbackEvent $cron): CallbackEvent;

    /**
     * Cron name
     *
     * @return string
     */
    abstract public function getName(): string;

    /**
     * If null cron can overlap.
     * If integer - minutes without overlap
     *
     * @return int|null
     */
    public abstract function withoutOverlappingMinutes(): ?int;

    /**
     * If multiple servers and cron must be run
     * on only one. Return true
     *
     * @return bool
     */
    abstract public function runOnOneServer(): bool;

    /**
     * Run cron logic
     */
    abstract public function execute(): void;

    /**
     * @return bool
     */
    protected function isLocalEnvironment(): bool
    {
        return config('app.env') === 'local' || config('app.debug', false);
    }
}
