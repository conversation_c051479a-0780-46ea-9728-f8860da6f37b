<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\Data\Repositories\Contracts\LogRepositoryContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class LogsCleanup extends Cron
{
    private LogRepositoryContract $logRepo;

    public function __construct()
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $this->logRepo = app()->make(LogRepositoryContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->daily();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'LogsCleanup';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->logRepo->deleteOldLogs();
    }
}
