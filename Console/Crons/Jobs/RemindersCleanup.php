<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\Invitations\Contracts\InvitationServiceContract;
use App\Core\System\Mails\Contracts\MailServiceContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class RemindersCleanup extends Cron
{
    private MailServiceContract $mailService;

    private InvitationServiceContract $invitationService;

    public function __construct()
    {
        $this->mailService = app()->make(MailServiceContract::class);
        $this->invitationService = app()->make(InvitationServiceContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->daily();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'RemindersCleanup';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->mailService->cleanUnconfirmedEmails();
        $this->invitationService->cleanUnacceptedInvitations();
    }
}
