<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\Invitations\Contracts\InvitationServiceContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class SendInvitationReminderEmails extends Cron
{
    private InvitationServiceContract $invitationService;

    public function __construct()
    {
        $this->invitationService = app()->make(InvitationServiceContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return !$this->isLocalEnvironment();
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->hourly();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'sendInvitationReminders';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->invitationService->sendInvitationsReminders('mails::invitation-reminder');
    }
}