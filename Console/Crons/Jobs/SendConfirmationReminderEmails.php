<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\System\Mails\Contracts\MailServiceContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class SendConfirmationReminderEmails extends Cron
{
    private MailServiceContract $mailService;

    public function __construct()
    {
        $this->mailService = app()->make(MailServiceContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return !$this->isLocalEnvironment();
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->hourly();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'sendConfirmationReminders';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->mailService->sendEmailConfirmationReminders('mails::email-confirmation-reminder');
    }
}
