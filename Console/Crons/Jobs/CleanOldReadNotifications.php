<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\Data\Repositories\Contracts\NotificationRepositoryContract;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\CallbackEvent;

class CleanOldReadNotifications extends Cron
{
    private NotificationRepositoryContract $notificationsRepo;

    public function __construct()
    {
        $this->notificationsRepo = app()->make(NotificationRepositoryContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return !$this->isLocalEnvironment();
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->twiceMonthly();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'cleanOldReadNotifications';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $from = Carbon::now()->subMonths(2);
        $this->notificationsRepo->deleteNotificationsOlderThen($from, true);

        $from = Carbon::now()->subMonths(6);
        $this->notificationsRepo->deleteNotificationsOlderThen($from, false);
    }
}