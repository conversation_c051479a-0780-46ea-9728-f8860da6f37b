<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\Dev\Contracts\DevServiceContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class CheckDefaultTablePartition extends Cron
{
    private DevServiceContract $devService;

    public function __construct()
    {
        $this->devService = app()->make(DevServiceContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return !$this->isLocalEnvironment();
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->dailyAt('22:00');
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'checkDefaultTablePartitions';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->devService->checkDefaultTablePartitions();
    }
}
