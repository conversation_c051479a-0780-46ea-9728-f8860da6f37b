<?php

namespace App\Console\Crons\Jobs;

use App\Console\Crons\Cron;
use App\Core\ExchangeRates\Contracts\ExchangeRatesServiceContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class GetExchangeRatesDaily extends Cron
{
    private ExchangeRatesServiceContract $exchangeRatesService;

    public function __construct()
    {
        $this->exchangeRatesService = app()->make(ExchangeRatesServiceContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return !$this->isLocalEnvironment();
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->daily();
//        return $cron->everyMinute();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'getExchangeRatesDaily';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->exchangeRatesService->getAndStoreNewExchangeRatesFromApi();
    }
}
