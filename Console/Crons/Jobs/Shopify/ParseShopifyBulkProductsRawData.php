<?php

namespace App\Console\Crons\Jobs\Shopify;

use App\Console\Crons\Cron;
use App\Core\Shopify\Contracts\ShopifyApiServiceContract;
use App\Core\System\Log\Log;
use Illuminate\Console\Scheduling\CallbackEvent;
use Throwable;

class ParseShopifyBulkProductsRawData extends Cron
{
    /**
     * @var ShopifyApiServiceContract $shopifyApiService
     */
    private ShopifyApiServiceContract $shopifyApiService;


    public function __construct()
    {
        $this->shopifyApiService = app()->make(ShopifyApiServiceContract::class);
    }

    public function enabled(): bool
    {
        return false;
    }

    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->everyMinute();
    }

    public function getName(): string
    {
        return 'ParseShopifyBulkProductsRawData';
    }

    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    public function runOnOneServer(): bool
    {
        return false;
    }

    public function execute(): void
    {
        try {
            $this->shopifyApiService->parseShopifyBulkProductsRawData();
        } catch (Throwable $exception) {
            Log::error($exception->getMessage());
        }
    }
}
