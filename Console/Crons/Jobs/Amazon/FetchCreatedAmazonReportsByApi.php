<?php

namespace App\Console\Crons\Jobs\Amazon;

use App\Console\Crons\Cron;
use App\Core\Reports\Contracts\AmazonReportServiceContract;
use Illuminate\Console\Scheduling\CallbackEvent;

class FetchCreatedAmazonReportsByApi extends Cron
{
    private AmazonReportServiceContract $reportService;

    public function __construct()
    {
        $this->reportService = app()->make(AmazonReportServiceContract::class);
    }

    /**
     * @inheritDoc
     */
    public function enabled(): bool
    {
        return !$this->isLocalEnvironment();
    }

    /**
     * @inheritDoc
     */
    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->everyMinute();
    }

    /**
     * @inheritDoc
     */
    public function getName(): string
    {
        return 'fetchCreatedAmazonReportsByApi';
    }

    /**
     * @inheritDoc
     */
    public function withoutOverlappingMinutes(): ?int
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function runOnOneServer(): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function execute(): void
    {
        $this->reportService->fetchCreatedAmazonReportsByApi();
    }
}
