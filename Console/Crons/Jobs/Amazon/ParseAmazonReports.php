<?php

namespace App\Console\Crons\Jobs\Amazon;

use App\Console\Crons\Cron;
use App\Core\AmazonCsv\Contracts\AmazonCsvServiceContract;
use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Data\Repositories\Contracts\AmazonReportRepositoryContract;
use App\Core\System\Log\Log;
use Illuminate\Console\Scheduling\CallbackEvent;
use Throwable;

class ParseAmazonReports extends Cron
{
    private AmazonCsvServiceContract $amazonCsvService;
    private AmazonReportRepositoryContract $amazonReportRepo;

    /** @noinspection PhpUnhandledExceptionInspection */
    public function __construct()
    {
        $this->amazonCsvService = app()->make(AmazonCsvServiceContract::class);
        $this->amazonReportRepo = app()->make(AmazonReportRepositoryContract::class);
    }

    public function enabled(): bool
    {
        return true;
    }

    public function schedule(CallbackEvent $cron): CallbackEvent
    {
        return $cron->everyTwoMinutes();
    }

    public function getName(): string
    {
        return 'parseQueuedAmazonReports';
    }

    public function withoutOverlappingMinutes(): ?int
    {
        return 3;
    }

    public function runOnOneServer(): bool
    {
        return false;
    }

    public function execute(): void
    {
        $reports = $this->amazonReportRepo
            ->getAmazonReportsForStatus([AmazonReportStatus::STATUS_QUEUED])
            ->take(4);

        $action = 'parsed';
        $debug = false;
//        $debug = true;
        foreach ($reports as $report) {
            try {
                /** @noinspection PhpConditionAlreadyCheckedInspection */
                $this->amazonCsvService
                    ->resetAndParseReport(
                        report: $report,
                        isDebug: $debug
                    );
            } catch (Throwable) {
                $action = 'failed';
            }

            $message = $this->message($report, $action);
            Log::debug($message);
        }
    }

    private function message(AmazonReport $report, string $action): string
    {
        $message = [
            'Report (ID: ' . $report->id . ')',
            $report->file->full_name . ' (' . $report->file->human_size . ')',
            '- ' . $action
        ];

        return implode(' ', $message);
    }
}
