<?php /** @noinspection PhpUnused */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Process\Factory;
use Illuminate\Process\PendingProcess;

class GenerateNewLocalSSLData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'evat:make-local-ssl
                            {domain=dev.evat.local : The local domain name of v-host}
                            {name=evat : Name of certificate files, ie. "evat.crt, "evat.key, "evat-ca.crt"}
                            {directory? : Directory where certificates will be stored}';

    protected $description = 'Generate new certificate and certificate authority certificate which enables local development';

    private PendingProcess $process;

    private int $days = 1825;
    private string $secret = 'testLocalCertsSecretPhrase';
    private string $domain;
    private string $filename;
    private string $directory;

    public function handle(): void
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $this->process = app()->make(Factory::class)->newPendingProcess();
        $this->domain = $this->argument('domain');
        $name = $this->argument('name');
        $this->filename = trim($name, '/');

        $directory = $this->argument('directory') ?? resource_path('/certificates/local/https');
        $this->directory = rtrim($directory, '/') . '/';

        $this->resolveCertificates();

        $this->printMessage();
    }

    private function resolveCertificates(): void
    {
        // Create Root CA
        $this->createCaKeyAndCertificate();

        // Create
        $this->createKeyAndCertificate();
    }

    private function createKeyAndCertificate(): void
    {
        $this->createKey();
        $this->createCsr();
        $this->createExt();
        $this->createCrt();
    }

    private function createKey(): void
    {
        $command = [
            'openssl genrsa',
            '-out ' . $this->getKeyPath(),
            '2048'
        ];
        $command = implode(' ', $command);

        $this->command($command);
    }

    private function createCsr(): void
    {
        $command = [
            'openssl req -new',
            '-key ' . $this->getKeyPath(),
            '-out ' . $this->getCsrPath(),
            '-subj ' . $this->getCsrSubjectsString()
        ];
        $command = implode(' ', $command);

        $this->command($command);
    }

    private function createExt(): void
    {
        $data = [
            'authorityKeyIdentifier=keyid,issuer',
            'basicConstraints=CA:FALSE',
            'keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment',
            'subjectAltName = @alt_names',
            '',
            '[alt_names]',
            'DNS.1 = ' . $this->domain,
        ];
        $data = implode("\n", $data);

        file_put_contents($this->getExtPath(), $data);
    }

    private function createCrt(): void
    {
        $command = [
            'openssl x509 -req',
            '-in ' . $this->getCsrPath(),
            '-CA ' . $this->getCaRootCrtPath(),
            '-CAkey ' . $this->getCaRootKeyPath(),
            '-passin pass:' . $this->secret,
            '-CAcreateserial',
            '-out ' . $this->getCrtPath(),
            '-days ' . $this->days,
            '-sha256',
            '-extfile ' . $this->getExtPath()
        ];

        $command = implode(' ', $command);

        $this->command($command);
    }

    private function createCaKeyAndCertificate(): void
    {
        $this->createCaRootKey();
        $this->createCaRootCrt();
    }

    private function createCaRootCrt(): void
    {
        $command = [
            'openssl req -x509 -new -nodes -key',
            $this->getCaRootKeyPath(),
            '-sha256 -days ' . $this->days,
            '-out ' . $this->getCaRootCrtPath(),
            '-passin pass:"' . $this->secret . '"',
            '-subj ' . $this->getCaSubjectsString()
        ];
        $command = implode(' ', $command);

        $this->command($command);
    }

    private function createCaRootKey(): void
    {
        $command = [
            'openssl genrsa -des3',
            '-out ' . $this->getCaRootKeyPath(),
            '-passout pass:"' . $this->secret . '"',
            '2048'
        ];
        $command = implode(' ', $command);

        $this->command($command);
    }

    private function getKeyPath(): string
    {
        return $this->getPathWithName() . '.key';
    }

    private function getCsrPath(): string
    {
        return $this->getPathWithName() . '.csr';
    }

    private function getExtPath(): string
    {
        return $this->getPathWithName() . '.ext';
    }

    private function getCrtPath(): string
    {
        return $this->getPathWithName() . '.crt';
    }

    private function getCaRootCrtPath(): string
    {
        return $this->getPathWithName() . '-root-ca.pem';
    }

    private function getCaRootKeyPath(): string
    {
        return $this->getPathWithName() . '-root-ca.key';
    }

    private function printMessage(): void
    {
        $titles = [
            'main' => 'SSL Certificate, Key and CA created!',
            'ca'   => 'Import in your\'s PC trusted CA store, Firefox has own store so you must import there also',
            'vh'   => 'Use this two in your Apache/Nginx vhost SSL config. Dont forget to to restart web server'
        ];

        $paths = [
            'cac' => $this->getCaRootCrtPath(),
            'key' => $this->getKeyPath(),
            'crt' => $this->getCrtPath(),
        ];
        $messages = array_merge($titles, $paths);

        $length = $this->getStringsTextLength($messages) + 2;

        $equals = $this->createString('=', $length);

        $this->info('');
        $this->warn($equals);
        $this->info($titles['main']);
        $this->warn($equals);
        $this->info($titles['ca']);
        $this->question($paths['cac']);
        $this->warn($equals);
        $this->info($titles['vh']);
        $this->question($paths['key']);
        $this->question($paths['crt']);
        $this->warn($equals);

        $this->info('');
    }

    private function getPathWithName(): string
    {
        return $this->directory . $this->filename;
    }

    private function getStringsTextLength(array $strings): int
    {
        $count = 0;
        foreach ($strings as $string) {
            $cnt = mb_strlen($string);
            if ($cnt > $count) {
                $count = $cnt;
            }
        }

        return $count;
    }

    /** @noinspection PhpSameParameterValueInspection */
    private function createString(string $char, int $length): string
    {
        $char = substr($char, 0, 1);
        $string = '';
        for ($i = 0; $i < $length; $i++) {
            $string = $string . $char;
        }

        return $string;
    }

    /** @noinspection PhpSameParameterValueInspection */
    private function command(string $command, bool $dump = false): void
    {
        if ($dump) {
            dd($command);
        }
        $this->process->run($command);
    }

    private function getCaSubjectsString(): string
    {
        $subjects = [
            'C'  => 'GB',
            'ST' => 'London',
            'O'  => 'The Secret CA',
            'OU' => 'The Secret CA Department',
            'CN' => 'secret-ca.example.com',
        ];

        $subjects = collect($subjects)
            ->map(function (string $value, string $key) {
                return $key . '=' . $value;
            })->implode(function ($value) {
                return $value;
            }, '/');

        return '"/' . $subjects . '"';
    }

    private function getCsrSubjectsString(): string
    {
        $subjects = [
            'C'  => 'GB',
            'ST' => 'London',
            'O'  => 'Local Dev',
            'OU' => 'Local Dev IT Department',
            'CN' => 'local.example.com',
        ];

        $subjects = collect($subjects)
            ->map(function (string $value, string $key) {
                return $key . '=' . $value;
            })->implode(function ($value) {
                return $value;
            }, '/');

        return '"/' . $subjects . '"';
    }
}
