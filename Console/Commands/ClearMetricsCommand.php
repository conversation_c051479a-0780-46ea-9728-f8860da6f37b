<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Queue\QueueManager;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Laravel\Horizon\Repositories\RedisJobRepository;
use Redis as BaseRedis;

/**
 * Console.
 */
class ClearMetricsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'evat:clear-horizon-metrics
                            {--queue= : The name of the queue to clear}
                            {--force : Force the operation to run when in production}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Flush the Horizon metrics';

    /**
     * @param RedisJobRepository $jobRepository
     * @param QueueManager $manager
     * @return int
     * @throws \RedisException
     */
    public function handle(RedisJobRepository $jobRepository, QueueManager $manager): int
    {
        $client = Redis::connection('horizon')->client();
        $keys = $client->keys('*');
        $prefix = $client->getOption(BaseRedis::OPT_PREFIX);

        $realKeys = [];
        foreach ($keys as $key) {
            $key = preg_replace("/^${prefix}/", '', $key);
            $realKeys[] = $key;
        }

        foreach ($realKeys as $key) {
            $client->del($key);
        }

        $connection = Arr::first($this->laravel['config']->get('horizon.defaults'))['connection'] ?? 'redis';
        $queues = $this->getQueues();

        foreach ($queues as $queue) {
            $jobRepository->purge($queue);
            $manager->connection($connection)->clear($queue);
        }

        $this->info('Cleared horizon cache');

        return 0;
    }

    private function getQueues(): array
    {
        $skipDrivers = [
            'sync',
            'database',
            'beanstalkd',
            'sqs',
        ];

        return collect(config('queue.connections'))
            ->filter(function (array $connection) use ($skipDrivers) {
                if (is_null($connection['driver'] ?? null)) {
                    return false;
                }

                return !in_array($connection['driver'], $skipDrivers);
            })->pluck('queue')->toArray();
    }
}
