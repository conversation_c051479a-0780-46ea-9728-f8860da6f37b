<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenerateModelDocumentation extends Command
{
    protected $signature = 'evat:model-docs {modelName?}';

    protected $description = 'Generate docBlock for all or single model';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $model = $this->argument('modelName');

        $command = 'ide-helper:models';
        $args = [
            '--no-interaction' => true,
            '--write'          => true
        ];

        if (!is_null($model)) {
            if (str_contains($model, '.')) {
                $model = substr($model, 0, strrpos($model, '.'));
            }

            $args['model'] = ['App\Core\Data\Models\\' . $model];
        }

        $this->call($command, $args);
    }
}
