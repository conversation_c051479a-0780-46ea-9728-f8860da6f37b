<?php

namespace App\Console;

use App\Core\System\Cron\Contracts\CronServiceContract;
use Illuminate\Console\Application as Artisan;
use Illuminate\Console\Command;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use ReflectionClass;
use Symfony\Component\Finder\Finder;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    private ?Schedule $schedule = null;

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule): void
    {
        $this->schedule = $schedule;
        $this->crons();
    }

    private function crons(): void
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $cronService = $this->app->make(CronServiceContract::class);

        /**
         * @var CronServiceContract $cronService
         */
        $cronService->initiateAllCronClasses($this->schedule);
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     * @throws \ReflectionException
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');
    }

    /**
     * Override. fucking Taylor is IDIOT
     *
     * @param array|string $paths
     * @throws \ReflectionException
     */
    protected function load($paths): void
    {
        $paths = array_unique(Arr::wrap($paths));

        $paths = array_filter($paths, function ($path) {
            return is_dir($path);
        });

        if (empty($paths)) {
            return;
        }

        // MUST BE HARDCODED $this->app->getNamespace() wants composer.json on prod
        $namespace = 'App\\';
        foreach ((new Finder)->in($paths)->files() as $command) {

            $command = $namespace . str_replace(
                    ['/', '.php'],
                    ['\\', ''],
                    Str::after($command->getPathname(), realpath(app_path()) . DIRECTORY_SEPARATOR)
                );

            if (is_subclass_of($command, Command::class) &&
                !(new ReflectionClass($command))->isAbstract()) {
                Artisan::starting(function ($artisan) use ($command) {
                    $artisan->resolve($command);
                });
            }
        }
    }
}
