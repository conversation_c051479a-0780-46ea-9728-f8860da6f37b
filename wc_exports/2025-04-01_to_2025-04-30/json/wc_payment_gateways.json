[{"id": "pre_install_woocommerce_payments_promotion", "title": "<span class=\"gateway-subtitle\" ><img class=\"wcpay-visa-icon wcpay-icon\" src=\"https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/visa.svg\" alt=\"Visa\"><img class=\"wcpay-mastercard-icon wcpay-icon\" src=\"https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/mastercard.svg\" alt=\"Mastercard\"><img class=\"wcpay-amex-icon wcpay-icon\" src=\"https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/amex.svg\" alt=\"Amex\"><img class=\"wcpay-googlepay-icon wcpay-icon\" src=\"https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/googlepay.svg\" alt=\"Googlepay\"><img class=\"wcpay-applepay-icon wcpay-icon\" src=\"https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/applepay.svg\" alt=\"Applepay\"></span>", "description": null, "order": "0", "enabled": false, "method_title": "WooPayments", "method_description": "Zahlungen leicht gemacht, ohne monatliche Gebühren – exklusiv entwickelt für WooCommerce-Shops. Nimm Zahlungen per Kreditkarte, <PERSON>bit<PERSON><PERSON> und andere beliebte Zahlungsmethoden an.", "method_supports": ["products", "refunds", "subscriptions", "multiple_subscriptions", "subscription_cancellation", "subscription_reactivation", "subscription_suspension", "subscription_amount_changes", "subscription_date_changes", "subscription_payment_method_change_admin", "subscription_payment_method_change_customer", "subscription_payment_method_change", "tokenization", "add_payment_method"], "settings": {"is_dismissed": {"id": "is_dismissed", "label": "Gateway ignorieren", "description": "", "type": "checkbox", "value": "no", "default": "no", "tip": "", "placeholder": ""}}, "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=pre_install_woocommerce_payments_promotion", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/pre_install_woocommerce_payments_promotion", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "bacs", "title": "Direkte Banküberweisung", "description": "Überweise direkt an unsere Bankverbindung. Bitte nutze die Bestellnummer als Verwendungszweck. Deine Bestellung wird erst nach Geldeingang auf unserem Konto versandt.", "order": "1", "enabled": false, "method_title": "Direkte Banküberweisung", "method_description": "Nimm persönlich Zahlungen per BACS entgegen. Eher unter der Bezeichnung „Banküberweisung“ bekannt.", "method_supports": ["products"], "settings": {"title": {"id": "title", "label": "Titel", "description": "<PERSON>s steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.", "type": "safe_text", "value": "Direkte Banküberweisung", "default": "Direkte Banküberweisung", "tip": "<PERSON>s steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.", "placeholder": ""}, "instructions": {"id": "instructions", "label": "Anweisungen", "description": "<PERSON><PERSON><PERSON><PERSON>, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.", "type": "textarea", "value": "", "default": "", "tip": "<PERSON><PERSON><PERSON><PERSON>, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.", "placeholder": ""}}, "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=bacs", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/bacs", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "cheque", "title": "Scheckzahlungen", "description": " Bitte sende deinen Scheck an die Adresse: Shop-Name, Shop-Straße, Shop-PLZ, Shop-Ort, Shop-Land.", "order": "2", "enabled": false, "method_title": "Scheckzahlungen", "method_description": "Nimm persönlich Zahlungen per Schecks entgegen. Dieser Offline-Zahlungsweg kann auch hilfreich sein, um K<PERSON>ufe zu testen.", "method_supports": ["products"], "settings": {"title": {"id": "title", "label": "Titel", "description": "<PERSON>s steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.", "type": "safe_text", "value": "Scheckzahlungen", "default": "Scheckzahlungen", "tip": "<PERSON>s steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.", "placeholder": ""}, "instructions": {"id": "instructions", "label": "Anweisungen", "description": "<PERSON><PERSON><PERSON><PERSON>, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.", "type": "textarea", "value": "", "default": "", "tip": "<PERSON><PERSON><PERSON><PERSON>, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.", "placeholder": ""}}, "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=cheque", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/cheque", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "cod", "title": "Cash on delivery", "description": "Pay with cash upon delivery.", "order": "3", "enabled": false, "method_title": "<PERSON>", "method_description": "Lass deine Kunden bei der Zustellung bezahlen – in bar oder mit anderen Zahlungsmethoden.", "method_supports": ["products"], "settings": {"title": {"id": "title", "label": "Titel", "description": "Beschreibung der Zahlungsmethode, die Kunden bei der Kaufabwicklung sehen.", "type": "safe_text", "value": "Cash on delivery", "default": "<PERSON>", "tip": "Beschreibung der Zahlungsmethode, die Kunden bei der Kaufabwicklung sehen.", "placeholder": ""}, "instructions": {"id": "instructions", "label": "Anweisungen", "description": "<PERSON><PERSON><PERSON><PERSON>, die der „Danke“-Seite hinzugefügt werden.", "type": "textarea", "value": "Pay with cash upon delivery.", "default": "<PERSON>.", "tip": "<PERSON><PERSON><PERSON><PERSON>, die der „Danke“-Seite hinzugefügt werden.", "placeholder": ""}, "enable_for_methods": {"id": "enable_for_methods", "label": "<PERSON><PERSON><PERSON> verfügbar machen", "description": "Falls die Zahlung per Nachnahme nur für bestimmte Versandarten möglich ist, bitte hier erfassen. <PERSON><PERSON> <PERSON>, um sie für alle Versandarten verfügbar zu machen.", "type": "multiselect", "value": "", "default": "", "tip": "Falls die Zahlung per Nachnahme nur für bestimmte Versandarten möglich ist, bitte hier erfassen. <PERSON><PERSON> <PERSON>, um sie für alle Versandarten verfügbar zu machen.", "placeholder": "", "options": {"Versandkostenpauschale": {"flat_rate": "Jed<PERSON> &quot;Versandkostenpaus<PERSON>e&quot; Methode", "flat_rate:3": "Andere Standorte &ndash; Expressversand (3-5 days) (#3)", "flat_rate:5": "Andere Standorte &ndash; Versandkosten (#5)"}, "Kostenlose Lieferung": {"free_shipping": "<PERSON><PERSON> &quot;<PERSON><PERSON>lose Lieferung&quot; Methode", "free_shipping:2": "Andere Standorte &ndash; Kostenloser Versand (7-10 days) (#2)", "free_shipping:4": "Andere Standorte &ndash; Kostenloser Versand (#4)"}, "Abholung vor Ort": {"local_pickup": "Jed<PERSON> &quot;Abholung vor Ort&quot; Methode"}}}, "enable_for_virtual": {"id": "enable_for_virtual", "label": "Zahlung per Nachnahme für virtuelle Bestellungen akzeptieren", "description": "", "type": "checkbox", "value": "yes", "default": "yes", "tip": "", "placeholder": ""}}, "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=cod", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/cod", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "ppcp-gateway", "title": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>", "description": "", "order": "4", "enabled": true, "method_title": "PayPal", "method_description": "<PERSON><PERSON> zahlen.", "method_supports": ["products", "refunds", "tokenization", "add_payment_method"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=ppcp-gateway", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/ppcp-gateway", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_link", "title": "Link", "description": "Link is a payment method that allows customers to save payment information  and use the payment details\n\t\t\tfor further payments.", "order": "5", "enabled": true, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_link", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_link", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_klarna", "title": "<PERSON><PERSON><PERSON>", "description": "Allow customers to pay over time with Klarna.", "order": "6", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_klarna", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_klarna", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_affirm", "title": "Affirm", "description": "Allow customers to pay over time with Affirm.", "order": "7", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_affirm", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_affirm", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_afterpay_clearpay", "title": "Clearpay / Afterpay", "description": "Allow customers to pay over time with Clearpay / Afterpay.", "order": "8", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_afterpay_clearpay", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_afterpay_clearpay", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_eps", "title": "EPS", "description": "EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials.", "order": "9", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_eps", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_eps", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_bancontact", "title": "Bancontact", "description": "Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation.", "order": "10", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "subscriptions", "tokenization", "multiple_subscriptions"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_bancontact", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_bancontact", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_ideal", "title": "iDEAL", "description": "iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials.", "order": "11", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "subscriptions", "multiple_subscriptions", "tokenization"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_ideal", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_ideal", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_sepa_debit", "title": "SEPA-Lastschrift", "description": "Reach 500 million customers and over 20 million businesses across the European Union.", "order": "12", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_sepa_debit", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_sepa_debit", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_p24", "title": "Przelewy24", "description": "Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods.", "order": "13", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_p24", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_p24", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe", "title": "Kredit-/De<PERSON><PERSON>te", "description": "", "order": "15", "enabled": true, "method_title": "Stripe", "method_description": "Accept debit and credit cards in 135+ currencies, methods such as SEPA, and one-touch checkout with Apple Pay.", "method_supports": ["products", "refunds", "tokenization", "add_payment_method"], "settings": {"api_credentials": {"id": "api_credentials", "label": "Stripe Account <PERSON>", "description": "", "type": "stripe_account_keys", "value": "", "default": "", "tip": "", "placeholder": ""}, "testmode": {"id": "testmode", "label": "Testmodus aktivieren", "description": "Zahlungs-Gateway mithil<PERSON> von Test-API-Schlüsseln in den Testmodus versetzen.", "type": "checkbox", "value": "no", "default": "yes", "tip": "Zahlungs-Gateway mithil<PERSON> von Test-API-Schlüsseln in den Testmodus versetzen.", "placeholder": ""}, "test_publishable_key": {"id": "test_publishable_key", "label": "Veröffentlich<PERSON><PERSON>", "description": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"pk_test_\" will be saved.", "type": "text", "value": "", "default": "", "tip": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"pk_test_\" will be saved.", "placeholder": ""}, "test_secret_key": {"id": "test_secret_key", "label": "Geheimer Testschlüssel", "description": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"sk_test_\" or \"rk_test_\" will be saved.", "type": "password", "value": "", "default": "", "tip": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"sk_test_\" or \"rk_test_\" will be saved.", "placeholder": ""}, "publishable_key": {"id": "publishable_key", "label": "Veröffentlichbarer Live-Schlüssel", "description": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"pk_live_\" will be saved.", "type": "text", "value": "pk_live_51JibzFDQVkNPO1FeL5K9X0KPwWgZbxKQKyRs9uDbvzavkiFkvS8IHerCSSXpsXO9o7x0RheseRUKVx2G4DPErhIT00pwW7hEHa", "default": "", "tip": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"pk_live_\" will be saved.", "placeholder": ""}, "secret_key": {"id": "secret_key", "label": "Geheimer Live-Schlüssel", "description": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"sk_live_\" or \"rk_live_\" will be saved.", "type": "password", "value": "***********************************************************************************************************", "default": "", "tip": "Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with \"sk_live_\" or \"rk_live_\" will be saved.", "placeholder": ""}, "webhook": {"id": "webhook", "label": "Webhook Endpoints", "description": "You must add the following webhook endpoint <strong style=\"background-color:#ddd;\">&nbsp;https://hawkbull.de/?wc-api=wc_stripe&nbsp;</strong> to your <a href=\"https://dashboard.stripe.com/account/webhooks\" target=\"_blank\">Stripe account settings</a> (if there isn't one already enabled). This will enable you to receive notifications on the charge statuses.<br><br>Warning: The most recent live webhook, received at 2025-05-20 03:29:20 UTC, could not be processed. Reason: The webhook was not signed with the expected signing secret. (The last live webhook to process successfully was timestamped 2025-04-02 10:55:15 UTC.)", "type": "title", "value": "", "default": "", "tip": "You must add the following webhook endpoint <strong style=\"background-color:#ddd;\">&nbsp;https://hawkbull.de/?wc-api=wc_stripe&nbsp;</strong> to your <a href=\"https://dashboard.stripe.com/account/webhooks\" target=\"_blank\">Stripe account settings</a> (if there isn't one already enabled). This will enable you to receive notifications on the charge statuses.<br><br>Warning: The most recent live webhook, received at 2025-05-20 03:29:20 UTC, could not be processed. Reason: The webhook was not signed with the expected signing secret. (The last live webhook to process successfully was timestamped 2025-04-02 10:55:15 UTC.)", "placeholder": ""}, "test_webhook_secret": {"id": "test_webhook_secret", "label": "Geheimer Webhook-Testschlüssel", "description": "Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ", "type": "password", "value": "", "default": "", "tip": "Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ", "placeholder": ""}, "webhook_secret": {"id": "webhook_secret", "label": "Geheimer Webhook-Schlüssel", "description": "Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ", "type": "password", "value": "whsec_kURdIcy6jdhv7e3o28Hc5F8OzOAj70vA", "default": "", "tip": "Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ", "placeholder": ""}, "statement_descriptor": {"id": "statement_descriptor", "label": "Verwendungszweck", "description": "Statement-Descriptors („Schlagwort“/Referenz für Kontoauszug) müssen auf 22 Zeichen begrenzt sein, können nicht die Sonderzeichen >, <, \", \\, ', *, /, (, ), {, } enthalten und dürfen nicht ausschließlich aus Nummern bestehen. Das wird auf dem Kontoauszug des Kunden in Großbuchstaben erscheinen.", "type": "text", "value": "", "default": "", "tip": "Statement-Descriptors („Schlagwort“/Referenz für Kontoauszug) müssen auf 22 Zeichen begrenzt sein, können nicht die Sonderzeichen >, <, \", \\, ', *, /, (, ), {, } enthalten und dürfen nicht ausschließlich aus Nummern bestehen. Das wird auf dem Kontoauszug des Kunden in Großbuchstaben erscheinen.", "placeholder": ""}, "short_statement_descriptor": {"id": "short_statement_descriptor", "label": "Short Statement Descriptor", "description": "Shortened version of the statement descriptor in combination with the customer order number.", "type": "text", "value": "", "default": "", "tip": "Shortened version of the statement descriptor in combination with the customer order number.", "placeholder": ""}, "capture": {"id": "capture", "label": "<PERSON>rte sofort belasten", "description": "<PERSON><PERSON><PERSON><PERSON>, ob die Belastung direkt erfasst wird oder nicht. Falls nicht aktiviert, gibt die Belastung eine Autorisierung aus und muss später erfasst werden. Nicht erfasste Belastungen laufen in 7 Tagen ab.", "type": "checkbox", "value": "yes", "default": "yes", "tip": "<PERSON><PERSON><PERSON><PERSON>, ob die Belastung direkt erfasst wird oder nicht. Falls nicht aktiviert, gibt die Belastung eine Autorisierung aus und muss später erfasst werden. Nicht erfasste Belastungen laufen in 7 Tagen ab.", "placeholder": ""}, "payment_request": {"id": "payment_request", "label": "Enable Payment Request Buttons. (Apple Pay/Google Pay) <br />By using Apple Pay, you agree to <a href=\"https://stripe.com/apple-pay/legal\" target=\"_blank\">Stripe</a> and <a href=\"https://developer.apple.com/apple-pay/acceptable-use-guidelines-for-websites/\" target=\"_blank\">Apple</a>'s terms of service. (Apple Pay domain verification is performed automatically in live mode; configuration can be found on the <a href=\"https://dashboard.stripe.com/settings/payments/apple_pay\" target=\"_blank\">Stripe dashboard</a>.)", "description": "Falls aktiviert und vom Browser unterstützt, können Kunden mit Apple Pay oder Chrome Payment Request zahlen.", "type": "checkbox", "value": "yes", "default": "yes", "tip": "Falls aktiviert und vom Browser unterstützt, können Kunden mit Apple Pay oder Chrome Payment Request zahlen.", "placeholder": ""}, "payment_request_button_type": {"id": "payment_request_button_type", "label": "Button-Typ", "description": "<PERSON><PERSON><PERSON><PERSON> den Button-Typ, den du anzeigen willst.", "type": "select", "value": "buy", "default": "default", "tip": "<PERSON><PERSON><PERSON><PERSON> den Button-Typ, den du anzeigen willst.", "placeholder": "", "options": {"default": "Only icon", "buy": "<PERSON><PERSON><PERSON>", "donate": "<PERSON><PERSON><PERSON>", "book": "Book"}}, "payment_request_button_theme": {"id": "payment_request_button_theme", "label": "<PERSON>ton-Theme", "description": "<PERSON><PERSON><PERSON><PERSON> das Button-Theme, das du anzeigen willst.", "type": "select", "value": "dark", "default": "dark", "tip": "<PERSON><PERSON><PERSON><PERSON> das Button-Theme, das du anzeigen willst.", "placeholder": "", "options": {"dark": "<PERSON><PERSON><PERSON>", "light": "Hell", "light-outline": "Hell mit Rand"}}, "payment_request_button_locations": {"id": "payment_request_button_locations", "label": "Payment Request Button Locations", "description": "Select where you would like Payment Request Buttons to be displayed", "type": "multiselect", "value": ["product", "cart", "checkout"], "default": ["product", "cart", "checkout"], "tip": "Select where you would like Payment Request Buttons to be displayed", "placeholder": "", "options": {"product": "Product", "cart": "<PERSON><PERSON>", "checkout": "Checkout"}}, "payment_request_button_size": {"id": "payment_request_button_size", "label": "Payment Request Button Size", "description": "Select the size of the button.", "type": "select", "value": "small", "default": "default", "tip": "Select the size of the button.", "placeholder": "", "options": {"small": "Small (40px)", "default": "De<PERSON>ult (48px)", "large": "Large (56px)"}}, "saved_cards": {"id": "saved_cards", "label": "Zahlung über gespeicherte Karten aktivieren", "description": "Falls aktiviert, können Benutzer an der Kasse mit einer gespeicherten Karte bezahlen. Die Karteninformationen werden auf Stripe-Servern gespeichert, nicht in Ihrem Shop.", "type": "checkbox", "value": "yes", "default": "yes", "tip": "Falls aktiviert, können Benutzer an der Kasse mit einer gespeicherten Karte bezahlen. Die Karteninformationen werden auf Stripe-Servern gespeichert, nicht in Ihrem Shop.", "placeholder": ""}, "sepa_tokens_for_other_methods": {"id": "sepa_tokens_for_other_methods", "label": "Enable SEPA Direct Debit tokens for other methods", "description": "If enabled, users will be able to pay with iDEAL or Bancontact and save the method as a SEPA Direct Debit method.", "type": "checkbox", "value": "yes", "default": "yes", "tip": "If enabled, users will be able to pay with iDEAL or Bancontact and save the method as a SEPA Direct Debit method.", "placeholder": ""}, "logging": {"id": "logging", "label": "Debug-Meldungen protokollieren", "description": "Speichere Debug-Meldungen im Protokoll des WooCommerce-Systemstatus.", "type": "checkbox", "value": "no", "default": "no", "tip": "Speichere Debug-Meldungen im Protokoll des WooCommerce-Systemstatus.", "placeholder": ""}, "amazon_pay_button_locations": {"id": "amazon_pay_button_locations", "label": "Amazon Pay Button Locations", "description": "Select where you would like Amazon Pay Button to be displayed", "type": "multiselect", "value": ["product", "cart"], "default": ["product", "cart"], "tip": "Select where you would like Amazon Pay Button to be displayed", "placeholder": "", "options": {"product": "Product", "cart": "<PERSON><PERSON>", "checkout": "Checkout"}}, "amazon_pay_button_size": {"id": "amazon_pay_button_size", "label": "Amazon Pay Button Size", "description": "Select the size of the button.", "type": "select", "value": "default", "default": "default", "tip": "Select the size of the button.", "placeholder": "", "options": {"small": "Small (40px)", "default": "De<PERSON>ult (48px)", "large": "Large (56px)"}}, "upe_checkout_experience_enabled": {"id": "upe_checkout_experience_enabled", "label": "Try the new payment experience (Early access) <br />Get early access to a new, smarter payment experience on checkout and let us know what you think by <a href=\"https://woocommerce.survey.fm/woocommerce-stripe-upe-opt-out-survey\" target=\"_blank\">submitting your feedback</a>. We recommend this feature for experienced merchants as the functionality is currently limited. <a href=\"https://woocommerce.com/document/stripe/admin-experience/new-checkout-experience/\" target=\"_blank\">Learn more</a>", "description": "New checkout experience allows you to manage all payment methods on one screen and display them to customers based on their currency and location.", "type": "checkbox", "value": "yes", "default": "no", "tip": "New checkout experience allows you to manage all payment methods on one screen and display them to customers based on their currency and location.", "placeholder": ""}}, "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe", "connection_url": null, "setup_help_text": "Your API details can be obtained from your <a href=\"https://dashboard.stripe.com/apikeys\">Stripe account</a>. Don’t have a Stripe account? <a href=\"https://dashboard.stripe.com/register\">Create one.</a>", "required_settings_keys": ["publishable_key", "secret_key"], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_us_bank_account", "title": "ACH Direct Debit", "description": "Pay directly from your US bank account via ACH.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization", "subscriptions"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_us_bank_account", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_us_bank_account", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_alipay", "title": "Alipay", "description": "Alipay is a popular wallet in China, operated by Ant Financial Services Group, a financial services provider affiliated with Alibaba.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_alipay", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_alipay", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_amazon_pay", "title": "Amazon Pay", "description": "Amazon Pay is a payment method that allows customers to pay with their Amazon account.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_amazon_pay", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_amazon_pay", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_blik", "title": "BLIK", "description": "BLIK enables customers in Poland to pay directly via online payouts from their bank account.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_blik", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_blik", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_boleto", "title": "Boleto", "description": "Boleto is an official payment method in Brazil. Customers receive a voucher that can be paid at authorized agencies or banks, ATMs, or online bank portals.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_boleto", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_boleto", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_oxxo", "title": "OXXO", "description": "OXXO is a Mexican chain of convenience stores that allows customers to pay bills and online purchases in-store with cash.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_oxxo", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_oxxo", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_multibanco", "title": "Multibanco", "description": "Multibanco is an interbank network that links the ATMs of all major banks in Portugal, allowing customers to pay through either their ATM or online banking environment.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_multibanco", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_multibanco", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_wechat_pay", "title": "WeChat Pay", "description": "WeChat Pay is a popular mobile payment and digital wallet service by WeChat in China.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_wechat_pay", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_wechat_pay", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_cashapp", "title": "Cash App Pay", "description": "Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_cashapp", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_cashapp", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_acss_debit", "title": "Pre-Authorized Debit", "description": "Canadian Pre-Authorized Debit is a payment method that allows customers to pay using their Canadian bank account.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization", "subscriptions"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_acss_debit", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_acss_debit", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_bacs_debit", "title": "Bacs Direct Debit", "description": "Bacs Direct Debit enables customers in the UK to pay by providing their bank account details.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_bacs_debit", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_bacs_debit", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}, {"id": "stripe_au_becs_debit", "title": "BECS Direct Debit", "description": "Pay directly from your Australian bank account via BECS.", "order": "", "enabled": false, "method_title": "", "method_description": "", "method_supports": ["products", "refunds", "tokenization", "subscriptions"], "settings": [], "needs_setup": false, "post_install_scripts": [], "settings_url": "https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_au_becs_debit", "connection_url": null, "setup_help_text": null, "required_settings_keys": [], "_links": {"self": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_au_becs_debit", "targetHints": {"allow": ["GET", "POST", "PUT", "PATCH"]}}], "collection": [{"href": "https://hawkbull.de/wp-json/wc/v3/payment_gateways"}]}}]