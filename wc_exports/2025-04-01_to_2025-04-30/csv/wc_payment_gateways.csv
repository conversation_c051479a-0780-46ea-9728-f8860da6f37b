id,title,description,order,enabled,method_title,method_description,method_supports_0,method_supports_1,method_supports_2,method_supports_3,method_supports_4,method_supports_5,method_supports_6,method_supports_7,method_supports_8,method_supports_9,method_supports_10,method_supports_11,method_supports_12,method_supports_13,settings_is_dismissed_id,settings_is_dismissed_label,settings_is_dismissed_description,settings_is_dismissed_type,settings_is_dismissed_value,settings_is_dismissed_default,settings_is_dismissed_tip,settings_is_dismissed_placeholder,needs_setup,settings_url,connection_url,setup_help_text,_links_self_0_href,_links_self_0_targetHints_allow_0,_links_self_0_targetHints_allow_1,_links_self_0_targetHints_allow_2,_links_self_0_targetHints_allow_3,_links_collection_0_href,settings_title_id,settings_title_label,settings_title_description,settings_title_type,settings_title_value,settings_title_default,settings_title_tip,settings_title_placeholder,settings_instructions_id,settings_instructions_label,settings_instructions_description,settings_instructions_type,settings_instructions_value,settings_instructions_default,settings_instructions_tip,settings_instructions_placeholder,settings_enable_for_methods_id,settings_enable_for_methods_label,settings_enable_for_methods_description,settings_enable_for_methods_type,settings_enable_for_methods_value,settings_enable_for_methods_default,settings_enable_for_methods_tip,settings_enable_for_methods_placeholder,settings_enable_for_methods_options_Versandkostenpauschale_flat_rate,settings_enable_for_methods_options_Versandkostenpauschale_flat_rate:3,settings_enable_for_methods_options_Versandkostenpauschale_flat_rate:5,settings_enable_for_methods_options_Kostenlose Lieferung_free_shipping,settings_enable_for_methods_options_Kostenlose Lieferung_free_shipping:2,settings_enable_for_methods_options_Kostenlose Lieferung_free_shipping:4,settings_enable_for_methods_options_Abholung vor Ort_local_pickup,settings_enable_for_virtual_id,settings_enable_for_virtual_label,settings_enable_for_virtual_description,settings_enable_for_virtual_type,settings_enable_for_virtual_value,settings_enable_for_virtual_default,settings_enable_for_virtual_tip,settings_enable_for_virtual_placeholder,settings_api_credentials_id,settings_api_credentials_label,settings_api_credentials_description,settings_api_credentials_type,settings_api_credentials_value,settings_api_credentials_default,settings_api_credentials_tip,settings_api_credentials_placeholder,settings_testmode_id,settings_testmode_label,settings_testmode_description,settings_testmode_type,settings_testmode_value,settings_testmode_default,settings_testmode_tip,settings_testmode_placeholder,settings_test_publishable_key_id,settings_test_publishable_key_label,settings_test_publishable_key_description,settings_test_publishable_key_type,settings_test_publishable_key_value,settings_test_publishable_key_default,settings_test_publishable_key_tip,settings_test_publishable_key_placeholder,settings_test_secret_key_id,settings_test_secret_key_label,settings_test_secret_key_description,settings_test_secret_key_type,settings_test_secret_key_value,settings_test_secret_key_default,settings_test_secret_key_tip,settings_test_secret_key_placeholder,settings_publishable_key_id,settings_publishable_key_label,settings_publishable_key_description,settings_publishable_key_type,settings_publishable_key_value,settings_publishable_key_default,settings_publishable_key_tip,settings_publishable_key_placeholder,settings_secret_key_id,settings_secret_key_label,settings_secret_key_description,settings_secret_key_type,settings_secret_key_value,settings_secret_key_default,settings_secret_key_tip,settings_secret_key_placeholder,settings_webhook_id,settings_webhook_label,settings_webhook_description,settings_webhook_type,settings_webhook_value,settings_webhook_default,settings_webhook_tip,settings_webhook_placeholder,settings_test_webhook_secret_id,settings_test_webhook_secret_label,settings_test_webhook_secret_description,settings_test_webhook_secret_type,settings_test_webhook_secret_value,settings_test_webhook_secret_default,settings_test_webhook_secret_tip,settings_test_webhook_secret_placeholder,settings_webhook_secret_id,settings_webhook_secret_label,settings_webhook_secret_description,settings_webhook_secret_type,settings_webhook_secret_value,settings_webhook_secret_default,settings_webhook_secret_tip,settings_webhook_secret_placeholder,settings_statement_descriptor_id,settings_statement_descriptor_label,settings_statement_descriptor_description,settings_statement_descriptor_type,settings_statement_descriptor_value,settings_statement_descriptor_default,settings_statement_descriptor_tip,settings_statement_descriptor_placeholder,settings_short_statement_descriptor_id,settings_short_statement_descriptor_label,settings_short_statement_descriptor_description,settings_short_statement_descriptor_type,settings_short_statement_descriptor_value,settings_short_statement_descriptor_default,settings_short_statement_descriptor_tip,settings_short_statement_descriptor_placeholder,settings_capture_id,settings_capture_label,settings_capture_description,settings_capture_type,settings_capture_value,settings_capture_default,settings_capture_tip,settings_capture_placeholder,settings_payment_request_id,settings_payment_request_label,settings_payment_request_description,settings_payment_request_type,settings_payment_request_value,settings_payment_request_default,settings_payment_request_tip,settings_payment_request_placeholder,settings_payment_request_button_type_id,settings_payment_request_button_type_label,settings_payment_request_button_type_description,settings_payment_request_button_type_type,settings_payment_request_button_type_value,settings_payment_request_button_type_default,settings_payment_request_button_type_tip,settings_payment_request_button_type_placeholder,settings_payment_request_button_type_options_default,settings_payment_request_button_type_options_buy,settings_payment_request_button_type_options_donate,settings_payment_request_button_type_options_book,settings_payment_request_button_theme_id,settings_payment_request_button_theme_label,settings_payment_request_button_theme_description,settings_payment_request_button_theme_type,settings_payment_request_button_theme_value,settings_payment_request_button_theme_default,settings_payment_request_button_theme_tip,settings_payment_request_button_theme_placeholder,settings_payment_request_button_theme_options_dark,settings_payment_request_button_theme_options_light,settings_payment_request_button_theme_options_light-outline,settings_payment_request_button_locations_id,settings_payment_request_button_locations_label,settings_payment_request_button_locations_description,settings_payment_request_button_locations_type,settings_payment_request_button_locations_value_0,settings_payment_request_button_locations_value_1,settings_payment_request_button_locations_value_2,settings_payment_request_button_locations_default_0,settings_payment_request_button_locations_default_1,settings_payment_request_button_locations_default_2,settings_payment_request_button_locations_tip,settings_payment_request_button_locations_placeholder,settings_payment_request_button_locations_options_product,settings_payment_request_button_locations_options_cart,settings_payment_request_button_locations_options_checkout,settings_payment_request_button_size_id,settings_payment_request_button_size_label,settings_payment_request_button_size_description,settings_payment_request_button_size_type,settings_payment_request_button_size_value,settings_payment_request_button_size_default,settings_payment_request_button_size_tip,settings_payment_request_button_size_placeholder,settings_payment_request_button_size_options_small,settings_payment_request_button_size_options_default,settings_payment_request_button_size_options_large,settings_saved_cards_id,settings_saved_cards_label,settings_saved_cards_description,settings_saved_cards_type,settings_saved_cards_value,settings_saved_cards_default,settings_saved_cards_tip,settings_saved_cards_placeholder,settings_sepa_tokens_for_other_methods_id,settings_sepa_tokens_for_other_methods_label,settings_sepa_tokens_for_other_methods_description,settings_sepa_tokens_for_other_methods_type,settings_sepa_tokens_for_other_methods_value,settings_sepa_tokens_for_other_methods_default,settings_sepa_tokens_for_other_methods_tip,settings_sepa_tokens_for_other_methods_placeholder,settings_logging_id,settings_logging_label,settings_logging_description,settings_logging_type,settings_logging_value,settings_logging_default,settings_logging_tip,settings_logging_placeholder,settings_amazon_pay_button_locations_id,settings_amazon_pay_button_locations_label,settings_amazon_pay_button_locations_description,settings_amazon_pay_button_locations_type,settings_amazon_pay_button_locations_value_0,settings_amazon_pay_button_locations_value_1,settings_amazon_pay_button_locations_default_0,settings_amazon_pay_button_locations_default_1,settings_amazon_pay_button_locations_tip,settings_amazon_pay_button_locations_placeholder,settings_amazon_pay_button_locations_options_product,settings_amazon_pay_button_locations_options_cart,settings_amazon_pay_button_locations_options_checkout,settings_amazon_pay_button_size_id,settings_amazon_pay_button_size_label,settings_amazon_pay_button_size_description,settings_amazon_pay_button_size_type,settings_amazon_pay_button_size_value,settings_amazon_pay_button_size_default,settings_amazon_pay_button_size_tip,settings_amazon_pay_button_size_placeholder,settings_amazon_pay_button_size_options_small,settings_amazon_pay_button_size_options_default,settings_amazon_pay_button_size_options_large,settings_upe_checkout_experience_enabled_id,settings_upe_checkout_experience_enabled_label,settings_upe_checkout_experience_enabled_description,settings_upe_checkout_experience_enabled_type,settings_upe_checkout_experience_enabled_value,settings_upe_checkout_experience_enabled_default,settings_upe_checkout_experience_enabled_tip,settings_upe_checkout_experience_enabled_placeholder,required_settings_keys_0,required_settings_keys_1
pre_install_woocommerce_payments_promotion,"<span class=""gateway-subtitle"" ><img class=""wcpay-visa-icon wcpay-icon"" src=""https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/visa.svg"" alt=""Visa""><img class=""wcpay-mastercard-icon wcpay-icon"" src=""https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/mastercard.svg"" alt=""Mastercard""><img class=""wcpay-amex-icon wcpay-icon"" src=""https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/amex.svg"" alt=""Amex""><img class=""wcpay-googlepay-icon wcpay-icon"" src=""https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/googlepay.svg"" alt=""Googlepay""><img class=""wcpay-applepay-icon wcpay-icon"" src=""https://hawkbull.de/wp-content/plugins/woocommerce/assets/images/payment-methods/applepay.svg"" alt=""Applepay""></span>",,0,False,WooPayments,"Zahlungen leicht gemacht, ohne monatliche Gebühren – exklusiv entwickelt für WooCommerce-Shops. Nimm Zahlungen per Kreditkarte, Debitkarte und andere beliebte Zahlungsmethoden an.",products,refunds,subscriptions,multiple_subscriptions,subscription_cancellation,subscription_reactivation,subscription_suspension,subscription_amount_changes,subscription_date_changes,subscription_payment_method_change_admin,subscription_payment_method_change_customer,subscription_payment_method_change,tokenization,add_payment_method,is_dismissed,Gateway ignorieren,,checkbox,no,no,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=pre_install_woocommerce_payments_promotion,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/pre_install_woocommerce_payments_promotion,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
bacs,Direkte Banküberweisung,Überweise direkt an unsere Bankverbindung. Bitte nutze die Bestellnummer als Verwendungszweck. Deine Bestellung wird erst nach Geldeingang auf unserem Konto versandt.,1,False,Direkte Banküberweisung,Nimm persönlich Zahlungen per BACS entgegen. Eher unter der Bezeichnung „Banküberweisung“ bekannt.,products,,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=bacs,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/bacs,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,title,Titel,"Dies steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.",safe_text,Direkte Banküberweisung,Direkte Banküberweisung,"Dies steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.",,instructions,Anweisungen,"Anweisung, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.",textarea,,,"Anweisung, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
cheque,Scheckzahlungen," Bitte sende deinen Scheck an die Adresse: Shop-Name, Shop-Straße, Shop-PLZ, Shop-Ort, Shop-Land.",2,False,Scheckzahlungen,"Nimm persönlich Zahlungen per Schecks entgegen. Dieser Offline-Zahlungsweg kann auch hilfreich sein, um Käufe zu testen.",products,,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=cheque,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/cheque,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,title,Titel,"Dies steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.",safe_text,Scheckzahlungen,Scheckzahlungen,"Dies steuert den Titel, den der Benutzer beim Bezahlvorgang sieht.",,instructions,Anweisungen,"Anweisung, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.",textarea,,,"Anweisung, die zur „Danke“-Seite und zu E-Mails hinzugefügt werden.",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
cod,Cash on delivery,Pay with cash upon delivery.,3,False,Per Nachnahme,Lass deine Kunden bei der Zustellung bezahlen – in bar oder mit anderen Zahlungsmethoden.,products,,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=cod,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/cod,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,title,Titel,"Beschreibung der Zahlungsmethode, die Kunden bei der Kaufabwicklung sehen.",safe_text,Cash on delivery,Per Nachnahme,"Beschreibung der Zahlungsmethode, die Kunden bei der Kaufabwicklung sehen.",,instructions,Anweisungen,"Anweisungen, die der „Danke“-Seite hinzugefügt werden.",textarea,Pay with cash upon delivery.,Per Nachnahme zahlen.,"Anweisungen, die der „Danke“-Seite hinzugefügt werden.",,enable_for_methods,Für Versandarten verfügbar machen,"Falls die Zahlung per Nachnahme nur für bestimmte Versandarten möglich ist, bitte hier erfassen. Leer lassen, um sie für alle Versandarten verfügbar zu machen.",multiselect,,,"Falls die Zahlung per Nachnahme nur für bestimmte Versandarten möglich ist, bitte hier erfassen. Leer lassen, um sie für alle Versandarten verfügbar zu machen.",,Jede &quot;Versandkostenpauschale&quot; Methode,Andere Standorte &ndash; Expressversand (3-5 days) (#3),Andere Standorte &ndash; Versandkosten (#5),Jede &quot;Kostenlose Lieferung&quot; Methode,Andere Standorte &ndash; Kostenloser Versand (7-10 days) (#2),Andere Standorte &ndash; Kostenloser Versand (#4),Jede &quot;Abholung vor Ort&quot; Methode,enable_for_virtual,Zahlung per Nachnahme für virtuelle Bestellungen akzeptieren,,checkbox,yes,yes,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
ppcp-gateway,Bezahlen Sie per Paypal,,4,True,PayPal,Mit PayPal zahlen.,products,refunds,tokenization,add_payment_method,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=ppcp-gateway,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/ppcp-gateway,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_link,Link,"Link is a payment method that allows customers to save payment information  and use the payment details
			for further payments.",5,True,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_link,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_link,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_klarna,Klarna,Allow customers to pay over time with Klarna.,6,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_klarna,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_klarna,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_affirm,Affirm,Allow customers to pay over time with Affirm.,7,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_affirm,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_affirm,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_afterpay_clearpay,Clearpay / Afterpay,Allow customers to pay over time with Clearpay / Afterpay.,8,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_afterpay_clearpay,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_afterpay_clearpay,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_eps,EPS,EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials.,9,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_eps,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_eps,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_bancontact,Bancontact,"Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation.",10,False,,,products,refunds,subscriptions,tokenization,multiple_subscriptions,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_bancontact,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_bancontact,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_ideal,iDEAL,iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials.,11,False,,,products,refunds,subscriptions,multiple_subscriptions,tokenization,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_ideal,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_ideal,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_sepa_debit,SEPA-Lastschrift,Reach 500 million customers and over 20 million businesses across the European Union.,12,False,,,products,refunds,tokenization,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_sepa_debit,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_sepa_debit,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_p24,Przelewy24,Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods.,13,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_p24,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_p24,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe,Kredit-/Debitkarte,,15,True,Stripe,"Accept debit and credit cards in 135+ currencies, methods such as SEPA, and one-touch checkout with Apple Pay.",products,refunds,tokenization,add_payment_method,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe,,"Your API details can be obtained from your <a href=""https://dashboard.stripe.com/apikeys"">Stripe account</a>. Don’t have a Stripe account? <a href=""https://dashboard.stripe.com/register"">Create one.</a>",https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,api_credentials,Stripe Account Keys,,stripe_account_keys,,,,,testmode,Testmodus aktivieren,Zahlungs-Gateway mithilfe von Test-API-Schlüsseln in den Testmodus versetzen.,checkbox,no,yes,Zahlungs-Gateway mithilfe von Test-API-Schlüsseln in den Testmodus versetzen.,,test_publishable_key,Veröffentlichbarer Testschlüssel,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""pk_test_"" will be saved.",text,,,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""pk_test_"" will be saved.",,test_secret_key,Geheimer Testschlüssel,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""sk_test_"" or ""rk_test_"" will be saved.",password,,,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""sk_test_"" or ""rk_test_"" will be saved.",,publishable_key,Veröffentlichbarer Live-Schlüssel,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""pk_live_"" will be saved.",text,pk_live_51JibzFDQVkNPO1FeL5K9X0KPwWgZbxKQKyRs9uDbvzavkiFkvS8IHerCSSXpsXO9o7x0RheseRUKVx2G4DPErhIT00pwW7hEHa,,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""pk_live_"" will be saved.",,secret_key,Geheimer Live-Schlüssel,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""sk_live_"" or ""rk_live_"" will be saved.",password,***********************************************************************************************************,,"Get your API keys from your stripe account. Invalid values will be rejected. Only values starting with ""sk_live_"" or ""rk_live_"" will be saved.",,webhook,Webhook Endpoints,"You must add the following webhook endpoint <strong style=""background-color:#ddd;"">&nbsp;https://hawkbull.de/?wc-api=wc_stripe&nbsp;</strong> to your <a href=""https://dashboard.stripe.com/account/webhooks"" target=""_blank"">Stripe account settings</a> (if there isn't one already enabled). This will enable you to receive notifications on the charge statuses.<br><br>Warning: The most recent live webhook, received at 2025-05-20 03:29:20 UTC, could not be processed. Reason: The webhook was not signed with the expected signing secret. (The last live webhook to process successfully was timestamped 2025-04-02 10:55:15 UTC.)",title,,,"You must add the following webhook endpoint <strong style=""background-color:#ddd;"">&nbsp;https://hawkbull.de/?wc-api=wc_stripe&nbsp;</strong> to your <a href=""https://dashboard.stripe.com/account/webhooks"" target=""_blank"">Stripe account settings</a> (if there isn't one already enabled). This will enable you to receive notifications on the charge statuses.<br><br>Warning: The most recent live webhook, received at 2025-05-20 03:29:20 UTC, could not be processed. Reason: The webhook was not signed with the expected signing secret. (The last live webhook to process successfully was timestamped 2025-04-02 10:55:15 UTC.)",,test_webhook_secret,Geheimer Webhook-Testschlüssel,Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ,password,,,Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ,,webhook_secret,Geheimer Webhook-Schlüssel,Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ,password,whsec_kURdIcy6jdhv7e3o28Hc5F8OzOAj70vA,,Erhalte deinen geheimen Webhook-Schlüssel im Webhooks-Abschnitt deines Stripe-Kontos. ,,statement_descriptor,Verwendungszweck,"Statement-Descriptors („Schlagwort“/Referenz für Kontoauszug) müssen auf 22 Zeichen begrenzt sein, können nicht die Sonderzeichen >, <, "", \, ', *, /, (, ), {, } enthalten und dürfen nicht ausschließlich aus Nummern bestehen. Das wird auf dem Kontoauszug des Kunden in Großbuchstaben erscheinen.",text,,,"Statement-Descriptors („Schlagwort“/Referenz für Kontoauszug) müssen auf 22 Zeichen begrenzt sein, können nicht die Sonderzeichen >, <, "", \, ', *, /, (, ), {, } enthalten und dürfen nicht ausschließlich aus Nummern bestehen. Das wird auf dem Kontoauszug des Kunden in Großbuchstaben erscheinen.",,short_statement_descriptor,Short Statement Descriptor,Shortened version of the statement descriptor in combination with the customer order number.,text,,,Shortened version of the statement descriptor in combination with the customer order number.,,capture,Karte sofort belasten,"Bestimmt, ob die Belastung direkt erfasst wird oder nicht. Falls nicht aktiviert, gibt die Belastung eine Autorisierung aus und muss später erfasst werden. Nicht erfasste Belastungen laufen in 7 Tagen ab.",checkbox,yes,yes,"Bestimmt, ob die Belastung direkt erfasst wird oder nicht. Falls nicht aktiviert, gibt die Belastung eine Autorisierung aus und muss später erfasst werden. Nicht erfasste Belastungen laufen in 7 Tagen ab.",,payment_request,"Enable Payment Request Buttons. (Apple Pay/Google Pay) <br />By using Apple Pay, you agree to <a href=""https://stripe.com/apple-pay/legal"" target=""_blank"">Stripe</a> and <a href=""https://developer.apple.com/apple-pay/acceptable-use-guidelines-for-websites/"" target=""_blank"">Apple</a>'s terms of service. (Apple Pay domain verification is performed automatically in live mode; configuration can be found on the <a href=""https://dashboard.stripe.com/settings/payments/apple_pay"" target=""_blank"">Stripe dashboard</a>.)","Falls aktiviert und vom Browser unterstützt, können Kunden mit Apple Pay oder Chrome Payment Request zahlen.",checkbox,yes,yes,"Falls aktiviert und vom Browser unterstützt, können Kunden mit Apple Pay oder Chrome Payment Request zahlen.",,payment_request_button_type,Button-Typ,"Wähle den Button-Typ, den du anzeigen willst.",select,buy,default,"Wähle den Button-Typ, den du anzeigen willst.",,Only icon,Kaufen,Spenden,Book,payment_request_button_theme,Button-Theme,"Wähle das Button-Theme, das du anzeigen willst.",select,dark,dark,"Wähle das Button-Theme, das du anzeigen willst.",,Dunkel,Hell,Hell mit Rand,payment_request_button_locations,Payment Request Button Locations,Select where you would like Payment Request Buttons to be displayed,multiselect,product,cart,checkout,product,cart,checkout,Select where you would like Payment Request Buttons to be displayed,,Product,Cart,Checkout,payment_request_button_size,Payment Request Button Size,Select the size of the button.,select,small,default,Select the size of the button.,,Small (40px),Default (48px),Large (56px),saved_cards,Zahlung über gespeicherte Karten aktivieren,"Falls aktiviert, können Benutzer an der Kasse mit einer gespeicherten Karte bezahlen. Die Karteninformationen werden auf Stripe-Servern gespeichert, nicht in Ihrem Shop.",checkbox,yes,yes,"Falls aktiviert, können Benutzer an der Kasse mit einer gespeicherten Karte bezahlen. Die Karteninformationen werden auf Stripe-Servern gespeichert, nicht in Ihrem Shop.",,sepa_tokens_for_other_methods,Enable SEPA Direct Debit tokens for other methods,"If enabled, users will be able to pay with iDEAL or Bancontact and save the method as a SEPA Direct Debit method.",checkbox,yes,yes,"If enabled, users will be able to pay with iDEAL or Bancontact and save the method as a SEPA Direct Debit method.",,logging,Debug-Meldungen protokollieren,Speichere Debug-Meldungen im Protokoll des WooCommerce-Systemstatus.,checkbox,no,no,Speichere Debug-Meldungen im Protokoll des WooCommerce-Systemstatus.,,amazon_pay_button_locations,Amazon Pay Button Locations,Select where you would like Amazon Pay Button to be displayed,multiselect,product,cart,product,cart,Select where you would like Amazon Pay Button to be displayed,,Product,Cart,Checkout,amazon_pay_button_size,Amazon Pay Button Size,Select the size of the button.,select,default,default,Select the size of the button.,,Small (40px),Default (48px),Large (56px),upe_checkout_experience_enabled,"Try the new payment experience (Early access) <br />Get early access to a new, smarter payment experience on checkout and let us know what you think by <a href=""https://woocommerce.survey.fm/woocommerce-stripe-upe-opt-out-survey"" target=""_blank"">submitting your feedback</a>. We recommend this feature for experienced merchants as the functionality is currently limited. <a href=""https://woocommerce.com/document/stripe/admin-experience/new-checkout-experience/"" target=""_blank"">Learn more</a>",New checkout experience allows you to manage all payment methods on one screen and display them to customers based on their currency and location.,checkbox,yes,no,New checkout experience allows you to manage all payment methods on one screen and display them to customers based on their currency and location.,,publishable_key,secret_key
stripe_us_bank_account,ACH Direct Debit,Pay directly from your US bank account via ACH.,,False,,,products,refunds,tokenization,subscriptions,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_us_bank_account,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_us_bank_account,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_alipay,Alipay,"Alipay is a popular wallet in China, operated by Ant Financial Services Group, a financial services provider affiliated with Alibaba.",,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_alipay,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_alipay,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_amazon_pay,Amazon Pay,Amazon Pay is a payment method that allows customers to pay with their Amazon account.,,False,,,products,refunds,tokenization,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_amazon_pay,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_amazon_pay,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_blik,BLIK,BLIK enables customers in Poland to pay directly via online payouts from their bank account.,,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_blik,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_blik,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_boleto,Boleto,"Boleto is an official payment method in Brazil. Customers receive a voucher that can be paid at authorized agencies or banks, ATMs, or online bank portals.",,False,,,products,,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_boleto,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_boleto,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_oxxo,OXXO,OXXO is a Mexican chain of convenience stores that allows customers to pay bills and online purchases in-store with cash.,,False,,,products,,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_oxxo,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_oxxo,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_multibanco,Multibanco,"Multibanco is an interbank network that links the ATMs of all major banks in Portugal, allowing customers to pay through either their ATM or online banking environment.",,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_multibanco,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_multibanco,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_wechat_pay,WeChat Pay,WeChat Pay is a popular mobile payment and digital wallet service by WeChat in China.,,False,,,products,refunds,,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_wechat_pay,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_wechat_pay,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_cashapp,Cash App Pay,"Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet.",,False,,,products,refunds,tokenization,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_cashapp,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_cashapp,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_acss_debit,Pre-Authorized Debit,Canadian Pre-Authorized Debit is a payment method that allows customers to pay using their Canadian bank account.,,False,,,products,refunds,tokenization,subscriptions,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_acss_debit,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_acss_debit,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_bacs_debit,Bacs Direct Debit,Bacs Direct Debit enables customers in the UK to pay by providing their bank account details.,,False,,,products,refunds,tokenization,,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_bacs_debit,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_bacs_debit,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
stripe_au_becs_debit,BECS Direct Debit,Pay directly from your Australian bank account via BECS.,,False,,,products,refunds,tokenization,subscriptions,,,,,,,,,,,,,,,,,,,False,https://hawkbull.de/wp-admin/admin.php?page=wc-settings&tab=checkout&section=stripe_au_becs_debit,,,https://hawkbull.de/wp-json/wc/v3/payment_gateways/stripe_au_becs_debit,GET,POST,PUT,PATCH,https://hawkbull.de/wp-json/wc/v3/payment_gateways,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
