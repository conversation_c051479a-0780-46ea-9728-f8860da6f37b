<?php

namespace App\Core\AmazonCsv\Handlers;

use App\Core\AmazonCsv\Handlers\Contracts\CycleContract;
use App\Core\AmazonCsv\Handlers\Helpers\ParsedData2RawTransactionExtractor;
use App\Core\AmazonCsv\Handlers\Helpers\RawData2DatabaseResolver;
use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportRawData;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Data\Models\EventType;
use App\Core\Data\Models\ItemTaxCode;
use App\Core\System\Helpers\Arr;
use App\Core\System\Helpers\Metric;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class CycleTwo extends CycleContract
{
    private string $delimiter;

    private AmazonReport $amazonReport;

    private ?int $salesChannelId = null;

    private Collection $cycleOneTransactions;

    private RawData2DatabaseResolver $databaseResolver;

    private string $dateTimeFormat;

    private string $csvDelimiter;

    /** @noinspection PhpUnhandledExceptionInspection */
    public function __construct(AmazonReport $amazonReport)
    {
        $this->csvDelimiter = config('evat.amazonParsing.csvDelimiter');
        $this->dateTimeFormat = config('date-formats.db-datetime');
        $this->amazonReport = $amazonReport;

        Metric::addMessage('Cycle 2 - START');

        Metric::start('Init transactions load');
        $this->loadCycleOneTransactions();
        Metric::end('Init transactions load');

        Metric::start('Init database data load');
        $this->databaseResolver = new RawData2DatabaseResolver($amazonReport);
        Metric::end('Init database data load');

        $this->delimiter = AmazonReportRawData::DELIMITER;

        // MUST BE LAST
        if (count($this->cycleOneTransactions) > 0) {
            try {
                $this->run();
            } catch (Throwable $exception) {
                $amazonReport->error_reason = $this->generateErrorReason(
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine(),
                    __LINE__
                );
                $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
                $amazonReport->save();
                if ($this->isDebug()) {
                    throw $exception;
                }
            }
        }
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    private function run(): void
    {
        // Parsing data
        Metric::start('Parsing');
        $transactions = $this->parseData();
        Metric::end('Parsing');

        // Resolve DB Data
        Metric::start('DB resolving');
        $this->databaseResolver->resolve();
        Metric::end('DB resolving');

        // Remove and delete from db transactions of INBOUND or RETURN types
        Metric::start('Removing unused transaction');
        $transactions = $this->stripAndDeleteUnusableTransactions($transactions);
        Metric::end('Removing unused transaction');

        // Bounds loaded database data to transaction
        Metric::start('Bound DB data to raw transactions');
        $transactions = $this->boundDatabaseDataToRawTransaction($transactions);
        Metric::end('Bound DB data to raw transactions');

        // Updates resolved transactions
        Metric::start('Updating resolved transactions');
        $this->updateTransactions($transactions);
        Metric::end('Updating resolved transactions');

        // Sets status for amazon_report
        Metric::start('Setting amazon status');
        $this->setAmazonReportStatus($transactions);
        Metric::end('Setting amazon status');
    }

    /**
     * @return AmazonReport
     */
    public function getAmazonReport(): AmazonReport
    {
        return $this->amazonReport->load('uploadUser');
    }

    private function setAmazonReportStatus(Collection $transactionsData): void
    {
        $amazonReport = $this->amazonReport;

        if ($transactionsData->count() < 1) {
            $messages = $amazonReport->user_messages ?? [];
            $messages[] = 'reports.Report doesnt contain any relevant data';
            $amazonReport->user_messages = $messages;
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_SUCCESS;

            $uploadedAt = Carbon::parse($amazonReport->uploaded_at)->startOfYear();
            $amazonReport->start_date = $uploadedAt->toDateString();
            $amazonReport->end_date = $uploadedAt->toDateString();
            $amazonReport->save();

            return;
        }

        $errorTransactions = $amazonReport
            ->loadCount('errorTransactions')
            ->error_transactions_count;

        $amazonReport->parsing_ended_at = Carbon::now()->toDateTimeString();
        $amazonReport->sales_channel_id = $this->salesChannelId;

        if ($errorTransactions > 0) {
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
            $amazonReport->save();

            return;
        }

        $amazonReport->report_status_id = AmazonReportStatus::STATUS_RESOLVED;
        $amazonReport->save();

        $this->amazonReport = $amazonReport;
    }

    private function parseData(): Collection
    {
        return $this->cycleOneTransactions
            ->map(function (array $amazonReportRawData) {
                $amazonReportRawData = new Arr($amazonReportRawData);
                // MUST BE IN ORDER
                $methods = [
                    'stringToArray',
                    'resolveFieldTypes',
                    'resolveCountry',
                    'resolveCurrency',
                    'resolveAmazonSalesChannel',
                    'resolveMarketplace',
                    'resolveProgram',
                    'resolveEventType',
                    'resolveItemTaxCode',
                    'resolveDeliveryCondition',
                    'resolveTaxReportingScheme',
                    'resolveTaxCollectionResponsibility',
                    // MUST BE LAST
                    'setupHeavyDBData',
                ];
                $errors = [];
                $parsedData = $amazonReportRawData;
                foreach ($methods as $method) {
                    try {
                        $parsedData = $this->{$method}($parsedData);
                    } catch (Throwable $exception) {
                        if ($this->isDebug()) {
                            throw $exception;
                        }
                        $errors[] = $this->generateRawDataErrorReasons(
                            $exception->getCode(),
                            $exception->getMessage(),
                            $exception->getFile(),
                            $exception->getLine(),
                            __LINE__
                        );
                    }
                }

                if (!is_array($parsedData)) {
                    $errors[] = $this->generateRawDataErrorReasons(
                        404,
                        'Data parsed but parsed data returns non-array object',
                        __FILE__,
                        __LINE__,
                        __LINE__
                    );
                }

                if (count($errors) > 0) {
                    $amazonReportRawData->error_reasons = $errors;
                    $amazonReportRawData->success = false;
                }

                if (is_array($parsedData)) {
                    $amazonReportRawData->parsed_data = $parsedData;
                }

                return $amazonReportRawData;
            });
    }

    /**
     * @param Collection $transactions
     * @return Collection
     * @throws Exception
     */
    private function boundDatabaseDataToRawTransaction(Collection $transactions): Collection
    {
        return $transactions->map(function (Arr $transaction) {
            /**
             * @var AmazonReportRawData $amazonReportRawData
             */
            $data = $transaction->parsed_data->toArray();
            if (!is_array($data) || !$transaction->success || !is_null($transaction->error_reasons)) {
                return $transaction;
            }

            $errors = [];
            try {
                $salesChannelData = $this->databaseResolver->getSalesChannel($data['sales_channel_id']);
                $this->salesChannelId = $salesChannelData['id'];
                $data['sales_channel_data'] = $salesChannelData;
                $data['sales_channel_id'] = $salesChannelData['id'];
            } catch (Throwable $exception) {
                if ($this->isDebug()) {
                    throw $exception;
                }

                $errors[] = $this->generateRawDataErrorReasons(
                    $exception->getCode(),
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine(),
                    __LINE__
                );
            }

            try {
                $departureCityData = $this->databaseResolver->getCity($data['departure_city_id']);
                $data['departure_city_data'] = $departureCityData->toArray();
                $data['departure_city_id'] = $departureCityData->id;
            } catch (Throwable) {
            }

            {
                // Resolve because address must be original, and tax country not
                $data['bill_to_country_id'] = $data['arrival_country_id'];
                $data['sale_recipient_country_id'] = $data['sale_arrival_country_id'];
                $data['recipient_country_data'] = $data['arrival_country_data'] ?? null;
                $data['sale_recipient_country_data'] = $data['sale_arrival_country_data'] ?? null;
            }

            try {
                $postalCodeCountryId = $data['arrival_country_id'] ?? $data['sale_arrival_country_id'];
                $postalCodeException = $this->databaseResolver->getPostalCodeException($data['arrival_post_code'], $postalCodeCountryId);
                if (!is_null($postalCodeException)) {
                    $data['bill_to_country_id'] = $postalCodeException->vat_country_id;
                    $data['sale_recipient_country_id'] = $postalCodeException->vat_country_id;
                    $data['recipient_country_data'] = $postalCodeException->vatCountry->toArray();
                    $data['sale_recipient_country_data'] = $postalCodeException->vatCountry->toArray();
                }
            } catch (Throwable) {
            }

            try {
                $arrivalCityData = $this->databaseResolver->getCity($data['arrival_city_id']);
                $data['arrival_city_data'] = $arrivalCityData->toArray();
                $data['arrival_city_id'] = $arrivalCityData->id;
            } catch (Throwable) {
            }

            try {
                $transactionSellerVatNumberData = $this->databaseResolver->getVatNumber($data['transaction_seller_vat_number']);
                $data['transaction_seller_vat_number_data'] = $transactionSellerVatNumberData->toArray();
            } catch (Throwable) {
            }

            try {
                $sellerDepartCountryVatNumberData = $this->databaseResolver->getVatNumber($data['seller_depart_country_vat_number']);
                $data['seller_depart_country_vat_number'] = $sellerDepartCountryVatNumberData->vat_number;
            } catch (Throwable) {
            }

            try {
                $sellerArrivalCountryVatNumberData = $this->databaseResolver->getVatNumber($data['seller_arrival_country_vat_number']);
                $data['seller_arrival_country_vat_number'] = $sellerArrivalCountryVatNumberData->vat_number;
            } catch (Throwable) {
            }

            $data['item_data'] = null;

            if (count($errors) > 0) {
                $transaction->error_reasons = $errors;
                $transaction->success = false;
            }

            $transaction->parsed_data = $data;

            return $transaction;
        });
    }

    /**
     * @param Collection $transactions
     * @return Collection
     */
    private function stripAndDeleteUnusableTransactions(Collection $transactions): Collection
    {
        return $transactions->filter(function (Arr $amazonReportRawData) {
            /**
             * @var AmazonReportRawData $amazonReportRawData
             */
            $parsedData = $amazonReportRawData->parsed_data;
            $eventTypeId = $parsedData['event_type_id'] ?? null;
            if (in_array($eventTypeId, [EventType::INBOUND, EventType::RETURN])) {
                return false;
            }

            if (is_null($parsedData['marketplace_id'] ?? null)) {
                return false;
            }

            return true;
        });
    }

    /**
     * @param array $row
     * @return array
     *
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function setupHeavyDBData(array $row): array
    {
        $salesChannel = $row['sales_channel_id'] ?? null;

        $amazonSalesChannelId = $row['amazon_sales_channel_id'] ?? null;

        $arrivalPostCode = $row['arrival_post_code'] ?? null;
        $arrivalCityId = $row['arrival_city_id'] ?? null;
        $arrivalCountryId = $row['arrival_country_id'] ?? null;
        $saleArrivalCountryId = $row['sale_arrival_country_id'] ?? null;

        $departurePostCode = $row['departure_post_code'] ?? null;
        $departureCityId = $row['departure_city_id'] ?? null;
        $departureCountryId = $row['departure_country_id'] ?? null;
        $saleDepartCountryId = $row['sale_depart_country_id'] ?? null;

        $transactionCompleteDate = $row['transaction_complete_date'] ?? null;
        $eventTypeId = $row['event_type_id'] ?? null;
        $transactionSellerVatNumber = $row['transaction_seller_vat_number'] ?? null;
        $transactionSellerVatNumberCountryId = $row['transaction_seller_vat_number_country_id'] ?? null;
        $buyerVatNumber = $row['buyer_vat_number'] ?? null;
        $buyerVatNumberCountryId = $row['buyer_vat_number_country_id'] ?? null;
        $sellerDepartCountryVatNumber = $row['seller_depart_country_vat_number'] ?? null;
        $sellerDepartVatNumberCountryId = $row['seller_depart_vat_number_country_id'] ?? null;
        $sellerArrivalCountryVatNumber = $row['seller_arrival_country_vat_number'] ?? null;
        $sellerArrivalVatNumberCountryId = $row['seller_arrival_vat_number_country_id'] ?? null;

        $shipFromCountryId = $departureCountryId;
        if (is_null($shipFromCountryId)) {
            $shipFromCountryId = $saleDepartCountryId;
        }

        if (!is_null($arrivalPostCode) && (!is_null($arrivalCountryId) || !is_null($saleArrivalCountryId))) {
            $postalCodeCountryId = $arrivalCountryId ?? $saleArrivalCountryId;
            $this->databaseResolver->addArrivalPostalCodeForLoad($arrivalPostCode, $postalCodeCountryId);
        }

        if (!is_null($salesChannel)) {
            $this->databaseResolver->addSalesChannelForLoad($salesChannel);
        }

        if (!is_null($arrivalCityId) || !is_null($departureCityId)) {
            if (!is_null($arrivalCityId) && !is_null($arrivalCountryId)) {
                $this->databaseResolver->addCityForLoad($arrivalCityId, $arrivalCountryId);
            }
            if (!is_null($departureCityId) && !is_null($departureCountryId)) {
                $this->databaseResolver->addCityForLoad($departureCityId, $departureCountryId);
            }
        }

        if (
            !is_null($amazonSalesChannelId)
            && !is_null($departurePostCode)
            && !is_null($departureCityId)
            && !is_null($shipFromCountryId)
        ) {
            $this->databaseResolver->addWarehouseForLoad(
                $eventTypeId,
                $amazonSalesChannelId,
                $departurePostCode,
                $salesChannel,
                $departureCityId,
                $shipFromCountryId,
                $transactionCompleteDate,
            );
        }

        $this->databaseResolver->addSellerVatNumbersForLoad(
            $eventTypeId,
            $transactionCompleteDate,
            $salesChannel,
            $transactionSellerVatNumber, $transactionSellerVatNumberCountryId,
            $buyerVatNumber, $buyerVatNumberCountryId,
            $sellerDepartCountryVatNumber, $sellerDepartVatNumberCountryId,
            $sellerArrivalCountryVatNumber, $sellerArrivalVatNumberCountryId
        );

        return $row;
    }

    /**
     * @throws \Exception
     */
    private function updateTransactions(Collection $transactionsData): void
    {
        $transactionsData = $transactionsData->map(function (Arr $transaction) {
            return $this->resolveTransactionRow($transaction);
        });

        $successTransactions = $transactionsData
            ->filter(function (Arr $transaction) {
                return $transaction->success;
            })->map(function (Arr $data) {
                /**
                 * @var AmazonReportRawData|Arr $data
                 */
                $key = [
                    $data->event_type_id,
                    $data->transaction_event_id,
                    $data->activity_transaction_id,
                ];

                $data->unique_invoice_key = implode('-', $key);

                return $data;
            })->values();

        $errorTransactions = $transactionsData->filter(function (Arr $transaction) {
            return !$transaction->success;
        })->values();

        if ($errorTransactions->count() > 0) {
            $fileData = [];
            foreach ($errorTransactions as $transaction) {
                $data = $this->resolveTransactionRowForCsv($transaction);
                $fileData[] = implode($this->csvDelimiter, $data);
            }
            $fileData = implode("\n", $fileData);

            $path = tmp_path('2_' . $this->amazonReport->id . time() . Str::random() . '.csv');
            $this->storeFile($path, $fileData);

            $query = $this->getUpdateQuery($path);
            DB::statement($query);

            try {
                unlink($path);
            } catch (Throwable) {
            }
        }

        if ($successTransactions->count() > 0) {
            $this->storeJsonFileForCycle(2, $successTransactions->toArray());
        }
    }

    private function resolveTransactionRowForCsv(Arr $transaction): array
    {
        /**
         * @var AmazonReportRawData $transaction
         */
        $success = $transaction->success;
        $eventTypeId = $transaction->event_type_id;
        $transactionEventId = $transaction->transaction_event_id;
        $activityTransactionId = $transaction->activity_transaction_id;
        $transactionDate = $transaction->transaction_date;
        $data = is_null($transaction->data) ? 'null' : "'" . $transaction->data . "'";

        /**
         * @var Arr|null $parsedData
         */
        $parsedData = $transaction->parsed_data;
        if (!is_null($parsedData)) {
            $parsedData = AmazonReportRawData::encodeErrorsOrParsedData($parsedData->toArray());
        } else {
            $parsedData = 'null';
        }
        /**
         * @var Arr|null $errorReasons
         */
        $errorReasons = $transaction->error_reasons;
        if (!is_null($errorReasons)) {
            $errorReasons = AmazonReportRawData::encodeErrorsOrParsedData($errorReasons->toArray());
        } else {
            $errorReasons = 'null';
        }

        return [
            $transaction->cycle,
            $transaction->amazon_report_id,
            $success === true ? 'true' : 'false',
            $errorReasons,
            $transaction->column_count,
            $eventTypeId ?? 'null',
            $transactionEventId ?? 'null',
            $activityTransactionId ?? 'null',
            $transactionDate ?? 'null',
            $data,
            $parsedData,
        ];
    }

    private function resolveTransactionRow(Arr $transaction): Arr
    {
        /**
         * @var AmazonReportRawData $transaction
         */
        $success = $transaction->success;
        $eventTypeId = null;
        $transactionEventId = null;
        $activityTransactionId = null;
        $transactionDate = null;
        $data = $transaction->data;
        /**
         * @var Arr $parsed
         */
        $parsed = $transaction->parsed_data;
        $errors = $transaction->error_reasons ?? [];

        $parsedData = null;
        $errorReasons = null;
        $extractor = new ParsedData2RawTransactionExtractor($parsed);
        try {
            try {
                $eventTypeId = $extractor->getEventTypeId();
            } catch (Throwable) {
            }
            try {
                $transactionEventId = $extractor->getTransactionEventId();
            } catch (Throwable) {
            }
            try {
                $activityTransactionId = $extractor->getActivityTransactionId();
            } catch (Throwable) {
            }
            try {
                $transactionDate = $extractor->getTransactionDate();
            } catch (Throwable) {
            }

            $parsedData = $parsed->toArray();
        } catch (Throwable $exception) {
            if ($this->isDebug()) {
                /** @noinspection PhpUnhandledExceptionInspection */
                throw $exception;
            }
            $errors[] = [
                'code'     => $exception->getCode(),
                'message'  => $exception->getMessage(),
                'location' => __FILE__ . ':L' . $exception->getLine(),
            ];
        }

        if (count($errors) > 0) {
            $success = false;
            $errorReasons = $errors;
        } else {
            $data = null;
        }

        return new Arr([
            'cycle'                   => AmazonReportRawData::CYCLE_PARTIALLY_DB_RESOLVED,
            'amazon_report_id'        => $this->amazonReport->id,
            'success'                 => $success,
            'error_reasons'           => $errorReasons,
            'column_count'            => $transaction->column_count,
            'event_type_id'           => $eventTypeId,
            'transaction_event_id'    => $transactionEventId,
            'activity_transaction_id' => $activityTransactionId,
            'transaction_date'        => $transactionDate,
            'data'                    => $data,
            'parsed_data'             => $parsedData,
        ]);
    }

    /**
     * @param string $path
     * @return string
     */
    private function getUpdateQuery(string $path): string
    {
        $columns = [
            'cycle',
            'amazon_report_id',
            'success',
            'error_reasons',
            'column_count',
            'event_type_id',
            'transaction_event_id',
            'activity_transaction_id',
            'transaction_date',
            'data',
            'parsed_data',
        ];

        $start = [];
        $start[] = 'COPY amazon_reports_raw_data';
        $start[] = '(' . implode(', ', $columns) . ')';
        $start[] = 'FROM';
        $start[] = "'$path'";
        $start[] = 'DELIMITER';
        $start[] = "'$this->csvDelimiter'";
        $start[] = 'NULL AS';
        $start[] = "'null';";

        return implode(' ', $start);
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveAmazonSalesChannel(array $row): array
    {
        $field = 'amazon_sales_channel_id';
        try {
            $salesChannelId = $row[$field] ?? '';
            $salesChannelId = $this->databaseResolver->getAmazonSalesChannelByCode($salesChannelId)->id;
            $row[$field] = $salesChannelId;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Sales channel not found - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveMarketplace(array $row): array
    {
        $field = 'marketplace_id';
        try {
            $marketplaceId = $row[$field] ?? null;

            $skipMarketplaces = [
                'N/A',
                'Off-Amazon'
            ];
            if (in_array($marketplaceId, $skipMarketplaces)) {
                $row[$field] = null;
                $marketplaceId = null;
            }

            if (is_null($marketplaceId)) {
                return $row;
            }
            $marketplaceId = mb_strtolower($marketplaceId);
            $marketplace = $this->databaseResolver->getMarketplaceByName($marketplaceId);
            $row['marketplace_data'] = $marketplace->toArray();
            $row[$field] = $marketplace->id;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Marketplace not found - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveProgram(array $row): array
    {
        $field = 'program_type_id';
        try {
            if (isset($row[$field])) {
                $programTypeId = $row[$field];
                $programTypeId = $this->databaseResolver->getProgramTypeByValue($programTypeId)->id;
                $row[$field] = $programTypeId;
            }
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Program type not found - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveEventType(array $row): array
    {
        $field = 'event_type_id';
        try {
            $eventTypeId = $row[$field] ?? '';
            $eventType = $this->databaseResolver->getEventTypeByAmazonCode($eventTypeId);
            $row['event_type_data'] = $eventType->toArray();
            $row[$field] = $eventType->id;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Transaction type not found - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveItemTaxCode(array $row): array
    {
        $field = 'item_tax_code_id';
        try {
            $itemTaxCodeName = $row[$field] ?? ItemTaxCode::A_GEN_STANDARD_CODE;
            if (is_null($itemTaxCodeName)) {
                return $row;
            }
            $itemTaxCodeId = $this->databaseResolver->getItemTaxCodeByCode($itemTaxCodeName)->id;
            $row[$field] = $itemTaxCodeId;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Product tax code not found - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveDeliveryCondition(array $row): array
    {
        $field = 'delivery_condition_id';
        try {
            $deliveryConditionId = $row[$field] ?? null;
            if (is_null($deliveryConditionId)) {
                return $row;
            }
            $deliveryConditionId = $this->databaseResolver->getDeliveryConditionByCode($deliveryConditionId)->id;
            $row[$field] = $deliveryConditionId;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Delivery condition not found - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveTaxReportingScheme(array $row): array
    {
        $field = 'tax_reporting_scheme_id';
        try {
            if (isset($row[$field])) {
                $value = $row[$field];
                $value = trim($value);
                if (mb_strlen($value) < 1) {
                    $value = null;
                }

                if (!is_null($value)) {
                    $row[$field] = $this->databaseResolver->getTaxReportingSchemeByAmazonCode($value)->id;
                }
            }

        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Tax reporting scheme resolving error - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveTaxCollectionResponsibility(array $row): array
    {
        $field = 'tax_collection_responsibility_id';
        try {
            if (isset($row[$field])) {
                $value = $row[$field];
                $value = trim($value);
                if (mb_strlen($value) < 1) {
                    $value = null;
                }

                if (!is_null($value)) {
                    $row[$field] = $this->databaseResolver->getTaxCollectionResponsibilityByAmazonCode($value)->id;
                }
            }

        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Tax collection responsibility resolving error - ' . $row[$field]);
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveCurrency(array $row): array
    {
        $countriesFields = [
            'transaction_currency_id',
            'vat_inv_currency_id',
        ];

        foreach ($countriesFields as $field) {
            $value = $row[$field] ?? null;
            if (is_null($value)) {
                continue;
            }

            try {
                $row[$field] = $this->databaseResolver->getCurrencyByCode($value)->id;
            } catch (Throwable) {
                throw new Exception('amazon.report-parsing-error-reason.Currency not found - ' . $value);
            }
        }

        return $row;
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveCountry(array $row): array
    {
        $countriesFields = [
            'item_manufacture_country_id',
            'departure_country_id',
            'arrival_country_id',
            'sale_depart_country_id',
            'sale_arrival_country_id',
            'seller_depart_vat_number_country_id',
            'seller_arrival_vat_number_country_id',
            'transaction_seller_vat_number_country_id',
            'buyer_vat_number_country_id',
            'tax_calculation_imputation_country_id'
        ];

        foreach ($countriesFields as $field) {
            $value = $row[$field] ?? null;
            if (is_null($value)) {
                continue;
            }
            $dataFieldName = substr($field, 0, -3) . '_data';
            try {
                $country = $this->databaseResolver->getCountryByCode($value);
                $row[$dataFieldName] = $country->toArray();
                $row[$field] = $country->id;
            } catch (Throwable) {
                $suffix = '(field: ' . $field . ' -> value: ' . $value . ')';
                throw new Exception('amazon.report-parsing-error-reason.Country not found - ' . $suffix);
            }
        }

        return $row;
    }

    /**
     * @param \App\Core\System\Helpers\Arr $amazonReportRawData
     * @return array
     * @throws \Throwable
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function stringToArray(Arr $amazonReportRawData): array
    {
        /**
         * @var AmazonReportRawData $amazonReportRawData
         */
        try {
            //43894
            $raw = $amazonReportRawData->data ?? '';
            $descriptionIndex = $this->getCorrectDescriptionIndex($amazonReportRawData);
            $columnsCount = $amazonReportRawData->column_count;
            $data = explode($this->delimiter, $raw);
            $data = collect($data)->map(function ($value) {
                $value = $value ?? '';
                $value = trim($value, '"');

                return str_replace('"', '“', $value);
            });

            $firstPartTake = $descriptionIndex;
            $firstPart = collect();
            $lastPartRaw = collect();

            foreach ($data as $key => $column) {
                if ($key < $firstPartTake) {
                    $firstPart->push($column);
                    continue;
                }

                $lastPartRaw->push($column);
            }

            $lastPartRaw = $lastPartRaw->reverse()->values();
            $lastPartTake = $columnsCount - $descriptionIndex - 1;
            $lastPart = collect();
            $description = collect();
            foreach ($lastPartRaw as $key => $column) {
                if ($key < $lastPartTake) {
                    $lastPart->push($column);
                    continue;
                }

                $description->push($column);
            }

            $description = $description->implode(function (string $description) {
                return $description;
            }, ' ');

            $lastPart = $lastPart->push($description)->reverse()->values();
            $data = $firstPart->merge($lastPart)->toArray();
        } catch (Throwable $exception) {
            if ($this->isDebug()) {
                throw $exception;
            }
        }

        if (count($data) !== $columnsCount) {
            throw new Exception('amazon.report-parsing-error-reason.Column count error');
        }

        try {
            $correctData = [];
            $columns = $this->getCorrectColumns($amazonReportRawData);
            foreach ($data as $key => $value) {
                $correctData[$columns[$key]] = $value;
            }

            return $correctData;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Could not assign keys to parsed row');
        }
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function resolveFieldTypes(array $row): array
    {
        $data = [];
        foreach ($row as $key => $value) {
            try {
                $value = $this->emptyValueToNull($value);
                if (is_null($value)) {
                    $data[$key] = $value;
                    continue;
                }
            } catch (Throwable) {
                throw new Exception('amazon.report-parsing-error-reason.Cleaning fields error');
            }

            try {
                if (is_null($row['tax_calculation_date'])) {
                    $row['tax_calculation_date'] = $row['transaction_complete_date'];
                }

                $value = $this->resolveDate($key, $value);
            } catch (Throwable) {
                throw new Exception('amazon.report-parsing-error-reason.Date error');
            }

            try {
                $value = $this->resolveInt($key, $value);
            } catch (Throwable) {
                throw new Exception('amazon.report-parsing-error-reason.Casting to integer error');
            }

            try {
                $value = $this->resolveFloat($key, $value);
            } catch (Throwable) {
                throw new Exception('amazon.report-parsing-error-reason.Casting to float error');
            }

            $invoiceNumberFields = ['activity_transaction_id'];
            if (in_array($key, $invoiceNumberFields)) {
                $value = $this->clearForInvoiceNumber($value);
            }

            $data[$key] = $value;
        }

        return $this->resolveExportOutsideEu($data);
    }

    /**
     * @param array $row
     * @return array
     * @throws Exception
     */
    private function resolveExportOutsideEu(array $row): array
    {
        try {
            $exportOutsideEu = $row['export_outside_eu'];
            if (!is_null($exportOutsideEu)) {
                $exportOutsideEu = $exportOutsideEu === 'y';
                $row['export_outside_eu'] = $exportOutsideEu;
            }

            return $row;
        } catch (Throwable) {
            throw new Exception('amazon.report-parsing-error-reason.Resolving export outside EU error');
        }
    }

    /**
     * @param string $key
     * @param mixed $value
     * @return string|int|float|null
     */
    private function resolveFloat(string $key, mixed $value): string|int|float|null
    {
        $floatFields = [
            'item_weight',
            'total_activity_weight',
            'cost_price_of_items',
            'price_of_items_amt_vat_excl',
            'promo_price_of_items_amt_vat_excl',
            'total_price_of_items_amt_vat_excl',
            'ship_charge_amt_vat_excl',
            'promo_ship_charge_amt_vat_excl',
            'total_ship_charge_amt_vat_excl',
            'gift_wrap_amt_vat_excl',
            'promo_gift_wrap_amt_vat_excl',
            'total_gift_wrap_amt_vat_excl',
            'total_activity_value_amt_vat_excl',
            'price_of_items_vat_rate_percent',
            'price_of_items_vat_amt',
            'promo_price_of_items_vat_amt',
            'total_price_of_items_vat_amt',
            'ship_charge_vat_rate_percent',
            'ship_charge_vat_amt',
            'promo_ship_charge_vat_amt',
            'total_ship_charge_vat_amt',
            'gift_wrap_vat_rate_percent',
            'gift_wrap_vat_amt',
            'promo_gift_wrap_vat_amt',
            'total_gift_wrap_vat_amt',
            'total_activity_value_vat_amt',
            'price_of_items_amt_vat_incl',
            'promo_price_of_items_amt_vat_incl',
            'total_price_of_items_amt_vat_incl',
            'ship_charge_amt_vat_incl',
            'promo_ship_charge_amt_vat_incl',
            'total_ship_charge_amt_vat_incl',
            'gift_wrap_amt_vat_incl',
            'promo_gift_wrap_amt_vat_incl',
            'total_gift_wrap_amt_vat_incl',
            'total_activity_value_amt_vat_incl',
            'vat_inv_converted_amt',
            'vat_inv_exchange_rate',
        ];

        if (is_null($value) || !in_array($key, $floatFields)) {
            return $value;
        }

        return (float)$value;
    }

    /**
     * @param string $key
     * @param mixed|null $value
     * @return string|int|float|null
     */
    private function resolveInt(string $key, mixed $value = null): string|int|float|null
    {
        $intFields = [
            'qty',
            'item_qty_supplementary_unit',
            'total_activity_supplementary_unit'
        ];

        if (is_null($value) || !in_array($key, $intFields)) {
            return $value;
        }

        return (int)$value;
    }

    /**
     * @param string $key
     * @param mixed|null $value
     * @return string|int|float|null
     */
    private function resolveDate(string $key, mixed $value = null): string|int|float|null
    {
        $dateFields = [
            'tax_calculation_date',
            'transaction_departure_date',
            'transaction_arrival_date',
            'transaction_complete_date',
            'vat_inv_exchange_rate_date',
        ];

        if (is_null($value) || !in_array($key, $dateFields)) {
            return $value;
        }

        return $this->parseDate($value);
    }

    /**
     * @param string $date
     * @return Carbon
     */
    private function parseDate(string $date): string
    {
        $date = str_replace('/', '-', $date);
        $date = str_replace(' ', '', $date);

        return Carbon::parse($date)->format($this->dateTimeFormat);
    }

    /**
     * @param $var
     * @return string|null
     */
    private function emptyValueToNull($var): ?string
    {
        $var = $var . '';
        $var = trim($var);
        if (mb_strlen($var) < 1) {
            $var = null;
        }

        return $var;
    }

    /**
     * @param object $amazonReportRawData
     * @return array
     */
    private function getCorrectColumns(object $amazonReportRawData): array
    {
        /**
         * @var AmazonReportRawData $amazonReportRawData
         */
        $cols = self::PARSED_COLUMN_KEYS;
        $columnCount = $amazonReportRawData->column_count;
        $unsetCols = [4, 93, 94];
        if ($columnCount !== 95) {
            foreach ($unsetCols as $col) {
                unset($cols[$col]);
            }
        }

        return array_values($cols);
    }

    /**
     * @param object $amazonReportRawData
     * @return int
     */
    private function getCorrectDescriptionIndex(object $amazonReportRawData): int
    {
        /**
         * @var AmazonReportRawData $amazonReportRawData
         */
        $columnCount = $amazonReportRawData->column_count;
        $index = 14;
        if ($columnCount !== 95) {
            $index = 13;
        }

        return $index;
    }

    private function loadCycleOneTransactions(): void
    {
        $this->cycleOneTransactions = collect($this->getJsonFileForCycle(1)?->toArray() ?? []);
    }

    protected function getCurrentFile(): string
    {
        return __FILE__;
    }

    // DO NOT PUT OPTIONAL PARAMS IN THIS ARRAY
    const PARSED_COLUMN_KEYS = [
        0  => 'sales_channel_id',
        1  => 'activity_period',
        2  => 'amazon_sales_channel_id',
        3  => 'marketplace_id',
        4  => 'program_type_id',
        5  => 'event_type_id',
        6  => 'transaction_event_id',
        7  => 'activity_transaction_id',
        8  => 'tax_calculation_date',
        9  => 'transaction_departure_date',
        10 => 'transaction_arrival_date',
        11 => 'transaction_complete_date',
        12 => 'item_id',
        13 => 'asin',
        14 => 'item_description',
        15 => 'item_manufacture_country_id',
        16 => 'qty',
        17 => 'item_weight',
        18 => 'total_activity_weight',
        19 => 'cost_price_of_items',
        20 => 'price_of_items_amt_vat_excl',
        21 => 'promo_price_of_items_amt_vat_excl',
        22 => 'total_price_of_items_amt_vat_excl',
        23 => 'ship_charge_amt_vat_excl',
        24 => 'promo_ship_charge_amt_vat_excl',
        25 => 'total_ship_charge_amt_vat_excl',
        26 => 'gift_wrap_amt_vat_excl',
        27 => 'promo_gift_wrap_amt_vat_excl',
        28 => 'total_gift_wrap_amt_vat_excl',
        29 => 'total_activity_value_amt_vat_excl',
        30 => 'price_of_items_vat_rate_percent',
        31 => 'price_of_items_vat_amt',
        32 => 'promo_price_of_items_vat_amt',
        33 => 'total_price_of_items_vat_amt',
        34 => 'ship_charge_vat_rate_percent',
        35 => 'ship_charge_vat_amt',
        36 => 'promo_ship_charge_vat_amt',
        37 => 'total_ship_charge_vat_amt',
        38 => 'gift_wrap_vat_rate_percent',
        39 => 'gift_wrap_vat_amt',
        40 => 'promo_gift_wrap_vat_amt',
        41 => 'total_gift_wrap_vat_amt',
        42 => 'total_activity_value_vat_amt',
        43 => 'price_of_items_amt_vat_incl',
        44 => 'promo_price_of_items_amt_vat_incl',
        45 => 'total_price_of_items_amt_vat_incl',
        46 => 'ship_charge_amt_vat_incl',
        47 => 'promo_ship_charge_amt_vat_incl',
        48 => 'total_ship_charge_amt_vat_incl',
        49 => 'gift_wrap_amt_vat_incl',
        50 => 'promo_gift_wrap_amt_vat_incl',
        51 => 'total_gift_wrap_amt_vat_incl',
        52 => 'total_activity_value_amt_vat_incl',
        53 => 'transaction_currency_id',
        54 => 'commodity_code',
        55 => 'statistical_code_depart',
        56 => 'statistical_code_arrival',
        57 => 'commodity_code_supplementary_unit',
        58 => 'item_qty_supplementary_unit',
        59 => 'total_activity_supplementary_unit',
        60 => 'item_tax_code_id',
        61 => 'departure_city_id',
        62 => 'departure_country_id',
        63 => 'departure_post_code',
        64 => 'arrival_city_id',
        65 => 'arrival_country_id',
        66 => 'arrival_post_code',
        67 => 'sale_depart_country_id',
        68 => 'sale_arrival_country_id',
        69 => 'transportation_mode',
        70 => 'delivery_condition_id',
        71 => 'seller_depart_vat_number_country_id',
        72 => 'seller_depart_country_vat_number',
        73 => 'seller_arrival_vat_number_country_id',
        74 => 'seller_arrival_country_vat_number',
        75 => 'transaction_seller_vat_number_country_id',
        76 => 'transaction_seller_vat_number',
        77 => 'buyer_vat_number_country_id',
        78 => 'buyer_vat_number',
        79 => 'tax_calculation_imputation_country_id',
        80 => 'taxable_jurisdiction',
        81 => 'taxable_jurisdiction_level',
        82 => 'vat_inv_number',
        83 => 'vat_inv_converted_amt',
        84 => 'vat_inv_currency_id',
        85 => 'vat_inv_exchange_rate',
        86 => 'vat_inv_exchange_rate_date',
        87 => 'export_outside_eu',
        88 => 'invoice_url',
        89 => 'buyer_name',
        90 => 'arrival_address',
        91 => 'supplier_name',
        92 => 'bill_from_vat_number',
        93 => 'tax_reporting_scheme_id',
        94 => 'tax_collection_responsibility_id',
    ];
}
