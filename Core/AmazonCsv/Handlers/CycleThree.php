<?php

namespace App\Core\AmazonCsv\Handlers;

use App\Core\AmazonCsv\Handlers\Contracts\CycleContract;
use App\Core\AmazonCsv\Handlers\Helpers\Calculator;
use App\Core\AmazonCsv\Handlers\Helpers\ItemSalePricesResolver;
use App\Core\AmazonCsv\Handlers\Helpers\RawData2DetectionPropertiesExtractor;
use App\Core\AmazonCsv\Handlers\Helpers\ResolvedData2DatabaseResolver;
use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportRawData;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\Currency;
use App\Core\Data\Models\EventType;
use App\Core\Data\Models\InvoiceStatus;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\Item;
use App\Core\Data\Models\ItemIdentificationType;
use App\Core\Data\Models\ItemMarketplace;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use App\Core\Invoices\Invoice\VatDetection;
use App\Core\Items\Contracts\ItemServiceContract;
use App\Core\System\Helpers\Metric;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class CycleThree extends CycleContract
{
    private int $updateChunkSize = 500;
    private int $insertChunkSize = 500;
    private AmazonReport $amazonReport;

    private ItemRepositoryContract $itemsRepo;
    private ResolvedData2DatabaseResolver $databaseResolver;
    private ItemServiceContract $itemService;
    private array $shippingForInsert = [];
    private array $wrapForInsert = [];
    private string $csvDelimiter;
    private array $items = [];
    private Collection $asins;
    private Collection $itemsDb;
    private array $transactionsToCreate = [];
    private Collection $invoiceSubtypes;

    /** @noinspection PhpUnhandledExceptionInspection */
    public function __construct(AmazonReport $amazonReport)
    {
        $this->csvDelimiter = config('evat.amazonParsing.csvDelimiter');

        $this->itemService = app()->make(ItemServiceContract::class);
        $this->itemsRepo = app()->make(ItemRepositoryContract::class);

        $this->itemsDb = collect();
        $this->asins = collect();

        $this->amazonReport = $amazonReport;

        /**
         * @var InvoiceRepositoryContract $invoiceRepo
         */
        $invoiceRepo = app()->make(InvoiceRepositoryContract::class);
        $this->invoiceSubtypes = $invoiceRepo->getAllInvoiceSubtypes()->keyBy('id');

        $this->databaseResolver = new ResolvedData2DatabaseResolver();

        // MUST BE LAST
        try {
            $this->run();
        } catch (Throwable $exception) {
            $amazonReport->error_reason = $this->generateErrorReason(
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine(),
                __LINE__
            );
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
            $amazonReport->save();
            if ($this->isDebug()) {
                throw $exception;
            }
        }
    }

    private function findItemsToDetectChanges(array $invoices): void
    {
        $asins = collect();
        foreach ($invoices as $invoiceItems) {
            foreach ($invoiceItems as $invoiceItem) {
                $data = $invoiceItem['parsed_data'];
                $asin = $data['asin'] ?? null;
                $companyId = $data['sales_channel_data']['company_id'] ?? null;
                if (!is_null($asin) && !is_null($companyId)) {
                    $asins->put($asin, [
                        'asin'      => $asin,
                        'companyId' => $companyId,
                    ]);
                }
            }
        }

        $this->asins = $this->getItemsByAsins($asins->toArray());
    }

    /**
     * IMPORTANT - order is IMPORTANT
     *
     * @throws Exception
     * @noinspection PhpDocMissingThrowsInspection
     */
    private function run(): void
    {
        Metric::addMessage('Cycle 3 - START');
        /*
         * Fetch transactions grouped by invoice
         */
        Metric::start('getRawTransactionsGroupedByInvoice');
        $invoices = $this->getRawTransactionsGroupedByInvoice();
        Metric::end('getRawTransactionsGroupedByInvoice');

        Metric::start('finding items to detect vat rate changes');
        $this->findItemsToDetectChanges($invoices);
        Metric::end('finding items to detect vat rate changes');

        /*
         * Make first pass, detect taxCalculationCountry and add it for load
         * Also add item tax code for load
         */
        Metric::start('first detect and add data for load');
        $invoices = $this->resolveFirstDetectAndAddDataForLoad($invoices);
        Metric::end('first detect and add data for load');

        // Resolve DB data
        Metric::start('resolving db data');
        $this->databaseResolver->resolve();
        Metric::end('resolving db data');

        /*
         * Calculates net value for items/shipping/promo...
         */
        Metric::start('calculating net');
        $invoices = $this->calculateDataAndGenerateInvoiceData($invoices);
        Metric::end('calculating net');

        /*
         * Second pass, second detect
         */
        Metric::start('second detect');
        $invoices = $this->resolveSecondDetectAndAddItemDataForLoad($invoices);
        Metric::end('second detect');

        /*
         * Re-calculates net value for items/shipping/promo...
         */
        Metric::start('re-calculating net');
        $invoices = $this->reCalculateInvoiceData($invoices);
        Metric::end('re-calculating net');

        $invoices = $this->saveAndBindSpecial($invoices);
        /*
         * Resolve items
         */
        Metric::start('resolve items');
        $this->resolveAndStoreItems();
        Metric::end('resolve items');

        /*
         * Binding items to invoices
         */
        Metric::start('binding items to invoices');
        /** @noinspection PhpUnhandledExceptionInspection */
        $invoices = $this->bindItemsToInvoicesAndCheckPurchasePriceIfNeeded($invoices);
        Metric::end('binding items to invoices');

        /*
         * Update raw transaction
         */
        Metric::start('update amazon report transactions raw');
        /** @noinspection PhpUnhandledExceptionInspection */
        $this->updateAmazonReportTransactionsRaw($invoices);
        Metric::end('update amazon report transactions raw');

        /*
         * Storing shipping and wrap as invoice items
         */
        Metric::start('store shipping and wrap');
        $this->storeShippingAndWrap();
        Metric::end('store shipping and wrap');

        /*
         * Sets status for amazon_report
         */
        Metric::start('set amazon report status');
        $this->setAmazonReportStatus();
        Metric::end('set amazon report status');
    }

    /**
     * @return AmazonReport
     */
    public function getAmazonReport(): AmazonReport
    {
        return $this->amazonReport->load('uploadUser');
    }

    /**
     * @param array $invoices
     * @return array
     */
    private function resolveFirstDetectAndAddDataForLoad(array $invoices): array
    {
//        $testKey = '1-305-9354985-4877914-U3sdqfGTr';
//        $invoices = [$testKey => $invoices[$testKey]];

        return array_map(function (array $invoiceItems) {
            return $this->resolveSingleInvoiceFirstPass($invoiceItems);
        }, $invoices);
    }


    /**
     * @param array $invoiceData
     * @return array
     */
    private function resolveSingleInvoiceFirstPass(array $invoiceData): array
    {
        return array_map(function (array $amazonReportRawData) {
            try {
                $data = $amazonReportRawData['parsed_data'];

                $detectionExtractor = new RawData2DetectionPropertiesExtractor($data);

                $itemTaxCodeId = $data['item_tax_code_id'];
                $asin = $data['asin'];
                $itemTaxCodeId = $this->resolveItemTaxCode($itemTaxCodeId, $asin);
                $data['item_tax_code_id'] = $itemTaxCodeId;

                $breakpoint = strtotime('2021-07-01');
                $breakpointGb = strtotime('2021-01-01');

                $taxCalculationDateTimestamp = $detectionExtractor->getTaxCalculationDateTimestamp();
                $taxCalculationDate = $detectionExtractor->getTaxCalculationDate();
                // Set after check
                $data['tax_calculation_date'] = $taxCalculationDate;

                $companyCountryId = $detectionExtractor->getCompanyCountryId();
                $shipFromCountryId = $detectionExtractor->getShipFromCountryId();
                $recipientCountryId = $detectionExtractor->getRecipientCountryId();
                $isDomesticReverseChargeApplicableInRecipientCountry = $detectionExtractor->isDomesticReverseChargeApplicableInRecipientCountry();
                $isRecipientCountryInEu = $detectionExtractor->isRecipientCountryInEu();
                $isShipFromCountryInEu = $detectionExtractor->isShipFromCountryInEu();
                $invoiceTypeId = $detectionExtractor->getInvoiceTypeId();
                $whetherTheCompanyHasPassedThreshold = $detectionExtractor->whetherTheCompanyHasPassedThreshold();
                $buyerVatNumberCountryId = $detectionExtractor->getBuyerVatNumberCountryId();
                $isCompanyCountryVatNumberPermanentEstablishment = $detectionExtractor->isCompanyCountryVatNumberPermanentEstablishment();

                // Check if fc transfer passes all checks
                $fcTransferCheckErrors = $detectionExtractor->checkFcTransfer();
                $data['messages'] = $fcTransferCheckErrors;

                $detectProps = [
                    // common
                    'companyCountryId'                                    => $companyCountryId,
                    'shipFromCountryId'                                   => $shipFromCountryId,
                    'recipientCountryId'                                  => $recipientCountryId,
                    'isDomesticReverseChargeApplicableInRecipientCountry' => $isDomesticReverseChargeApplicableInRecipientCountry,
                    'isRecipientCountryInEu'                              => $isRecipientCountryInEu,
                    'isShipFromCountryInEu'                               => $isShipFromCountryInEu,
                    'invoiceTypeId'                                       => $invoiceTypeId,
                    'whetherTheCompanyHasPassedThreshold'                 => $whetherTheCompanyHasPassedThreshold,
                    'buyerVatNumberCountryId'                             => $buyerVatNumberCountryId
                ];

                $newWay = $taxCalculationDateTimestamp >= $breakpoint;
                $checkCountries = [
                    $data['sale_arrival_country_id'],
                    $data['sale_depart_country_id'],
                ];
                if (in_array(Country::GB, $checkCountries) && $taxCalculationDateTimestamp >= $breakpointGb) {
                    $newWay = true;
                }

                if ($newWay) {
                    $isCompanyRegisteredForOSS = $detectionExtractor->isCompanyRegisteredForOSS();
                    $companyOssRegistrationCountryId = $detectionExtractor->getCompanyOSSRegistrationCountryId();
                    $isMarketplaceRegisteredForOSS = $detectionExtractor->isMarketplaceRegisteredForOSS();
                    $marketplaceOSSRegistrationCountryId = $detectionExtractor->getMarketplaceOSSRegistrationCountryId();

                    $isCompanyRegisteredForIOSS = $detectionExtractor->isCompanyRegisteredForIOSS();
                    $companyIOSSRegistrationCountryId = $detectionExtractor->getCompanyIOSSRegistrationCountryId();
                    $isMarketplaceRegisteredForIOSS = $detectionExtractor->isMarketplaceRegisteredForIOSS();
                    $marketplaceIOSSRegistrationCountryId = $detectionExtractor->getMarketplaceIOSSRegistrationCountryId();
                    $isCompanyCountryInEu = $detectionExtractor->isCompanyCountryInEU();
                    $platformTypeId = $detectionExtractor->getPlatformType();
                    $isOnlyVatNumberAndWarehouseInCompanyCountry = $detectionExtractor->isOnlyVatNumberAndWarehouseInCompanyCountry();
                    $isMarketplaceIncorporatedInEu = $detectionExtractor->isMarketplaceIncorporatedInEu();
                    $buyerVatNumberValid = $detectionExtractor->isBuyerVatNumberValid();

                    $detectProps['isCompanyRegisteredForOSS'] = $isCompanyRegisteredForOSS;
                    $detectProps['companyOssRegistrationCountryId'] = $companyOssRegistrationCountryId;
                    $detectProps['isMarketplaceRegisteredForOSS'] = $isMarketplaceRegisteredForOSS;
                    $detectProps['marketplaceOSSRegistrationCountryId'] = $marketplaceOSSRegistrationCountryId;
                    $detectProps['isCompanyRegisteredForIOSS'] = $isCompanyRegisteredForIOSS;
                    $detectProps['companyIOSSRegistrationCountryId'] = $companyIOSSRegistrationCountryId;
                    $detectProps['isMarketplaceRegisteredForIOSS'] = $isMarketplaceRegisteredForIOSS;
                    $detectProps['marketplaceIOSSRegistrationCountryId'] = $marketplaceIOSSRegistrationCountryId;
                    $detectProps['isCompanyCountryInEu'] = $isCompanyCountryInEu;
                    $detectProps['platformTypeId'] = $platformTypeId;
                    $detectProps['isOnlyVatNumberAndWarehouseInCompanyCountry'] = $isOnlyVatNumberAndWarehouseInCompanyCountry;
                    $detectProps['isMarketplaceIncorporatedInEu'] = $isMarketplaceIncorporatedInEu;
                    $detectProps['isSaleAbove150Euro'] = false;
                    $detectProps['isSaleAbove135GBP'] = false;
                    $detectProps['isCompanyCountryVatNumberPermanentEstablishment'] = $isCompanyCountryVatNumberPermanentEstablishment;
                    $detectProps['isBuyerVatNumberValid'] = $buyerVatNumberValid;

                    $detected = $this->detectAfterFirstOfJuly21(
                        calculationDate: $taxCalculationDate,
                        companyCountryId: $companyCountryId,
                        shipFromCountryId: $shipFromCountryId,
                        billToCountryId: $recipientCountryId,
                        isDomesticReverseChargeApplicableInBillToCountry: $isDomesticReverseChargeApplicableInRecipientCountry,
                        platformTypeId: $platformTypeId,
                        invoiceTypeId: $invoiceTypeId,
                        isOnlyVatNumberAndWarehouseInCompanyCountry: $isOnlyVatNumberAndWarehouseInCompanyCountry,
                        whetherTheCompanyHasPassedThreshold: $whetherTheCompanyHasPassedThreshold,
                        isCompanyCountryVatNumberPermanentEstablishment: $isCompanyCountryVatNumberPermanentEstablishment,
                        isCompanyRegisteredForOss: $isCompanyRegisteredForOSS,
                        companyOssRegistrationCountryId: $companyOssRegistrationCountryId,
                        isMarketplaceRegisteredForOss: $isMarketplaceRegisteredForOSS,
                        marketplaceOssRegistrationCountryId: $marketplaceOSSRegistrationCountryId,
                        isCompanyRegisteredForIoss: $isCompanyRegisteredForIOSS,
                        companyIossRegistrationCountryId: $companyIOSSRegistrationCountryId,
                        isMarketplaceRegisteredForIoss: $isMarketplaceRegisteredForIOSS,
                        marketplaceIossRegistrationCountryId: $marketplaceIOSSRegistrationCountryId,
                        isSaleAbove150Euro: false,
                        isSaleAbove135GBP: false,
                        isMarketplaceIncorporatedInEu: $isMarketplaceIncorporatedInEu,
                        shipToCountryId: $buyerVatNumberCountryId,
                        isShipToVatNumberValid: $buyerVatNumberValid
                    );
                } else {
                    $hasWaiverInRecipientCountry = $detectionExtractor->hasWaiverInRecipientCountry();
                    $hasVatNumberInRecipientCountry = $detectionExtractor->hasVatNumberInRecipientCountry();
                    $hasWarehouseInRecipientCountry = $detectionExtractor->hasWarehouseInRecipientCountry();

                    $detectProps['hasWaiverInRecipientCountry'] = $hasWaiverInRecipientCountry;
                    $detectProps['hasVatNumberInRecipientCountry'] = $hasVatNumberInRecipientCountry;
                    $detectProps['hasWarehouseInRecipientCountry'] = $hasWarehouseInRecipientCountry;

                    $detected = $this->detectBeforeFirstOfJuly21(
                        $companyCountryId,
                        $shipFromCountryId,
                        $recipientCountryId,
                        $isDomesticReverseChargeApplicableInRecipientCountry,
                        $isRecipientCountryInEu,
                        $isShipFromCountryInEu,
                        $invoiceTypeId,
                        $whetherTheCompanyHasPassedThreshold,
                        $hasWaiverInRecipientCountry,
                        $hasVatNumberInRecipientCountry,
                        $hasWarehouseInRecipientCountry,
                        $buyerVatNumberCountryId,
                    );
                }

                $this->databaseResolver->addItemTaxCodeIdForLoad($itemTaxCodeId, $detected->getTaxCalculationCountryId());
                $this->databaseResolver->addSupplierIfPurchaseInvoice($data, $invoiceTypeId);

                $noCurrencyTypes = [
                    EventType::FC_TRANSFER
                ];
                if (!in_array($data['event_type_id'], $noCurrencyTypes)) {
                    $this->databaseResolver->addCurrencyForLoad($data['transaction_complete_date'], $data['transaction_currency_id']);
                }

                $data['detectProps'] = $detectProps;
                $data['detected'] = $detected->toArray();

                $amazonReportRawData['parsed_data'] = $data;
            } catch (Throwable $exception) {
                if ($this->isDebug()) {
                    /** @noinspection PhpUnhandledExceptionInspection */
                    throw $exception;
                }
                $errors = $amazonReportRawData['error_reasons'];
                if (!is_array($errors)) {
                    $errors = [];
                }

                $errors[] = $this->generateRawDataErrorReasons(
                    $exception->getCode(),
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine(),
                    __LINE__
                );
                $amazonReportRawData['success'] = false;
                $amazonReportRawData['error_reasons'] = $errors;
            }

            return $amazonReportRawData;
        }, $invoiceData);
    }

    /**
     * @param array $invoices
     * @return array
     * @throws Exception
     */
    private function resolveSecondDetectAndAddItemDataForLoad(array $invoices): array
    {
//        $testKey = '10-408-7724279-0443551-5024294983';
//        $invoices = [$testKey => $invoices[$testKey]];

        return array_map(function (array $invoiceItems) {
            return $this->resolveSingleInvoiceSecondPassAndAddItemDataForLoad($invoiceItems);
        }, $invoices);
    }

    /**
     * @param array $invoiceData
     * @return array
     * @throws Exception
     */
    private function resolveSingleInvoiceSecondPassAndAddItemDataForLoad(array $invoiceData): array
    {
        $currencyId = Currency::EUR;
        $sum = 0.0;
        $transactionDate = null;
        foreach ($invoiceData as $invoiceItem) {
            $data = $invoiceItem['parsed_data'];
            $transactionDate = $data['transaction_complete_date'];
            $currencyId = $data['transaction_currency_id'];
            if (!$invoiceItem['success']) {
                continue;
            }
            $sum = $sum + $data['invoiceItemData']['item_sales_price_net'] ?? 0.0;
        }

        $salePriceAbove150Euro = $this->databaseResolver->exchangeToEuro($transactionDate, $sum, $currencyId) > 150.0;
        $salePriceAbove135Gbp = $this->databaseResolver->exchangeToGbp($transactionDate, $sum, $currencyId) > 135.0;

        return array_map(function (array $amazonReportRawData) use (
            $currencyId,
            $sum,
            $salePriceAbove150Euro,
            $salePriceAbove135Gbp
        ) {
            try {
                if (!$amazonReportRawData['success']) {
                    return $amazonReportRawData;
                }

                $data = $amazonReportRawData['parsed_data'];

                $breakpoint = strtotime('2021-07-01');
                $breakpointGb = strtotime('2021-01-01');

                $invoiceDate = $data['tax_calculation_date'];
                $taxCalculationDate = strtotime($invoiceDate);

                $detectProps = $data['detectProps'];

                $companyCountryId = $detectProps['companyCountryId'];
                $shipFromCountryId = $detectProps['shipFromCountryId'];
                $recipientCountryId = $detectProps['recipientCountryId'];
                $isDomesticReverseChargeApplicableInRecipientCountry = $detectProps['isDomesticReverseChargeApplicableInRecipientCountry'];
                $isRecipientCountryInEu = $detectProps['isRecipientCountryInEu'];
                $isShipFromCountryInEu = $detectProps['isShipFromCountryInEu'];
                $invoiceTypeId = $detectProps['invoiceTypeId'];
                $whetherTheCompanyHasPassedThreshold = $detectProps['whetherTheCompanyHasPassedThreshold'];
                $buyerVatNumberCountryId = $detectProps['buyerVatNumberCountryId'];

                $newWay = $taxCalculationDate >= $breakpoint;
                $checkCountries = [
                    $data['sale_arrival_country_id'],
                    $data['sale_depart_country_id'],
                ];

                if (in_array(Country::GB, $checkCountries) && $taxCalculationDate >= $breakpointGb) {
                    $newWay = true;
                }

                if ($newWay) {
                    $isCompanyRegisteredForOSS = $detectProps['isCompanyRegisteredForOSS'];
                    $companyOssRegistrationCountryId = $detectProps['companyOssRegistrationCountryId'];
                    $isMarketplaceRegisteredForOSS = $detectProps['isMarketplaceRegisteredForOSS'];
                    $marketplaceOSSRegistrationCountryId = $detectProps['marketplaceOSSRegistrationCountryId'];
                    $isCompanyRegisteredForIOSS = $detectProps['isCompanyRegisteredForIOSS'];
                    $companyIOSSRegistrationCountryId = $detectProps['companyIOSSRegistrationCountryId'];
                    $isMarketplaceRegisteredForIOSS = $detectProps['isMarketplaceRegisteredForIOSS'];
                    $marketplaceIOSSRegistrationCountryId = $detectProps['marketplaceIOSSRegistrationCountryId'];
                    $platformTypeId = $detectProps['platformTypeId'];
                    $isOnlyVatNumberAndWarehouseInCompanyCountry = $detectProps['isOnlyVatNumberAndWarehouseInCompanyCountry'];
                    $isMarketplaceIncorporatedInEu = $detectProps['isMarketplaceIncorporatedInEu'];
                    $isCompanyCountryVatNumberPermanentEstablishment = $detectProps['isCompanyCountryVatNumberPermanentEstablishment'];
                    $isBuyerVatNumberValid = $detectProps['isBuyerVatNumberValid'];

                    $detected = $this->detectAfterFirstOfJuly21(
                        calculationDate: $invoiceDate,
                        companyCountryId: $companyCountryId,
                        shipFromCountryId: $shipFromCountryId,
                        billToCountryId: $recipientCountryId,
                        isDomesticReverseChargeApplicableInBillToCountry: $isDomesticReverseChargeApplicableInRecipientCountry,
                        platformTypeId: $platformTypeId,
                        invoiceTypeId: $invoiceTypeId,
                        isOnlyVatNumberAndWarehouseInCompanyCountry: $isOnlyVatNumberAndWarehouseInCompanyCountry,
                        whetherTheCompanyHasPassedThreshold: $whetherTheCompanyHasPassedThreshold,
                        isCompanyCountryVatNumberPermanentEstablishment: $isCompanyCountryVatNumberPermanentEstablishment,
                        isCompanyRegisteredForOss: $isCompanyRegisteredForOSS,
                        companyOssRegistrationCountryId: $companyOssRegistrationCountryId,
                        isMarketplaceRegisteredForOss: $isMarketplaceRegisteredForOSS,
                        marketplaceOssRegistrationCountryId: $marketplaceOSSRegistrationCountryId,
                        isCompanyRegisteredForIoss: $isCompanyRegisteredForIOSS,
                        companyIossRegistrationCountryId: $companyIOSSRegistrationCountryId,
                        isMarketplaceRegisteredForIoss: $isMarketplaceRegisteredForIOSS,
                        marketplaceIossRegistrationCountryId: $marketplaceIOSSRegistrationCountryId,
                        isSaleAbove150Euro: $salePriceAbove150Euro,
                        isSaleAbove135GBP: $salePriceAbove135Gbp,
                        isMarketplaceIncorporatedInEu: $isMarketplaceIncorporatedInEu,
                        shipToCountryId: $buyerVatNumberCountryId,
                        isShipToVatNumberValid: $isBuyerVatNumberValid
                    );

                } else {
                    $hasWaiverInRecipientCountry = $detectProps['hasWaiverInRecipientCountry'];
                    $hasVatNumberInRecipientCountry = $detectProps['hasVatNumberInRecipientCountry'];
                    $hasWarehouseInRecipientCountry = $detectProps['hasWarehouseInRecipientCountry'];

                    $detected = $this->detectBeforeFirstOfJuly21(
                        $companyCountryId,
                        $shipFromCountryId,
                        $recipientCountryId,
                        $isDomesticReverseChargeApplicableInRecipientCountry,
                        $isRecipientCountryInEu,
                        $isShipFromCountryInEu,
                        $invoiceTypeId,
                        $whetherTheCompanyHasPassedThreshold,
                        $hasWaiverInRecipientCountry,
                        $hasVatNumberInRecipientCountry,
                        $hasWarehouseInRecipientCountry,
                        $buyerVatNumberCountryId,
                    );
                }

                $amazonReportRawData['parsed_data'] = $this->resolveSecondPassDataAndType($amazonReportRawData, $detected);
            } catch (Throwable $exception) {
                if ($this->isDebug()) {
                    throw $exception;
                }
                $errors = $amazonReportRawData['error_reasons'];
                if (!is_array($errors)) {
                    $errors = [];
                }

                $errors[] = $this->generateRawDataErrorReasons(
                    $exception->getCode(),
                    $exception->getMessage(),
                    $exception->getFile(),
                    $exception->getLine(),
                    __LINE__
                );
                $amazonReportRawData['success'] = false;
                $amazonReportRawData['error_reasons'] = $errors;
            }

            return $amazonReportRawData;
        }, $invoiceData);
    }

    private function resolveSecondPassDataAndType(array $amazonReportRawData, DetectedPropertiesContract $detected): array
    {
        $this->resolveSpecial($amazonReportRawData, $detected);

        return $this->resolveSecondPassData($amazonReportRawData, $detected);
    }

    private function resolveSecondPassData(array $amazonReportRawData, DetectedPropertiesContract $detected): array
    {
        $parsedData = $amazonReportRawData['parsed_data'];
        $itemTaxCodeId = $parsedData['item_tax_code_id'];
        $asin = $parsedData['asin'];
        $itemTaxCodeId = $this->resolveItemTaxCode($itemTaxCodeId, $asin);
        $parsedData['item_tax_code_id'] = $itemTaxCodeId;
        $this->databaseResolver
            ->addItemTaxCodeIdForLoad($itemTaxCodeId, $detected->getTaxCalculationCountryId());

        unset($parsedData['detectProps']);
        $parsedData['detected'] = $detected->toArray();

        $invoiceData = $parsedData['invoiceData'];

        $invoiceData['customer_type_id'] = $detected->getCustomerTypeId();
        $invoiceData['invoice_subtype_id'] = $detected->getInvoiceSubtypeId();
        $invoiceData['tax_reporting_scheme_id'] = $detected->getTaxReportingSchemeId();
        $invoiceData['tax_reporting_country_id'] = $detected->getTaxReportingCountryId();
        $invoiceData['tax_calculation_country_id'] = $detected->getTaxCalculationCountryId();
        $invoiceData['tax_collection_responsibility_id'] = $detected->getTaxCollectionResponsibilityId();

        $parsedData['invoiceData'] = $invoiceData;

        $invoiceItemData = $parsedData['invoiceItemData'];
        if (!$detected->isTaxCharged()) {
            $invoiceItemData['vat_percent'] = null;
        }

        if (!is_null($invoiceItemData['shipping']['item_sales_price_net'])) {
            $this->addShippingForInsert($amazonReportRawData);
        }

        if (!is_null($invoiceItemData['wrap']['item_sales_price_net'])) {
            $this->addWrapForInsert($amazonReportRawData);
        }

        $parsedData['invoiceItemData'] = $invoiceItemData;

        // Unset
        $parsedData = $this->unsetShippingAndWrapData($parsedData);

        /**
         * Must be after setting of invoice_data
         *
         * @noinspection PhpUnhandledExceptionInspection
         */
        $parsedData = $this->resolvePurchaseInvoiceAndSupplierData($parsedData);

        $this->addItemDataForLoad($parsedData);

        return $parsedData;
    }

    private function resolveSpecial(array $amazonReportRawData, DetectedPropertiesContract $detected): void
    {
        if (!$detected->hasDeemedDetected()) {
            return;
        }

        $detected = $detected->getDeemedDetected();
        $newAmazonReportRawData = $amazonReportRawData;
        $data = $newAmazonReportRawData['parsed_data'];
        $invoiceData = $data['invoiceData'];
        $invoiceDate = $invoiceData['invoice_date'];
        $prefix = 'DS-';

        $parsingInvoiceNumber = $prefix . $data['invoiceData']['parsing_invoice_number'];
        $newAmazonReportRawData['unique_invoice_key'] = $parsingInvoiceNumber;
        $invoiceData['parsing_invoice_number'] = $parsingInvoiceNumber;

        $transactionId = $prefix . $data['activity_transaction_id'];
        $newAmazonReportRawData['activity_transaction_id'] = $transactionId;
        $data['activity_transaction_id'] = $transactionId;

        $invoiceItem = $data['invoiceItemData'];
        $invoiceItem = $this->resolveDeemedNet($invoiceItem, $detected, $invoiceDate);
        if (!is_null($invoiceItem['shipping']['item_sales_price_net'])) {
            $invoiceItem['shipping'] = $this->resolveDeemedNet($invoiceItem['shipping'], $detected, $invoiceDate);
        }

        if (!is_null($invoiceItem['wrap']['item_sales_price_net'])) {
            $invoiceItem['wrap'] = $this->resolveDeemedNet($invoiceItem['wrap'], $detected, $invoiceDate);
        }

        $invoiceData['connected_invoice_type'] = 'DEEMED';

        $data['invoiceData'] = $invoiceData;
        $data['invoiceItemData'] = $invoiceItem;
        $newAmazonReportRawData['parsed_data'] = $data;

        $newAmazonReportRawData['parsed_data'] = $this->resolveSecondPassData($newAmazonReportRawData, $detected);

        $this->transactionsToCreate[] = $newAmazonReportRawData;
    }

    private function resolveDeemedNet(array $item, DetectedPropertiesContract $detected, $invoiceDate): array
    {
        $itemTaxCodeId = $item['item_tax_code_id'];
        $vatPercentage = 0.0;
        $net = $item['item_sales_price_net'] ?? 0.0;
        $discountNet = $item['discount_net'];
        $vatAmount = 0.0;
        $discountVatAmount = 0.0;

        if ($detected->isTaxCharged()) {
            $vatPercentage = $this->databaseResolver
                ->getVatPercentageForCountryOnDate(
                    $detected->getTaxCalculationCountryId(),
                    $itemTaxCodeId,
                    $invoiceDate
                );

            $gross = Calculator::netToGross($net, $vatPercentage);
            $vatAmount = $gross - $net;
            $discountVatAmount = null;
            if (!is_null($discountNet)) {
                $discountGross = Calculator::netToGross($discountNet, $vatPercentage);
                $discountVatAmount = $discountGross - $discountNet;
            }
        }

        $item['item_sales_price_net'] = $net;
        $item['discount_net'] = $discountNet;
        $item['item_sales_price_vat_amount'] = $vatAmount;
        $item['vat_percent'] = $vatPercentage;
        $item['discount_vat_amount'] = $discountVatAmount;

        return $item;
    }

    private function addShippingForInsert(array $amazonReportRawData): void
    {
        $parsedData = $amazonReportRawData['parsed_data'];
        $invoiceItemData = $parsedData['invoiceItemData'];

        $shipping = $invoiceItemData['shipping'];
        $invoiceItemData['qty'] = 1;
        $invoiceItemData['invoice_item_type_id'] = ItemType::SHIPPING;
        $invoiceItemData['item_description'] = 'common.Shipping';
        $invoiceItemData['warehouse_id'] = null;

        $invoiceItemData['item_sales_price_net'] = $shipping['item_sales_price_net'];
        $invoiceItemData['item_sales_price_vat_amount'] = $shipping['item_sales_price_vat_amount'];

        $invoiceItemData['discount_net'] = $shipping['discount_net'];
        $invoiceItemData['discount_vat_amount'] = $shipping['discount_vat_amount'];

        $parsedData['invoiceItemData'] = $invoiceItemData;
        $parsedData = $this->unsetShippingAndWrapData($parsedData);

        $amazonReportRawData['parsed_data'] = $parsedData;

        $this->shippingForInsert[] = $amazonReportRawData;
    }

    /**
     * @param array $amazonReportRawData
     */
    private function addWrapForInsert(array $amazonReportRawData): void
    {
        $parsedData = $amazonReportRawData['parsed_data'];
        $invoiceItemData = $parsedData['invoiceItemData'];

        $wrap = $invoiceItemData['wrap'];
        $invoiceItemData['qty'] = 1;
        $invoiceItemData['invoice_item_type_id'] = ItemType::PACKAGING;
        $invoiceItemData['item_description'] = 'common.Packaging';
        $invoiceItemData['warehouse_id'] = null;

        $invoiceItemData['item_sales_price_net'] = $wrap['item_sales_price_net'];
        $invoiceItemData['item_sales_price_vat_amount'] = $wrap['item_sales_price_vat_amount'];

        $invoiceItemData['discount_net'] = $wrap['discount_net'];
        $invoiceItemData['discount_vat_amount'] = $wrap['discount_vat_amount'];

        $parsedData['invoiceItemData'] = $invoiceItemData;
        $parsedData = $this->unsetShippingAndWrapData($parsedData);

        $amazonReportRawData['parsed_data'] = $parsedData;

        $this->wrapForInsert[] = $amazonReportRawData;
    }

    private function unsetShippingAndWrapData(array $parsedData): array
    {
        $invoiceItemData = $parsedData['invoiceItemData'];

        unset($invoiceItemData['wrap']);
        unset($invoiceItemData['shipping']);

        $parsedData['invoiceItemData'] = $invoiceItemData;

        return $parsedData;
    }

    private function saveAndBindSpecial(array $invoices): array
    {
        $transactions = collect($this->transactionsToCreate)
            ->groupBy('unique_invoice_key');
        if ($transactions->count() < 1) {
            return $invoices;
        }

        return array_merge($invoices, $transactions->toArray());
    }

    private function calculateDataAndGenerateInvoiceData(array $invoices): array
    {
        $amazonReport = $this->getAmazonReport();
        $createUserId = $amazonReport->upload_user_id;
        $amazonReportId = $amazonReport->id;

        return array_map(function (array $invoiceItems) use ($amazonReportId, $createUserId) {
            return array_map(function (array $amazonReportRawData) use ($amazonReportId, $createUserId) {
                try {
                    $data = $amazonReportRawData['parsed_data'];

                    $taxCalculationDate = $data['tax_calculation_date'];
                    $warehouse = $this->getWarehouse($data);
                    $shipFromCountryId = $warehouse['country_id'];
                    $companyId = $data['sales_channel_data']['company_id'];
                    $marketplaceId = $data['marketplace_id'];
                    $shipFromVatNumber = $this->getShipFromVatNumber($data);
                    $shipToVatNumber = $this->getCustomerVatNumber($data);
                    $buyerVatNumberCountryId = $data['detectProps']['buyerVatNumberCountryId'] ?? null;

                    $shipToPostalCode = $data['arrival_post_code'];
                    $shipToCountryId = $data['arrival_country_id'] ?? null;
                    if (is_null($shipToCountryId)) {
                        $shipToCountryId = $data['sale_arrival_country_id'];
                    }
                    $invoiceDate = $data['transaction_complete_date'];

                    $salesCurrencyId = $data['transaction_currency_id'];
                    if (is_null($salesCurrencyId) && $data['event_type_id'] === EventType::FC_TRANSFER) {
                        $salesCurrencyId = $data['departure_country_data']['currency_id'];
                    }

                    $messages = $data['messages'];

                    $billToCountryId = $data['sale_arrival_country_id'] ?? null;
                    $billToPostalCode = $data['arrival_post_code'] ?? null;
                    $billToVatNumber = $this->getCustomerVatNumber($data);
                    $shipToName = $data['buyer_name'] ?? null;
                    $shipToAddress = $data['arrival_address'];

                    $company = $data['sales_channel_data']['company'] ?? null;

                    if ($data['event_type_id'] === EventType::FC_TRANSFER) {
                        $billToCountryId = $data['arrival_country_id'] ?? null;
                    }

                    if (in_array($data['event_type_id'], [EventType::COMMINGLING_BUY, EventType::FC_TRANSFER])) {
                        $shipToName = $company['full_legal_name'] ?? null;
                        $address = $company['address'] ?? null;
                        $address = $address['display_address'] ?? null;
                        if (!is_null($address)) {
                            $shipToAddress = $address;
                        }
                    }

                    $data['invoiceData'] = [
                        'invoice_date'           => $invoiceDate,
                        'invoice_number'         => $data['vat_inv_number'],
                        'parsing_invoice_number' => $companyId . '-' . $amazonReportRawData['unique_invoice_key'],

                        'bill_to_name'        => $data['buyer_name'],
                        'bill_to_address'     => $data['arrival_address'],
                        'bill_to_country_id'  => $billToCountryId,
                        'bill_to_postal_code' => $billToPostalCode,
                        'bill_to_vat_number'  => $billToVatNumber,

                        'ship_to_name'        => $shipToName,
                        'ship_to_address'     => $shipToAddress,
                        'ship_to_country_id'  => $shipToCountryId,
                        'ship_to_postal_code' => $shipToPostalCode,
                        'ship_to_vat_number'  => $shipToVatNumber,

                        'ship_from_country_id' => $shipFromCountryId,
                        'ship_from_vat_number' => $shipFromVatNumber,

                        'marketplace_id'                   => $marketplaceId,
                        'delivery_condition_id'            => $data['delivery_condition_id'],
                        'tax_reporting_scheme_id'          => null,
                        'tax_collection_responsibility_id' => null,
                        'invoice_status_id'                => InvoiceStatus::ISSUED,
                        'data'                             => null,
                        'resource'                         => null,
                        'resource_id'                      => $amazonReportId,
                        'create_user_id'                   => $createUserId,
                        'tax_calculation_country_id'       => null,
                        'tax_reporting_country_id'         => null,
                        'customer_type_id'                 => null,
                        'sales_currency_id'                => $salesCurrencyId,
                        'invoice_subtype_id'               => null,
                        'item_type_id'                     => ItemType::GOODS,
                        'reporting_date'                   => $data['transaction_complete_date'],
                        'tax_calculation_date'             => $taxCalculationDate,
                        'company_id'                       => $companyId,
                        'order_number'                     => $this->clearForInvoiceNumber($amazonReportRawData['transaction_event_id'] ?? null),
                        'order_date'                       => $data['tax_calculation_date'] ?? null,
                        'buyer_vat_number_country_id'      => $buyerVatNumberCountryId,
                        'messages'                         => $messages,

                        'supplier_id'          => null,
                        'bill_from_country_id' => null,
                        'bill_from_vat_number' => null,

                        'connected_invoice_id'   => null,
                        'connected_invoice_type' => null,
                    ];

                    $data = $this->calculateInvoiceItemData($data);

                    $amazonReportRawData['parsed_data'] = $data;
                } catch (Throwable $exception) {
                    if ($this->isDebug()) {
                        throw $exception;
                    }
                    $errors = $amazonReportRawData['error_reasons'];
                    if (!is_array($errors)) {
                        $errors = [];
                    }

                    $errors[] = $this->generateRawDataErrorReasons(
                        $exception->getCode(),
                        $exception->getMessage(),
                        $exception->getFile(),
                        $exception->getLine(),
                        __LINE__
                    );
                    $amazonReportRawData['success'] = false;
                    $amazonReportRawData['error_reasons'] = $errors;
                }

                return $amazonReportRawData;
            }, $invoiceItems);
        }, $invoices);
    }

    private function reCalculateInvoiceData(array $invoices): array
    {
        return array_map(function (array $invoiceItems) {
            return array_map(function (array $amazonReportRawData) {
                try {
                    $parsedData = $amazonReportRawData['parsed_data'];

                    $parsedData = $this->calculateInvoiceItemData($parsedData);

                    $parsedData = $this->resolveDiscountData($parsedData);
                    $parsedData = $this->resolveShippingData($parsedData);
                    $parsedData = $this->resolveWrapData($parsedData);

                    $parsedData = $this->unsetShippingAndWrapData($parsedData);

                    $amazonReportRawData['parsed_data'] = $parsedData;
                } catch (Throwable $exception) {
                    if ($this->isDebug()) {
                        throw $exception;
                    }
                    $errors = $amazonReportRawData['error_reasons'];
                    if (!is_array($errors)) {
                        $errors = [];
                    }

                    $errors[] = $this->generateRawDataErrorReasons(
                        $exception->getCode(),
                        $exception->getMessage(),
                        $exception->getFile(),
                        $exception->getLine(),
                        __LINE__
                    );
                    $amazonReportRawData['success'] = false;
                    $amazonReportRawData['error_reasons'] = $errors;
                }

                return $amazonReportRawData;
            }, $invoiceItems);
        }, $invoices);
    }

    private function calculateInvoiceItemData(array $data): array
    {
        $taxCalculationCountryId = $data['detected']['taxCalculationCountryId'];
        $taxCalculationDate = $data['tax_calculation_date'];
        $qty = (int)$data['qty'];
        $gross = $this->getGross($data);
        $net = $this->getNet($data);
        $vatAmount = ($gross ?? 0) - ($net ?? 0);
        $itemTaxCodeId = $data['item_tax_code_id'];
        $asin = $data['asin'];
        $itemTaxCodeId = $this->resolveItemTaxCode($itemTaxCodeId, $asin);
        $data['item_tax_code_id'] = $itemTaxCodeId;

        $vatPercentage = $this->databaseResolver
            ->getVatPercentageForCountryOnDate(
                $taxCalculationCountryId,
                $itemTaxCodeId,
                $taxCalculationDate
            );

        $vatPercentageFloat = null;
        if (!is_null($vatPercentage)) {
            $vatPercentageFloat = $vatPercentage / 100;
        }
        $vatPercentageFloatCsv = $data['price_of_items_vat_rate_percent'];

        if (!$data['detected']['isTaxCharged']) {
            $net = $gross;
            $vatAmount = 0.0;

            $vatPercentage = null;
        } elseif ($vatPercentageFloat !== $vatPercentageFloatCsv) {
            $vatAmount = Calculator::vatAmountFromGross($gross, $vatPercentage ?? 0);
            $net = $gross - $vatAmount;
        }

        $data['price_of_items_vat_rate_percent_detected'] = $vatPercentage;

        $warehouse = $this->getWarehouse($data);
        $warehouseId = $warehouse['id'];

        $data['invoiceItemData'] = [
            // Common
            'invoice_id'                  => null,
            'item_id'                     => null,
            'qty'                         => $qty,
            'invoice_item_type_id'        => ItemType::GOODS,
            'item_description'            => $data['item_description'],
            'warehouse_id'                => $warehouseId,

            // Item price data
            'item_sales_price_net'        => $net,
            'item_sales_price_vat_amount' => $vatAmount,
            'vat_percent'                 => $vatPercentage,

            // Item discount data
            'discount_net'                => null,
            'discount_vat_amount'         => null,
            'item_tax_code_id'            => $itemTaxCodeId,

            // Shipping data
            'shipping'                    => [
                'item_sales_price_net'        => null,
                'item_sales_price_vat_amount' => null,
                'discount_net'                => null,
                'discount_vat_amount'         => null,
            ],

            // Wrap data
            'wrap'                        => [
                'item_sales_price_net'        => null,
                'item_sales_price_vat_amount' => null,
                'discount_net'                => null,
                'discount_vat_amount'         => null,
            ]
        ];

        return $data;
    }

    /**
     * @param array $data
     * @return string|null
     */
    private function getShipFromVatNumber(array $data): ?string
    {
        // $vatNumber = $data['transaction_seller_vat_number'];
        // #2878 - rekli smo gledamo seller_depart_country_vat_number, a ne transaction_seller_vat_number
        $vatNumber = $data['seller_depart_country_vat_number'] ?? null;

        if ($data['event_type_id'] === EventType::COMMINGLING_BUY) {
            $vatNumber = $data['buyer_vat_number'];
        }

        return $vatNumber;
    }

    /**
     * @param array $data
     * @return string|null
     */
    private function getCustomerVatNumber(array $data): ?string
    {
        $vatNumber = $data['buyer_vat_number'];
        $eventTypeId = $data['event_type_id'];

        if ($eventTypeId === EventType::COMMINGLING_BUY) {
            $vatNumber = $data['transaction_seller_vat_number'];
        }

        if ($eventTypeId === EventType::FC_TRANSFER) {
            $arrivalCountryId = $data['sale_arrival_country_id'];
            if (is_null($arrivalCountryId)) {
                $arrivalCountryId = $data['arrival_country_id'];
            }

            $vatNumbers = $data['sales_channel_data']['company']['vat_numbers'];
            foreach ($vatNumbers as $number) {
                if ($number['country_id'] === $arrivalCountryId) {
                    $vatNumber = $number['vat_number'];
                    break;
                }
            }
        }

        $buyerVatNumberCountryDataCode = $data['buyer_vat_number_country_data']['vat_code'] ?? null;
        if (is_null($buyerVatNumberCountryDataCode)) {
            return $vatNumber;
        }

        if (!Str::startsWith($vatNumber, $buyerVatNumberCountryDataCode)) {
            $vatNumber = $buyerVatNumberCountryDataCode . $vatNumber;
        }

        return preg_replace('/\s+/', '', $vatNumber);
    }

    /**
     * @param array $data
     * @return array
     */
    private function getWarehouse(array $data): array
    {
        $countryId = $data['departure_country_id'];
        if (is_null($countryId)) {
            $countryId = $data['sale_depart_country_id'];
        }
        $warehouse = null;
        foreach ($data['sales_channel_data']['company']['company_warehouses'] as $warehouseData) {
            $warehouseData = $warehouseData['warehouse'];
            if ($warehouseData['country_id'] === $countryId) {
                $warehouse = $warehouseData;
                break;
            }
        }

        return $warehouse;
    }

    /**
     * @param array $data
     * @return float|null
     */
    private function getGross(array $data): ?float
    {
        $gross = $data['price_of_items_amt_vat_incl'];
        if ($data['event_type_id'] === EventType::FC_TRANSFER) {
            $gross = $data['cost_price_of_items'];
        }
        if ($gross < 0) {
            $gross *= -1;
        }
        $qty = (int)$data['qty'];

        return $gross / $qty;
    }

    /**
     * @param array $data
     * @return float|null
     */
    private function getNet(array $data): ?float
    {
        $net = $data['price_of_items_amt_vat_excl'];
        if ($data['event_type_id'] === EventType::FC_TRANSFER) {
            $net = $data['cost_price_of_items'];
        }
        if ($net < 0) {
            $net *= -1;
        }
        $qty = (int)$data['qty'];

        return $net / $qty;
    }

    private function detectAfterFirstOfJuly21(
        string $calculationDate,
        int $companyCountryId,
        int $shipFromCountryId,
        int $billToCountryId,
        bool $isDomesticReverseChargeApplicableInBillToCountry,
        int $platformTypeId,
        int $invoiceTypeId,
        bool $isOnlyVatNumberAndWarehouseInCompanyCountry,
        bool $whetherTheCompanyHasPassedThreshold,
        bool $isCompanyCountryVatNumberPermanentEstablishment,
        ?bool $isCompanyRegisteredForOss = null,
        ?int $companyOssRegistrationCountryId = null,
        ?bool $isMarketplaceRegisteredForOss = null,
        ?int $marketplaceOssRegistrationCountryId = null,
        ?bool $isCompanyRegisteredForIoss = null,
        ?int $companyIossRegistrationCountryId = null,
        ?bool $isMarketplaceRegisteredForIoss = null,
        ?int $marketplaceIossRegistrationCountryId = null,
        ?bool $isSaleAbove150Euro = null,
        ?bool $isSaleAbove135GBP = null,
        ?bool $isMarketplaceIncorporatedInEu = null,
        ?int $shipToCountryId = null,
        bool $isShipToVatNumberValid = false,
    ): DetectedPropertiesContract {
        return (new VatDetection())
            ->detectAfterFirstOfJuly21(
                calculationDate: $calculationDate,
                companyCountryId: $companyCountryId,
                isOnlyVatNumberAndWarehouseInCompanyCountry: $isOnlyVatNumberAndWarehouseInCompanyCountry,
                isCompanyCountryVatNumberPermanentEstablishment: $isCompanyCountryVatNumberPermanentEstablishment,
                whetherTheCompanyHasPassedThreshold: $whetherTheCompanyHasPassedThreshold,

                shipFromCountryId: $shipFromCountryId,

                billToCountryId: $billToCountryId,
                isDomesticReverseChargeApplicableInBillToCountry: $isDomesticReverseChargeApplicableInBillToCountry,

                platformTypeId: $platformTypeId,
                invoiceTypeId: $invoiceTypeId,

                isCompanyRegisteredForOss: $isCompanyRegisteredForOss,
                companyOssRegistrationCountryId: $companyOssRegistrationCountryId,
                isCompanyRegisteredForIoss: $isCompanyRegisteredForIoss,
                companyIossRegistrationCountryId: $companyIossRegistrationCountryId,

                isMarketplaceRegisteredForOss: $isMarketplaceRegisteredForOss,
                marketplaceOssRegistrationCountryId: $marketplaceOssRegistrationCountryId,
                isMarketplaceRegisteredForIoss: $isMarketplaceRegisteredForIoss,
                marketplaceIossRegistrationCountryId: $marketplaceIossRegistrationCountryId,
                isMarketplaceIncorporatedInEu: $isMarketplaceIncorporatedInEu,

                isSaleAbove150Euro: $isSaleAbove150Euro,
                isSaleAbove135GBP: $isSaleAbove135GBP,

                shipToCountryId: $shipToCountryId,
                isShipToVatNumberValid: $isShipToVatNumberValid
            );
    }

    private function detectBeforeFirstOfJuly21(
        int $companyCountryId,
        int $shipFromCountryId,
        int $recipientCountryId,
        bool $isDomesticReverseChargeApplicableInRecipientCountry,
        bool $isRecipientCountryInEu,
        bool $isShipFromCountryInEu,
        int $invoiceTypeId,
        bool $whetherTheCompanyHasPassedThreshold,
        bool $hasWaiverInRecipientCountry,
        bool $hasVatNumberInRecipientCountry,
        bool $hasWarehouseInRecipientCountry,
        ?int $buyerVatNumberCountryId = null,
    ): DetectedPropertiesContract {
        return (new VatDetection())
            ->detectBeforeFirstOfJuly21(
                $companyCountryId,
                $shipFromCountryId,
                $recipientCountryId,
                $isDomesticReverseChargeApplicableInRecipientCountry,
                $isRecipientCountryInEu,
                $isShipFromCountryInEu,
                $invoiceTypeId,
                $whetherTheCompanyHasPassedThreshold,
                $hasWaiverInRecipientCountry,
                $hasVatNumberInRecipientCountry,
                $hasWarehouseInRecipientCountry,
                $buyerVatNumberCountryId,
            );
    }

    /**
     * @return array
     */
    private function getRawTransactionsGroupedByInvoice(): array
    {
        return ($this->getJsonFileForCycle(2) ?? collect())
            ->groupBy('unique_invoice_key')
            ->toArray();
    }

    private function storeShippingAndWrap(): void
    {
        $data = array_merge($this->shippingForInsert, $this->wrapForInsert);
        $shippingAndWrapChunked = collect($data)->chunk($this->insertChunkSize);

        foreach ($shippingAndWrapChunked as $key => $shippingAndWrap) {
            $fileData = [];
            $filename = '2sw_' . $key . $this->amazonReport->id . time() . Str::random() . '.csv';
            $path = tmp_path($filename);
            $query = $this->getUpdateQuery($path);

            foreach ($shippingAndWrap as $item) {
                $fileData[] = $this->resolveCsvRow($item);
            }

            $fileData = implode("\n", $fileData);
            $this->storeFile($path, $fileData);

            DB::statement($query);
        }
    }

    /**
     * @param array $invoicesData
     * @return array
     */
    private function bindItemsToInvoicesAndCheckPurchasePriceIfNeeded(array $invoicesData): array
    {
        $data = [];
        foreach ($invoicesData as $invoiceNum => $invoice) {
            $invoiceData = [];
            foreach ($invoice as $invoiceItem) {

                try {
                    $asin = $invoiceItem['parsed_data']['asin'] ?? null;
                    $purchasePriceNet = 0.0;
                    if (!is_null($asin)) {
                        /**
                         * @var Item $item
                         */
                        $item = $this->itemsDb->get($asin);
                        if (!is_null($item)) {
                            $itemId = $item->id;
                            $invoiceItem['parsed_data']['invoiceItemData']['item_id'] = $itemId;
                            $purchasePriceNet = $item->purchase_price_net ?? 0.0;

                            if ($purchasePriceNet === 0.0) {
                                /**
                                 * @var \App\Core\Data\Models\ItemSalePrice|null $itemSalesPrice
                                 */
                                $itemSalesPrice = $item->itemMarketplaces->first()?->itemSalePrices?->first() ?? null;
                                if (!is_null($itemSalesPrice)) {
                                    $purchasePriceNet = $itemSalesPrice->net_price;
                                }
                            }
                        }
                    }

                    // Resolve purchase price if FC_TRANSFER
                    if ($invoiceItem['event_type_id'] === EventType::FC_TRANSFER) {
                        // Get
                        $invoiceItemData = $invoiceItem['parsed_data']['invoiceItemData'];
                        // Update
                        $invoiceItemData['item_sales_price_net'] = $purchasePriceNet;
                        $invoiceItemData['item_sales_price_vat_amount'] = 0;
                        $invoiceItemData['vat_percent'] = 0;

                        // Set
                        $invoiceItem['parsed_data']['invoiceItemData'] = $invoiceItemData;
                    }
                } catch (Throwable $exception) {
                    if ($this->isDebug()) {
                        /** @noinspection PhpUnhandledExceptionInspection */
                        throw $exception;
                    }
                    $errors = $invoiceItem['error_reasons'];
                    if (!is_array($errors)) {
                        $errors = [];
                    }

                    $errors[] = $this->generateRawDataErrorReasons(
                        $exception->getCode(),
                        $exception->getMessage(),
                        $exception->getFile(),
                        $exception->getLine(),
                        __LINE__
                    );
                    $invoiceItem['success'] = false;
                    $invoiceItem['error_reasons'] = $errors;
                }

                $invoiceData[] = $invoiceItem;
            }
            $data[$invoiceNum] = $invoiceData;

        }

        return $data;
    }

    /**
     * @param array $invoicesData
     */
    private function updateAmazonReportTransactionsRaw(array $invoicesData): void
    {
        $invoicesData = collect($invoicesData)
            ->flatten(1)
            ->chunk($this->updateChunkSize);

        foreach ($invoicesData as $key => $invoicesItems) {
            $fileData = [];
            $filename = '3_' . $key . $this->amazonReport->id . time() . Str::random() . '.csv';
            $path = tmp_path($filename);
            $query = $this->getUpdateQuery($path);

            foreach ($invoicesItems as $item) {
                $fileData[] = $this->resolveCsvRow($item);
            }

            $fileData = implode("\n", $fileData);
            $this->storeFile($path, $fileData);

            try {
                DB::statement($query);
                $this->deleteFile($path);
            } catch (Throwable $e) {
                $this->deleteFile($path);
                /** @noinspection PhpUnhandledExceptionInspection */
                throw $e;
            }
        }
    }

    /**
     * @param string $path
     * @return string
     */
    private function getUpdateQuery(string $path): string
    {
        $columns = [
            'cycle',
            'amazon_report_id',
            'success',
            'error_reasons',
            'column_count',
            'event_type_id',
            'transaction_event_id',
            'activity_transaction_id',
            'transaction_date',
            'data',
            'parsed_data',
        ];

        $start = [];
        $start[] = 'COPY amazon_reports_raw_data';
        $start[] = '(' . implode(', ', $columns) . ')';
        $start[] = 'FROM';
        $start[] = "'$path'";
        $start[] = 'DELIMITER';
        $start[] = "'$this->csvDelimiter'";
        $start[] = 'NULL AS';
        $start[] = "'null';";

        return implode(' ', $start);
    }

    /**
     * @param array $invoiceItem
     * @return string
     */
    private function resolveCsvRow(array $invoiceItem): string
    {
        $success = $invoiceItem['success'];
        $parsed = $invoiceItem['parsed_data'];
        $errors = $invoiceItem['error_reasons'] ?? [];
        if (!is_array($errors)) {
            $errors = [];
        }

        $parsedData = 'null';
        $errorReasons = 'null';
        if (is_array($parsed)) {
            $parsedData = $this->encodeErrorsOrParsedData($parsed);
        }

        if (count($errors) > 0) {
            $success = false;
            $errorReasons = $this->encodeErrorsOrParsedData($errors);
        }

        $data = [
            AmazonReportRawData::CYCLE_DB_RESOLVED,
            $invoiceItem['amazon_report_id'],
            $success === true ? 'true' : 'false',
            $errorReasons,
            $invoiceItem['column_count'],
            $invoiceItem['event_type_id'],
            $invoiceItem['transaction_event_id'],
            $invoiceItem['activity_transaction_id'],
            $invoiceItem['transaction_date'],
            'null',
            $parsedData,
        ];

        return implode($this->csvDelimiter, $data);
    }

    private function encodeErrorsOrParsedData(array $data): string
    {
        $data = AmazonReportRawData::encodeErrorsOrParsedData($data);

        return str_replace($this->csvDelimiter, ' ', $data);
    }

    private function setAmazonReportStatus(): void
    {
        $amazonReport = $this->amazonReport;

        $cycleTwoTransactions = $amazonReport
            ->loadCount('cycleTwoTransactions')
            ->cycle_two_transactions_count;

        // Still left to do
        if ($cycleTwoTransactions > 0) {
            return;
        }

        $errorTransactions = $amazonReport
            ->loadCount('errorTransactions')
            ->error_transactions_count;

        $amazonReport->parsing_ended_at = Carbon::now()->toDateTimeString();

        if ($errorTransactions > 0) {
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
            $amazonReport->save();

            return;
        }

        $amazonReport->report_status_id = AmazonReportStatus::STATUS_SYNCED_DB;
        $amazonReport->save();
    }


    /**
     * @param array $data
     * @return array
     */
    private function resolveDiscountData(array $data): array
    {
        $gross = $this->getGross($data);
        if ($gross === 0.0) {
            $gross = null;
        }

        $promoPriceGross = $data['promo_price_of_items_amt_vat_incl'];
        $promoPriceNet = $data['promo_price_of_items_amt_vat_excl'] ?? 0;
        $vatRateCsv = $data['price_of_items_vat_rate_percent'];
        $vatRate = $data['price_of_items_vat_rate_percent_detected'];
        $vatRateFloat = null;
        if (!is_null($vatRate)) {
            $vatRateFloat = $vatRate / 100;
        }

        if ($promoPriceGross === 0) {
            $promoPriceGross = null;
        }

        if (is_null($promoPriceGross) || is_null($gross)) {
            return $data;
        }

        $promoPriceGross = Calculator::abs($promoPriceGross);
        $promoPriceNet = Calculator::abs($promoPriceNet);
        $promoPriceVatAmount = $promoPriceGross - $promoPriceNet;
        if ($vatRateFloat !== $vatRateCsv) {
            $promoPriceVatAmount = Calculator::vatAmountFromGross($promoPriceGross, $vatRate ?? 0);
            $promoPriceNet = $promoPriceGross - $promoPriceVatAmount;
        }

        $data['invoiceItemData']['discount_net'] = $promoPriceNet;
        $data['invoiceItemData']['discount_vat_amount'] = $promoPriceVatAmount;

        return $data;
    }

    /**
     * @param array $data
     * @return array
     */
    private function resolveShippingData(array $data): array
    {
        $shippingPriceGross = $data['ship_charge_amt_vat_incl'];
        $shippingPriceNet = $data['ship_charge_amt_vat_excl'] ?? 0;

        $promoShippingPriceGross = $data['promo_ship_charge_amt_vat_incl'];
        $promoShippingPriceNet = $data['promo_ship_charge_amt_vat_excl'];
        $promoShippingPriceVatAmount = null;

        $vatRateCsv = $data['ship_charge_vat_rate_percent'];
        $vatRate = $data['price_of_items_vat_rate_percent_detected'];
        $vatRateFloat = 0;
        if (!is_null($vatRate)) {
            $vatRateFloat = $vatRate / 100;
        }

        if ($shippingPriceGross === 0) {
            $shippingPriceGross = null;
        }

        if (is_null($shippingPriceGross)) {
            return $data;
        }

        $shippingPriceGross = Calculator::abs($shippingPriceGross);
        $shippingPriceNet = Calculator::abs($shippingPriceNet);
        $shippingPriceVatAmount = $shippingPriceGross - $shippingPriceNet;

        if (!is_null($promoShippingPriceGross)) {
            $promoShippingPriceGross = Calculator::abs($promoShippingPriceGross);
            $promoShippingPriceNet = Calculator::abs($promoShippingPriceNet ?? 0);
            $promoShippingPriceVatAmount = $promoShippingPriceGross - $promoShippingPriceNet;
        }

        if ($vatRateFloat !== $vatRateCsv) {
            $shippingPriceVatAmount = Calculator::vatAmountFromGross($shippingPriceGross, $vatRate ?? 0);
            $shippingPriceNet = $shippingPriceGross - $shippingPriceVatAmount;

            if (!is_null($promoShippingPriceGross)) {
                $promoShippingPriceVatAmount = Calculator::vatAmountFromGross($promoShippingPriceGross, $vatRate ?? 0);
                $promoShippingPriceNet = $promoShippingPriceGross - $promoShippingPriceVatAmount;
            }
        }

        $shipping = [
            'item_sales_price_net'        => $shippingPriceNet,
            'item_sales_price_vat_amount' => $shippingPriceVatAmount,
            'discount_net'                => $promoShippingPriceNet,
            'discount_vat_amount'         => $promoShippingPriceVatAmount
        ];

        $data['invoiceItemData']['shipping'] = $shipping;

        return $data;
    }

    /**
     * @param array $data
     * @return array
     */
    private function resolveWrapData(array $data): array
    {
        $gross = $this->getGross($data);
        if ($gross === 0.0) {
            $gross = null;
        }

        $wrapPriceGross = $data['gift_wrap_amt_vat_incl'];
        $wrapPriceNet = $data['gift_wrap_amt_vat_excl'] ?? 0;

        $promoWrapPriceGross = $data['promo_gift_wrap_amt_vat_incl'];
        $promoWrapPriceNet = $data['promo_gift_wrap_amt_vat_excl'];
        $promoWrapPriceVatAmount = null;

        $vatRateCsv = $data['gift_wrap_vat_rate_percent'];
        $vatRate = $data['price_of_items_vat_rate_percent_detected'];
        $vatRateFloat = 0;
        if (!is_null($vatRate)) {
            $vatRateFloat = $vatRate / 100;
        }

        if ($wrapPriceGross === 0) {
            $wrapPriceGross = null;
        }

        if (is_null($wrapPriceGross) || is_null($gross)) {
            return $data;
        }

        $wrapPriceGross = Calculator::abs($wrapPriceGross);
        $wrapPriceNet = Calculator::abs($wrapPriceNet);
        $wrapPriceVatAmount = $wrapPriceGross - $wrapPriceNet;

        if (!is_null($promoWrapPriceGross)) {
            $promoWrapPriceGross = Calculator::abs($promoWrapPriceGross);
            $promoWrapPriceNet = Calculator::abs($promoWrapPriceNet ?? 0);
            $promoWrapPriceVatAmount = $promoWrapPriceGross - $promoWrapPriceNet;
        }

        if ($vatRateFloat !== $vatRateCsv) {
            $wrapPriceVatAmount = Calculator::vatAmountFromGross($wrapPriceGross, $vatRate ?? 0);
            $wrapPriceNet = $wrapPriceGross - $wrapPriceVatAmount;

            if (!is_null($promoWrapPriceGross)) {
                $promoWrapPriceVatAmount = Calculator::vatAmountFromGross($promoWrapPriceGross, $vatRate ?? 0);
                $promoWrapPriceNet = $promoWrapPriceGross - $promoWrapPriceVatAmount;
            }
        }

        $wrap = [
            'item_sales_price_net'        => $wrapPriceNet,
            'item_sales_price_vat_amount' => $wrapPriceVatAmount,
            'discount_net'                => $promoWrapPriceNet,
            'discount_vat_amount'         => $promoWrapPriceVatAmount
        ];

        $data['invoiceItemData']['wrap'] = $wrap;

        return $data;
    }

    /**
     * @param array $data
     */
    private function addItemDataForLoad(array $data): void
    {
        $salesChannelData = $data['sales_channel_data'];
        $invoiceData = $data['invoiceData'];
        $invoiceItemData = $data['invoiceItemData'];
        $itemId = Str::ascii(trim($data['item_id']));
        $asin = Str::ascii(trim($data['asin']));
        $timestamp = strtotime($invoiceData['invoice_date']);
        $net = $invoiceItemData['item_sales_price_net'] ?? 0;
        $vat = $invoiceItemData['item_sales_price_vat_amount'] ?? 0;
        $gross = $net + $vat;
        $purchasePriceNet = $gross;
        $purchasePriceCost = 0.0;
        $purchasePriceCurrency = $invoiceData['sales_currency_id'];

        $itemData = [
            'id'                         => null,
            'company_id'                 => $salesChannelData['company_id'],
            'manufacture_country_id'     => $data['item_manufacture_country_id'],
            'item_tax_code_id'           => $data['item_tax_code_id'],
            'unit_id'                    => $data['commodity_code_supplementary_unit'],
            'unit_value'                 => $data['item_qty_supplementary_unit'],
            'sku'                        => $itemId,
            'net'                        => $net,
            'vat'                        => $vat,
            'gross'                      => $gross,
            'asin'                       => $asin,
            'weight'                     => $data['item_weight'] ?? null,
            'description'                => $data['item_description'],
            'identifiers'                => null,
            'purchase_price_cost'        => $purchasePriceCost,
            'purchase_price_net'         => $purchasePriceNet,
            'purchase_price_currency_id' => $purchasePriceCurrency,

            'timestamp'       => $timestamp,
            'date'            => date('Y-m-d', $timestamp),
            'invoiceData'     => $data['invoiceData'],
            'invoiceItemData' => $data['invoiceItemData'],
            'marketplaceId'   => $data['marketplace_id'],
        ];

        $this->items[$asin][] = $itemData;
    }

    /**
     * @return void
     * @throws Exception
     */
    private function resolveAndStoreItems(): void
    {
        // ORDER IS IMPORTANT !!!
        $relations = [
            'itemMarketplaces.itemItemIdentificationTypes',
            'itemMarketplaces.itemSalePrices',
        ];

        $items = collect($this->items)
            ->filter(function ($itemMarketplaces) {
                $check = collect($itemMarketplaces)
                    ->filter(function ($itemMarketplace) {
                        return $itemMarketplace['purchase_price_net'] > 0.0;
                    })->count();

                return $check > 0;
            });

        Metric::start('mapItemsMarketplacesPrices', 'resolve items');
        // ORDER IS IMPORTANT !!!
        // 1. - mapItemsMarketplacesPrices
        $csvItems = $this->mapItemsMarketplacesPrices($items);
        Metric::end('mapItemsMarketplacesPrices');

        Metric::start('resolveAsins', 'resolve items');
        // 2. - resolveAsinsByCompanyId
        $asins = $this->resolveAsinsByCompanyId($csvItems);
        Metric::end('resolveAsins');

        Metric::start('storingAndFetchingItems', 'resolve items');
        // 3. - storeAndGetAllItems
        $storedItems = $this->storeAndGetAllItems($csvItems, $asins)->load($relations);
        Metric::end('storingAndFetchingItems');

        Metric::start('storingItemMarketplaces', 'resolve items');
        // 4. - saveItemMarketplacesIfNotExists
        $storedItems = $this->saveItemMarketplacesIfNotExists($storedItems, $csvItems)->load($relations);
        Metric::end('storingItemMarketplaces');

        Metric::start('saveItemPrices', 'resolve items');
        // ORDER NOT IMPORTANT
        $storedItems = $this->saveItemPrices($storedItems, $csvItems)->load($relations);
        Metric::end('saveItemPrices');

        Metric::start('saveItemIdentificationTypes', 'resolve items');
        $storedItems = $this->saveItemIdentificationTypes($storedItems, $csvItems)->load($relations);
        Metric::end('saveItemIdentificationTypes');

        // MUST BE LAST
        $this->itemsDb = $storedItems;
    }

    private function saveItemIdentificationTypes(EloquentCollection $storedItems, Collection $csvItems): EloquentCollection
    {
        $delimiter = '-###-';
        $existing = $storedItems->map(function (Item $item) use ($delimiter) {
            $identificators = collect();

            foreach ($item->itemMarketplaces as $itemMarketplace) {
                foreach ($itemMarketplace->itemItemIdentificationTypes as $itemIdentificationType) {
                    $identifier = [
                        $itemIdentificationType->item_marketplace_id,
                        $itemIdentificationType->item_identification_type_id,
                        $itemIdentificationType->value,
                    ];
                    $identifier = implode($delimiter, $identifier);
                    $identificators->push($identifier);
                }
            }

            return $identificators;
        })->flatten()->keyBy(function (string $identifier) {
            return $identifier;
        })->values()->toArray();

        $identifiers = [];
        foreach ($csvItems as $asin => $csvItemMarketplaces) {
            /**
             * @var Item $item
             */
            $item = $storedItems->get($asin);
            $itemMarketplaces = $item->itemMarketplaces->keyBy('marketplace_id');
            foreach ($csvItemMarketplaces as $csvItemMarketplacesPerPrice) {
                $csvItemMarketplace = $csvItemMarketplacesPerPrice->first();
                $csvItemMarketplaceId = $csvItemMarketplace['marketplaceId'] ?? null;
                if (is_null($csvItemMarketplaceId)) {
                    continue;
                }
                /**
                 * @var ItemMarketplace $itemMarketplace
                 */
                $itemMarketplace = $itemMarketplaces->get($csvItemMarketplaceId);
                $csvAsin = Str::ascii(trim($csvItemMarketplace['asin']));
                $csvAsinKeyString = [
                    $itemMarketplace->id,
                    ItemIdentificationType::ASIN,
                    $csvAsin
                ];
                $csvAsinKeyString = implode($delimiter, $csvAsinKeyString);

                $csvSku = Str::ascii(trim($csvItemMarketplace['sku']));
                $csvSkuKeyString = [
                    $itemMarketplace->id,
                    ItemIdentificationType::SKU,
                    $csvSku
                ];
                $csvSkuKeyString = implode($delimiter, $csvSkuKeyString);

                if (!in_array($csvSkuKeyString, $existing)) {
                    $csvSkuData = [
                        $itemMarketplace->id,
                        ItemIdentificationType::SKU,
                        "'" . $csvSku . "'"
                    ];
                    $identifiers[] = '(' . implode(', ', $csvSkuData) . ')';
                }

                if (!in_array($csvAsinKeyString, $existing)) {
                    $csvAsinData = [
                        $itemMarketplace->id,
                        ItemIdentificationType::ASIN,
                        "'" . $csvAsin . "'"
                    ];
                    $identifiers[] = '(' . implode(', ', $csvAsinData) . ')';
                }
            }
        }

        if (count($identifiers) > 1) {
            $identifiers = implode(', ', $identifiers);
            $query = 'INSERT INTO item_item_identification_types (item_marketplace_id, item_identification_type_id, value) VALUES ' . $identifiers . ';';
            DB::statement($query);
        }

        return $storedItems;
    }

    private function saveItemPrices(EloquentCollection $storedItems, Collection $csvItems): EloquentCollection
    {
        $dbData = [];
        foreach ($csvItems as $sku => $csvItemMarketplaces) {
            /**
             * @var Item $storedItem
             * @var Collection $csvItemMarketplaces
             */
            $storedItem = $storedItems->get($sku);
            $storedItemMarketplaces = $storedItem->itemMarketplaces
                ->keyBy('marketplace_id');

            foreach ($csvItemMarketplaces as $csvItemPrices) {
                $marketplaceId = $csvItemPrices->first()['marketplaceId'] ?? null;
                if (is_null($marketplaceId)) {
                    continue;
                }

                /**
                 * @var Collection $csvItemPrices
                 */
                $storedItemMarketplace = $storedItemMarketplaces->get($marketplaceId);
                $storedItemSalePrices = $storedItemMarketplace->itemSalePrices;

                $resolver = new ItemSalePricesResolver($storedItemMarketplace->id);

                foreach ($storedItemSalePrices as $storedItemSalePrice) {
                    $resolver->addItemSalesPrice(
                        $storedItemSalePrice->start_date,
                        $storedItemSalePrice->net_price,
                        $storedItemSalePrice->gross_price,
                        $storedItemSalePrice->currency_id,
                        $storedItemSalePrice->id,
                    );
                }

                foreach ($csvItemPrices as $csvItemPrice) {
                    $priceNet = $csvItemPrice['net'] ?? null;
                    $priceGross = $csvItemPrice['gross'] ?? null;
                    $priceDate = $csvItemPrice['date'] ?? null;
                    $priceCurrencyId = data_get($csvItemPrice, 'invoiceData.sales_currency_id');

                    // If no price data - nothing to do
                    if (is_null($priceNet) || is_null($priceGross) || is_null($priceDate) || is_null($priceCurrencyId)) {
                        continue;
                    }

                    $resolver->addItemSalesPrice(
                        $priceDate,
                        $priceNet,
                        $priceGross,
                        $priceCurrencyId,
                    );
                }

                $dbData[] = $resolver->resolve();
            }
        }

        $insert = [];
        $update = [];
        $delete = [];
        foreach ($dbData as $resolver) {
            /**
             * @var ItemSalePricesResolver $resolver
             */

            foreach ($resolver->getForDelete() as $price) {
                $delete[] = $price;
            }

            foreach ($resolver->getForUpdate() as $item) {
                $item['start_date'] = "'" . $item['start_date'] . "'";

                $update[] = $item;
            }

            foreach ($resolver->getForInsert() as $item) {
                unset($item['id']);
                $item['start_date'] = "'" . $item['start_date'] . "'";

                $insert[] = $item;
            }
        }

        if (count($insert) > 0) {
            $fields = array_keys($insert[0]);
            $fields = implode(', ', $fields);

            $insertString = [];
            foreach ($insert as $insertItem) {
                $insertString[] = '(' . implode(', ', $insertItem) . ')';
            }
            $insertString = implode(', ', $insertString);

            $query = 'INSERT INTO item_sale_prices(' . $fields . ') VALUES' . $insertString . ';';
            DB::statement($query);
        }

        if (count($update) > 0) {
            $updateString = [];
            foreach ($update as $updateItem) {
                $updateString[] = '(' . implode(', ', $updateItem) . ')';
            }
            $updateString = implode(', ', $updateString);

            $query = [];
            $query[] = 'UPDATE item_sale_prices AS isp SET';
            $query[] = 'currency_id = u.currency_id, item_marketplace_id = u.item_marketplace_id, start_date = u.start_date::date, net_price = u.net_price, gross_price = u.gross_price';
            $query[] = 'FROM (values ' . $updateString . ')';
            $query[] = 'AS u(id, currency_id, net_price, start_date, gross_price, item_marketplace_id)';
            $query[] = 'WHERE isp.item_marketplace_id = u.item_marketplace_id AND isp.id = u.id;';
            $query = implode(' ', $query);

            DB::statement($query);
        }

        if (count($delete) > 0) {
            $deleteString = [];
            foreach ($delete as $price) {
                $deleteString[] = '(id = ' . $price['id'] . ' AND item_marketplace_id = ' . $price['item_marketplace_id'] . ')';
            }
            $deleteString = implode(' OR ', $deleteString);
            $query = 'DELETE FROM item_sale_prices WHERE ' . $deleteString . ';';

            DB::statement($query);
        }

        return $storedItems;
    }

    private function saveItemMarketplacesIfNotExists(EloquentCollection $storedItems, Collection $csvItems): EloquentCollection
    {
        $insert = [];
        $fields = [];
        foreach ($csvItems as $sku => $csvItemMarketplaces) {
            /**
             * @var Item $storedItem
             */
            $storedItem = $storedItems->get($sku);
            $storedItemMarketplaces = $storedItem->itemMarketplaces
                ->keyBy('marketplace_id');
            foreach ($csvItemMarketplaces as $csvItems) {
                $csvItem = $csvItems->first();
                $csvMarketplaceId = $csvItem['marketplaceId'] ?? null;
                if (is_null($csvMarketplaceId) || $storedItemMarketplaces->has($csvMarketplaceId)) {
                    continue;
                }

                $insertMarketplace = [
                    'item_id'        => $storedItem->id,
                    'marketplace_id' => $csvItem['marketplaceId'],
                    'description'    => "'" . $csvItem['description'] . "'",
                ];
                $fields = array_keys($insertMarketplace);
                $insert[] = '(' . implode(', ', $insertMarketplace) . ')';
            }
        }

        if (count($insert) > 0) {
            $fields = implode(', ', $fields);
            $query = 'INSERT INTO item_marketplaces (' . $fields . ') VALUES ' . implode(', ', $insert) . ';';
            DB::statement($query);
        }

        return $storedItems;
    }

    /**
     * @throws Exception
     */
    private function storeAndGetAllItems(Collection $csvItems, Collection $asins): EloquentCollection
    {
        if ($csvItems->count() < 1) {
            return $this->getItemsByAsins($asins->toArray());
        }

        $exitingItems = $this->getItemsByAsins($asins->toArray());

        $insertItems = [];
        $fields = [];
        foreach ($csvItems as $itemMarketplaces) {
            /**
             * @var Collection $itemMarketplaces
             * @var Collection $itemPrices
             */
            /** @noinspection PhpUndefinedMethodInspection */
            $item = $itemMarketplaces->first()->first();
            $asin = Str::ascii(trim($item['asin']));
            $sku = Str::ascii(trim($item['sku']));
            if ($exitingItems->has($asin)) {
                continue;
            }

            $identifiers = [
                [
                    'name'  => 'ASIN',
                    'value' => $asin,
                ],
                [
                    'name'  => 'SKU',
                    'value' => $sku
                ],
            ];

            $identifiers = $this->itemService->buildIdentifiersJson($identifiers, null, 'csv');

            $insertItemData = [
                'name'                       => "'" . Str::limit($item['description'], 50) . "'",
                'description'                => "'" . $item['description'] . "'",
                'company_id'                 => $item['company_id'] ?? 'null',
                'manufacture_country_id'     => $item['manufacture_country_id'] ?? 'null',
                'item_tax_code_id'           => $item['item_tax_code_id'] ?? 'null',
                'unit_id'                    => $item['unit_id'] ?? 'null',
                'identifiers'                => "('" . $identifiers . "')::jsonb",
                'item_type_id'               => ItemType::GOODS,
                'unit_value'                 => $item['unit_value'] ?? 'null',
                'purchase_price_cost'        => $item['purchase_price_cost'],
                'purchase_price_currency_id' => $item['purchase_price_currency_id'],
                'purchase_price_net'         => $item['purchase_price_net'],
                'is_purchase_price_guessed'  => 'true',
                'resource_id'                => $this->amazonReport->id,
                'resource'                   => "'" . AmazonReport::class . "'"
            ];

            // Fields for query
            $fields = array_keys($insertItemData);
            $insertItemData = implode(', ', $insertItemData);
            $insertItems[] = '(' . $insertItemData . ')';
        }

        Metric::start('insertItemsToDb', 'storingAndFetchingItems');
        if (count($insertItems) > 0) {
            $query = implode(', ', $insertItems);
            $fields = implode(', ', $fields);
            $query = 'insert into items (' . $fields . ') values ' . $query . ';';
            DB::statement($query);
        }
        Metric::end('insertItemsToDb');

        return $this->getItemsByAsins($asins->toArray());
    }

    /**
     * @param array $asins
     * @return EloquentCollection keyBy-ed by ASIN
     */
    private function getItemsByAsins(array $asins): EloquentCollection
    {
        return $this->itemsRepo
            ->getItemsByAsinsAndCompaniesIds($asins)
            ->groupBy(function (Item $item) use ($asins) {
                $identifiers = $item->identifiers_text;

                $key = 0;
                foreach ($asins as $asinData) {
                    if (Str::contains($identifiers, '/' . $asinData['asin'] . '/')) {
                        $key = $asinData['asin'];
                        break;
                    }
                }

                return $key;
            })->map(function (Collection $itemsByAsin) {
                /**
                 * @var Item $item
                 */
                $item = $itemsByAsin->first();

                return $item;
            });
    }

    private function resolveAsinsByCompanyId(Collection $csvItems): Collection
    {
        return $csvItems->map(function (Collection $itemMarketplace, $asin) {
            /** @noinspection PhpUndefinedMethodInspection */
            return [
                'asin'      => Str::ascii(trim($asin)),
                'companyId' => $itemMarketplace
                    ->first()
                    ->first()['company_id']
            ];
        })->values();
    }

    private function mapItemsMarketplacesPrices(Collection $items): Collection
    {
        return $items->map(function (array $itemData) {
            $itemData = collect($itemData)->groupBy(fn(array $item) => $item['marketplaceId'] ?? 'FC');

            if ($itemData->count() > 1) {
                $itemData->forget(0);
            }

            return $itemData->map(function (Collection $itemDataOnMarketplace) {
                return $itemDataOnMarketplace
                    ->sortBy(function (array $item) {
                        return $item['timestamp'];
                    }, SORT_NATURAL)
                    ->groupBy(function (array $item) {
                        return '_' . $item['net'] ?? 0;
                    })
                    ->map(function (Collection $samePrices) {
                        return $samePrices->first();
                    })->sortBy(function ($item) {
                        return $item['timestamp'];
                    }, SORT_NATURAL, true);
            });
        });
    }

    private function resolvePurchaseInvoiceAndSupplierData(array $data): array
    {
        $invoiceData = $data['invoiceData'];
        $invoiceSubtypeId = $invoiceData['invoice_subtype_id'];
        $invoiceTypeId = $this->invoiceSubtypes->get($invoiceSubtypeId)->invoice_type_id;
        if ($invoiceTypeId !== InvoiceType::PURCHASE_INVOICE) {
            return $data;
        }

        $supplierVatNumber = $data['bill_from_vat_number'] ?? null;
        $supplier = $this->databaseResolver->getSupplierByVatNumber($supplierVatNumber ?? '');
        if (is_null($supplierVatNumber) || is_null($supplier)) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new Exception('Supplier or supplier VAT number is null');
        }

        $company = $data['sales_channel_data']['company'];

        $shipFromVatNumber = $invoiceData['ship_from_vat_number'] ?? null;

        $shipToName = $invoiceData['ship_to_name'] ?? null;
        $shipToAddress = $invoiceData['ship_to_address'] ?? null;
        $shipToCountryId = $invoiceData['ship_to_country_id'] ?? null;
        $shipToPostalCode = $invoiceData['ship_to_postal_code'] ?? null;
        $shipToVatNumber = $invoiceData['ship_to_vat_number'] ?? null;

        // Bill to
        $invoiceData['bill_to_name'] = $shipToName;
        $invoiceData['bill_to_address'] = $shipToAddress;
        $invoiceData['bill_to_country_id'] = ($company['address']['country_id'] ?? null) ?? $shipToCountryId;
        $invoiceData['bill_to_postal_code'] = ($company['address']['postal_code'] ?? null) ?? $shipToPostalCode;
        $invoiceData['bill_to_vat_number'] = $shipFromVatNumber;

        // Ship to
        $invoiceData['ship_to_address'] = null;
        $invoiceData['ship_to_vat_number'] = $shipFromVatNumber;

        // Ship from
        $invoiceData['ship_from_vat_number'] = $shipToVatNumber;

        // Supplier vat number
        $invoiceData['supplier_id'] = $supplier->id;
        $invoiceData['bill_from_country_id'] = $supplier->identification_number_country_id;
        $invoiceData['bill_from_vat_number'] = $supplier->identification_number_value;

        $data['invoiceData'] = $invoiceData;

        return $data;
    }

    protected function getCurrentFile(): string
    {
        return __FILE__;
    }

    private function resolveItemTaxCode(int $itemTaxCodeId, ?string $asin = null): int
    {
        if (is_null($asin)) {
            return $itemTaxCodeId;
        }

        /**
         * @var Item $item
         */
        $item = $this->asins->get($asin);
        if (!is_null($item)) {
            $itemTaxCodeId = $item->item_tax_code_id;
        }

        return $itemTaxCodeId;
    }
}
