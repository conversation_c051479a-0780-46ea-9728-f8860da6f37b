<?php

namespace App\Core\AmazonCsv\Handlers;

use App\Core\AmazonCsv\Handlers\Contracts\CycleContract;
use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportRawData;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Reports\Contracts\AmazonReportServiceContract;
use App\Core\System\Helpers\Metric;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Throwable;

class CycleOne extends CycleContract
{
    private int $chunkSize = 1250;

    private AmazonReport $amazonReport;

    /** @noinspection PhpUnhandledExceptionInspection */
    public function __construct(AmazonReport $amazonReport)
    {
        // MUST BE IN ORDER
        $amazonReport->load('uploadUser');

        $amazonReport->report_status_id = AmazonReportStatus::STATUS_PARSING_STARTED;
        $amazonReport->parsing_started_at = Carbon::now()->toDateTimeString();
        $amazonReport->save();
        $this->amazonReport = $amazonReport;

        Metric::addMessage('Cycle 1 - START');

        /** @noinspection PhpUnhandledExceptionInspection */
        try {
            $this->run();
        } catch (Throwable $exception) {
            $amazonReport->error_reason = $this->generateErrorReason(
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine(),
                __LINE__
            );
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
            $amazonReport->save();
            if ($this->isDebug()) {
                throw $exception;
            }
        }
    }

    /**
     * @return AmazonReport
     */
    public function getAmazonReport(): AmazonReport
    {
        return $this->amazonReport;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    private function run(): void
    {
        $this->deleteAllCyclesFiles();
        $amazonReport = $this->amazonReport;

        $file = $amazonReport->file;
        $content = $this->resolveFile($file->file_path);
        if ($content->isChunked()) {
            $this->resolveChunks($content->getChunks());

            return;
        }

        $content = $content->getContent();
        if (count($content) < 2) {
            throw new Exception('amazon.report-parsing-error-reason.No rows found');
        }

        $isQuotedCommaDelimited = $this->isQuotedCommaDelimited($content[0]);
        $isCommaDelimited = $this->isCommaDelimited($content[0]);
        $header = explode(AmazonReportRawData::DELIMITER, $content[0]);
        if ($isQuotedCommaDelimited || $isCommaDelimited) {
            $header = str_getcsv($content[0]);
        }

        $columnCount = count($header);

        if (!$this->isColumnCountOk($columnCount)) {
            throw new Exception('amazon.report-parsing-error-reason.Column count error');
        }

        unset($content[0]);
        $fileData = [];
        foreach ($content as $rowRaw) {
            $row = trim($rowRaw);
            if (mb_strlen($row) < 1) {
                continue;
            }

            if ($isQuotedCommaDelimited || $isCommaDelimited) {
                $row = str_getcsv($row);
                $row = implode(AmazonReportRawData::DELIMITER, $row);
            }
            $row = str_replace("'", '“', $row);
            $row = trim($row);
            if (mb_strlen($row) < 1) {
                continue;
            }
            $row = $this->binaryStringToString($row);
            $row = [
                'cycle'        => AmazonReportRawData::CYCLE_READ,
                'success'      => true,
                'column_count' => $columnCount,
                'data'         => $row
            ];

            $fileData[] = $row;
        }

        if (count($fileData) < 1) {
            throw new Exception('amazon.report-parsing-error-reason.Row count error');
        }

        $this->storeJsonFileForCycle(1, $fileData);

        $amazonReport->report_status_id = AmazonReportStatus::STATUS_READ;
        $amazonReport->parsing_ended_at = Carbon::now()->toDateTimeString();
        $amazonReport->save();

        $this->amazonReport = $amazonReport;
    }

    /**
     * @param int $columnCount
     * @return bool
     */
    private function isColumnCountOk(int $columnCount): bool
    {
        return $columnCount === 92 || $columnCount === 95;
    }

    /**
     * @param $path
     * @return object
     * @throws \Exception
     */
    private function resolveFile($path): object
    {
        if (!file_exists($path)) {
            throw new Exception('File not found' . ' (' . $path . ')');
        }
        $content = file_get_contents($path);
        if (is_string_binary($content)) {
            try {
                $content = gzdecode($content);
            } catch (Exception) {
            }
        }
        $content = $this->stripBOM($content);
        $content = str_replace("\x00", '', $content);
        $content = str_replace(config('evat.amazonParsing.csvDelimiter'), '-', $content);
        $content = html_entity_decode($content);

        $content = str_replace("'", '`', $content);
        $content = str_replace("\r", '', $content);
        $content = str_replace("\n", "\r\n", $content);

        $tmpRow = explode("\r\n", $content)[0];
        if ($this->isSemicolonDelimited($tmpRow)) {
            $content = str_replace(';', AmazonReportRawData::DELIMITER, $content);
        }

        if ($this->isTabDelimited($tmpRow)) {
            $content = str_replace("\t", AmazonReportRawData::DELIMITER, $content);
        }
        $content = trim($content, "\r\n");

        $content = explode("\r\n", $content);

        return new class ($content, $this->chunkSize) {
            private int $chunkSize;
            private Collection $content;
            private string $header;

            public function __construct(array $content, int $chunkSize)
            {
                $this->header = $content[0] ?? [];
                unset($content[0]);
                $this->content = collect($content);
                $this->chunkSize = $chunkSize;
            }

            public function isChunked(): bool
            {
                return $this->content->count() > $this->chunkSize;
            }

            public function getContent(): array
            {
                return $this->content
                    ->prepend($this->header)
                    ->values()
                    ->toArray();
            }

            public function getChunks(): array
            {
                return $this->content
                    ->chunk($this->chunkSize)
                    ->map(function (Collection $chunk) {
                        return $chunk->prepend($this->header);

                    })
                    ->values()
                    ->toArray();
            }
        };
    }

    /**
     * @param string $row
     * @return bool
     */
    private function isQuotedCommaDelimited(string $row): bool
    {
        $isQuotedCommaDelimited = explode('","', $row);
        $isQuotedCommaDelimitedWithSpaceBefore = explode('" ,"', $row);
        $isQuotedCommaDelimitedWithSpaceAfter = explode('", "', $row);

        $counts = [
            count($isQuotedCommaDelimited),
            count($isQuotedCommaDelimitedWithSpaceBefore),
            count($isQuotedCommaDelimitedWithSpaceAfter)
        ];
        $check = false;
        foreach ($counts as $count) {
            if ($count > 1) {
                $check = true;
            }
        }

        return $check;
    }

    /**
     * @param string $row
     * @return bool
     */
    private function isCommaDelimited(string $row): bool
    {
        $isCommaDelimited = explode(',', $row);

        return count($isCommaDelimited) > 90;
    }

    /**
     * @param string $row
     * @return bool
     */
    private function isSemicolonDelimited(string $row): bool
    {
        $isSemicolonDelimited = explode(';', $row);

        return count($isSemicolonDelimited) > 90;
    }

    /**
     * @param string $row
     * @return bool
     */
    private function isTabDelimited(string $row): bool
    {
        $isTabDelimited = explode("\t", $row);

        return count($isTabDelimited) > 90;
    }

    private function stripBOM($string): array|string|null
    {
        $string = $this->stripUtf8Bom($string);
        $string = $this->stripUtf16Be($string);

        return $this->stripUtf16Le($string);
    }

    private function stripUtf8Bom($string): array|string|null
    {
        return preg_replace('/^\xef\xbb\xbf/', '', $string);
    }

    private function stripUtf16Le($string): array|string|null
    {
        return preg_replace('/^\xff\xfe/', '', $string);
    }

    private function stripUtf16Be($string): array|string|null
    {
        return preg_replace('/^\xfe\xff/', '', $string);
    }

    protected function getCurrentFile(): string
    {
        return __FILE__;
    }

    private function binaryStringToString(string $str): string
    {
        $encoding = mb_detect_encoding($str);
        if ($encoding === 'UTF-8') {
            return $str;
        }

        $str = mb_convert_encoding($str, 'UTF-8', 'auto');

        return preg_replace('/[[:^print:]]/', '', $str);
    }

    private function resolveChunks(array $chunks): void
    {
        $report = $this->amazonReport;
        $file = $report->file;

        $fileName = $file->name;
        $extension = $file->extension;
        $mime = $file->mime;
        $uploadUserId = $report->upload_user_id;
        $description = $report->description;
        $uploadInstitutionInstitutionTypeId = $report->upload_institution_institution_type_id;
        $uploadCompanyId = $report->upload_company_id;
        $salesChannelId = $report->sales_channel_id;
        $reportStatusId = AmazonReportStatus::STATUS_QUEUED;
        $batchId = $report->batch_id;
        $originalReportId = $report->id;
        $uploadedAt = $report->uploaded_at;
        $chunkName = $report->chunk_name;

        /**
         * @var AmazonReportServiceContract $reportService
         * @noinspection PhpUnhandledExceptionInspection
         */
        $reportService = app()->make(AmazonReportServiceContract::class);
        /**
         * @var array<AmazonReport> $saved
         */
        $saved = [];
        $error = false;
        foreach ($chunks as $key => $chunk) {
            try {
                $data = implode("\r\n", $chunk);
                $size = mb_strlen($data, '8bit');
                $name = $fileName . '-(part-' . str_pad($key + 1, 3, '0', STR_PAD_LEFT) . ').' . $extension;

                $newReport = $reportService->storeCsvFileData(
                    fullName: $name,
                    extension: $extension,
                    mime: $mime,
                    size: $size,
                    data: $data,
                    uploadUserId: $uploadUserId,
                    description: $description,
                    uploadInstitutionInstitutionTypeId: $uploadInstitutionInstitutionTypeId,
                    uploadCompanyId: $uploadCompanyId,
                    salesChannelId: $salesChannelId,
                    reportStatusId: $reportStatusId,
                    batchId: $batchId,
                    originalReportId: $originalReportId,
                    chunkName: $chunkName,
                    uploadedAt: $uploadedAt
                );

                $saved[] = $newReport;

            } catch (Throwable) {
                $error = true;
                break;
            }
        }

        if ($error) {
            foreach ($saved as $savedReport) {
                try {
                    $savedReport->delete();
                } catch (Throwable) {
                }
            }

            return;
        }

        $report->delete();
    }
}
