<?php

namespace App\Core\AmazonCsv\Handlers;

use App\Core\AmazonCsv\Handlers\Contracts\CycleContract;
use App\Core\AmazonCsv\Handlers\Helpers\ErrorCodesEnum;
use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportRawData;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Data\Models\EventType;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceStatus;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Repositories\Contracts\AmazonReportRepositoryContract;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use App\Core\System\Helpers\Metric;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class CycleFour extends CycleContract
{
    private int $fetchRawRowsSize = 100000;

    private array $invoicesDb = [];

    private AmazonReport $amazonReport;

    private AmazonReportRepositoryContract $amazonReportRepo;

    private AmazonReportRawData $firstTransaction;

    private AmazonReportRawData $lastTransaction;

    /**
     * @var \Illuminate\Support\Collection<InvoiceSubtype>
     */
    private Collection $invoiceSubtypes;

    /** @noinspection PhpUnhandledExceptionInspection */
    public function __construct(AmazonReport $amazonReport)
    {
        $amazonReport->load(
            'firstTransaction',
            'lastTransaction',
            'uploadUser'
        );

        /**
         * @var AmazonReportRepositoryContract $amazonReportRepo
         */
        $amazonReportRepo = app()->make(AmazonReportRepositoryContract::class);
        $this->amazonReportRepo = $amazonReportRepo;
        $this->amazonReport = $amazonReport;

        /**
         * @var InvoiceRepositoryContract $invoiceRepo
         */
        $invoiceRepo = app()->make(InvoiceRepositoryContract::class);
        $this->invoiceSubtypes = $invoiceRepo->getAllInvoiceSubtypes()->keyBy('id');

        // MUST BE LAST
        try {
            $firstTransaction = $amazonReport->firstTransaction;
            $lastTransaction = $amazonReport->lastTransaction;
            if (is_null($firstTransaction) || is_null($lastTransaction)) {
                throw new Exception('reports.no-transactions');
            }
            $this->firstTransaction = $firstTransaction;
            $this->lastTransaction = $lastTransaction;

            $this->run();
        } catch (Throwable $exception) {
            $amazonReport->error_reason = $this->generateErrorReason(
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine(),
                __LINE__
            );
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
            $amazonReport->save();
            if ($this->isDebug()) {
                throw $exception;
            }
        }
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    private function run(): void
    {
        Metric::addMessage('Cycle 4 - START');

        /*
         * Checks if report is double. If true, deletes all raw_transactions,
         * sets report status to double and returns true
         *
         */
        Metric::start('checkIfReportIsDouble');
        $check = $this->checkAndResolveIfReportIsDouble();
        Metric::end('checkIfReportIsDouble');
        if ($check) {
            return;
        }

        /*
         * Fetch transactions grouped by invoice
         */
        Metric::start('getSyncedTransactionsGroupedByInvoice');
        $transactions = $this->getSyncedTransactionsGroupedByInvoice();
        Metric::end('getSyncedTransactionsGroupedByInvoice');

        /*
         * Chunk deemed
         */
        Metric::start('chunkDeemed');
        $transactions = $this->chunkDeemed($transactions);
        Metric::end('chunkDeemed');

        $notDeemedTransactions = $transactions->get('not-deemed', []);
        $deemedTransactions = $transactions->get('deemed', []);

        $this->resolveStoreInvoices($notDeemedTransactions, 'notDeemed');

        /*
         * Bind Connected Invoice
         */
        Metric::start('bindConnectedInvoice');
        $deemedTransactions = $this->bindConnectedInvoices($deemedTransactions);
        Metric::end('bindConnectedInvoice');

        $this->resolveStoreInvoices($deemedTransactions, 'deemed');

        /*
         * Sets status for amazon_report
         */
        Metric::start('set amazon report status');
        $this->setAmazonReportData();
        Metric::end('set amazon report status');
    }

    private function setAmazonReportData(): void
    {
        $amazonReport = $this->amazonReport;

        $errorTransactions = $amazonReport
            ->loadCount('errorTransactions')
            ->error_transactions_count;

        $amazonReport->parsing_ended_at = Carbon::now()->toDateTimeString();

        if ($errorTransactions > 0) {
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_FAILED;
            $amazonReport->save();

            return;
        }

        $amazonReport->report_status_id = AmazonReportStatus::STATUS_SUCCESS;

        $amazonReport->start_date = $this->firstTransaction->transaction_date;
        $amazonReport->end_date = $this->lastTransaction->transaction_date;

        $amazonReport->save();

        $amazonReport = $amazonReport->unsetRelations();
        unset($amazonReport->success_transactions_count);
        unset($amazonReport->error_transactions_count);

        $this->deleteAllCyclesFiles();
    }

    private function checkAndResolveIfReportIsDouble(): bool
    {
        $firstTransactionDate = $this->firstTransaction->transaction_date;
        $lastTransactionDate = $this->lastTransaction->transaction_date;
        $salesChannelId = $this->lastTransaction->parsed_data['sales_channel_id'];
        $amazonReport = $this->amazonReport;
        $amazonReportId = $amazonReport->id;

        $isDouble = DB::table('amazon_reports')
            ->selectRaw('count(*)')
            ->where('original_report_id', '!=', $amazonReport->original_report_id)
            ->whereIn('report_status_id', [
                AmazonReportStatus::STATUS_SUCCESS,
                AmazonReportStatus::STATUS_DOUBLE,
                AmazonReportStatus::STATUS_PARSING_STARTED,
                AmazonReportStatus::STATUS_READ,
                AmazonReportStatus::STATUS_RESOLVED,
                AmazonReportStatus::STATUS_SYNCED_DB,
            ])
            ->whereRaw("sales_channel_id = $salesChannelId and (('$firstTransactionDate'::date, '$lastTransactionDate'::date) overlaps (start_date::date, end_date::date))")
            ->first()?->count ?? 1;

        $isDouble = $isDouble > 0;

        if ($isDouble) {
            $query = "delete from amazon_reports_raw_data where amazon_report_id = $amazonReportId";
            DB::statement($query);
            $amazonReport->report_status_id = AmazonReportStatus::STATUS_DOUBLE;
            $amazonReport->start_date = $firstTransactionDate;
            $amazonReport->end_date = $lastTransactionDate;
            $amazonReport->save();
        }

        return $isDouble;
    }

    /**
     * @param array $invoices
     */
    private function storeInvoicesItems(array $invoices): void
    {
        if (count($invoices) < 1) {
            return;
        }

        $first = data_get(reset($invoices)[0] ?? [], 'parsed_data.invoiceItemData');
        if (is_null($first)) {
            return;
        }
        $columns = $this->getInsertInvoicesItemsColumnsString($first);
        $amazonReportsRawDataIds = [];
        $insertValues = [];
        foreach ($invoices as $invoice) {
            foreach ($invoice as $invoiceItem) {
                $invoiceItemData = $invoiceItem['parsed_data']['invoiceItemData'];
                ksort($invoiceItemData, SORT_NATURAL);

                $invoiceData = $invoiceItem['parsed_data']['invoiceData'];
                $dbInvoice = $this->invoicesDb[$invoiceData['parsing_invoice_number']];

                $invoiceItemData['invoice_id'] = $dbInvoice['id'];

                $set = [];
                foreach ($invoiceItemData as $field) {
                    $set[] = $this->formatTypeForSql($field);
                }

                $insertValues[] = '(' . implode(', ', $set) . ')';

                $amazonReportsRawDataIds[] = $invoiceItem['id'];
            }
        }

        if (count($insertValues) > 0) {
            /*
             * Store invoices items
             */
            $insertValues = implode(', ', $insertValues);
            $query = 'INSERT INTO invoice_items(' . $columns . ') VALUES ' . $insertValues . ';';
            DB::statement($query);
        }

        Metric::start('deleteAmazonReportsRawData', 'storeInvoicesItems');
        $this->deleteResolvedAmazonReportsRawData($amazonReportsRawDataIds);
        Metric::end('deleteAmazonReportsRawData');
    }


    private function deleteResolvedAmazonReportsRawData(array $amazonReportsRawDataIds): void
    {
        if (count($amazonReportsRawDataIds) > 0) {
            $ids = implode(', ', $amazonReportsRawDataIds);
            DB::statement('DELETE FROM amazon_reports_raw_data WHERE id IN(' . $ids . ');');
        }
    }

    /**
     * @param array $invoiceItem
     * @return string
     */
    private function getInsertInvoicesItemsColumnsString(array $invoiceItem): string
    {
        $columns = array_keys($invoiceItem);
        sort($columns, SORT_NATURAL);

        return implode(', ', $columns);
    }

    /**
     * @throws Exception
     */
    private function storeInvoices(array $invoicesData): void
    {
        if (count($invoicesData) < 1) {
            return;
        }

        $invoicesData = array_values($invoicesData);
        $first = data_get(reset($invoicesData)[0] ?? [], 'parsed_data.invoiceData');
        if (is_null($first)) {
            return;
        }


        $invoicesNumbers = collect($invoicesData)->map(function (array $invoiceItems) {
            $invoice = $invoiceItems[0]['parsed_data']['invoiceData'];
            $invoicesNumber = $invoice['parsing_invoice_number'];

            return $this->formatTypeForSql($invoicesNumber);
        })->toArray();

        $invoicesDb = $this->getInvoicesByInvoicesNumbersStringsArray($invoicesNumbers);

        $invoicesQuery = [];
        foreach ($invoicesData as $invoiceItems) {
            $invoice = $invoiceItems[0]['parsed_data']['invoiceData'];
            $parsingInvoiceNumber = $invoice['parsing_invoice_number'];
            /*
             * THIS CHECK IS IMPORTANT
             *
             * When chunking, single item can get in one report, other in other one.
             * If we remove this check. We can get false positive double parsing_invoice_number
             * database constraint exception.
             */
            if ($invoicesDb->has($parsingInvoiceNumber)) {
                continue;
            }

            $invoicesDb->put($parsingInvoiceNumber, true);

            $invoiceItemsData = $this->resolveInvoicesDataField($invoiceItems);
            $invoiceStatusId = $this->resolveInvoiceStatus($invoiceItems);

            $invoiceNumber = $invoice['invoice_number'];
            if (is_null($invoiceNumber)) {
                $invoiceNumber = $parsingInvoiceNumber;
            }

            $invoice['invoice_number'] = $invoiceNumber;
            $invoice['resource'] = AmazonReport::class;
            $invoice['data'] = $invoiceItemsData;
            $invoice['invoice_status_id'] = $invoiceStatusId;

            ksort($invoice, SORT_NATURAL);

            $singleInvoiceQuery = [];
            foreach ($invoice as $key => $field) {
                $field = $this->formatTypeForSql($field);
                $singleInvoiceQuery[$key] = $field;
            }

            $invoicesQuery[] = '(' . implode(',', $singleInvoiceQuery) . ')';
        }

        // INSERT
        $columns = $this->getInsertInvoicesColumnsString($first);
        $invoicesQuery = implode(', ', $invoicesQuery);
        $query = 'INSERT INTO invoices(' . $columns . ') VALUES' . $invoicesQuery . ';';
        try {
            Metric::start('insertInvoices', 'storeInvoices');
            DB::statement($query);
            Metric::end('insertInvoices');
        } catch (QueryException $exception) {
            if ($exception->getCode() === 'P0001') {
                $message = $exception->getMessage();
                $message = explode('" already exists', $message);
                $message = explode('"', $message[0] ?? '');
                $invoiceKey = $message[1] ?? null;
                if (!is_null($invoiceKey)) {
                    $message = _l('reports.tampered', ['number' => $invoiceKey]);
                    if (Str::contains($invoiceKey, 'E+')) {
                        $message = _l('reports.tampered-excel', ['number' => $invoiceKey]);
                    }
                    throw new Exception($message, ErrorCodesEnum::FILE_TAMPERED);
                }
            }

            throw $exception;
        }

        $invoicesDb = $this->getInvoicesByInvoicesNumbersStringsArray($invoicesNumbers);
        $data = [];
        foreach ($invoicesDb as $invoice) {
            /**
             * @var Invoice $invoice
             */
            $data[$invoice->parsing_invoice_number] = [
                'id'   => $invoice->id,
                'date' => $invoice->invoice_date,
            ];
        }

        $this->invoicesDb = $data;
    }

    private function getInvoicesByInvoicesNumbersStringsArray(array $invoicesNumbers): Collection
    {
        $invoicesNumbers = implode(', ', $invoicesNumbers);

        $data = DB::select('SELECT id, parsing_invoice_number, invoice_date, company_id FROM invoices WHERE parsing_invoice_number IN(' . $invoicesNumbers . ') order by id');

        return collect($data)->keyBy('parsing_invoice_number');
    }

    private function resolveInvoiceStatus(array $invoiceItems): int
    {
        $invoice = $invoiceItems[0]['parsed_data']['invoiceData'];
        $invoiceSubtypeId = $invoice['invoice_subtype_id'];
        /**
         * @var InvoiceSubtype $invoiceSubtype
         */
        $invoiceSubtype = $this->invoiceSubtypes->get($invoiceSubtypeId);
        $taxCollectionResponsibilityId = $invoice['tax_collection_responsibility_id'];
        $status = InvoiceStatus::ISSUED;
        if (in_array($invoiceSubtype->invoice_type_id, [InvoiceType::PURCHASE_INVOICE, InvoiceType::DEBIT_NOTE])) {
            $status = InvoiceStatus::EXPENSES;
        } elseif ($taxCollectionResponsibilityId === TaxCollectionResponsibility::MARKETPLACE) {
            $status = InvoiceStatus::DEEMED_SUPPLIES;
        }

        foreach ($invoiceItems as $invoiceItem) {
            if ($invoiceItem['event_type_id'] === EventType::DONATION) {
                $status = InvoiceStatus::DRAFT;
                break;
            }

            $data = $invoiceItem['parsed_data'];

            $errors = array_filter($data['messages'] ?? [], function ($message) {
                $type = $message['type'] ?? 'error';

                return $type === 'danger' || $type === 'error' || $type === 'warning';
            });
            if (count($errors) > 0) {
                $status = InvoiceStatus::DRAFT;
                break;
            }
        }

        return $status;
    }

    /**
     * @param array $invoiceItems
     * @return array
     */
    private function resolveInvoicesDataField(array $invoiceItems): array
    {
        $unsetData = [
            'invoiceData',
            'detected',
            'item_data',
            'supplier_name',
            'departure_city_data',
            'arrival_country_data',
            'transaction_event_id',
            'vat_inv_converted_amt',
            'departure_country_data',
            'sales_channel_data',
            'activity_transaction_id',
            'statistical_code_depart',
            'transaction_seller_vat_number_data',
            'seller_arrival_vat_number_country_data',
            'tax_calculation_imputation_country_data',
            'transaction_seller_vat_number_country_data',
            'marketplace_data',
            'arrival_city_data',
            'event_type_data',
            'sale_depart_country_data',
            'sale_arrival_country_data',
            'buyer_name',
        ];
        $invoiceItemsData = [];
        foreach ($invoiceItems as $invoiceItem) {
            $data = $invoiceItem['parsed_data'];

            foreach ($unsetData as $unset) {
                unset($data[$unset]);
            }

            $invoiceItemsData[] = $data;
        }

        return $invoiceItemsData;
    }

    /**
     * @param array $invoice
     * @return string
     */
    private function getInsertInvoicesColumnsString(array $invoice): string
    {
        $columns = array_keys($invoice);
        sort($columns, SORT_NATURAL);

        return implode(', ', $columns);
    }

    /**
     * @param mixed $field
     * @return mixed
     */
    private function formatTypeForSql(mixed $field): mixed
    {
        return match (true) {
            is_null($field) => 'null',
            is_array($field) => "('" . json_encode($field) . "')::jsonb",
            is_string($field) => "'" . $field . "'",
            $field === true || $field === false => $field ? 'true' : 'false',
            default => $field
        };
    }

    /**
     * @return AmazonReport
     */
    public function getAmazonReport(): AmazonReport
    {
        return $this->amazonReport;
    }

    /**
     * @return array
     */
    private function getSyncedTransactionsGroupedByInvoice(): array
    {
        $invoicesKeys = $this->getUniqueInvoicesKeys();
        $keys = implode(', ', $invoicesKeys);

        $data = [];
        if (count($invoicesKeys) < 1) {
            return $data;
        }

        $dbData = $this->amazonReportRepo
            ->getRawAmazonReportRawDataByUniqueInvoiceKeys($this->amazonReport->id, $keys);

        foreach ($dbData as $invoiceItem) {
            $invoiceItem->parsed_data = AmazonReportRawData::decodeErrorsOrParsedData($invoiceItem->parsed_data);
            $invoiceKey = $invoiceItem->unique_invoice_key;
            $invoiceItem = (array)$invoiceItem;

            $data[$invoiceKey][] = $invoiceItem;
        }

        return $data;
    }

    private function chunkDeemed(array $transactions): Collection
    {
        return collect($transactions)
            ->groupBy(function (array $invoiceData) {
                $connectionType = $invoiceData[0]['parsed_data']['invoiceData']['connected_invoice_type'] ?? null;
                $isDeemed = $connectionType === 'DEEMED';

                return $isDeemed ? 'deemed' : 'not-deemed';
            })
            ->map(function (Collection $transactions) {
                return $transactions->toArray();
            });
    }

    private function resolveStoreInvoices(array $transactions, string $metricKey): void
    {
        /*
         * Store invoices
         */
        $storeKey = 'store' . $metricKey . 'Invoices';
        Metric::start($storeKey);
        /** @noinspection PhpUnhandledExceptionInspection */
        $this->storeInvoices($transactions);
        Metric::end($storeKey);

        /*
         * Store invoices items
         */
        $storeInvoicesItemsKey = 'store' . $metricKey . 'InvoicesItems';
        Metric::start($storeInvoicesItemsKey);
        $this->storeInvoicesItems($transactions);
        Metric::end($storeInvoicesItemsKey);
    }

    private function bindConnectedInvoices(array $transactions): array
    {
        $normalSaved = $this->amazonReport
            ->invoices
            ->keyBy(function (Invoice $invoice) {
                return 'DS-' . $invoice->parsing_invoice_number;
            });

        return collect($transactions)
            ->map(function (array $invoiceItems) use ($normalSaved) {
                $invoiceItems = collect($invoiceItems)
                    ->map(function (array $invoiceItem) use ($normalSaved) {
                        $invoiceData = $invoiceItem['parsed_data']['invoiceData'];
                        $invoiceNumber = $invoiceData['parsing_invoice_number'];

                        $normalId = $normalSaved->get($invoiceNumber)->id;
                        $invoiceData['connected_invoice_id'] = $normalId;

                        $invoiceItem['parsed_data']['invoiceData'] = $invoiceData;

                        return $invoiceItem;
                    });

                return $invoiceItems->toArray();
            })->toArray();
    }

    /**
     * @return array
     */
    private function getUniqueInvoicesKeys(): array
    {
        return $this->amazonReportRepo
            ->getAmazonReportRawDataInvoices(
                $this->amazonReport->id,
                AmazonReportStatus::STATUS_SYNCED_DB,
                AmazonReportRawData::CYCLE_DB_RESOLVED,
                $this->fetchRawRowsSize
            )->pluck('key')
            ->toArray();
    }

    protected function getCurrentFile(): string
    {
        return __FILE__;
    }
}
