<?php

namespace App\Core\AmazonCsv\Handlers\Helpers;

use App\Core\System\Helpers\Arr;
use Exception;

class ParsedData2RawTransactionExtractor
{
    private ?int $eventTypeId;
    private ?string $transactionEventId;
    private ?string $activityTransactionId;
    private ?string $transactionDate;

    public function __construct(Arr $parsed)
    {
        $this->eventTypeId = $parsed['event_type_id'] ?? null;
        $this->transactionEventId = $parsed['transaction_event_id'] ?? null;
        $this->activityTransactionId = $parsed['activity_transaction_id'] ?? null;
        $this->transactionDate = $parsed['transaction_complete_date'] ?? null;
    }

    /**
     * @return int
     * @throws Exception
     */
    public function getEventTypeId(): int
    {
        if (is_null($this->eventTypeId)) {
            throw new Exception('amazon.report-parsing-error-reason.No event type');
        }

        return $this->eventTypeId;
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getTransactionEventId(): string
    {
        if (is_null($this->transactionEventId)) {
            throw new Exception('amazon.report-parsing-error-reason.No transaction event ID');
        }

        return $this->transactionEventId;
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getActivityTransactionId(): string
    {
        if (is_null($this->activityTransactionId)) {
            throw new Exception('amazon.report-parsing-error-reason.No activity transaction ID');
        }

        return $this->activityTransactionId;
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getTransactionDate(): string
    {
        if (is_null($this->transactionDate)) {
            throw new Exception('amazon.report-parsing-error-reason.No transaction complete date');
        }

        return $this->transactionDate;
    }
}
