<?php

namespace App\Core\AmazonCsv\Handlers\Helpers;

use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonSalesChannel;
use App\Core\Data\Models\City;
use App\Core\Data\Models\CompanyWarehouse;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\Currency;
use App\Core\Data\Models\DeliveryCondition;
use App\Core\Data\Models\EventType;
use App\Core\Data\Models\ItemCodeCategory;
use App\Core\Data\Models\ItemTaxCode;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\Marketplace;
use App\Core\Data\Models\Platform;
use App\Core\Data\Models\PostalCodeException;
use App\Core\Data\Models\ProgramType;
use App\Core\Data\Models\SalesChannel;
use App\Core\Data\Models\TaxationMethod;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Data\Models\VatNumber;
use App\Core\Data\Models\Warehouse;
use App\Core\Data\Models\WarehouseOperator;
use App\Core\Data\Repositories\Contracts\AmazonSalesChannelRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\DeliveryConditionRepositoryContract;
use App\Core\Data\Repositories\Contracts\EventTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Data\Repositories\Contracts\MarketplacesRepositoryContract;
use App\Core\Data\Repositories\Contracts\ProgramTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxCollectionResponsibilityRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxReportingSchemeRepositoryContract;
use App\Core\System\String\Encoding;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Throwable;

class RawData2DatabaseResolver implements Arrayable
{
    private CountryRepositoryContract $countryRepo;
    private CurrencyRepositoryContract $currencyRepo;
    private AmazonSalesChannelRepositoryContract $amazonSalesChannelRepo;
    private MarketplacesRepositoryContract $marketplaceRepo;
    private ProgramTypeRepositoryContract $programTypeRepo;
    private EventTypeRepositoryContract $eventTypeRepo;
    private ItemRepositoryContract $itemRepo;
    private DeliveryConditionRepositoryContract $deliveryConditionRepo;
    private TaxReportingSchemeRepositoryContract $taxReportingSchemeRepo;
    private TaxCollectionResponsibilityRepositoryContract $taxCollectionResponsibilityRepo;
    private Collection|EloquentCollection|array $countriesByCode;
    private Collection|EloquentCollection|array $countriesById;
    private Collection|EloquentCollection|array $currenciesByCode;
    /** @noinspection PhpPropertyOnlyWrittenInspection */
    private Collection|EloquentCollection|array $currenciesById;
    private Collection|EloquentCollection|array $amazonSalesChannels;
    private Collection|EloquentCollection|array $marketplacesByName;
    /** @noinspection PhpPropertyOnlyWrittenInspection */
    private Collection|EloquentCollection|array $marketplacesById;
    private Collection|EloquentCollection|array $programTypes;
    private Collection|EloquentCollection|array $eventTypes;
    private Collection|EloquentCollection|array $itemTaxCodes;
    private Collection|EloquentCollection|array $deliveryConditions;
    private Collection|EloquentCollection|array $taxReportingSchemes;
    private Collection|EloquentCollection|array $taxCollectionResponsibilities;
    private Collection $salesChannels;
    private Collection $warehouses;
    private Collection $vatNumbers;
    private Collection $cities;
    private array $salesChannelsDb;
    private Collection $vatNumbersDb;
    private Collection $citiesDb;
    private Collection $arrivalPostalCodes;
    private Collection $arrivalPostalCodesDb;

    /**
     * @param AmazonReport $amazonReport
     * @throws BindingResolutionException
     * @noinspection PhpUnusedParameterInspection
     */
    public function __construct(AmazonReport $amazonReport)
    {
        // Init
        $this->salesChannels = collect();
        $this->warehouses = collect();
        $this->vatNumbers = collect();
        $this->cities = collect();
        $this->arrivalPostalCodes = collect();

        $this->salesChannelsDb = [];
        $this->vatNumbersDb = collect();
        $this->citiesDb = collect();
        $this->arrivalPostalCodesDb = collect();

        $this->resolveRepos();
        $this->loadInitDBData();
    }

    /**
     *
     * @throws BindingResolutionException
     */
    private function resolveRepos(): void
    {
        $this->countryRepo = app()->make(CountryRepositoryContract::class);
        $this->currencyRepo = app()->make(CurrencyRepositoryContract::class);
        $this->amazonSalesChannelRepo = app()->make(AmazonSalesChannelRepositoryContract::class);
        $this->marketplaceRepo = app()->make(MarketplacesRepositoryContract::class);
        $this->programTypeRepo = app()->make(ProgramTypeRepositoryContract::class);
        $this->eventTypeRepo = app()->make(EventTypeRepositoryContract::class);
        $this->itemRepo = app()->make(ItemRepositoryContract::class);
        $this->deliveryConditionRepo = app()->make(DeliveryConditionRepositoryContract::class);
        $this->taxReportingSchemeRepo = app()->make(TaxReportingSchemeRepositoryContract::class);
        $this->taxCollectionResponsibilityRepo = app()->make(TaxCollectionResponsibilityRepositoryContract::class);
    }

    /**
     * @param string $code
     * @return Country|null
     */
    public function getCountryByCode(string $code): ?Country
    {
        return $this->countriesByCode->get($code);
    }

    /**
     * @param int $id
     * @return Country|null
     */
    public function getCountryById(int $id): ?Country
    {
        return $this->countriesById->get($id);
    }

    /**
     * @param string $code
     * @return Currency|null
     */
    public function getCurrencyByCode(string $code): ?Currency
    {
        return $this->currenciesByCode->get($code);
    }

    /**
     * @param int $id
     * @return Currency|null
     * @noinspection PhpUnused
     */
    public function getCurrencyById(int $id): ?Currency
    {
        return $this->currenciesById->get($id);
    }

    /**
     * @param string $code
     * @return AmazonSalesChannel|null
     */
    public function getAmazonSalesChannelByCode(string $code): ?AmazonSalesChannel
    {
        return $this->amazonSalesChannels->get($code);
    }

    /**
     * @param string $name
     * @return Marketplace|null
     */
    public function getMarketplaceByName(string $name): ?Marketplace
    {
        return $this->marketplacesByName->get($name);
    }

    /**
     * @param int $id
     * @return Marketplace|null
     * @noinspection PhpUnused
     */
    public function getMarketplaceById(int $id): ?Marketplace
    {
        return $this->marketplacesById->get($id);
    }

    /**
     * @param string $value
     * @return ProgramType|null
     */
    public function getProgramTypeByValue(string $value): ?ProgramType
    {
        return $this->programTypes->get($value);
    }

    /**
     * @param string $amazonCode
     * @return EventType|null
     */
    public function getEventTypeByAmazonCode(string $amazonCode): ?EventType
    {
        return $this->eventTypes->get($amazonCode);
    }

    public function getItemTaxCodeByCode(string $code): ItemTaxCode
    {
        $itemTaxCode = $this->itemTaxCodes->get($code);
        if (is_null($itemTaxCode)) {
            $itemTaxCode = $this->createItemTaxCode($code);
        }

        return $itemTaxCode;
    }


    private function createItemTaxCode(string $code): ItemTaxCode
    {
        $itemTaxCode = new ItemTaxCode();

        $itemTaxCode->item_type_id = ItemType::GOODS;
        $itemTaxCode->taxation_method_id = TaxationMethod::GENERAL;
        $itemTaxCode->description = 'Do not use! Manually added tax code.';
        $itemTaxCode->code = $code;
        $itemTaxCode->item_code_category_id = ItemCodeCategory::GENERAL;

        $itemTaxCode->save();

        return $itemTaxCode;
    }

    /**
     * @param string $code
     * @return DeliveryCondition|null
     */
    public function getDeliveryConditionByCode(string $code): ?DeliveryCondition
    {
        return $this->deliveryConditions->get($code);
    }

    /**
     * @param string $amazonCode
     * @return TaxReportingScheme
     */
    public function getTaxReportingSchemeByAmazonCode(string $amazonCode): TaxReportingScheme
    {
        return $this->taxReportingSchemes->get($amazonCode);
    }

    /**
     * @param string $amazonCode
     * @return TaxCollectionResponsibility|null
     */
    public function getTaxCollectionResponsibilityByAmazonCode(string $amazonCode): ?TaxCollectionResponsibility
    {
        return $this->taxCollectionResponsibilities->get($amazonCode);
    }

    /**
     * Loads low impact DB data
     */
    private function loadInitDBData(): void
    {
        $countries = $this->countryRepo->getAllCountries();
        $this->countriesByCode = $countries->keyBy('code');
        $this->countriesById = $countries->keyBy('id');

        $currencies = $this->currencyRepo->getAllCurrencies();
        $this->currenciesByCode = $currencies->keyBy('code');
//        $this->currenciesById = $currencies->keyBy('id');

        $this->amazonSalesChannels = $this->amazonSalesChannelRepo
            ->getAll()
            ->keyBy('code');

        $marketplaces = $this->marketplaceRepo->getAllForAmazon();
        $this->marketplacesByName = $marketplaces
            ->keyBy(function (Marketplace $marketplace) {
                return mb_strtolower($marketplace->name);
            });
//        $this->marketplacesById = $marketplaces->keyBy('id');

        $this->programTypes = $this->programTypeRepo
            ->getAll()
            ->keyBy('value');

        $this->eventTypes = $this->eventTypeRepo
            ->getAllForAmazon()
            ->keyBy('amazon_code');

        $this->itemTaxCodes = $this->itemRepo
            ->getAllItemTaxCodes()
            ->keyBy('code');

        $this->deliveryConditions = $this->deliveryConditionRepo
            ->getAll()
            ->keyBy('code');

        $this->taxReportingSchemes = $this->taxReportingSchemeRepo
            ->getAllForAmazon()
            ->keyBy('amazon_code');

        $this->taxCollectionResponsibilities = $this->taxCollectionResponsibilityRepo
            ->getAllForAmazon()
            ->keyBy('amazon_code');
    }

    /**
     * @param string $salesChannelUID
     */
    public function addSalesChannelForLoad(string $salesChannelUID): void
    {
        if ($this->salesChannels->has($salesChannelUID)) {
            return;
        }

        $this->salesChannels->put($salesChannelUID, $salesChannelUID);
    }

    /**
     * @param string $city
     * @param int $countryId
     */
    public function addCityForLoad(string $city, int $countryId): void
    {
        $encoding = mb_detect_encoding($city);
        if ($encoding !== 'UTF-8') {
            $city = Encoding::fixUTF8($city);
        }
        $city = mb_strtoupper($city);
        $key = Str::toBase64($city);

        if (!$this->cities->has($key)) {
            $data = [
                'city'      => $city,
                'countryId' => $countryId
            ];
            $this->cities->put($key, $data);
        }
    }

    /**
     * @param int $eventTypeId
     * @param int $amazonSalesChannelId
     * @param string $departurePostCode
     * @param string $salesChannelId
     * @param string $departureCityId
     * @param int $departureCountryId
     * @param string $transactionCompleteDate
     */
    public function addWarehouseForLoad(
        int $eventTypeId,
        int $amazonSalesChannelId,
        string $departurePostCode,
        string $salesChannelId,
        string $departureCityId,
        int $departureCountryId,
        string $transactionCompleteDate
    ): void {
        $departurePostCode = mb_strtoupper($departurePostCode);

        if ($this->warehouses->has($departurePostCode)) {
            return;
        }

        $skipEventTypes = [
            EventType::RETURN,
            EventType::COMMINGLING_BUY,
            EventType::COMMINGLING_SELL
        ];

        if (in_array($eventTypeId, $skipEventTypes)) {
            return;
        }

        $warehouse = [
            'salesChannelId'          => $salesChannelId,
            'amazonSalesChannelId'    => $amazonSalesChannelId,
            'departurePostCode'       => $departurePostCode,
            'departureCityId'         => $departureCityId,
            'departureCountryId'      => $departureCountryId,
            'transactionCompleteDate' => $transactionCompleteDate,
        ];

        $departurePostCode = str_replace(' ', '', $departurePostCode);
        $this->warehouses->put($departurePostCode, $warehouse);
    }

    /**
     * @param int $eventTypeId
     * @param string $transactionCompleteDate
     * @param string $salesChannel
     * @param string|null $transactionSellerVatNumber
     * @param int|null $transactionSellerVatNumberCountryId
     * @param string|null $buyerVatNumber
     * @param int|null $buyerVatNumberCountryId
     * @param string|null $sellerDepartCountryVatNumber
     * @param int|null $sellerDepartVatNumberCountryId
     * @param string|null $sellerArrivalCountryVatNumber
     * @param int|null $sellerArrivalVatNumberCountryId
     */
    public function addSellerVatNumbersForLoad(
        int $eventTypeId,
        string $transactionCompleteDate,
        string $salesChannel,
        ?string $transactionSellerVatNumber = null,
        ?int $transactionSellerVatNumberCountryId = null,
        ?string $buyerVatNumber = null,
        ?int $buyerVatNumberCountryId = null,
        ?string $sellerDepartCountryVatNumber = null,
        ?int $sellerDepartVatNumberCountryId = null,
        ?string $sellerArrivalCountryVatNumber = null,
        ?int $sellerArrivalVatNumberCountryId = null
    ): void {
        /**
         * Ako je event type id COMMINGLING_BUY
         * transactionSellerVatNumber --> je zapravo buyerVatNumber
         * transactionSellerVatNumberCountryId --> je zapravo buyerVatNumberCountryId
         */
        if ($eventTypeId === EventType::COMMINGLING_BUY) {
            $transactionSellerVatNumber = $buyerVatNumber;
            $transactionSellerVatNumberCountryId = $buyerVatNumberCountryId;
        }

        if (!is_null($transactionSellerVatNumber) && !is_null($transactionSellerVatNumberCountryId)) {
            $vatNumber = [
                'vatNumber'         => $transactionSellerVatNumber,
                'salesChannel'      => $salesChannel,
                'countryId'         => $transactionSellerVatNumberCountryId,
                'firstTimeInReport' => $transactionCompleteDate
            ];
            $this->addVatNumber($vatNumber);
        }

        /**
         * ako je COMMINGLING_BUY ignore seller i arrival country vat no
         */
        if ($eventTypeId !== EventType::COMMINGLING_BUY) {
            if (!is_null($sellerDepartCountryVatNumber) && !is_null($sellerDepartVatNumberCountryId)) {
                $vatNumber = [
                    'vatNumber'         => $sellerDepartCountryVatNumber,
                    'salesChannel'      => $salesChannel,
                    'countryId'         => $sellerDepartVatNumberCountryId,
                    'firstTimeInReport' => $transactionCompleteDate
                ];
                $this->addVatNumber($vatNumber);
            }

            if (!is_null($sellerArrivalCountryVatNumber) && !is_null($sellerArrivalVatNumberCountryId)) {
                $vatNumber = [
                    'vatNumber'         => $sellerArrivalCountryVatNumber,
                    'salesChannel'      => $salesChannel,
                    'countryId'         => $sellerArrivalVatNumberCountryId,
                    'firstTimeInReport' => $transactionCompleteDate
                ];
                $this->addVatNumber($vatNumber);
            }
        }
    }

    private function addVatNumber(array $vatNumber): void
    {
        $number = preg_replace('/\s+/', '', $vatNumber['vatNumber']);
        $vatNumber['vatNumber'] = $number;
        if (!$this->vatNumbers->has($vatNumber)) {
            $this->vatNumbers->put($number, $vatNumber);
        }
    }

    /**
     * @param string $arrivalPostCode
     * @param int $arrivalCountryId
     */
    public function addArrivalPostalCodeForLoad(string $arrivalPostCode, int $arrivalCountryId): void
    {
        $arrivalPostCode = trim($arrivalPostCode);

        /**
         * @var Collection $codes
         */
        $codes = $this->arrivalPostalCodes->get($arrivalCountryId, collect());
        $codes->put($arrivalPostCode, $arrivalPostCode);

        $this->arrivalPostalCodes->put($arrivalCountryId, $codes);
    }

    /**
     * Loads data from db + cleanup
     */
    public function resolve(): void
    {
        $this->resolveCities();
        $this->resolveDbSalesChannels();
        $this->resolveWarehouses();
        $this->resolveVatNumbers();
        $this->resolvePostalCodesExceptions();
    }

    /** @noinspection PhpVariableIsUsedOnlyInClosureInspection */
    private function resolveVatNumbers(): void
    {
        $skipVatNumbers = [
            'LU20260743',
            'LU20944528',
            'LU19647148',
            'DE814584193',
            'FR12487773327',
            '*************',
            'ESW0184081H',
            'NL815158464',
            'PL5262907815',
            'SE516412220101',
            'GB727255821',
        ];

        $vatNumbers = $this->vatNumbers
            ->filter(function (array $vatNumber) use ($skipVatNumbers) {
                $number = $vatNumber['vatNumber'];
                if (in_array($number, $skipVatNumbers)) {
                    return false;
                }

                /**
                 * @var Country $country
                 */
                $code = $this->countriesById->get($vatNumber['countryId'] ?? 0)?->vat_code;
                if (is_null($code)) {
                    return false;
                }

                return Str::startsWith($number, $code);
            })->map(function (array $vatNumber) {
                $salesChannelId = null;
                $companyId = null;
                $salesChannel = null;
                try {
                    $salesChannel = $this->getSalesChannel($vatNumber['salesChannel']);
                } catch (Throwable) {
                }
                if (!is_null($salesChannel)) {
                    $salesChannelId = $salesChannel['id'];
                    $companyId = $salesChannel['company_id'];
                }

                $vatNumber['companyId'] = $companyId;
                $vatNumber['salesChannelId'] = $salesChannelId;
                $vatNumber['vatNumber'] = preg_replace('/\s+/', '', $vatNumber['vatNumber']);

                return $vatNumber;
            })->filter(function ($vatNumber) {
                return !is_null($vatNumber['companyId']);
            });

        $vatNumbersNumbers = $vatNumbers->keys()->toArray();

        $companiesIds = $vatNumbers->keyBy('companyId')->keys()->toArray();
        $countriesIds = $vatNumbers->keyBy('countryId')->keys()->toArray();
        /**
         * @var Collection $vatNumbersDb
         */
        $vatNumbersDb = VatNumber::whereIn('company_id', $companiesIds)
            ->whereIn('country_id', $countriesIds)
            ->get()
            ->groupBy(function (VatNumber $vatNumber) {
                return $vatNumber->company_id . '-' . $vatNumber->country_id;
            })->map(function (Collection $vatNumbers) {
                return $vatNumbers->keyBy('vat_number');
            });

        foreach ($vatNumbers as $vatNumber) {
            $companyId = $vatNumber['companyId'];
            $countryId = $vatNumber['countryId'];
            $number = $vatNumber['vatNumber'];
            $firstTimeInReport = Carbon::parse($vatNumber['firstTimeInReport']);
            $key = $companyId . '-' . $countryId;

            $newVatNumberDb = new VatNumber();

            /**
             * @var VatNumber|null $companyCountryVatNumberDb
             */
            $companyCountryVatNumberDb = $vatNumbersDb->get($key, collect())->get($number);
            if (!is_null($companyCountryVatNumberDb)) {
                $companyCountryVatNumberDbDate = Carbon::parse($companyCountryVatNumberDb->calculated_register_date);
                if ($companyCountryVatNumberDbDate->lt($firstTimeInReport)) {
                    continue;
                }

                $newVatNumberDb = $companyCountryVatNumberDb;
            }

            $newVatNumberDb->country_id = $countryId;
            $newVatNumberDb->vat_number = $number;
            $newVatNumberDb->company_id = $companyId;
            $newVatNumberDb->first_time_in_report = $firstTimeInReport->toDateString();
            $newVatNumberDb->register_date = $firstTimeInReport->toDateString();
            $newVatNumberDb->guessed = true;

            try {
                $newVatNumberDb->save();
            } catch (Throwable) {
            }
        }

        $this->vatNumbersDb = VatNumber::whereIn('vat_number', $vatNumbersNumbers)
            ->get()
            ->keyBy('vat_number');
    }

    private function resolvePostalCodesExceptions(): void
    {
        $postalCodesPerCountry = $this->arrivalPostalCodes;
        $countriesIds = $postalCodesPerCountry->keys()->toArray();
        /**
         * @var Collection $postalCodesExceptions
         */
        $postalCodesExceptionsPerCountry = PostalCodeException::whereIn('postal_code_country_id', $countriesIds)
            ->get()
            ->load('vatCountry')
            ->groupBy('postal_code_country_id');

        $postalCodesPerCountry = $postalCodesPerCountry->map(function (Collection $postalCodes, int $countryId) use ($postalCodesExceptionsPerCountry) {
            /**
             * @var Collection $postalCodesExceptions
             */
            $postalCodesExceptions = $postalCodesExceptionsPerCountry->get($countryId, collect());

            return $postalCodes->map(function (string $postalCode) use ($postalCodesExceptions) {
                return $postalCodesExceptions->filter(function (PostalCodeException $postalCodeException) use ($postalCode) {
                    return Str::startsWith($postalCode, $postalCodeException->postal_code_start_with);
                })->first();
            })->filter(function (?PostalCodeException $postalCodeException) {
                return !is_null($postalCodeException);
            });
        })->filter(function (Collection $postalCodeExceptions) {
            return $postalCodeExceptions->count() > 0;
        });

        $this->arrivalPostalCodesDb = $postalCodesPerCountry;
    }

    private function resolveWarehouses(): void
    {
        $warehousesData = $this->warehouses;
        $postalCodes = $warehousesData->pluck('departurePostCode')
            ->toArray();

        $companiesIds = [];
        foreach ($this->salesChannelsDb as $salesChannelDb) {
            if (!in_array($salesChannelDb['company_id'], $companiesIds)) {
                $companiesIds[] = $salesChannelDb['company_id'];
            }
        }
        /**
         * @var Collection|Warehouse[] $warehouses
         */
        $warehouses = $this->getWarehouses($postalCodes, $companiesIds);

        $missing = collect();
        $rawCompanyWarehouses = collect();
        foreach ($warehousesData as $key => $warehouseData) {
            try {
                $salesChannel = $this->getSalesChannel($warehouseData['salesChannelId']);
            } catch (Throwable) {
                continue;
            }

            $companyId = $salesChannel['company_id'];
            $postalCode = $warehouseData['departurePostCode'];
            $rawKey = $companyId . '-' . Str::toBase64($postalCode);
            if (!$rawCompanyWarehouses->has($rawKey)) {
                $rawCompanyWarehouses->put($rawKey, [
                        'company_id'      => $companyId,
                        'warehouse_id'    => $postalCode,
                        'first_time_used' => $warehouseData['transactionCompleteDate'],
                    ]
                );
            }

            if ($warehouses->has($key) || $missing->has($key)) {
                continue;
            }

            $global = $warehouseData['amazonSalesChannelId'] === AmazonSalesChannel::AFN;
            $confirmed = false;
            $source = 'S';
            $uid = $warehouseData['departurePostCode'];
            $name = 'Amazon FC ' . $uid;
            $warehouseOperator = WarehouseOperator::OPERATOR_AMAZON;

            if (!$global) {
                $companyName = $salesChannel['company']['full_legal_name'];
                $warehouseOperator = WarehouseOperator::where('name', $companyName)->first();
                if (is_null($warehouseOperator)) {
                    $warehouseOperator = new WarehouseOperator();
                    $warehouseOperator->name = $companyName;
                    $warehouseOperator->save();
                }

                $name = $companyName . ' ' . $uid;
                $warehouseOperator = $warehouseOperator->id;
                $source = 'U';
                $confirmed = true;
            }

            $cityName = $warehouseData['departureCityId'];
            $encoding = mb_detect_encoding($cityName);
            if ($encoding !== 'UTF-8') {
                $cityName = Encoding::fixUTF8($cityName);
            }
            $cityName = Str::toBase64($cityName);
            $cityId = $this->citiesDb->get($cityName)->id ?? null;

            $insertData = [
                'uid'                   => $uid,
                'name'                  => $name,
                'country_id'            => $warehouseData['departureCountryId'],
                'warehouse_operator_id' => $warehouseOperator,
                'city_id'               => $cityId,
                'global'                => $global,
                'postal_code'           => $warehouseData['departurePostCode'],
                'platform_id'           => Platform::PLATFORM_AMAZON,
                'source'                => $source,
                'confirmed'             => $confirmed,
            ];
            $missing->push($insertData);
        }

        if (count($missing) > 0) {
            Warehouse::insert($missing->toArray());
        }

        /**
         * @var Collection|Warehouse[] $warehouses
         */
        $warehouses = $this->getWarehouses($postalCodes, $companiesIds);
        $existingCompanyWarehouses = $this->getExistingCompanyWarehouses();

        $insertCompanyWarehouses = collect();
        foreach ($rawCompanyWarehouses as $rawKey => $rawCompanyWarehouse) {
            if ($existingCompanyWarehouses->has($rawKey)) {
                continue;
            }

            $postalCodeUppercaseNoBlanks = $rawCompanyWarehouse['warehouse_id'] ?? '';
            $postalCodeUppercaseNoBlanks = mb_strtoupper($postalCodeUppercaseNoBlanks);
            $postalCodeUppercaseNoBlanks = preg_replace('/\s+/', '', $postalCodeUppercaseNoBlanks);

            $dbWarehouse = $warehouses->get($postalCodeUppercaseNoBlanks);
            if (is_null($dbWarehouse)) {
                continue;
            }

            $rawCompanyWarehouse['warehouse_id'] = $dbWarehouse->id;
            $insertCompanyWarehouses->put($rawKey, $rawCompanyWarehouse);
        }

        if ($insertCompanyWarehouses->count() > 0) {
            CompanyWarehouse::insert($insertCompanyWarehouses->toArray());
        }

        $this->resolveDbSalesChannels(true);
    }

    /**
     * @return Collection
     */
    private function getExistingCompanyWarehouses(): Collection
    {
        /**
         * @var Collection|SalesChannel[] $salesChannels
         */
        $salesChannels = $this->salesChannelsDb;
        $existingCompanyWarehouses = collect();
        foreach ($salesChannels as $salesChannel) {
            $warehouses = $salesChannel['company']['company_warehouses'];
            foreach ($warehouses as $warehouse) {
                $companyId = $warehouse['company_id'];
                $postalCode = $warehouse['warehouse']['search_postal_code_uppercase'];
                if (is_null($postalCode)) {
                    continue;
                }
                $key = $companyId . '-' . Str::toBase64($postalCode);
                $existingCompanyWarehouses->put($key, $warehouse);
            }
        }

        return $existingCompanyWarehouses;
    }

    /**
     * @param array $postalCodes
     * @param array $companiesIds
     * @return Collection
     */
    private function getWarehouses(array $postalCodes, array $companiesIds): Collection
    {
        $columns = [
            'company_warehouses.id as company_warehouse_id',
            'company_warehouses.company_id as company_id',
            'warehouses.*'
        ];

        return Warehouse::select($columns)
            ->leftJoin('company_warehouses', 'warehouses.id', '=', 'company_warehouses.warehouse_id')
            ->whereIn('warehouses.postal_code', $postalCodes)
            ->where(function (Builder $query) use ($companiesIds) {
                $query->whereNull('company_warehouses.company_id')
                    ->orWhereIn('company_warehouses.company_id', $companiesIds);
            })
            ->get()
            ->keyBy('search_postal_code_uppercase_no_blanks');
    }

    private function resolveCities(): void
    {
        $citiesData = $this->cities;
        $citesNames = $citiesData->pluck('city')->toArray();

        /**
         * @var Collection|City[] $citiesDb
         */
        $citiesDb = City::whereIn('name', $citesNames)
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy(function (City $city) {
                return Str::toBase64($city->name);
            });

        $missing = collect();
        foreach ($citiesData as $key => $cityData) {
            /**
             * @var Collection $sameNameCitiesDb
             */
            $sameNameCitiesDb = $citiesDb->get($key);
            if (!is_null($sameNameCitiesDb)) {
                $sameNameCitiesDb = $sameNameCitiesDb->filter(function (City $city) use ($cityData) {
                    return $city->country_id === $cityData['countryId'];
                });

                if ($sameNameCitiesDb->count() > 0) {
                    continue;
                }
            }
            $data = [
                'country_id' => $cityData['countryId'],
                'name'       => $cityData['city'],
            ];
            $missing->push($data);
        }

        if (count($missing) > 0) {
            City::insert($missing->toArray());
        }

        $citiesDbData = City::whereIn('name', $citesNames)
            ->orderBy('id', 'DESC')
            ->get()
            ->groupBy(function (City $city) {
                return Str::toBase64($city->name);
            });

        $citiesDb = collect();
        foreach ($citiesDbData as $key => $sameNameCities) {
            /**
             * @var Collection|City[] $sameNameCities
             */
            $city = $citiesData [$key];
            foreach ($sameNameCities as $cityDb) {
                if ($city['countryId'] === $cityDb->country_id) {
                    if (!$citiesDb->has($key)) {
                        $citiesDb->put($key, $cityDb);
                    }
                }
            }
        }

        $this->citiesDb = $citiesDb;
    }

    /**
     * @param bool $load
     */
    private function resolveDbSalesChannels(bool $load = false): void
    {
        $salesChannelsUAIDs = $this->salesChannels
            ->keys()
            ->toArray();

        $salesChannelsDb = SalesChannel::whereIn('uaid', $salesChannelsUAIDs)
            ->get()
            ->load(
                'company.companyWarehouses.warehouse',
            );

        if ($load) {
            $salesChannelsDb->load(
                'company.address.city',
                'company.address.country',
                'company.thresholds',
                'company.waivers',
                'company.companyWarehouses.warehouse',
                'company.vatNumbers',
                'company.euThresholds',
                'company.ossNumbers',
                'company.iossNumbers',
            );
        }

        $this->salesChannelsDb = $salesChannelsDb->keyBy('uaid')->toArray();
    }

    public function toArray(): array
    {
        return [
            'salesChannels' => $this->salesChannels->toArray(),
            'warehouses'    => $this->warehouses->toArray(),
            'vatNumbers'    => $this->vatNumbers->toArray(),
        ];
    }

    /**
     * @param string $uaid
     * @return array
     * @throws Exception
     */
    public function getSalesChannel(string $uaid): array
    {
        $salesChannel = $this->salesChannelsDb[$uaid] ?? null;
        if (is_null($salesChannel)) {
            throw new Exception('amazon.report-parsing-error-reason.Sales channel not found');
        }

        return $salesChannel;
    }

    /**
     * @param string $city
     * @return City
     * @throws Exception
     */
    public function getCity(string $city): City
    {
        $city = mb_strtolower($city);
        $city = Str::toBase64($city);
        $city = $this->citiesDb->get($city);
        if (is_null($city)) {
            throw new Exception('amazon.report-parsing-error-reason.City not found');
        }

        return $city;
    }

    /**
     * @param string $vatNumberStr
     * @return VatNumber
     * @throws Exception
     */
    public function getVatNumber(string $vatNumberStr): VatNumber
    {
        $vatNumber = trim($vatNumberStr);
        $vatNumber = $this->vatNumbersDb->get($vatNumber);
        if (is_null($vatNumber)) {
            throw new Exception('');
        }

        return $vatNumber;
    }

    public function getPostalCodeException(string $arrivalPostalCode, int $arrivalCountryId): ?PostalCodeException
    {
        $arrivalPostalCode = trim($arrivalPostalCode);

        /**
         * @var PostalCodeException $postalCodeException
         */
        $postalCodeException = $this->arrivalPostalCodesDb
            ->get($arrivalCountryId, collect())
            ->get($arrivalPostalCode);

        if (is_null($postalCodeException) || is_null($postalCodeException->vat_country_id)) {
            return null;
        }

        return $postalCodeException;
    }
}
