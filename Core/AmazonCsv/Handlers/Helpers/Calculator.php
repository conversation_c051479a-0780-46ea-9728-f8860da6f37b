<?php

namespace App\Core\AmazonCsv\Handlers\Helpers;

class Calculator
{
    /**
     * @param float $gross
     * @param float $percentage
     * @return float
     */
    public static function grossToNet(float $gross, float $percentage): float
    {
        $gross = self::abs($gross);
        $percentage = self::abs($percentage);

        $floatPercentage = (100 + $percentage) / 100;

        return $gross / $floatPercentage;
    }

    /**
     * @param float $gross
     * @param float $percentage
     * @return float
     */
    public static function vatAmountFromGross(float $gross, float $percentage): float
    {
        $gross = self::abs($gross);
        $percentage = self::abs($percentage);
        $net = self::grossToNet($gross, $percentage);
        $net = round($net, 8);

        return $gross - $net;
    }


    /**
     * @param float $net
     * @param float $percentage
     * @return float
     */
    public static function netToGross(float $net, float $percentage): float
    {
        $net = self::abs($net);
        $percentage = self::abs($percentage);

        $floatPercentage = ($percentage / 100);

        return ($net + ($net * $floatPercentage));
    }

    /**
     * @param float $val
     * @return float
     */
    public static function abs(float $val): float
    {
        return $val < 0 ? ($val * -1) : $val;
    }
}
