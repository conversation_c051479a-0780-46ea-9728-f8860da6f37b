<?php

namespace App\Core\AmazonCsv\Handlers\Helpers;

use App\Core\Data\Models\EventType;
use App\Core\Data\Models\OssNumberType;
use App\Core\Data\Models\PlatformType;
use App\Core\Invoices\Invoice\VatDetection;
use Illuminate\Support\Collection;

class RawData2DetectionPropertiesExtractor
{
    private Collection $data;

    public function __construct(array $data)
    {
        $this->data = collect($data);
    }

    public function getEventTypeId(): int
    {
        return $this->data->get('event_type_id');
    }

    public function getTaxCalculationDate(): string
    {
        // If B2B there is no tax nor tax calculation date
        $taxCalculationDate = $this->data->get('tax_calculation_date');
        if (is_null($taxCalculationDate)) {
            $taxCalculationDate = $this->data->get('transaction_complete_date');
        }

        return $taxCalculationDate;
    }

    public function getTaxCalculationDateTimestamp(): int
    {
        return strtotime($this->getTaxCalculationDate());
    }

    public function getCompany(): array
    {
        return $this->data->get('sales_channel_data')['company'];
    }

    public function getMarketplace(): ?array
    {
        return $this->data->get('marketplace_data');
    }

    public function getCompanyCountry(): array
    {
        return $this->getCompany()['address']['country'];
    }

    public function getCompanyCountryId(): int
    {
        return $this->getCompanyCountry()['id'];
    }

    public function getShipFromCountryId(): int
    {
        $countryId = $this->data->get('sale_depart_country_id');
        if ($this->getEventTypeId() === EventType::FC_TRANSFER || $this->getEventTypeId() === EventType::DONATION) {
            $countryId = $this->data->get('departure_country_id');
        }

        return $countryId;
    }

    public function getRecipientCountryId(): int
    {
        return $this->getRecipientCountry()['id'];
    }

    public function getRecipientCountry(): array
    {
        $country = $this->data->get('sale_recipient_country_data');
        if ($this->getEventTypeId() === EventType::FC_TRANSFER || $this->getEventTypeId() === EventType::DONATION) {
            $country = $this->data->get('recipient_country_data');
        }

        return $country;
    }

    /** @noinspection PhpUnused */
    public function getArrivalCountryId(): int
    {
        return $this->getRecipientCountry()['id'];
    }

    /** @noinspection PhpUnused */
    public function getArrivalCountry(): array
    {
        $country = $this->data->get('sale_arrival_country_data');
        if ($this->getEventTypeId() === EventType::FC_TRANSFER) {
            $country = $this->data->get('arrival_country_data');
        }

        return $country;
    }

    public function isCompanyCountryInEU(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $inEuStartDate = $this->getCompanyCountry()['in_eu_start_date'];
        if (is_null($inEuStartDate)) {
            return false;
        }
        $inEuEndDate = $this->getCompanyCountry()['in_eu_end_date'];
        $inEuStartDate = strtotime($inEuStartDate);
        $isCompanyCountryInEu = $taxCalculationDate >= $inEuStartDate;

        if (!is_null($inEuEndDate)) {
            $inEuEndDate = strtotime($inEuEndDate);
            $isCompanyCountryInEu = $isCompanyCountryInEu && ($taxCalculationDate < $inEuEndDate);
        }

        return $isCompanyCountryInEu;
    }

    public function isRecipientCountryInEu(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $country = $this->getRecipientCountry();

        $inEuStartDate = $country['in_eu_start_date'];
        $inEuEndDate = $country['in_eu_end_date'];
        if (is_null($inEuStartDate)) {
            return false;
        }
        $inEuStartDate = strtotime($inEuStartDate);
        $isInEu = $taxCalculationDate >= $inEuStartDate;
        if (!is_null($inEuEndDate)) {
            $inEuEndDate = strtotime($inEuEndDate);
            $isInEu = $isInEu && ($taxCalculationDate < $inEuEndDate);
        }

        return $isInEu;
    }

    public function isBuyerVatNumberValid(): bool
    {
        $buyerVatNumber = $this->data->get('buyer_vat_number');
        if (is_null($buyerVatNumber)) {
            return false;
        }

        if ($this->data->get('seller_depart_vat_number_country_id') === $this->data->get('seller_arrival_vat_number_country_id')) {
            return true;
        }

        $rate = $this->data->get('price_of_items_vat_rate_percent', 0.0);

        return $rate === 0.0;
    }

    public function isShipFromCountryInEu(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $country = $this->data->get('sale_depart_country_data');
        if ($this->getEventTypeId() === EventType::FC_TRANSFER || $this->getEventTypeId() === EventType::DONATION) {
            $country = $this->data->get('departure_country_data');
        }

        $inEuStartDate = $country['in_eu_start_date'];
        $inEuEndDate = $country['in_eu_end_date'];
        if (is_null($inEuStartDate)) {
            return false;
        }
        $inEuStartDate = strtotime($inEuStartDate);
        $isInEu = $taxCalculationDate >= $inEuStartDate;
        if (!is_null($inEuEndDate)) {
            $inEuEndDate = strtotime($inEuEndDate);
            $isInEu = $isInEu && ($taxCalculationDate < $inEuEndDate);
        }

        return $isInEu;
    }

    public function isDomesticReverseChargeApplicableInRecipientCountry(): bool
    {
        $domesticReverseChargeApplicableFrom = $this->getRecipientCountry()['domestic_reverse_charge_applicable_from'] ?? null;
        if (is_null($domesticReverseChargeApplicableFrom)) {
            return false;
        }

        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $domesticReverseChargeApplicableFrom = strtotime($domesticReverseChargeApplicableFrom);

        return $taxCalculationDate >= $domesticReverseChargeApplicableFrom;
    }

    public function getPlatformType(): int
    {
        return PlatformType::MARKETPLACE;
    }

    public function getInvoiceTypeId(): int
    {
        return $this->data->get('event_type_data')['invoice_type_id'];
    }

    public function whetherTheCompanyHasPassedThreshold(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $breakpoint = strtotime('2021-07-01');
        $recipientCountry = $this->getRecipientCountry();

        $thresholds = $this->getCompany()['thresholds'];
        if ($taxCalculationDate >= $breakpoint) {
            $thresholds = $this->getCompany()['eu_thresholds'];
        }
        $thresholds = collect($thresholds);

        return (new VatDetection())
            ->whetherTheCompanyHasPassedThreshold(
                $taxCalculationDate,
                $breakpoint,
                $thresholds,
                $recipientCountry
            );
    }

    public function isCompanyRegisteredForOSS(): bool
    {
        return $this->getOSSNumbersOnCalculationDate()->count() > 0;
    }

    public function getCompanyOSSRegistrationCountryId(): ?int
    {
        $oss = $this->getOSSNumbersOnCalculationDate()->first();
        if (is_null($oss)) {
            return null;
        }

        return $oss['issue_country_id'];
    }

    /**
     * @return Collection
     */
    private function getOSSNumbersOnCalculationDate(): Collection
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();


        return collect($this->getCompany()['oss_numbers'])
            ->filter(function ($oss) use ($taxCalculationDate) {
                if ((int)$oss['type_id'] !== OssNumberType::UNION_SCHEME) {
                    return false;
                }
                $registerDate = strtotime($oss['register_date']);
                $endDate = $oss['end_date'];
                $check = $taxCalculationDate >= $registerDate;
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                    $check = $check && ($taxCalculationDate < $endDate);
                }

                return $check;
            });
    }

    public function isMarketplaceRegisteredForOSS(): bool
    {
        if ($this->getEventTypeId() === EventType::FC_TRANSFER) {
            // or false, makes no difference
            return true;
        }

        return !is_null($this->getMarketplace()['oss_country_id']);
    }

    public function getMarketplaceOSSRegistrationCountryId(): ?int
    {
        $marketplace = $this->getMarketplace();
        if (is_null($marketplace)) {
            return null;
        }

        return $marketplace['oss_country_id'];
    }

    public function isCompanyRegisteredForIOSS(): bool
    {
        return $this->getIOSSNumbersOnCalculationDate()->count() > 0;
    }

    public function getCompanyIOSSRegistrationCountryId(): ?int
    {
        $ioss = $this->getIOSSNumbersOnCalculationDate()->first();
        if (is_null($ioss)) {
            return null;
        }

        return $ioss['issue_country_id'];
    }

    /**
     * @return Collection
     */
    private function getIOSSNumbersOnCalculationDate(): Collection
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();

        return collect($this->getCompany()['ioss_numbers'])
            ->filter(function ($ioss) use ($taxCalculationDate) {
                $registerDate = strtotime($ioss['register_date']);
                $endDate = $ioss['end_date'];
                $check = $taxCalculationDate >= $registerDate;
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                    $check = $check && ($taxCalculationDate < $endDate);
                }

                return $check;
            });
    }

    public function isMarketplaceRegisteredForIOSS(): bool
    {
        if ($this->getEventTypeId() == EventType::FC_TRANSFER) {
            // or false, makes no difference
            return true;
        }

        return !is_null($this->getMarketplace()['ioss_country_id']);
    }

    public function getMarketplaceIOSSRegistrationCountryId(): ?int
    {
        $marketplace = $this->getMarketplace();
        if (is_null($marketplace)) {
            return null;
        }

        return $this->getMarketplace()['ioss_country_id'];
    }

    public function isMarketplaceIncorporatedInEu(): bool
    {
        if ($this->getEventTypeId() == EventType::FC_TRANSFER) {
            // or false, makes no difference
            return true;
        }

        return $this->getMarketplace()['is_marketplace_incorporated_in_eu'] ?? false;
    }

    public function getBuyerVatNumberCountryId(): ?int
    {
        if ($this->getEventTypeId() === EventType::FC_TRANSFER) {
            return $this->data->get('arrival_country_id');
        }

        $buyerVatNumberCountryId = $this->data->get('buyer_vat_number_country_id');
        $buyerVatNumber = $this->data->get('buyer_vat_number');
        if (is_null($buyerVatNumber)) {
            return null;
        }

        return $buyerVatNumberCountryId;
    }

    public function isOnlyVatNumberAndWarehouseInCompanyCountry(): bool
    {
        $company = $this->getCompany();
        $companyCountryId = $this->getCompanyCountryId();
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $warehouses = collect($company['companyWarehouses'] ?? []);
        $vatNumbers = collect($company['vat_numbers']);

        return (new VatDetection())
            ->isOnlyVatNumberAndWarehouseInCompanyCountry(
                $taxCalculationDate,
                $companyCountryId,
                $vatNumbers,
                $warehouses
            );
    }

    public function hasWaiverInRecipientCountry(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $recipientCountryId = $this->getRecipientCountry()['id'];
        $waivers = collect($this->getCompany()['waivers'] ?? []);

        return (new VatDetection())
            ->hasWaiverInRecipientCountry(
                $taxCalculationDate,
                $recipientCountryId,
                $waivers
            );
    }

    public function hasVatNumberInRecipientCountry(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $recipientCountryId = $this->getRecipientCountry()['id'];
        $vatNumbers = collect($this->getCompany()['vat_numbers'] ?? []);

        return (new VatDetection())
            ->hasVatNumberInRecipientCountry(
                $taxCalculationDate,
                $recipientCountryId,
                $vatNumbers
            );
    }

    public function hasWarehouseInRecipientCountry(): bool
    {
        $taxCalculationDate = $this->getTaxCalculationDateTimestamp();
        $recipientCountryId = $this->getRecipientCountry()['id'];

        $warehouses = $this->getCompany()['companyWarehouses'] ?? [];

        return (new VatDetection())
            ->hasWarehouseInRecipientCountry(
                $taxCalculationDate,
                $recipientCountryId,
                $warehouses
            );
    }

    /**
     * @return array
     */
    public function checkFcTransfer(): array
    {
        if ($this->getEventTypeId() !== EventType::FC_TRANSFER) {
            return [];
        }

        $errors = [];
        $departureCountryId = $this->data->get('departure_country_id');
        $arrivalCountryId = $this->data->get('arrival_country_id');
        $vatNumbersData = $this->getCompany()['vat_numbers'];
        $vatNumbers = [];
        foreach ($vatNumbersData as $vatNumber) {
            $vatNumbers[$vatNumber['country_id']] = $vatNumber;
        }
        $vatNumbersCountriesIds = array_keys($vatNumbers);

        if (!in_array($departureCountryId, $vatNumbersCountriesIds)) {
            $errors[] = $this->createInvoiceMessage('invoice-error.Departure country VAT Number is missing and warehouse in departure country is being used');
        }

        if (!in_array($arrivalCountryId, $vatNumbersCountriesIds)) {
            $errors[] = $this->createInvoiceMessage('invoice-error.Arrival country VAT Number is missing and warehouse in arrival country is being used');
        }

        return $errors;
    }

    /** @noinspection PhpSameParameterValueInspection */
    private function createInvoiceMessage(string $message, ?string $type = null, ?bool $devOnly = null, ?string $ref = null): array
    {
        $type = $type ?? 'warning';
        $devOnly = $devOnly ?? false;

        return [
            'type'    => $type,
            'message' => $message,
            'devOnly' => $devOnly,
            'ref'     => $ref,
        ];
    }

    public function isCompanyCountryVatNumberPermanentEstablishment(): bool
    {
        $companyCountryId = $this->getCompanyCountryId();
        $vatNumber = collect($this->getCompany()['vat_numbers'] ?? [])
            ->groupBy('country_id')
            ->get($companyCountryId, collect())
            ->filter(function ($vatNumber) {
                $date = $this->getTaxCalculationDateTimestamp();
                $startDate = strtotime($vatNumber['calculated_register_date']);
                $endDate = $vatNumber['end_date'];
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                }

                if ($date < $startDate) {
                    return false;
                }

                if (!is_null($endDate) && $date >= $endDate) {
                    return false;
                }

                return true;
            })->first();

        if (is_null($vatNumber)) {
            return true;
        }

        return $vatNumber['permanent_establishment'];
    }

    public function toArray(): array
    {
        return [
            'eventTypeId'                                         => $this->getEventTypeId(),
            'taxCalculationDate'                                  => $this->getTaxCalculationDate(),
            'company'                                             => $this->getCompany(),
            'companyCountryId'                                    => $this->getCompanyCountryId(),
            'shipFromCountryId'                                   => $this->getShipFromCountryId(),
            'recipientCountryId'                                  => $this->getRecipientCountryId(),
            'isCompanyCountryInEU'                                => $this->isCompanyCountryInEU(),
            'isShipFromCountryInEu'                               => $this->isShipFromCountryInEu(),
            'isRecipientCountryInEu'                              => $this->isRecipientCountryInEu(),
            'isDomesticReverseChargeApplicableInRecipientCountry' => $this->isDomesticReverseChargeApplicableInRecipientCountry(),
            'platformType'                                        => $this->getPlatformType(),
            'invoiceTypeId'                                       => $this->getInvoiceTypeId(),
            'whetherTheCompanyHasPassedThreshold'                 => $this->whetherTheCompanyHasPassedThreshold(),
            'isCompanyRegisteredForOSS'                           => $this->isCompanyRegisteredForOSS(),
            'companyOssRegistrationCountryId'                     => $this->getCompanyOSSRegistrationCountryId(),
            'isMarketplaceRegisteredForOSS'                       => $this->isMarketplaceRegisteredForOSS(),
            'marketplaceOSSRegistrationCountryId'                 => $this->getMarketplaceOSSRegistrationCountryId(),
            'isCompanyRegisteredForIOSS'                          => $this->isCompanyRegisteredForIOSS(),
            'companyIOSSRegistrationCountryId'                    => $this->getCompanyIOSSRegistrationCountryId(),
            'isMarketplaceRegisteredForIOSS'                      => $this->isMarketplaceRegisteredForIOSS(),
            'marketplaceIOSSRegistrationCountryId'                => $this->getMarketplaceIOSSRegistrationCountryId(),
            'isMarketplaceIncorporatedInEu'                       => $this->isMarketplaceIncorporatedInEu(),
            'buyerVatNumberCountryId'                             => $this->getBuyerVatNumberCountryId(),
            'isOnlyVatNumberAndWarehouseInCompanyCountry'         => $this->isOnlyVatNumberAndWarehouseInCompanyCountry(),
            'hasWaiverInRecipientCountry'                         => $this->hasWaiverInRecipientCountry(),
            'hasVatNumberInRecipientCountry'                      => $this->hasVatNumberInRecipientCountry(),
            'hasWarehouseInRecipientCountry'                      => $this->hasWarehouseInRecipientCountry(),
            'fcTransferErrors'                                    => $this->checkFcTransfer(),
            'isCompanyCountryVatNumberPermanentEstablishment'     => $this->isCompanyCountryVatNumberPermanentEstablishment()
        ];
    }
}
