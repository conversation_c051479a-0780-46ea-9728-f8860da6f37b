<?php

namespace App\Core\AmazonCsv\Handlers\Helpers;

use App\Core\Common\Common\DataTransfer\DataTransferObject;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\CountryItemTaxCode;
use App\Core\Data\Models\Currency;
use App\Core\Data\Models\ExchangeRate;
use App\Core\Data\Models\IdentificationNumber;
use App\Core\Data\Models\IdentificationNumberType;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\Supplier;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ResolvedData2DatabaseResolver
{
    private Collection $itemTaxCodesData;
    private Collection $itemTaxCodesDb;
    private Collection $currencies;
    private Collection $exchangeRates;
    private Collection $suppliers;
    private Collection $countries;

    public function __construct()
    {
        $this->itemTaxCodesData = collect();
        $this->itemTaxCodesDb = collect();
        $this->currencies = collect();
        $this->exchangeRates = collect();
        $this->suppliers = collect();
        $this->countries = Country::select(['id', 'code'])
            ->orderBy('name')
            ->get()
            ->keyBy('code');
    }

    public function resolve(): void
    {
        $this->resolveItemTaxCodes();
        $this->resolveCurrencies();
        $this->resolveSuppliers();
    }

    private function resolveCurrencies(): void
    {
        $currencies = $this->currencies->map(function (Collection $dates) {
            $datesData = $dates->sortBy(function ($date) {
                return $date;
            });

            $dates = new DataTransferObject();

            $dates->dateFrom = $datesData->first();
            $dates->dateTo = $datesData->last();
            $dates->rates = collect();

            return $dates;
        })->map(function (DataTransferObject $dates, int $currencyId) {
            $addSubDays = 3;
            $dateFrom = Carbon::parse($dates->dateFrom);
            $dateTo = Carbon::parse($dates->dateTo);
            $firstLast = [
                $dateFrom->clone()->subDays($addSubDays - 1)->toDateString(),
                $dateTo->clone()->addDays($addSubDays - 1)->toDateString()
            ];

            $dbRates = ExchangeRate::where(function (Builder $query) use ($currencyId, $firstLast) {
                $query->where('currency_id', $currencyId)
                    ->whereBetween('date', $firstLast);
            })
                ->orderBy('currency_id')
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            $periodDates = CarbonPeriod::create($firstLast[0], $firstLast[1]);
            $rates = collect();
            $last = null;
            foreach ($periodDates as $periodDate) {
                $date = $periodDate->toDateString();
                $exchangeRate = $dbRates->get($date);
                if (is_null($exchangeRate)) {
                    if (!is_null($last)) {
                        $last = clone $last;
                    }
                    $exchangeRate = $last;
                }

                if (!is_null($exchangeRate)) {
                    $exchangeRate->date = $date;
                }

                $last = $exchangeRate;
                $rates->put($date, $exchangeRate);
            }

            $rates = $rates->filter(function (?ExchangeRate $rate = null) use ($dateFrom, $dateTo) {
                if (is_null($rate)) {
                    return false;
                }

                $date = Carbon::parse($rate->date);

                return $date->gte($dateFrom) && $date->lte($dateTo);
            });

            $dates->rates = $rates;

            return $dates;
        });

        $this->exchangeRates = $currencies;
    }

    private function resolveItemTaxCodes(): void
    {
        $columns = [
            'country_item_tax_codes.id as country_item_tax_code_id',
            'country_vat_rates.id as country_vat_rate_id',
            'country_item_tax_codes.country_id',
            'country_item_tax_codes.item_tax_code_id',
            'country_vat_rates.value as percentage',
            'country_vat_rates.effective_from as date_from',
            'country_vat_rates.end_date'
        ];

        $collection = [];
        foreach ($this->itemTaxCodesData as $itemTaxCodeData) {
            $collection[] = '(' . implode(', ', $itemTaxCodeData) . ')';
        }
        $taxCodes = collect();
        if (count($collection) > 0) {
            $collection = implode(', ', $collection);
            $taxCodes = CountryItemTaxCode::select($columns)
                ->join('country_vat_rates', function (JoinClause $join) {
                    $join->on('country_item_tax_codes.vat_rate_type_id', '=', 'country_vat_rates.vat_rate_type_id')
                        ->on('country_item_tax_codes.country_id', '=', 'country_vat_rates.country_id');
                })
                ->whereRaw('("country_item_tax_codes"."item_tax_code_id", "country_item_tax_codes"."country_id") IN (' . $collection . ')')
                ->orderBy('country_vat_rates.effective_from', 'DESC')
                ->get()
                ->map(function ($countryItemTaxCodeData) {
                    $countryItemTaxCodeData->percentage = (float)$countryItemTaxCodeData->percentage;

                    return $countryItemTaxCodeData;
                })
                ->groupBy('country_id');
        }

        $this->itemTaxCodesDb = $taxCodes;
    }

    public function resolveSuppliers(): void
    {
        if ($this->suppliers->count() < 1) {
            return;
        }

        $suppliers = $this->suppliers;
        $suppliersVatNumbers = $suppliers->keyBy('vatNumber')->keys()->toArray();
        $companyId = $suppliers->first()['companyId'] ?? null;
        $dbSuppliers = $this->getCompanySuppliers($companyId, $suppliersVatNumbers);

        $missingSuppliers = $suppliers->filter(function (array $supplier) use ($dbSuppliers) {
            return !$dbSuppliers->has($supplier['vatNumber']);
        });

        if ($missingSuppliers->count() > 0) {
            /** @noinspection PhpUnhandledExceptionInspection */
            $this->insertMissingSuppliersAndVatNumbers($missingSuppliers);
            $dbSuppliers = $this->getCompanySuppliers($companyId, $suppliersVatNumbers);
        }

        $this->suppliers = $dbSuppliers;
    }

    public function getSupplierByVatNumber(string $supplierVatNumber): ?IdentificationNumber
    {
        return $this->suppliers->get($supplierVatNumber);
    }

    private function insertMissingSuppliersAndVatNumbers(Collection $missingSuppliers): void
    {
        foreach ($missingSuppliers as $missingSupplier) {
            $vatNumber = $missingSupplier['vatNumber'] ?? null;
            if (is_null($vatNumber)) {
                /** @noinspection PhpUnhandledExceptionInspection */
                throw new Exception('VAT number not found for supplier: ' . $vatNumber);
            }

            $country = null;
            foreach ($this->countries as $countryDb) {
                /**
                 * @var Country $countryDb
                 */
                $countryDbCode = $countryDb->code;
                if (Str::startsWith($vatNumber, $countryDbCode)) {
                    $country = $countryDb;
                    break;
                }
            }

            if (is_null($country)) {
                /** @noinspection PhpUnhandledExceptionInspection */
                throw new Exception('Country not found for supplier VAT number: ' . $vatNumber);
            }
            $supplier = new Supplier();
            $supplier->company_id = $missingSupplier['companyId'];
            $supplier->company_name = $missingSupplier['name'];
            $supplier->save();

            $identificationNumber = new IdentificationNumber();
            $identificationNumber->value = $vatNumber;
            $identificationNumber->identification_number_type_id = IdentificationNumberType::VAT_NUMBER;
            $identificationNumber->country_id = $country->id;
            $identificationNumber->resource = Supplier::class;
            $identificationNumber->resource_id = $supplier->id;
            $identificationNumber->save();
        }
    }

    private function getCompanySuppliers(int $companyId, array $suppliersVatNumbers): Collection
    {
        $columns = [
            'suppliers.*',
            'identification_numbers.id as identification_number_id',
            'identification_numbers.value as identification_number_value',
            'identification_numbers.country_id as identification_number_country_id',
            'identification_numbers.identification_number_type_id as identification_number_type_id',
        ];

        return IdentificationNumber::select($columns)
            ->join('suppliers', function (JoinClause $join) {
                $join->on('suppliers.id', '=', 'identification_numbers.resource_id')->where('resource', Supplier::class);
            })->where(function (Builder $query) use ($companyId) {
                $query->where('suppliers.company_id', $companyId)
                    ->orWhere('suppliers.is_global', true);
            })
            ->whereIn('identification_numbers.value', $suppliersVatNumbers)
            ->orderBy('suppliers.is_global', 'DESC')
            ->get()
            ->keyBy('identification_number_value');
    }

    /**
     * @param int $countryId
     * @param int $itemTaxCodeId
     * @param string $date
     * @return float|null
     */
    public function getVatPercentageForCountryOnDate(int $countryId, int $itemTaxCodeId, string $date): ?float
    {
        $percentage = null;
        $date = strtotime($date);
        $code = $this->itemTaxCodesDb
            ->get($countryId, collect())
            ->groupBy(function (CountryItemTaxCode $countryItemTaxCode) {
                return $countryItemTaxCode->item_tax_code_id;
            })
            ->get($itemTaxCodeId)
            ?->filter(function (CountryItemTaxCode $countryItemTaxCode) use ($date) {
                $dateFrom = strtotime($countryItemTaxCode->date_from);
                $dateTo = $countryItemTaxCode->end_date;
                $check = $date >= $dateFrom;

                if (!is_null($dateTo)) {
                    $dateTo = strtotime($dateTo);
                    $check = $check && $date <= $dateTo;
                }

                return $check;
            })->first();

        if (!is_null($code)) {
            $percentage = $code->percentage;
        }

        return $percentage;
    }

    /**
     * @param string $taxCalculationDate
     * @param int $transactionCurrencyId
     */
    public function addCurrencyForLoad(string $taxCalculationDate, int $transactionCurrencyId): void
    {
        /**
         * @var Collection $currency
         * @var Collection $currencyGBP
         */
        if ($transactionCurrencyId !== Currency::EUR) {
            $currency = $this->currencies->get($transactionCurrencyId, collect());
            if (!$currency->has($taxCalculationDate)) {
                $currency->put($taxCalculationDate, $taxCalculationDate);
            }
            $this->currencies->put($transactionCurrencyId, $currency);
        }

        if ($transactionCurrencyId !== Currency::GBP) {
            $currencyGBP = $this->currencies->get(Currency::GBP, collect());
            if (!$currencyGBP->has($taxCalculationDate)) {
                $currencyGBP->put($taxCalculationDate, $taxCalculationDate);
            }
            $this->currencies->put(Currency::GBP, $currencyGBP);
        }
    }

    /**
     * @param string $date
     * @param float $sum
     * @param int|null $currencyId
     * @return float
     */
    public function exchangeToEuro(string $date, float $sum, ?int $currencyId = null): float
    {
        $date = Carbon::parse($date)->toDateString();
        if (is_null($currencyId) || $currencyId === Currency::EUR) {
            return $sum;
        }

        $rate = $this->exchangeRates
            ->get($currencyId)
            ->rates
            ->get($date)
            ?->value ?? null;
        if (is_null($rate)) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new Exception('Exchange rate not found. DB maybe not synced');
        }

        return round($sum / $rate, 4, PHP_ROUND_HALF_EVEN);
    }

    /**
     * @param string $date
     * @param float $sum
     * @param int|null $currencyId
     * @return float
     */
    public function exchangeToGbp(string $date, float $sum, ?int $currencyId = null): float
    {
        $date = Carbon::parse($date)->toDateString();
        if (is_null($currencyId) || $currencyId === Currency::GBP) {
            return $sum;
        }

        /** @noinspection PhpUnhandledExceptionInspection */
        $sum = $this->exchangeToEuro($date, $sum, $currencyId);

        $rate = $this->exchangeRates
            ->get(Currency::GBP)
            ->rates
            ->get($date)
            ?->value ?? null;

        return round($sum * $rate, 4, PHP_ROUND_HALF_EVEN);
    }

    public function addItemTaxCodeIdForLoad(int $itemTaxCodeId, int $taxCalculationCountryId): void
    {
        $key = $itemTaxCodeId . '-' . $taxCalculationCountryId;
        if ($this->itemTaxCodesData->has($key)) {
            return;
        }

        $data = [
            'itemTaxCode'             => $itemTaxCodeId,
            'taxCalculationCountryId' => $taxCalculationCountryId
        ];

        $this->itemTaxCodesData->put($key, $data);
    }

    public function addSupplierIfPurchaseInvoice($data, $invoiceTypeId): void
    {
        if ($invoiceTypeId !== InvoiceType::PURCHASE_INVOICE) {
            return;
        }

        $supplierName = $data['supplier_name'] ?? null;
        $supplierVatNumber = $data['bill_from_vat_number'] ?? null;
        $companyId = $data['sales_channel_data']['company']['id'] ?? null;
        if (is_null($supplierName) || is_null($supplierVatNumber) || is_null($companyId)) {
            return;
        }

        $supplierVatNumber = trim($supplierVatNumber);
        $supplierVatNumber = mb_strtoupper($supplierVatNumber);

        $supplier = [
            'name'      => $supplierName,
            'vatNumber' => $supplierVatNumber,
            'companyId' => $companyId
        ];

        $this->suppliers->put($supplierVatNumber, $supplier);
    }

    public function toArray(): array
    {
        return [
            'itemTaxCodesData' => $this->itemTaxCodesData->toArray(),
            'itemTaxCodesDb'   => $this->itemTaxCodesDb->toArray(),
        ];
    }
}
