<?php

namespace App\Core\AmazonCsv\Handlers\Helpers;

use Illuminate\Support\Collection;

class ItemSalePricesResolver
{
    private Collection $data;
    private ?Collection $forInsert = null;
    private ?Collection $forUpdate = null;
    private ?Collection $forDelete = null;

    public function __construct(private readonly int $itemMarketplaceId)
    {
        $this->data = collect();
    }

    public function addItemSalesPrice(
        string $startDate,
        float $net,
        float $gross,
        int $currencyId,
        ?int $salesPriceId = null
    ): self {
        $price = [
            'id'                  => $salesPriceId,
            'currency_id'         => $currencyId,
            'net_price'           => $net,
            'start_date'          => $startDate,
            'gross_price'         => $gross,
            'item_marketplace_id' => $this->itemMarketplaceId,
        ];

        $this->data->push($price);

        return $this;
    }


    public function resolve(): self
    {
        // Init
        $this->forInsert = collect();
        $this->forUpdate = collect();
        $this->forDelete = collect();

        // Sort by date ASC
        $prices = $this->data->sortBy(function (array $price) {
            return strtotime($price['start_date']);
        }, SORT_NATURAL);

        $ids = $prices->filter(function (array $price) {
            return !is_null($price['id']);
        })->keyBy('id')->pluck('id')->values()->toArray();

        $prices = $prices->groupBy(function (array $price) {
            return strtotime($price['start_date']);
        })->map(function (Collection $prices) {
            $price = $prices->first();
            $price['remove'] = false;

            return $price;
        })->values();

        // Must be separated because $prices are passed to callback
        $prices = $prices->map(function (array $price, int $index) use ($prices) {
            $previous = $prices->get($index - 1);
            $startDate = strtotime($price['start_date']);
            $startDate = date(config('date-formats.db-date'), $startDate);
            $price['start_date'] = $startDate;
            if (!is_null($previous) && $previous['net_price'] === $price['net_price']) {
                $price['remove'] = true;
            }

            return $price;
        })->filter(function (array $price) {
            return !$price['remove'];
        })->map(function (array $price) use (&$ids) {
            unset($price['remove']);
            $id = array_pop($ids) ?? null;
            $price['id'] = $id;

            return $price;
        });

        // If no prices - do nothing
        if ($prices->count() < 1) {
            return $this;
        }

        foreach ($ids as $priceId) {
            $this->forDelete->push([
                'id'                  => $priceId,
                'item_marketplace_id' => $this->itemMarketplaceId
            ]);
        }

        foreach ($prices as $price) {
            if (!is_null($price['id'])) {
                $this->forUpdate->push($price);
                continue;
            }

            $this->forInsert->push($price);
        }

        return $this;
    }

    public function getData(): Collection
    {
        return $this->data;
    }

    public function getForInsert(): Collection
    {
        if (is_null($this->forInsert)) {
            $this->resolve();
        }

        return $this->forInsert;
    }

    public function getForUpdate(): Collection
    {
        if (is_null($this->forUpdate)) {
            $this->resolve();
        }

        return $this->forUpdate;
    }

    public function getForDelete(): Collection
    {
        if (is_null($this->forDelete)) {
            $this->resolve();
        }

        return $this->forDelete;
    }
}
