<?php

namespace App\Core\AmazonCsv\Handlers\Contracts;

use App\Core\Data\Models\AmazonReport;
use App\Core\System\Helpers\Arr;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Str;
use Throwable;

abstract class CycleContract
{
    public static bool $isDebug = false;
    private ?Filesystem $filesystem = null;

    protected function getFilesystem(): Filesystem
    {
        if (is_null($this->filesystem)) {
            /**
             * @noinspection PhpUnhandledExceptionInspection *
             */
            $this->filesystem = app()->make(Filesystem::class);
        }

        return $this->filesystem;
    }

    /**
     * @return AmazonReport
     */
    abstract public function getAmazonReport(): AmazonReport;

    abstract protected function getCurrentFile(): string;

    protected function generateErrorReason(string $message, string $throwFile, int $throwLine, int $catchLine): string
    {
        $message = Str::limit($message, 2000);

        return $message . ' [thrown in `' . $throwFile . ':L' . $throwLine . '`, caught in `' . $this->getCurrentFile() . 'L:' . $catchLine . '`';
    }

    protected function generateRawDataErrorReasons(int $code, string $message, string $throwFile, int $throwLine, int $catchLine): array
    {
        $message = Str::limit($message, 2000);

        $throwFile = $this->cleanFilePath($throwFile);
        $catchFile = $this->cleanFilePath($this->getCurrentFile());

        return [
            'code'    => $code,
            'message' => $message,
            'thrown'  => $throwFile . ':L' . $throwLine,
            'caught'  => $catchFile . ':L' . $catchLine,
        ];
    }

    private function cleanFilePath($path): string
    {
        $pathExploded = explode('/app/', $path);
        if (count($pathExploded) < 2) {
            return $path;
        }

        return '/app/' . $pathExploded[1];
    }

    protected function isDebug(): bool
    {
        return self::$isDebug;
    }

    public function clearForInvoiceNumber(?string $data): ?string
    {
        if (is_null($data)) {
            return null;
        }

        return str_replace('/', '+', $data);
    }

    public function getJsonFileForCycle(int $cycle): ?Arr
    {
        $path = $this->buildPathForFile($cycle);
        try {
            $data = file_get_contents($path);
            $data = json_decode($data, true);
            if (!is_array($data)) {
                return null;
            }

            return new Arr($data);
        } catch (Throwable) {
            return null;
        }
    }

    public function storeJsonFileForCycle(int $cycle, ?array $data): void
    {
        $data = $data ?? [];
        $path = $this->buildPathForFile($cycle);
        $this->storeFile($path, evat_json_encode($data));
    }

    private function buildPathForFile(int $cycle): string
    {
        $amazonReport = $this->getAmazonReport();

        return $amazonReport->getUploadPath() . 'cycle-' . $cycle . '.json';
    }

    protected function deleteAllCyclesFiles(): void
    {
        $cycles = range(1, 4);
        foreach ($cycles as $cycle) {
            $path = $this->buildPathForFile($cycle);
            $this->deleteFile($path);
        }
    }

    protected function deleteFile(string $path): void
    {
        if (file_exists($path)) {
            try {
                $this->getFilesystem()->delete($path);
            } catch (Throwable) {
            }
        }
    }

    protected function storeFile(string $path, mixed $content, int $mode = 01777): void
    {
        $this->getFilesystem()->put($path, $content);
        $this->getFilesystem()->chmod($path, $mode);
    }
}
