<?php

namespace App\Core\AmazonCsv\Contracts;

use App\Core\Data\Models\AmazonReport;

interface AmazonCsvServiceContract
{
    /**
     * @param AmazonReport $report
     * @return AmazonReport
     */
    public function resetAmazonReport(AmazonReport $report): AmazonReport;

    /**
     * @param int $reportId
     * @return AmazonReport|null
     */
    public function resolveSingleReportCycleOneById(int $reportId): ?AmazonReport;

    /**
     * @param int $reportId
     * @return AmazonReport|null
     */
    public function resolveSingleReportCycleTwoById(int $reportId): ?AmazonReport;

    /**
     * @param int $reportId
     * @return AmazonReport|null
     */
    public function resolveSingleReportCycleThreeById(int $reportId): ?AmazonReport;

    /**
     * @param int $reportId
     * @return AmazonReport|null
     */
    public function resolveSingleReportCycleFourById(int $reportId): ?AmazonReport;

    public function resetAndParseReport(AmazonReport $report, bool $isDebug = false, bool $doReset = true): ?AmazonReport;
}
