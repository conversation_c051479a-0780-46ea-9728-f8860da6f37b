<?php

namespace App\Core\AmazonCsv;

use App\Core\AmazonCsv\Contracts\AmazonCsvServiceContract;
use App\Core\AmazonCsv\Exceptions\AmazonTestParseDevException;
use App\Core\AmazonCsv\Handlers\Contracts\CycleContract;
use App\Core\AmazonCsv\Handlers\CycleFour;
use App\Core\AmazonCsv\Handlers\CycleOne;
use App\Core\AmazonCsv\Handlers\CycleThree;
use App\Core\AmazonCsv\Handlers\CycleTwo;
use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Data\Repositories\Contracts\AmazonReportRepositoryContract;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\System\Notifications\Contracts\NotificationsServiceContract;
use Carbon\Carbon;
use Throwable;

class AmazonCsvService implements AmazonCsvServiceContract
{
    private AmazonReportRepositoryContract $amazonReportRepo;
    private NotificationsServiceContract $notificationsService;
    private InvoiceRepositoryContract $invoiceRepo;
    private ItemRepositoryContract $itemRepo;

    public function __construct(
        AmazonReportRepositoryContract $amazonReportRepo,
        NotificationsServiceContract $notificationsService,
        InvoiceRepositoryContract $invoiceRepo,
        ItemRepositoryContract $itemRepo

    ) {
        set_time_limit(60 * 30);
        ini_set('memory_limit', '-1');

        $this->amazonReportRepo = $amazonReportRepo;
        $this->notificationsService = $notificationsService;
        $this->invoiceRepo = $invoiceRepo;
        $this->itemRepo = $itemRepo;
    }

    private function resolveSingleReportCycleOne(AmazonReport $report): ?AmazonReport
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $cycle = new CycleOne($report);
        $this->cycleHandlerUserNotificationHelper($cycle);

        return $cycle->getAmazonReport();
    }

    private function resolveSingleReportCycleTwo(AmazonReport $report): ?AmazonReport
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $cycle = new CycleTwo($report);
        $this->cycleHandlerUserNotificationHelper($cycle);

        return $cycle->getAmazonReport();
    }

    private function resolveSingleReportCycleThree(AmazonReport $report): ?AmazonReport
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $cycle = new CycleThree($report);
        $this->cycleHandlerUserNotificationHelper($cycle);

        return $cycle->getAmazonReport();
    }

    private function resolveSingleReportCycleFour(AmazonReport $report): ?AmazonReport
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $cycle = new CycleFour($report);
        $this->cycleHandlerUserNotificationHelper($cycle);

        return $cycle->getAmazonReport();
    }

    public function resolveSingleReportCycleOneById(int $reportId): ?AmazonReport
    {
        $report = $this->amazonReportRepo->getReportById($reportId);
        if (is_null($report)) {
            return null;
        }

        return $this->resolveSingleReportCycleOne($report);
    }

    public function resolveSingleReportCycleTwoById(int $reportId): ?AmazonReport
    {
        $report = $this->amazonReportRepo->getReportById($reportId);
        if (is_null($report)) {
            return null;
        }

        return $this->resolveSingleReportCycleTwo($report);
    }

    public function resolveSingleReportCycleThreeById(int $reportId): ?AmazonReport
    {
        $report = $this->amazonReportRepo->getReportById($reportId);
        if (is_null($report)) {
            return null;
        }

        return $this->resolveSingleReportCycleThree($report);
    }

    public function resolveSingleReportCycleFourById(int $reportId): ?AmazonReport
    {
        $report = $this->amazonReportRepo->getReportById($reportId);
        if (is_null($report)) {
            return null;
        }

        return $this->resolveSingleReportCycleFour($report);
    }

    public function cycleHandlerUserNotificationHelper(CycleContract $cycle): void
    {
        $amazonReport = $cycle->getAmazonReport();
        $userId = $amazonReport->upload_user_id;
        $locale = $amazonReport->uploadUser?->locale ?? null;

        if ($amazonReport->report_status_id === AmazonReportStatus::STATUS_FAILED) {
            $url = route('sales-channels.amazon.reports.failed-row', $amazonReport->id);
            $message = _l(
                'amazon.report-parsing-error-reason.common-error',
                [
                    'name' => $amazonReport->file->full_name,
                    'url'  => $url
                ],
                $locale
            );

            $this->notificationsService->errorNotificationForUser($userId, $message);
        }

        $batchId = $amazonReport->batch_id;
        $count = $this->amazonReportRepo->getCountOfQueuedReportsByBatchId($batchId);
        if ($count < 1) {
            $message = _l(
                'reports.Your reports in batch uploaded at have been processed',
                [
                    'batch' => $batchId,
                    'time'  => Carbon::parse($amazonReport->uploaded_at)->format(_l_date_format('datetime-without-seconds')),
                ],
                $locale
            );
            $this->notificationsService->infoNotificationForUser($userId, $message);
        }
    }

    /**
     * @inheritDoc
     */
    public function resetAmazonReport(AmazonReport $report): AmazonReport
    {
        $report->report_status_id = AmazonReportStatus::STATUS_QUEUED;
        $report->error_reason = null;
        $report->start_date = null;
        $report->end_date = null;
        $report->sales_channel_id = null;
        $report->parsing_started_at = null;
        $report->parsing_ended_at = null;
        $report->save();

        try {
            $this->invoiceRepo->deleteInvoicesByAmazonReportId($report->id);
        } catch (Throwable) {
        }
        try {
            $this->amazonReportRepo->deleteAmazonReportRawDataByAmazonReportId($report->id);
        } catch (Throwable) {
        }
        try {
            $this->itemRepo->deleteNotConnectedItemsByAmazonReportId($report->id);
        } catch (Throwable) {
        }

        return $report;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    public function resetAndParseReport(AmazonReport $report, bool $isDebug = false, bool $doReset = true): ?AmazonReport
    {
        set_time_limit(60 * 30);
        ini_set('memory_limit', '-1');

        if ($doReset) {
            $this->resetAmazonReport($report);
        }

        $amazonReportId = $report->id;

        // Set debug
        if ($isDebug) {
            CycleContract::$isDebug = true;
        }

        try {
            $this->resolveSingleReportCycleOneById($amazonReportId);

            $report = $this->amazonReportRepo->getReportById($amazonReportId);
            if (!is_null($report) && $report->report_status_id === AmazonReportStatus::STATUS_READ) {
                $this->resolveSingleReportCycleTwoById($amazonReportId);
            }

            $report = $this->amazonReportRepo->getReportById($amazonReportId);
            if (!is_null($report) && $report->report_status_id === AmazonReportStatus::STATUS_RESOLVED) {
                $this->resolveSingleReportCycleThreeById($amazonReportId);
            }

            $report = $this->amazonReportRepo->getReportById($amazonReportId);
            if (!is_null($report) && $report->report_status_id === AmazonReportStatus::STATUS_SYNCED_DB) {
                $this->resolveSingleReportCycleFourById($amazonReportId);
            }

            return $this->amazonReportRepo->getReportById($amazonReportId)?->load('transactions');
        } catch (Throwable $exception) {
            if (config('app.env') === 'local') {
                throw  $exception;
            }

            throw new AmazonTestParseDevException($exception->getMessage(), 500, $exception);
        }
    }
}
