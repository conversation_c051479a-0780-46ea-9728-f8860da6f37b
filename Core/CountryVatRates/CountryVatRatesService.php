<?php

namespace App\Core\CountryVatRates;

use App\Core\CountryVatRates\Contracts\CountryVatRatesServiceContract;
use App\Core\Data\Models\CountryVatRate;
use App\Core\Data\Repositories\Contracts\CountryVatRateRepositoryContract;

class CountryVatRatesService implements CountryVatRatesServiceContract
{
    /**
     * @var CountryVatRateRepositoryContract
     */
    private $countryVatRateRep;

    public function __construct(CountryVatRateRepositoryContract $countryVatRateRep)
    {
        $this->countryVatRateRep = $countryVatRateRep;
    }

    /**
     * @inheritDoc
     */
    public function storeCountryVatRate(
        int $countryId,
        float $value,
        int $vatRateTypeId,
        string $effectiveFrom,
        ?string $endDate = null
    ): CountryVatRate {

        $countryVatRate = $this->countryVatRateRep->getEmptyCountryVatRatesModel();
        $countryVatRate->country_id = $countryId;
        $countryVatRate->value = $value;
        $countryVatRate->vat_rate_type_id = $vatRateTypeId;
        $countryVatRate->effective_from = $effectiveFrom;
        $countryVatRate->end_date = $endDate;

        $countryVatRate->save();

        return $countryVatRate;
    }

    /**
     * @inheritDoc
     */
    public function updateCountryVatRate(
        int $countryVatRateId,
        int $countryId,
        float $value,
        int $vatRateTypeId,
        string $effectiveFrom,
        ?string $endDate = null
    ): CountryVatRate {
        $countryVatRate = $this->countryVatRateRep->getCountryVatRatesById($countryVatRateId);
        $countryVatRate->country_id = $countryId;
        $countryVatRate->value = $value;
        $countryVatRate->vat_rate_type_id = $vatRateTypeId;
        $countryVatRate->effective_from = $effectiveFrom;
        $countryVatRate->end_date = $endDate;


        $countryVatRate->save();

        return $countryVatRate;

    }
}
