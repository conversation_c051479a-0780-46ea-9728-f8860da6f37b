<?php

namespace App\Core\CountryVatRates\Contracts;

use App\Core\Data\Models\CountryVatRate;

interface CountryVatRatesServiceContract
{
    /**
     * @param int $countryId
     * @param float $value
     * @param int $vatRateTypeId
     * @param string $effectiveFrom
     * @param string $endDate |null
     * @return CountryVatRate
     */
    public function storeCountryVatRate(
        int $countryId,
        float $value,
        int $vatRateTypeId,
        string $effectiveFrom,
        ?string $endDate = null
    ): CountryVatRate;

    /**
     * @param int $countryVatRateId
     * @param int $countryId
     * @param float $value
     * @param int $vatRateTypeId
     * @param string $effectiveFrom
     * @param string $endDate |null
     * @return CountryVatRate
     */
    public function updateCountryVatRate(
        int $countryVatRateId,
        int $countryId,
        float $value,
        int $vatRateTypeId,
        string $effectiveFrom,
        ?string $endDate = null
    ): CountryVatRate;

}
