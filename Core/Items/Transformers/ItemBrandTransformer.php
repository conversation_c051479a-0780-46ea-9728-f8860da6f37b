<?php

namespace App\Core\Items\Transformers;

use App\Core\Data\Models\Brand;
use League\Fractal\TransformerAbstract;

class ItemBrandTransformer extends TransformerAbstract
{
    public function transform(Brand $brand)
    {
        return [
            'id' => $brand->id,
            'value' => $brand->id,
            'name' => $brand->name,
            'description' => $brand->description,
            'label' => $brand->name,
        ];
    }
}
