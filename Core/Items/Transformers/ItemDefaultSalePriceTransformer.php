<?php

namespace App\Core\Items\Transformers;

use App\Core\Data\Models\Currency;
use App\Core\Data\Models\ItemSalePrice;
use App\Web\Items\Transformers\CurrencyTransformer;
use League\Fractal\TransformerAbstract;

class ItemDefaultSalePriceTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'currency'
    ];

    private Currency $defaultCurrency;

    public function __construct(Currency $defaultCurrency)
    {
        $this->defaultCurrency = $defaultCurrency;
    }

    public function transform(ItemSalePrice $itemSalePrice)
    {
        $netPrice = $itemSalePrice->net_price;
        $grossPrice = $itemSalePrice->gross_price;

        return [
            'id'         => $itemSalePrice->id,
            'netPrice'   => $netPrice ?? 0.00,
            'grossPrice' => $grossPrice ?? 0.00,
            'startDate'  => $itemSalePrice->start_date,
            'key'        => null,
        ];
    }

    public function includeCurrency(ItemSalePrice $itemSalePrice)
    {
        $currency = $itemSalePrice->currency;
        if (is_null($currency)) {
            $currency = $this->defaultCurrency;
        }

        return $this->item($currency, new CurrencyTransformer());
    }
}
