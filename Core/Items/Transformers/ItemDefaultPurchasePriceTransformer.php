<?php

namespace App\Core\Items\Transformers;

use App\Core\Data\Models\Currency;
use App\Core\Data\Models\ItemPurchasePrice;
use App\Web\Items\Transformers\CurrencyTransformer;
use League\Fractal\TransformerAbstract;

class ItemDefaultPurchasePriceTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'currency'
    ];

    private Currency $defaultCurrency;

    public function __construct(Currency $defaultCurrency)
    {
        $this->defaultCurrency = $defaultCurrency;
    }

    public function transform(ItemPurchasePrice $itemPurchasePrice)
    {
        $netPrice = $itemPurchasePrice->net_price;
        $cost = $itemPurchasePrice->cost;
        $price = $itemPurchasePrice->price;

        return [
            'id'        => $itemPurchasePrice->id,
            'netPrice'  => $netPrice ?? 0.00,
            'cost'      => $cost ?? 0.00,
            'price'     => $price ?? 0.00,
            'startDate' => $itemPurchasePrice->start_date,
            'key'       => null,
        ];
    }

    public function includeCurrency(ItemPurchasePrice $itemPurchasePrice)
    {
        $currency = $itemPurchasePrice->currency;
        if (is_null($currency)) {
            $currency = $this->defaultCurrency;
        }

        return $this->item($currency, new CurrencyTransformer());
    }
}
