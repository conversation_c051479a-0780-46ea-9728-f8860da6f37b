<?php

namespace App\Core\Items\Transformers;

use App\Core\Data\Models\ItemCommodityCodeType;
use App\Web\Items\Transformers\CommodityCodeTypeTransformer;
use League\Fractal\TransformerAbstract;

class ItemCommodityCodeTypeTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'commodityCodeType'
    ];

    public function transform(ItemCommodityCodeType $itemCommodityCodeType)
    {
        return [
            'id'                  => $itemCommodityCodeType->id ?? null,
            'itemId'              => $itemCommodityCodeType->item_id ?? null,
            'value'               => $itemCommodityCodeType->value ?? null,
            'commodityCodeTypeId' => $itemCommodityCodeType->commodity_code_type_id,
            'commodityCodeType'   => null,
        ];
    }

    public function includeCommodityCodeType(ItemCommodityCodeType $itemCommodityCodeType)
    {
        if (is_null($itemCommodityCodeType->commodity_code_type_id)) {
            return null;
        }

        return $this->item($itemCommodityCodeType->commodityCodeType, new CommodityCodeTypeTransformer());
    }
}
