<?php

namespace App\Core\Items\Transformers;

use App\Core\Data\Models\Brand;
use App\Web\Common\Transformers\MultiselectItemTransformerAbstract;

class ItemBrandMultiselectTransformer extends MultiselectItemTransformerAbstract
{
    protected function getValue($brand): string|int|float|bool|null
    {
        /**
         * @var Brand $brand
         */
        return $brand->id;
    }

    protected function getLabel($brand): string
    {
        /**
         * @var Brand $brand
         */
        return $brand->name;
    }

    protected function getData($brand): array
    {
        /**
         * @var Brand $brand
         */
        return [
            'id'          => $brand->id,
            'description' => $brand->description,
        ];
    }
}
