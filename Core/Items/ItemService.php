<?php

namespace App\Core\Items;

use App\Core\Common\Common\DataTransfer\DataTransferObject;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\Currency;
use App\Core\Data\Models\Item;
use App\Core\Data\Models\ItemCommodityCodeType;
use App\Core\Data\Models\ItemMarketplace;
use App\Core\Data\Models\Platform;
use App\Core\Data\Models\Store;
use App\Core\Data\Repositories\Contracts\BrandRepositoryContract;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemPurchasePriceRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemSalePriceRepositoryContract;
use App\Core\Items\Contracts\ItemServiceContract;
use App\Core\Items\Exceptions\ItemCommodityCodeTypeDataException;
use App\Core\Items\Exceptions\ItemIdentificationTypeDataException;
use App\Core\Items\Exceptions\ItemMarketplaceDataException;
use App\Core\Items\Transformers\ItemBrandTransformer;
use App\Core\Items\Transformers\ItemCommodityCodeTypeTransformer;
use App\Core\Items\Transformers\ItemDefaultPurchasePriceTransformer;
use App\Core\Items\Transformers\ItemDefaultSalePriceTransformer;
use App\Web\Items\Transformers\EmptyMarketplaceTransformer;
use App\Web\Items\Transformers\ItemItemIdentificationTypeTransformer;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Fluent;

class ItemService implements ItemServiceContract
{
    private ItemRepositoryContract $itemRepo;
    private BrandRepositoryContract $brandRepo;
    private CurrencyRepositoryContract $currencyRepo;
    private ItemPurchasePriceRepositoryContract $itemPurchasePriceRepo;
    private ItemSalePriceRepositoryContract $itemSalePriceRepo;

    public function __construct(
        ItemRepositoryContract $itemRepo,
        BrandRepositoryContract $brandRepo,
        CurrencyRepositoryContract $currencyRepo,
        ItemPurchasePriceRepositoryContract $itemPurchasePriceRepo,
        ItemSalePriceRepositoryContract $itemSalePriceRepo,
    ) {
        $this->itemRepo = $itemRepo;
        $this->brandRepo = $brandRepo;
        $this->currencyRepo = $currencyRepo;
        $this->itemPurchasePriceRepo = $itemPurchasePriceRepo;
        $this->itemSalePriceRepo = $itemSalePriceRepo;
    }

    public function storeItem(Company $company, Store $storeItem): Item
    {
        $data = $this->fluentizeData($storeItem->data);
        $platforms = data_get($storeItem->data, 'platforms');
        $itemCommodityCodeTypes = data_get($storeItem->data, 'itemCommodityCodeTypes');

        $item = $data->id ? $this->itemRepo->getItemById($data->id) : $this->itemRepo->getEmptyItemModel();

        DB::transaction(function () use ($company, $data, $platforms, $item, $itemCommodityCodeTypes) {
            $identifiers = $this->resolveIdentifierData(false, $itemCommodityCodeTypes, $platforms);
            $this->storeItemGeneralData($company, $item, $data, $identifiers);
            $this->resolveItemCommodityCodeTypes($item, $data);
            $this->resolveItemMarketplaces($platforms, $item);
        });

        return $item;
    }

    public function storeItemGeneralData(Company $company, Item $item, Fluent $data, array $identifiers): void
    {
        $isGuessed = (float)$item->purchase_price_net === (float)$data->purchasePriceNet
            && (float)$item->purchase_price_cost === (float)$data->purchasePriceCost
            && $item->is_purchase_price_guessed;

        $item->company_id = $company->id;
        $item->name = $data->name;
        $item->description = $data->description;
        $item->item_type_id = $data->itemTypeId;
        $item->brand_id = $data->brand?->id;
        $item->item_category_id = $data->itemCategory?->id;
        $item->item_tax_code_id = $data->itemTaxCode?->id;
        $item->identifiers = $identifiers;
        $item->manufacturer = $data->manufacturer;
        $item->manufacture_country_id = $data->manufactureCountry?->id;
        $item->business_model_id = $data->businessModel?->id;
        $item->unit_id = $data->unit?->id;
        $item->unit_value = $data->unitValue;
        $item->purchase_price_net = $data->purchasePriceNet;
        $item->purchase_price_cost = $data->purchasePriceCost;
        $item->purchase_price_currency_id = $data->purchasePriceCurrency?->id;
        $item->is_purchase_price_guessed = $isGuessed;

        $item->save();
    }

    /**
     * @inheritDoc
     */
    public function resolveItemCommodityCodeTypes(Item $item, Fluent $data): void
    {
        $itemCommodityCodeTypes = $data->itemCommodityCodeTypes;
        foreach ($itemCommodityCodeTypes->toArray() as $itemCommodityCodeType) {

            $isDeleted = data_get($itemCommodityCodeType, 'isDeleted');

            if ($isDeleted && is_null($itemCommodityCodeType['id'])) {
                continue;
            }

            if ($isDeleted && $itemCommodityCodeType['id']) {
                $this->itemRepo->deleteItemCommodityCodeTypeById($itemCommodityCodeType['id']);
                continue;
            }

            $hasValue = $this->hasValue($itemCommodityCodeType['value']);
            if (!$hasValue && is_null($itemCommodityCodeType['id'])) {
                continue;
            }

            $itemCommodityCodeTypeModel = $this->itemRepo->emptyItemCommodityCodeType();
            if (!is_null($itemCommodityCodeType['id'])) {
                $itemCommodityCodeTypeModel = $this->itemRepo->getItemCommodityCodeTypeById($itemCommodityCodeType['id']);

                if (($itemCommodityCodeTypeModel->item_id !== $item->id) ||
                    ($itemCommodityCodeTypeModel->commodity_code_type_id !== $itemCommodityCodeType['commodityCodeTypeId'])
                ) {
                    throw new ItemCommodityCodeTypeDataException();
                }

                if (!is_null($itemCommodityCodeTypeModel) && !$hasValue) {
                    $this->itemRepo->deleteItemCommodityCodeTypeById($itemCommodityCodeType['id']);
                }

                if (is_null($itemCommodityCodeTypeModel)) {
                    $itemCommodityCodeTypeModel = $this->itemRepo->emptyItemCommodityCodeType();
                }
            }

            $this->storeItemCommodityCodeType(
                $itemCommodityCodeTypeModel,
                $item,
                $itemCommodityCodeType['commodityCodeTypeId'],
                $itemCommodityCodeType['value']
            );
        }
    }

    /**
     * @param ItemCommodityCodeType $itemCommodityCodeType
     * @param Item $item
     * @param int|null $commodityCodeTypeId
     * @param string|null $value
     */
    private function storeItemCommodityCodeType(
        ItemCommodityCodeType $itemCommodityCodeType,
        Item $item,
        ?int $commodityCodeTypeId,
        ?string $value = null
    ): void {
        $itemCommodityCodeType->value = $value;
        $itemCommodityCodeType->commodity_code_type_id = $commodityCodeTypeId;
        $itemCommodityCodeType->item_id = $item->id;

        $itemCommodityCodeType->save();
    }

    /**
     * @param array $marketplace
     * @param Item $item
     * @return ItemMarketplace
     * @throws ItemMarketplaceDataException
     */
    private function storeItemMarketplace(array $marketplace, Item $item): ItemMarketplace
    {
        $itemMarketplace = null;
        $itemMarketplaceId = data_get($marketplace, 'itemMarketplaceId');

        if (!is_null($itemMarketplaceId)) {
            $itemMarketplace = $this->itemRepo->getItemMarketplaceById($itemMarketplaceId);
            if (($itemMarketplace->item_id !== $item->id) ||
                ($itemMarketplace->marketplace_id !== $marketplace['id'])
            ) {
                throw new ItemMarketplaceDataException();
            }
        }

        if (is_null($itemMarketplaceId)) {
            $itemMarketplace = $this->itemRepo->getEmptyItemMarketplace();
        }

        $itemMarketplace->item_id = $item->id;
        $itemMarketplace->marketplace_id = $marketplace['id'];
        $itemMarketplace->description = $marketplace['description'];

        $itemMarketplace->save();

        return $itemMarketplace;
    }

    /**
     * @param array $itemItemIdentificationTypes
     * @param Item $item
     * @param ItemMarketplace $itemMarketplaces
     * @throws ItemIdentificationTypeDataException
     */
    private function storeItemItemIdentificationTypes(array $itemItemIdentificationTypes, Item $item, ItemMarketplace $itemMarketplaces): void
    {
        foreach ($itemItemIdentificationTypes as $itemItemIdentificationTypeData) {
            $shouldBeDeleted = data_get($itemItemIdentificationTypeData, 'isDeleted');
            if ($shouldBeDeleted && is_null($itemItemIdentificationTypeData['id'])) {
                continue;
            }

            if ($shouldBeDeleted && !is_null($itemItemIdentificationTypeData['id'])) {
                $item->itemItemIdentificationTypes()->find($itemItemIdentificationTypeData['id'])->delete();
                continue;
            }

            $hasValue = $this->hasValue($itemItemIdentificationTypeData['value']);
            if ($hasValue) {
                $itemItemIdentificationType = null;
                if (!is_null($itemItemIdentificationTypeData['id'])) {
                    $itemItemIdentificationType = $this->itemRepo->getItemItemIdentificationTypeById($itemItemIdentificationTypeData['id']);
                    if (($itemMarketplaces->id !== $itemItemIdentificationTypeData['itemMarketplaceId']) ||
                        ($itemItemIdentificationType->item_identification_type_id !== $itemItemIdentificationTypeData['itemIdentificationTypeId'])) {
                        throw new ItemIdentificationTypeDataException();
                    }
                }

                if (is_null($itemItemIdentificationTypeData['id'])) {
                    $itemItemIdentificationType = $this->itemRepo->getEmptyItemItemIdentificationTypeModel();
                }

                $itemItemIdentificationType->item_identification_type_id = $itemItemIdentificationTypeData['itemIdentificationTypeId'];
                $itemItemIdentificationType->value = $itemItemIdentificationTypeData['value'];
                $itemItemIdentificationType->item_marketplace_id = $itemMarketplaces->id;

                $itemItemIdentificationType->save();
            }
        }
    }

    /**
     * @param array $itemPurchasePriceData
     * @param ItemMarketplace $itemMarketplace
     */
    private function storePurchasePrices(array $itemPurchasePriceData, ItemMarketplace $itemMarketplace): void
    {
        $itemPurchasePrice = is_null($itemPurchasePriceData['id']) ?
            $this->itemRepo->getEmptyItemPurchasePrice() :
            $this->itemRepo->getItemPurchasePriceById($itemPurchasePriceData['id']);

        $netPrice = $this->cleanPriceValue($itemPurchasePriceData['netPrice']);
        $cost = $this->cleanPriceValue($itemPurchasePriceData['cost']);

        $itemPurchasePrice->currency_id = $itemPurchasePriceData['currency']['id'];
        $itemPurchasePrice->net_price = $netPrice;
        $itemPurchasePrice->cost = $cost;
        $itemPurchasePrice->start_date = $itemPurchasePriceData['startDate'];
        $itemPurchasePrice->item_marketplace_id = $itemMarketplace->id;

        $itemPurchasePrice->save();
    }

    /**
     * @param array $itemSalePriceData
     * @param ItemMarketplace $itemMarketplace
     */
    private function storeSalePrices(array $itemSalePriceData, ItemMarketplace $itemMarketplace): void
    {
        $itemSalePrice = is_null($itemSalePriceData['id']) ? $this->itemRepo->getEmptyItemSalePrice() : $this->itemRepo->getItemSalePriceById($itemSalePriceData['id']);

        $netPrice = $this->cleanPriceValue($itemSalePriceData['netPrice']);
        $grossPrice = $this->cleanPriceValue($itemSalePriceData['grossPrice']);

        $itemSalePrice->currency_id = $itemSalePriceData['currency']['id'];
        $itemSalePrice->net_price = $netPrice;
        $itemSalePrice->gross_price = $grossPrice;
        $itemSalePrice->start_date = $itemSalePriceData['startDate'];
        $itemSalePrice->item_marketplace_id = $itemMarketplace->id;

        $itemSalePrice->save();
    }

    /**
     * @param array $platforms
     * @param Item $item
     * @throws ItemIdentificationTypeDataException
     * @throws ItemMarketplaceDataException
     */
    private function resolveItemMarketplaces(array $platforms, Item $item)
    {
        foreach ($platforms as $platform) {
            $marketplace = data_get($platform, 'marketplace');
            $shouldBeDeleted = data_get($platform, 'isDeleted');
            $itemMarketplaceId = data_get($platform, 'marketplace.itemMarketplaceId');

            if ($shouldBeDeleted && is_null($itemMarketplaceId)) {
                continue;
            }

            if ($shouldBeDeleted && $itemMarketplaceId) {
                $this->itemRepo->deleteItemMarketplaceById($itemMarketplaceId, $item);

                continue;
            }

            $itemMarketplace = $this->storeItemMarketplace($marketplace, $item);

            $this->storeItemItemIdentificationTypes(
                $marketplace['itemItemIdentificationTypes'],
                $item,
                $itemMarketplace
            );

            $this->resolveItemPurchasePrices(
                $marketplace['itemPurchasePrices'],
                $itemMarketplace
            );

            $this->resolveItemSalePrices($marketplace['itemSalePrices'], $itemMarketplace);
        }
    }

    /**
     * @param array $itemPurchasePrices
     * @param ItemMarketplace $itemMarketplace
     */
    private function resolveItemPurchasePrices(array $itemPurchasePrices, ItemMarketplace $itemMarketplace): void
    {
        foreach ($itemPurchasePrices as $itemPurchasePrice) {

            $shouldBeDeleted = data_get($itemPurchasePrice, 'isDeleted');
            if ($shouldBeDeleted) {
                $this->itemPurchasePriceRepo->deleteItemPurchasePriceById($itemPurchasePrice['id']);
                continue;
            }

            $this->storePurchasePrices($itemPurchasePrice, $itemMarketplace);
        }
    }

    /**
     * @param array $itemSalePrices
     * @param ItemMarketplace $itemMarketplace
     */
    private function resolveItemSalePrices(array $itemSalePrices, ItemMarketplace $itemMarketplace): void
    {
        foreach ($itemSalePrices as $itemSalePrice) {
            $shouldBeDeleted = data_get($itemSalePrice, 'isDeleted');
            if ($shouldBeDeleted) {
                $this->itemSalePriceRepo->deleteItemSalePriceById($itemSalePrice['id']);
                continue;
            }

            $this->storeSalePrices($itemSalePrice, $itemMarketplace);
        }
    }

    /**
     * @param string|null $value
     * @param int $length
     * @return bool
     */
    private function hasValue(?string $value = null, int $length = 0): bool
    {
        if (is_null($value)) {
            return false;
        }
        $value = trim($value);

        return mb_strlen($value) > $length;
    }

    /**
     * @param string|null $price
     * @return float|null
     */
    private function cleanPriceValue(?string $price): ?float
    {
        if (is_null($price)) {
            return null;
        }

        $price = str_replace(',', '', $price);

        return (float)$price;
    }

    /**
     * @inheritDoc
     */
    public function resolveIdentifierData(bool $isCsvData, array $itemCommodityCodeTypes, array $platforms): array
    {
        $source = $isCsvData ? 'csv' : 'user';
        $identifierData = [];

        foreach ($itemCommodityCodeTypes as $itemCommodityCodeType) {
            if (data_get($itemCommodityCodeType, 'isDeleted')) {
                continue;
            }

            $hasValue = $this->hasValue($itemCommodityCodeType['value']);
            if ($hasValue) {
                $identifierData[] = [
                    'name'  => $itemCommodityCodeType['commodityCodeType']['name'],
                    'value' => $itemCommodityCodeType['value']
                ];
            }
        }

        foreach ($platforms as $platform) {
            $marketplace = $platform['marketplace'];
            foreach ($marketplace['itemItemIdentificationTypes'] as $itemItemIdentificationType) {
                if (data_get($itemItemIdentificationType, 'isDeleted')) {
                    continue;
                }

                $hasValue = $this->hasValue($itemItemIdentificationType['value']);
                if ($hasValue) {
                    $identifierData[] = [
                        'name'  => $itemItemIdentificationType['itemIdentificationType']['name'],
                        'value' => $itemItemIdentificationType['value']
                    ];
                }
            }
        }

        return collect([
            $source => $identifierData
        ])->toArray();
    }

    public function getItemEmptyModels(): DataTransferObject
    {
        $euro = $this->currencyRepo->getCurrencyById(Currency::EUR);

        $itemPurchasePrice = $this->itemRepo->getEmptyItemPurchasePrice();
        $itemPurchasePrice = transform_data($itemPurchasePrice, new ItemDefaultPurchasePriceTransformer($euro));

        $itemSalePrice = $this->itemRepo->getEmptyItemSalePrice();
        $itemSalePrice = transform_data($itemSalePrice, new ItemDefaultSalePriceTransformer($euro));

        $itemCommodityType = $this->itemRepo->emptyItemCommodityCodeType();
        $itemCommodityType = transform_data($itemCommodityType, new ItemCommodityCodeTypeTransformer());

        $brand = $this->brandRepo->getEmptyBrandModel();
        $brand = transform_data($brand, new ItemBrandTransformer());

        $identificationType = $this->itemRepo->getEmptyItemItemIdentificationTypeModel();
        $itemIdentificationType = transform_data($identificationType, new ItemItemIdentificationTypeTransformer());

        $itemMarketplace = $this->itemRepo->getEmptyItemMarketplace();
        $itemMarketplace = transform_data($itemMarketplace, new EmptyMarketplaceTransformer());

        $newEmptyData = new DataTransferObject();

        $newEmptyData->itemPurchasePrice = $itemPurchasePrice->toArray();
        $newEmptyData->itemSalePrice = $itemSalePrice->toArray();
        $newEmptyData->itemCommodityCode = $itemCommodityType->toArray();
        $newEmptyData->itemBrand = $brand->toArray();
        $newEmptyData->itemIdentificationType = $itemIdentificationType->toArray();
        $newEmptyData->itemMarketplace = $itemMarketplace->toArray();

        return $newEmptyData;
    }

    public function getItemActionUrls(): DataTransferObject
    {
        return new DataTransferObject([
            'autoSave'               => route('items.auto-save'),
            'deleteAutoSave'         => route('items.delete-auto-save'),
            'getItemCategories'      => route('items.product-categories'),
            'getCategoryBreadcrumbs' => route('items.product-category.breadcrumbs'),
            'brandSearch'            => route('items.brand.search')
        ]);
    }

    public function resolvePlatformLogos(): DataTransferObject
    {
        return new DataTransferObject([
            Platform::PLATFORM_AMAZON     => [
                'logo' => '/img/platforms/amazon.svg'
            ],
            Platform::PLATFORM_EBAY       => [
                'logo' => '/img/platforms/e-bay.svg'
            ],
            Platform::PLATFORM_SHOPIFY    => [
                'logo' => '/img/platforms/shopify.svg'
            ],
            Platform::PLATFORM_WEBSHOP    => [
                'logo' => null
            ],
            Platform::PLATFORM_ALIEXPRESS => [
                'logo' => '/img/platforms/aliexpress.svg'
            ],
            Platform::PLATFORM_ALIBABA    => [
                'logo' => '/img/platforms/alibaba.svg'
            ],
            Platform::PLATFORM_WISH       => [
                'logo' => '/img/platforms/wish.svg'
            ],
            Platform::PLATFORM_ETSY       => [
                'logo' => '/img/platforms/etsy.svg'
            ]
        ]);
    }

    private function fluentizeData(array $data): Fluent
    {
        $chunk = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $part = $this->fluentizeData($value);
            } else {
                $part = $value;
            }
            $chunk[$key] = $part;
        }

        return new Fluent($chunk);
    }

    public function buildIdentifiers(array $data, ?Item $item = null, string $source = 'user'): array
    {
        $sources = ['csv', 'user'];
        if (!in_array($source, $sources)) {
            throw new Exception('DEV exception - source not found. Pleas check or update sources list ($sources) in method ' . __METHOD__);
        }

        $commodityCodes = $this->itemRepo->getAllCommodityCodes();
        $itemIdentificationTypes = $this->itemRepo->getAllItemIdentificationTypes();
        $availableIdentifiers = $itemIdentificationTypes->concat($commodityCodes)
            ->pluck('name')
            ->toArray();

        $identifiers = $item?->identifiers ?? [];
        // Necessary so as not to create duplicates
        $sourceData = collect($identifiers[$source] ?? [])->keyBy('value');
        foreach ($data as $identifierData) {
            if (!in_array($identifierData['name'], $availableIdentifiers)) {
                throw new Exception('DEV exception - source not found. Pleas check or update item_identification_types or commodity_code_types table.');
            }
            $sourceData->put($identifierData['value'], $identifierData);
        }

        $identifiers[$source] = $sourceData->values()->toArray();

        return $identifiers;
    }

    public function buildIdentifiersJson(array $data, ?Item $item = null, string $source = 'user'): string
    {
        return json_encode($this->buildIdentifiers($data, $item, $source));
    }
}
