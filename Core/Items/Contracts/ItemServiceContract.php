<?php

namespace App\Core\Items\Contracts;

use App\Core\Common\Common\DataTransfer\DataTransferObject;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\Item;
use App\Core\Data\Models\Store;
use Exception;
use Illuminate\Support\Fluent;

interface ItemServiceContract
{
    public function storeItem(Company $company, Store $storeItem): Item;

    public function storeItemGeneralData(Company $company, Item $item, Fluent $data, array $identifiers);

    /**
     * @param Item $item
     * @param Fluent $data
     */
    public function resolveItemCommodityCodeTypes(Item $item, Fluent $data): void;

    /**
     * @param bool $isCsvData
     * @param array $itemCommodityCodeTypes
     * @param array $platforms
     * @return array
     */
    public function resolveIdentifierData(bool $isCsvData, array $itemCommodityCodeTypes, array $platforms): array;

    public function resolvePlatformLogos(): DataTransferObject;

    public function getItemEmptyModels(): DataTransferObject;

    public function getItemActionUrls(): DataTransferObject;

    /**
     * @param array $data -> name => value, where name === (commodity_code_types.name || item_identification_types.name
     * @param Item|null $item
     * @param string $source
     * @return array
     * @throws Exception
     */
    public function buildIdentifiers(array $data, ?Item $item = null, string $source = 'user'): array;

    /**
     * @param array $data -> name => value, where name === (commodity_code_types.name || item_identification_types.name
     * @param Item|null $item
     * @param string $source
     * @return string
     * @throws Exception
     */
    public function buildIdentifiersJson(array $data, ?Item $item = null, string $source = 'user'): string;
}
