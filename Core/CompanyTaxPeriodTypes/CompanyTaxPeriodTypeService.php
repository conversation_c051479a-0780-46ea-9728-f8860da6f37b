<?php

namespace App\Core\CompanyTaxPeriodTypes;

use App\Core\Common\Common\DataTransfer\DataTransferObject;
use App\Core\Common\History\History;
use App\Core\CompanyTaxPeriodTypes\Contracts\CompanyTaxPeriodTypeServiceContract;
use App\Core\Data\Models\TaxPeriodType;
use App\Core\Data\Repositories\Contracts\CompanyTaxPeriodTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxPeriodRepositoryContract;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class CompanyTaxPeriodTypeService implements CompanyTaxPeriodTypeServiceContract
{
    private CompanyTaxPeriodTypeRepositoryContract $companyTaxPeriodTypeRepo;
    private TaxPeriodRepositoryContract $taxPeriodRepo;
    private CountryRepositoryContract $countryRepo;

    public function __construct(
        CompanyTaxPeriodTypeRepositoryContract $companyTaxPeriodTypeRepo,
        TaxPeriodRepositoryContract $taxPeriodRepo,
        CountryRepositoryContract $countryRepo
    ) {

        $this->companyTaxPeriodTypeRepo = $companyTaxPeriodTypeRepo;
        $this->taxPeriodRepo = $taxPeriodRepo;
        $this->countryRepo = $countryRepo;
    }

    /**
     * @inheritDoc
     */
    public function storeUpdateDeleteCompanyTaxPeriodType(
        ?int $companyTaxPeriodTypeId,
        int $companyId,
        int $countryId,
        string $startDate,
        int $taxPeriodTypeId,
        bool $delete
    ): void {
        $countryName = $this->countryRepo->getCountryById($countryId)->name;
        $langParams = ['country' => $countryName];
        $actionUpdate = true;

        if ($companyTaxPeriodTypeId) {
            if ($delete) {
                $companyTaxPeriodType = $this->companyTaxPeriodTypeRepo->getCompanyTaxPeriodTypeById($companyTaxPeriodTypeId);
                History::deleted('history.Tax period types deleted for country', $companyTaxPeriodType, null, $langParams);

                $this->companyTaxPeriodTypeRepo->deleteCompanyTaxPeriodTypeById($companyTaxPeriodTypeId);

                return;
            } else {
                $companyTaxPeriodType = $this->companyTaxPeriodTypeRepo->getCompanyTaxPeriodTypeById($companyTaxPeriodTypeId);
            }
        } else {
            $companyTaxPeriodType = $this->companyTaxPeriodTypeRepo->getEmptyCompanyTaxPeriodTypeModel();
            $actionUpdate = false;
        }

        $startDate = Carbon::parse($startDate)->toDateString();

        if (
            $actionUpdate &&
            $companyTaxPeriodType->company_id === $companyId &&
            $companyTaxPeriodType->country_id === $countryId &&
            $companyTaxPeriodType->start_date === $startDate &&
            $companyTaxPeriodType->tax_period_type_id === $taxPeriodTypeId
        ) {
            return;
        }

        $companyTaxPeriodType->company_id = $companyId;
        $companyTaxPeriodType->country_id = $countryId;
        $companyTaxPeriodType->start_date = $startDate;
        $companyTaxPeriodType->tax_period_type_id = $taxPeriodTypeId;

        $companyTaxPeriodType->save();

        if ($actionUpdate) {
            History::updated('history.Tax period types updated for country', $companyTaxPeriodType, null, $langParams);
        } else {
            History::created('history.Tax period types created for country', $companyTaxPeriodType, null, $langParams);
        }
    }

    public function getCompanyTaxPeriods(
        int $companyId,
        string $startDate,
        string $endDate,
        array $countryIds
    ): Collection {
        $companyTaxPeriodTypes = $this->companyTaxPeriodTypeRepo
            ->getCompanyTaxPeriodTypes($companyId, $startDate)
            ->keyBy('country_id');

        $companyTaxPeriods = collect();
        foreach ($countryIds as $countryId) {
            /**
             * @var \App\Core\Data\Models\CompanyTaxPeriodType $taxPeriodType
             */
            $taxPeriodType = $companyTaxPeriodTypes->get($countryId);
            $taxPeriodTypeId = TaxPeriodType::M;
            if (!is_null($taxPeriodType)) {
                $taxPeriodTypeId = $taxPeriodType->tax_period_type_id;
            }
            //@todo get tax periods based on tax period type and start and end date
            $taxPeriods = $this->taxPeriodRepo
                ->getTaxPeriodFromDatesAndTaxPeriodType(
                    $startDate,
                    $endDate,
                    $taxPeriodTypeId
                );
            foreach ($taxPeriods as $taxPeriod) {
                $ctp = new DataTransferObject();
                $ctp->countryId = $countryId;
                $ctp->taxPeriodId = $taxPeriod->id;

                $companyTaxPeriods->push($ctp);
            }
        }

        return $companyTaxPeriods;
    }
}
