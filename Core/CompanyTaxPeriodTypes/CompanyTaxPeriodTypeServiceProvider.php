<?php

namespace App\Core\CompanyTaxPeriodTypes;

use App\Core\CompanyTaxPeriodTypes\Contracts\CompanyTaxPeriodTypeServiceContract;
use App\Core\System\Providers\CoreModuleServiceProvider;

class CompanyTaxPeriodTypeServiceProvider extends CoreModuleServiceProvider
{
    protected function resolve(): void
    {
        $this->bind(CompanyTaxPeriodTypeServiceContract::class, CompanyTaxPeriodTypeService::class);
    }
}
