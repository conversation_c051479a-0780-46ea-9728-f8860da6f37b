<?php

namespace App\Core\CompanyTaxPeriodTypes\Contracts;

use Illuminate\Support\Collection;

interface CompanyTaxPeriodTypeServiceContract
{
    /**
     * @param int $companyTaxPeriodTypeId |null
     * @param int $companyId
     * @param int $countryId
     * @param string $startDate
     * @param int $taxPeriodTypeId
     * @param bool $delete
     * @return void
     */
    public function storeUpdateDeleteCompanyTaxPeriodType(?int $companyTaxPeriodTypeId, int $companyId, int $countryId, string $startDate, int $taxPeriodTypeId, bool $delete): void;

    public function getCompanyTaxPeriods(
        int $companyId,
        string $startDate,
        string $endDate,
        array $countryIds
    ): Collection;
}
