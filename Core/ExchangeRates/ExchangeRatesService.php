<?php

namespace App\Core\ExchangeRates;

use App\Core\Data\Models\Currency;
use App\Core\Data\Models\ExchangeRate;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\ExchangeRatesRepositoryContract;
use App\Core\ExchangeRates\Api\FixerApi;
use App\Core\ExchangeRates\Contracts\ExchangeRatesServiceContract;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ExchangeRatesService implements ExchangeRatesServiceContract
{
    private ExchangeRatesRepositoryContract $exchangeRatesRepo;
    private CurrencyRepositoryContract $currencyRepo;

    public function __construct(
        ExchangeRatesRepositoryContract $exchangeRatesRepo,
        CurrencyRepositoryContract $currencyRepo
    ) {
        $this->exchangeRatesRepo = $exchangeRatesRepo;
        $this->currencyRepo = $currencyRepo;
    }

    /**
     * @inheritDoc
     */
    public function getExchangeRatesForDates(array $dates): Collection
    {
        $rates = $this->exchangeRatesRepo->getRatesForDates($dates)
            ->groupBy('date')
            ->map(function (Collection $rates) {
                return $rates->keyBy('currency_id');
            });

        foreach ($dates as $date) {
            if (!$rates->has($date)) {
                $rates->put($date, $this->calculateAverageForMonth(Carbon::parse($date)));
            }
        }

        return $rates;
    }

    /**
     * @inheritDoc
     */
    public function convertFromEuro(float $value, ?ExchangeRate $exchangeRate = null): float
    {
        if (is_null($exchangeRate)) { //Euro
            return $value;
        }

        $ratio = $exchangeRate->value;

        return $value * $ratio;

    }

    /**
     * @inheritDoc
     */
    public function convertToEuro(float $value, ?ExchangeRate $exchangeRate = null): float
    {
        if (is_null($exchangeRate)) { //Euro
            return $value;
        }

        $ratio = $exchangeRate->value;

        return $value / $ratio;
    }

    /**
     * @inheritDoc
     */
    public function convertCurrencies(float $value, ?ExchangeRate $from = null, ?ExchangeRate $to = null): float
    {
        if ((is_null($from) && is_null($to)) || (!is_null($from) && !is_null($to) && ($from->currency_id === $to->currency_id))) {
            return $value;
        }
        $eurValue = $this->convertToEuro($value, $from);

        return $this->convertFromEuro($eurValue, $to);
    }

    /**
     * @inheritDoc
     */
    public function calculateAverageForMonth(Carbon $date): Collection
    {
        $start = $date->startOfMonth()->toDateTimeString();
        $end = $date->clone()->endOfMonth()->toDateTimeString();
        $date = $date->toDateString();

        return $this->exchangeRatesRepo->getRatesBetweenDates($start, $end)
            ->groupBy('currency_id')
            ->map(function (Collection $exchangeRates) use ($date) {
                $exchangeRate = $exchangeRates->first();
                $exchangeRate->value = $exchangeRates->avg('value');
                $exchangeRate->date = $date;

                return $exchangeRate;
            });
    }

    public function getAndStoreNewExchangeRatesFromApi(): void
    {
        $fixerApi = new FixerApi();
        $start = $this->exchangeRatesRepo->getLastDate();
        $end = Carbon::now()
            ->toDateString();

        $response = $fixerApi->getExchangeRateForPeriod($start, $end);
        foreach ($response as $item) {
            if ($item['date'] !== $start) {
                $this->setExchangeRates(
                    $item['date'],
                    $item['rates']
                );
            }
        }
    }

    /**
     * @inheritDoc
     */
    public function getAndStoreExchangeRatesFromApiForDate(string $date): void
    {
        $fixerApi = new FixerApi();
        $response = $fixerApi->getExchangeRateForDate($date);
        $date = $response['date'];
        $rates = $response['rates'];

        $this->setExchangeRates($date, $rates);
    }

    /**
     * @param string $date
     * @param array $rates
     */
    private function setExchangeRates(string $date, array $rates): void
    {
        $currencies = $this->currencyRepo
            ->getAllCurrencies()
            ->keyBy('code');

        foreach ($rates as $key => $rate) {
            /**
             * @var Currency $currency
             */
            $currency = $currencies->get($key);
            if (!is_null($currency)) {
                $this->storeExchangeRate($date, $currency->id, $rate);
            }
        }
    }

    /**
     * @inheritDoc
     */
    public function storeExchangeRate(string $date, int $currencyId, float $value): void
    {
        $exchangeRate = $this->exchangeRatesRepo->getEmptyExchangeRateModel();
        $exchangeRate->date = $date;
        $exchangeRate->currency_id = $currencyId;
        $exchangeRate->value = $value;

        $exchangeRate->save();
    }

    /**
     * @inheritDoc
     */
    public function refreshExchangeRates(bool $dryRun = true): void
    {
        set_time_limit(60 * 30);
        ini_set('memory_limit', '-1');

        if (!$dryRun) {
            DB::statement('TRUNCATE  exchange_rates RESTART IDENTITY CASCADE;');
            DB::statement('VACUUM exchange_rates;');
        }
        $fixerApi = new FixerApi();
        $startYear = 2016;
        $endDate = Carbon::now()->subDays();
        $endYear = $endDate->year;
        $yearsRange = range($startYear, $endYear);
        $years = [];
        foreach ($yearsRange as $year) {
            $year = Carbon::parse($year . '-01-01');
            $start = $year->startOfYear()->toDateString();
            $end = $year->endOfYear()->toDateString();
            if ($year->year === $endYear) {
                $end = $endDate->toDateString();
            }
            $years[$year->year] = [
                'start' => $start,
                'end'   => $end,
            ];
        }

        $insertData = [];
        $currencies = $this->currencyRepo->getAllCurrencies()
            ->keyBy('code')
            ->pluck('id', 'code');

        foreach ($years as $year => $dates) {
            $data = $fixerApi->getHistoryExchangeRateForPeriod($dates['start'], $dates['end']);
            foreach ($data['rates'] as $date => $dateCurrencies) {
                $month = Carbon::parse($date)->month;
                foreach ($dateCurrencies as $code => $value) {
                    $currencyId = $currencies->get($code);
                    $insertData[$year][$month][] = [
                        'date'        => $date,
                        'currency_id' => $currencyId,
                        'value'       => $value
                    ];
                }
            }
        }
        $insertData = collect($insertData);

        if (!$dryRun) {
            foreach ($insertData as $months) {
                foreach ($months as $month) {
                    ExchangeRate::insert($month);
                }
            }
        }

        dd('Done ' . ($dryRun ? 'dry' : 'real'));
    }
}
