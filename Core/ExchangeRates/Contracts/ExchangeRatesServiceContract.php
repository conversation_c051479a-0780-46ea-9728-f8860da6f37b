<?php

namespace App\Core\ExchangeRates\Contracts;

use App\Core\Data\Models\ExchangeRate;
use Carbon\Carbon;
use Illuminate\Support\Collection;

interface ExchangeRatesServiceContract
{
    /**
     * @param array $dates
     * @return Collection
     */
    public function getExchangeRatesForDates(array $dates): Collection;

    /**
     * @param float $value
     * @param ExchangeRate|null $exchangeRate
     * @return float
     */
    public function convertFromEuro(float $value, ?ExchangeRate $exchangeRate = null): float;

    /**
     * @param float $value
     * @param ExchangeRate|null $exchangeRate
     * @return float
     */
    public function convertToEuro(float $value, ?ExchangeRate $exchangeRate = null): float;

    /**
     * @param float $value
     * @param ExchangeRate|null $from
     * @param ExchangeRate|null $to
     * @return float
     */
    public function convertCurrencies(float $value, ?ExchangeRate $from = null, ?ExchangeRate $to = null): float;

    /**
     * @param Carbon $date
     * @return Collection
     */
    public function calculateAverageForMonth(Carbon $date): Collection;

    public function getAndStoreNewExchangeRatesFromApi(): void;

    /**
     * @param string $date
     */
    public function getAndStoreExchangeRatesFromApiForDate(string $date): void;

    /**
     * @param string $date
     * @param int $currencyId
     * @param float $value
     */
    public function storeExchangeRate(
        string $date,
        int $currencyId,
        float $value
    ): void;

    /**
     * DANGER
     * If dry run false, will truncate exchange rates table
     *
     * @param bool $dryRun
     */
    public function refreshExchangeRates(bool $dryRun = true): void;
}
