<?php

namespace App\Core\ExchangeRates\Api;

use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use GuzzleHttp\Client;


class FixerApi
{
    private function getFixerApiBaseUrl(): string
    {
        return config('evat.fixerApi.url');
    }

    public function getLatestExchangeRate(): Collection
    {
        $date = Carbon::now()
            ->subDays(1)
            ->toDateString();

        return $this->makeRequest($date);
    }

    public function getExchangeRateForDate(string $date): Collection
    {
        return $this->makeRequest($date);
    }

    public function getExchangeRateForPeriod(string $startDate, string $endDate): Collection
    {
        $dates = CarbonPeriod::dates($startDate, $endDate);
        $results = collect();
        foreach ($dates as $date) {
            $results->push(
                $this->makeRequest(
                    $date->format('Y-m-d')
                ));
        }

        return $results;
    }

    private function makeRequest(string $date): Collection
    {
        $token = config('evat.fixerApi.token');

        $client = new Client();
        $response = $client->request('GET',
            $this->getFixerApiBaseUrl() . '/api/' . $date,
            [
                'query' => ['access_key' => $token]
            ]
        )
            ->getBody()
            ->getContents();
        $response = json_decode($response, true);

        return collect($response);
    }

    public function getHistoryExchangeRateForPeriod(mixed $start, mixed $end): Collection
    {
        $token = config('evat.fixerApi.token');

        $client = new Client();
        $response = $client->request('GET',
            $this->getFixerApiBaseUrl() . '/api/timeseries',
            [
                'query' => [
                    'access_key' => $token,
                    'start_date' => $start,
                    'end_date'   => $end,
                ]
            ]
        )
            ->getBody()
            ->getContents();
        $response = json_decode($response, true);

        return collect($response);
    }
}
