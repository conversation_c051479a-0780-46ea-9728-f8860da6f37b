<?php

namespace App\Core\CompanyTaxPayment;

use App\Core\CompanyTaxPayment\Contracts\CompanyTaxPaymentServiceContract;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\TaxSchemeName;
use App\Core\Data\Repositories\Contracts\CompanyTaxPaymentRepositoryContract;
use App\Core\Data\Repositories\Contracts\PaymentRecipientsRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxSchemesRepositoryContract;
use Illuminate\Support\Collection;

class CompanyTaxPaymentService implements CompanyTaxPaymentServiceContract
{
    private TaxSchemesRepositoryContract $taxSchemesRepo;
    private PaymentRecipientsRepositoryContract $paymentRecipientsRepo;
    private CompanyTaxPaymentRepositoryContract $companyTaxPaymentRepo;

    /**
     * @param TaxSchemesRepositoryContract $taxSchemesRepo
     * @param PaymentRecipientsRepositoryContract $paymentRecipientsRepo
     * @param CompanyTaxPaymentRepositoryContract $companyTaxPaymentRepo
     */
    public function __construct(
        TaxSchemesRepositoryContract $taxSchemesRepo,
        PaymentRecipientsRepositoryContract $paymentRecipientsRepo,
        CompanyTaxPaymentRepositoryContract $companyTaxPaymentRepo
    ) {
        $this->taxSchemesRepo = $taxSchemesRepo;
        $this->paymentRecipientsRepo = $paymentRecipientsRepo;
        $this->companyTaxPaymentRepo = $companyTaxPaymentRepo;
    }

    /**
     * @inheritDoc
     */
    public function hasPaymentMethod(int $countryId, Company $company): Collection
    {
        $taxSchemeCountry = $this->taxSchemesRepo->getTaxSchemeByCountryId($countryId);
        $taxSchemeNameVat = !is_null($taxSchemeCountry->where('tax_scheme_name_id', '=', TaxSchemeName::VALUE_ADDED_TAX)->first());
        $taxSchemeNameOss = !is_null($taxSchemeCountry->where('tax_scheme_name_id', '=', TaxSchemeName::ONE_STOP_SHOP)->first());
        $taxSchemeNameIOss = !is_null($taxSchemeCountry->where('tax_scheme_name_id', '=', TaxSchemeName::IMPORT_ONE_STOP_SHOP)->first());

        $manualPayment = $this->paymentRecipientsRepo
            ->getPaymentRecipientByCountryIdAndCompanyId($countryId, $company->id)
            ->load('taxScheme');

        $manualPaymentBankVat = !is_null($manualPayment->where('taxScheme.tax_scheme_name_id', '=', TaxSchemeName::VALUE_ADDED_TAX)->first());
        $manualPaymentBankOss = !is_null($manualPayment->where('taxScheme.tax_scheme_name_id', '=', TaxSchemeName::ONE_STOP_SHOP)->first());
        $manualPaymentBankIOss = !is_null($manualPayment->where('taxScheme.tax_scheme_name_id', '=', TaxSchemeName::IMPORT_ONE_STOP_SHOP)->first());

        $taxOffice = $company->taxOffices->where('country_id', '=', $countryId)->first();

        /**
         * @var \App\Core\Data\Models\InstitutionTypeCountryPartner $accountant
         */
        $accountant = $company->accountants
            ->where('country_id', $countryId)
            ->first();

        $accountant = $accountant?->accountant?->institution;

        $hasTaxOfficeBank = !is_null($taxOffice?->bank);
        $hasAccountantBank = !is_null($accountant?->bank);

        $hasPaymentMethod = [
            'taxOffice'  => [
                'vatTaxOffice'  => $hasTaxOfficeBank && $taxSchemeNameVat,
                'ossTaxOffice'  => $hasTaxOfficeBank && $taxSchemeNameOss,
                'iossTaxOffice' => $hasTaxOfficeBank && $taxSchemeNameIOss
            ],
            'accountant' => [
                'vatAccountant'  => $hasAccountantBank && $taxSchemeNameVat,
                'ossAccountant'  => $hasAccountantBank && $taxSchemeNameOss,
                'iossAccountant' => $hasAccountantBank && $taxSchemeNameIOss
            ],
            'manual'     => [
                'vatManual'  => $manualPaymentBankVat,
                'ossManual'  => $manualPaymentBankOss,
                'iossManual' => $manualPaymentBankIOss
            ]
        ];

        return collect($hasPaymentMethod);
    }

    /**
     * @inheritDoc
     */
    public function storePaymentMethod(int $companyId, array $taxPayments): void
    {
        foreach ($taxPayments as $taxPayment) {
            $payment = $this->companyTaxPaymentRepo->getEmptyCompanyTaxPaymentModel();

            if (!is_null($taxPayment['id'])) {
                $payment = $this->companyTaxPaymentRepo->getCompanyTaxPaymentById($taxPayment['id']);
            }

            if (!is_null($taxPayment['taxPaymentReference'])) {
                $payment->company_id = $companyId;
                $payment->country_id = $taxPayment['countryId'];
                $payment->tax_payment_reference_id = $taxPayment['taxPaymentReference'];
                $payment->tax_payment_type_id = $taxPayment['taxPaymentType'];
                $payment->save();
            }
        }
    }
}
