<?php

namespace App\Core\CompanyTaxPayment\Contracts;

use App\Core\Data\Models\Company;
use Illuminate\Support\Collection;

interface CompanyTaxPaymentServiceContract
{
    /**
     * @param int $countryId
     * @param Company $company
     * @return Collection
     */
    public function hasPaymentMethod(int $countryId, Company $company): Collection;

    /**
     * @param int $companyId
     * @param array $taxPayments
     * @return void
     */
    public function storePaymentMethod(int $companyId, array $taxPayments): void;

}
