<?php

namespace App\Core\Banks;

use App\Core\Banks\Contracts\BankServiceContract;
use App\Core\Common\History\History;
use App\Core\Data\Models\Bank;
use App\Core\Data\Repositories\Contracts\BankRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;

class BankService implements BankServiceContract
{
    /**
     * @var BankRepositoryContract
     */
    private $bankRepo;
    /**
     * @var CompanyRepositoryContract
     */
    private $companyRepo;

    /**
     * BankService constructor.
     *
     * @param BankRepositoryContract $bankRepo
     */
    public function __construct(BankRepositoryContract $bankRepo, CompanyRepositoryContract $companyRepo)
    {
        $this->bankRepo = $bankRepo;
        $this->companyRepo = $companyRepo;
    }

    /**
     * @inheritDoc
     */
    public function storeUpdateBank(
        string $name,
        string $beneficiary,
        string $swift,
        ?string $iban = null,
        ?string $accountNumber = null,
        ?string $routingNumber = null,
        ?int $bankId = null,
        ?int $companyId = null,
        ?int $institutionId = null,
        ?bool $ibanValid = null,
        ?bool $ibanValidated = null,
        ?string $branchName = null,
        ?string $accountOwner = null
    ): ?Bank {
        if (is_null($bankId)) {
            $bank = $this->bankRepo->getEmptyBankModel();
            $bankDetailsEdited = false;
        } else {
            $bank = $this->bankRepo->getBankById($bankId);
            if (is_null($bank)) {
                return null;
            }
            $bankDetailsEdited = true;
        }

        if (!is_null($companyId)) {
            $company = $this->companyRepo->getCompanyById($companyId)->load('bank');
            if (is_null($company)) {
                return null;
            }
        }

        $name = $this->emptyStringToNull($name);
        $beneficiary = $this->emptyStringToNull($beneficiary);
        $iban = $this->emptyStringToNull($iban);
        $accountNumber = $this->emptyStringToNull($accountNumber);
        $routingNumber = $this->emptyStringToNull($routingNumber);
        $swift = $this->emptyStringToNull($swift);
        $branchName = $this->emptyStringToNull($branchName);

        $bank->name = $name;
        $bank->branch_name = $branchName;
        $bank->beneficiary = $beneficiary;
        $bank->iban = $iban;
        $bank->account_number = $accountNumber;
        $bank->routing_number = $routingNumber;
        $bank->swift = $swift;
        $bank->company_id = $companyId;
        $bank->institution_id = $institutionId;
        $bank->account_owner_name = $accountOwner;

        if (!is_null($ibanValid)) {
            $bank->iban_valid = $ibanValid;
        }
        if (!is_null($ibanValidated)) {
            $bank->iban_validated = $ibanValidated;
        }

        $bank->save();

        if (!is_null($companyId)) {
            if ($bankDetailsEdited) {
                History::updated('history.Bank details edited', $bank);
            } else {
                History::updated('history.Bank details created', $bank);
            }
        }

        return $bank;
    }

    /**
     * @param string|null $string
     * @return string|null
     */
    private function emptyStringToNull(?string $string = null): ?string
    {
        if (is_null($string) || !is_string($string)) {
            return null;
        }

        $string = trim($string);
        if (mb_strlen($string) < 1) {
            return null;
        }

        return $string;
    }
}
