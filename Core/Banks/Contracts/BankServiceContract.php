<?php

namespace App\Core\Banks\Contracts;

use App\Core\Data\Models\Bank;

interface BankServiceContract
{
    public function storeUpdateBank(
        string $name,
        string $beneficiary,
        string $swift,
        ?string $iban = null,
        ?string $accountNumber = null,
        ?string $routingNumber = null,
        ?int $bankId = null,
        ?int $companyId = null,
        ?int $institutionId = null,
        ?bool $ibanValid = null,
        ?bool $ibanValidated = null,
        ?string $branchName = null,
        ?string $accountOwner = null
    ): ?Bank;
}
