<?php

namespace App\Core\Customers\DataTransfer;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;


class CustomerTransferObject implements Arrayable
{
    /**
     * @var int
     */
    private $companyId;
    /**
     * @var int|null
     */
    private $id;
    /**
     * @var string|null
     */
    private $companyName;
    /**
     * @var string|null
     */
    private $firstName;
    /**
     * @var string|null
     */
    private $lastName;
    /**
     * @var string|null
     */
    private $additionalData;
    /**
     * @var array|null
     */
    private $contacts;
    /**
     * @var Collection
     */
    private $addresses;
    /**
     * @var bool
     */
    private $isPerson;
    /**
     * @var array|null
     */
    private $vatNumbers;

    private $taxNumbers;

    private $voesNumber;

    /**
     * CustomerTransferObject constructor.
     *
     * @param int $companyId
     * @param bool $isPerson
     * @param int|null $id
     * @param string|null $companyName
     * @param string|null $firstName
     * @param string|null $lastName
     * @param string|null $additionalData
     * @param array|null $contacts
     * @param array|null $addresses
     * @param array|null $vatNumbers
     */
    public function __construct(
        int $companyId,
        bool $isPerson = false,
        ?int $id = null,
        ?string $companyName = null,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $additionalData = null,
        ?array $contacts = [],
        ?array $addresses = [],
        ?array $vatNumbers = [],
        ?array $taxNumbers = [],
        ?array $voesNumber = [],
    ) {
        $this->companyId = $companyId;
        $this->isPerson = $isPerson;
        $this->id = $id;
        $this->companyName = $companyName;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->additionalData = $additionalData;
        $this->contacts = collect($contacts);
        $this->addresses = collect($addresses);
        $this->vatNumbers = collect($vatNumbers);
        $this->taxNumbers = collect($taxNumbers);
        $this->voesNumber = collect($voesNumber);
    }

    public function toArray()
    {
        return [
            'id'             => $this->getId(),
            'isSaved'        => $this->isSaved(),
            'firstName'      => $this->getFirstName(),
            'lastName'       => $this->getLastName(),
            'companyName'    => $this->getCompanyName(),
            'additionalData' => $this->getAdditionalData(),
            'contacts'       => $this->getContacts()->toArray(),
            'addresses'      => $this->getAddresses()->toArray(),
            'isPerson'       => $this->isPerson(),
            'vatNumbers'     => $this->getVatNumbers()->toArray(),
            'taxNumbers'     => $this->getTaxNumbers()->toArray(),
            'voesNumber'     => $this->getVoesNumber()->toArray()
        ];
    }

    public function isSaved(): bool
    {
        return !is_null($this->getId());
    }

    /**
     * @return bool
     */
    public function isPerson(): bool
    {
        return $this->isPerson;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    /**
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    /**
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    /**
     * @return string|null
     */
    public function getAdditionalData(): ?string
    {
        return $this->additionalData;
    }

    /**
     * @return Collection|null
     */
    public function getContacts(): ?Collection
    {
        return $this->contacts;
    }

    /**
     * @return Collection|null
     */
    public function getAddresses(): ?Collection
    {
        return $this->addresses;
    }

    /**
     * @return Collection|null
     */
    public function getVatNumbers(): ?Collection
    {
        return $this->vatNumbers;
    }

    public function getTaxNumbers(): ?Collection
    {
        return $this->taxNumbers;
    }

    public function getVoesNumber(): ?Collection
    {
        return $this->voesNumber;
    }
}
