<?php

namespace App\Core\Customers;

use App\Core\Customers\Contracts\CustomerServiceContract;
use App\Core\Customers\DataTransfer\CustomerTransferObject;
use App\Core\Data\Models\IdentificationNumber;
use App\Core\Data\Models\IdentificationNumberType;
use App\Core\Data\Repositories\Contracts\AddressRepositoryContract;
use App\Core\Data\Repositories\Contracts\CityRepositoryContract;
use App\Core\Data\Repositories\Contracts\ContactRepositoryContract;
use App\Core\Data\Repositories\Contracts\CustomerRepositoryContract;
use App\Core\Data\Models\Customer;
use App\Core\Data\Repositories\Contracts\IdentificationNumberRepositoryContract;

class CustomerService implements CustomerServiceContract
{
    private CustomerRepositoryContract $customerRepo;
    private ContactRepositoryContract $contactRepo;
    private AddressRepositoryContract $addressRepo;
    private CityRepositoryContract $cityRepo;
    private IdentificationNumberRepositoryContract $identificationNumberRepo;

    public function __construct(
        CustomerRepositoryContract $customerRepo,
        ContactRepositoryContract $contactRepo,
        AddressRepositoryContract $addressRepo,
        CityRepositoryContract $cityRepo,
        IdentificationNumberRepositoryContract $identificationNumberRepo,
    ) {
        $this->customerRepo = $customerRepo;
        $this->contactRepo = $contactRepo;
        $this->addressRepo = $addressRepo;
        $this->cityRepo = $cityRepo;
        $this->identificationNumberRepo = $identificationNumberRepo;
    }

    /**
     * @inheritDoc
     */
    public function storeCustomer(
        int $companyId,
        ?string $companyName,
        ?string $firstName
    ): Customer {
        $customer = $this->customerRepo->getEmptyCustomerModel();
        $customer->company_id = $companyId;
        $customer->company_name = $companyName;
        $customer->first_name = $firstName;

        $customer->save();

        return $customer;
    }

    /**
     * @inheritDoc
     */
    public function fullStoreUpdateCustomer(
        CustomerTransferObject $customerTransfer
    ): Customer {
        $customer = $this->customerRepo->getEmptyCustomerModel();
        if ($customerTransfer->isSaved()) {
            $customer = $this->customerRepo->getCustomerById($customerTransfer->getId());
        }

        $customer->company_id = $customerTransfer->getCompanyId();
        $customer->company_name = $customerTransfer->getCompanyName();
        $customer->first_name = $customerTransfer->getFirstName();
        $customer->last_name = $customerTransfer->getLastName();
        $customer->additional_data = $customerTransfer->getAdditionalData();
        $customer->is_person = $customerTransfer->isPerson();
        $customer->save();

        {
            // Save contacts
            $contacts = $customerTransfer->getContacts();
            foreach ($contacts as $contact) {
                $value = $contact['contact']['value'];

                if (!is_null($value)) {
                    $this->saveContact(
                        $contact['id'],
                        $customer->id,
                        $value,
                        $contact['contact']['id']
                    );
                }
            }
        }

        {
            // Save addresses
            $addresses = $customerTransfer->getAddresses();
            foreach ($addresses as $address) {

                $this->saveAddresses(
                    $customer->id,
                    $address['street'],
                    $address['houseNumber'],
                    $address['cityName'],
                    $address['postalCode'],
                    $address['country']['id'],
                    $address['addressType']['id'],
                    $address['addressId'],
                    $address['state'],
                    $address['addition'],
                    $address['id']
                );
            }
        }

        // Save Vat numbers
        $vatNumbers = $customerTransfer->getVatNumbers();
        if (!$customer->is_person && $vatNumbers->isNotEmpty()) {
            foreach ($vatNumbers as $vatNumber) {
                $this->saveVatNumber(
                    $customer->id,
                    $vatNumber['country']['vatCode'] . $vatNumber['vatNumber'],
                    $vatNumber['country']['id'],
                    $vatNumber['delete'],
                    $vatNumber['id']
                );
            }
        }

        // Save Tax numbers
        $taxNumbers = $customerTransfer->getTaxNumbers();
        if (!$customer->is_person && $taxNumbers->isNotEmpty()) {
            foreach ($taxNumbers as $taxNumber) {
                $this->saveTaxNumber(
                    $customer->id,
                    $taxNumber['country']['vatCode'] . $taxNumber['taxNumber'],
                    $taxNumber['country']['id'],
                    $taxNumber['delete'],
                    $taxNumber['id']
                );
            }
        }

        // Save VOES number
        $voesNumber = $customerTransfer->getVoesNumber();
        if (!$customer->is_person && ($voesNumber['voesNumber']) || $voesNumber['delete']) {
            $this->saveVoesNumber(
                $customer->id,
                $voesNumber['voesNumber'],
                $voesNumber['delete'],
                $voesNumber['id']
            );
        }

        return $customer;
    }

    private function saveIdentificationNumber(
        int $customerId,
        bool $delete,
        ?int $countryId,
        ?int $customerIdentificationNumberId = null
    ): ?IdentificationNumber {
        if (!is_null($customerIdentificationNumberId) && $delete) {
            $this->identificationNumberRepo->deleteById($customerIdentificationNumberId);

            return null;
        }

        $customerIdentificationNumber = $this->identificationNumberRepo->getEmptyModel();
        if (!is_null($customerIdentificationNumberId)) {
            $customerIdentificationNumber = $this->identificationNumberRepo->getById($customerIdentificationNumberId);
        }

        $customerIdentificationNumber->resource_id = $customerId;
        $customerIdentificationNumber->resource = Customer::class;
        $customerIdentificationNumber->country_id = $countryId;

        return $customerIdentificationNumber;
    }

    private function saveVatNumber(
        int $customerId,
        string $vatNumber,
        int $countryId,
        bool $delete,
        ?int $customerVatNumberId = null
    ): void {
        $customerVatNumber = $this->saveIdentificationNumber($customerId, $delete, $countryId, $customerVatNumberId);
        if (!$customerVatNumber) {
            return;
        }

        $customerVatNumber->identification_number_type_id = IdentificationNumberType::VAT_NUMBER;
        $customerVatNumber->value = $vatNumber;
        $customerVatNumber->save();
    }

    private function saveTaxNumber(
        int $customerId,
        string $taxNumber,
        int $countryId,
        bool $delete,
        ?int $customerTaxNumberId = null
    ): void {
        $customerTaxNumber = $this->saveIdentificationNumber($customerId, $delete, $countryId, $customerTaxNumberId);
        if (!$customerTaxNumber) {
            return;
        }

        $customerTaxNumber->identification_number_type_id = IdentificationNumberType::TAX_NUMBER;
        $customerTaxNumber->value = $taxNumber;
        $customerTaxNumber->save();
    }

    private function saveVoesNumber(
        int $customerId,
        ?string $voesNumber,
        ?bool $delete,
        ?int $customerVoesNumberId = null
    ): void {
        $customerVoesNumber = $this->saveIdentificationNumber($customerId, $delete, null, $customerVoesNumberId);
        if (!$customerVoesNumber) {
            return;
        }

        $customerVoesNumber->identification_number_type_id = IdentificationNumberType::OSS_NUMBER;
        $customerVoesNumber->value = $voesNumber;
        $customerVoesNumber->save();
    }

    private function saveAddresses(
        int $customerId,
        string $street,
        string $houseNumber,
        string $cityName,
        string $postalCode,
        int $countryId,
        int $addressTypeId,
        ?int $addressId = null,
        ?string $state = null,
        ?string $addition = null,
        ?int $customerAddressId = null
    ): void {
        $address = $this->addressRepo->getEmptyAddressModel();
        if (!is_null($addressId)) {
            $address = $this->addressRepo->getAddressById($addressId);
        }

        $city = $this->cityRepo
            ->createCityIfNotExists(
                $cityName,
                $countryId
            );

        $address->street = $street;
        $address->house_number = $houseNumber;
        $address->postal_code = $postalCode;
        $address->country_id = $countryId;
        $address->city_id = $city->id;
        $address->state = $state;
        $address->addition = $addition;
        $address->save();


        $this->saveCustomerAddresses(
            $customerId,
            $address->id,
            $addressTypeId,
            $customerAddressId
        );
    }

    private function saveCustomerAddresses(
        int $customerId,
        int $addressId,
        int $addressTypeId,
        ?int $customerAddressId
    ): void {
        $customerAddress = $this->customerRepo->getEmptyCustomerAddressModel();
        if (!is_null($customerAddressId)) {
            $customerAddress = $this->customerRepo->getCustomerAddressById($customerAddressId);
        }

        $customerAddress->customer_id = $customerId;
        $customerAddress->address_id = $addressId;
        $customerAddress->address_type_id = $addressTypeId;

        $customerAddress->save();
    }

    private function saveContact(
        int $contactTypeId,
        int $customerId,
        string $value,
        ?int $contactId = null
    ): void {
        $value = trim($value);
        $contact = $this->contactRepo->getEmptyContactModel();
        if (!is_null($contactId)) {
            $contact = $this->contactRepo->getContactById($contactId);
        }

        $contact->contact_type_id = $contactTypeId;
        $contact->customer_id = $customerId;
        $contact->value = $value;
        $contact->save();
    }

    /**
     * @inheritDoc
     */
    public function deleteAddress(int $customerAddressId): void
    {
        $addressId = $this->customerRepo->getCustomerAddressById($customerAddressId);

        $this->customerRepo->deleteCustomerAddressById($customerAddressId);

        if (!is_null($addressId)) {
            $this->addressRepo->deleteAddressById($addressId->address_id);
        }
    }

    /**
     * @inheritDoc
     */
    public function deleteCustomer(int $customerId): void
    {
        $contacts = $this->contactRepo->getContactByCustomerId($customerId);
        foreach ($contacts as $contact) {
            $this->contactRepo->deleteContactById($contact->id);
        }

        $customersAddresses = $this->customerRepo->getCustomersAddressesByCustomerId($customerId);

        foreach ($customersAddresses as $customerAddress) {
            $this->deleteAddress($customerAddress->id);
        }

        $this->customerRepo->deleteCustomerIdentificationNumbersByCustomerId($customerId);

        $this->customerRepo->deleteCustomerById($customerId);
    }


}
