<?php

namespace App\Core\Customers\Contracts;

use App\Core\Customers\DataTransfer\CustomerTransferObject;
use App\Core\Data\Models\Customer;

interface CustomerServiceContract
{
    /**
     * @param int $companyId
     * @param string|null $companyName
     * @param string|null $firstName
     * @return Customer
     */
    public function storeCustomer(
        int $companyId,
        ?string $companyName,
        ?string $firstName
    ): Customer;

    /**
     * @param CustomerTransferObject $customerTransfer
     * @return Customer
     */
    public function fullStoreUpdateCustomer(
        CustomerTransferObject $customerTransfer
    ): Customer;

    /**
     * @param int $customerAddressId
     */
    public function deleteAddress(int $customerAddressId): void;

    /**
     * @param int $customerId
     */
    public function deleteCustomer(int $customerId): void;
}
