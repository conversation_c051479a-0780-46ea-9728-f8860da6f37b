<?php

namespace App\Core\IossNumbers;

use App\Core\Common\History\History;
use App\Core\Data\Models\IossNumber;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\IossNumberRepositoryContract;
use App\Core\IossNumbers\Contracts\IossNumberServiceContract;
use Illuminate\Support\Carbon;

class IossNumberService implements IossNumberServiceContract
{
    private IossNumberRepositoryContract $iossNumberRepo;

    private CompanyRepositoryContract $companyRepo;

    public function __construct(IossNumberRepositoryContract $iossNumberRepo, CompanyRepositoryContract $companyRepo)
    {
        $this->iossNumberRepo = $iossNumberRepo;
        $this->companyRepo = $companyRepo;
    }

    /**
     * @inheritDoc
     */
    public function storeUpdateDeleteIossNumber(
        int $companyId,
        int $issueCountryId,
        int $typeId,
        string $number,
        string $registerDate,
        bool $delete,
        ?string $endDate = null,
        ?int $iossNumberId = null
    ): void {
        if ($delete && !is_null($iossNumberId)) {
            $iossNumber = $this->iossNumberRepo->getIossNumberById($iossNumberId);
            $countryName = $iossNumber->issueCountry->name ?? null;
            $langParams = ['country' => $countryName];
            History::deleted('history.Ioss number deleted for country', $iossNumber, null, $langParams);

            $this->iossNumberRepo->deleteIossNumberById($iossNumberId);

            return;
        }

        $iossNum = $this->iossNumberRepo->getEmptyIossNumberModel();
        $message = 'history.Ioss number created for country';

        if (!is_null($iossNumberId)) {
            $iossNum = $this->iossNumberRepo->getIossNumberById($iossNumberId);
            $message = 'history.Ioss number updated for country';
            if (is_null($iossNum)) {
                return;
            }
        }

        $registerDate = Carbon::parse($registerDate)->toDateString();
        if (!is_null($endDate)) {
            $endDate = Carbon::parse($endDate)->toDateString();
        }

        // Ovo nema smisla i potenciajlni je bug ako se doda neko polje u bazu, a ovdje ne izmijeni
        // ali neznam koje je tocno razlog tome osim sto se dopise history ako i nije nista izmjenjeno
        if (
            $iossNum->company_id === $companyId
            && $iossNum->type_id === $typeId
            && $iossNum->number === $number
            && $iossNum->register_date === $registerDate
            && $iossNum->issue_country_id === $issueCountryId
            && $iossNum->end_date === $endDate
        ) {
            return;
        }

        $iossNum->company_id = $companyId;
        $iossNum->type_id = $typeId;
        $iossNum->number = $number;
        $iossNum->register_date = $registerDate;
        $iossNum->end_date = $endDate;
        $iossNum->issue_country_id = $issueCountryId;

        $iossNum->save();

        $countryName = $iossNum->issueCountry->name ?? null;
        $langParams = ['country' => $countryName];
        History::updated($message, $iossNum, null, $langParams);
    }

    /**
     * @inheritDoc
     */
    public function getMainIossNumber(int $companyId): IossNumber | null
    {
        return $this->iossNumberRepo->getMainIossNumber($companyId);
    }
}
