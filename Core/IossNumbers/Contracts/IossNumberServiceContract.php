<?php

namespace App\Core\IossNumbers\Contracts;

use App\Core\Data\Models\IossNumber;
interface IossNumberServiceContract
{
    /**
     * @param int $companyId
     * @param int $issueCountryId
     * @param int $typeId
     * @param string $number
     * @param string $registerDate
     * @param bool $delete
     * @param string|null $endDate
     * @param int|null $iossNumberId
     * @return void
     */
    public function storeUpdateDeleteIossNumber(
        int $companyId,
        int $issueCountryId,
        int $typeId,
        string $number,
        string $registerDate,
        bool $delete,
        ?string $endDate = null,
        ?int $iossNumberId = null
    ): void;

    /**
     * @param int $companyId
     * @return IossNumber|null
     */
    public function getMainIossNumber(int $companyId): IossNumber|null;
}
