<?php

namespace App\Core\Imports;

use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

HeadingRowFormatter::default('none');

class CzechTaxOfficesImport implements ToArray, WithHeadingRow
{

    public function array(array $row)
    {
        $row = $row[0];

        return [
            'taxOfficeName'   => $row['Tax office'],
            'street'          => $row['Street'],
            'houseNumber'     => $row['House number'],
            'addressAddition' => $row['Address addition'],
            'city'            => $row['City'],
            'postalCode'      => $row['Postal code'],
            'id'              => $row['Id']
        ];
    }
}
