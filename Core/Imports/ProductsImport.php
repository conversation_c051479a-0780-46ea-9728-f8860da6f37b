<?php

namespace App\Core\Imports;

use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

HeadingRowFormatter::default('none');

class ProductsImport implements ToArray, WithHeadingRow
{

    public function array(array $row)
    {
        $row = $row[0];

        return [
            'ecommerceAccount'   => $row['Ecommerce account'],
            'sku'                => $row['SKU'],
            'vatRateType'        => $row['Vat rate type'],
            'manufactureCountry' => $row['Manufacture country'],
            'commodityCode'      => $row['Commodity code'],
            'purchasePrice'      => $row['Purchase price'],
            'currency'           => $row['Currency'],
            'margin'             => $row['Margin'],
            'marginType'         => $row['Margin type'],
            'description'        => $row['Description'],
        ];
    }
}
