<?php

namespace App\Core\FlatVatRates;

use App\Core\Common\History\History;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\FlatVatRateRepositoryContract;
use App\Core\FlatVatRates\Contracts\FlatVatRateServiceContract;
use Illuminate\Support\Carbon;

class FlatVatRateService implements FlatVatRateServiceContract
{
    private FlatVatRateRepositoryContract $flatVatRateRepo;
    private CountryRepositoryContract $countryRepo;

    public function __construct(FlatVatRateRepositoryContract $flatVatRateRepo, CountryRepositoryContract $countryRepo)
    {
        $this->flatVatRateRepo = $flatVatRateRepo;
        $this->countryRepo = $countryRepo;
    }

    public function storeUpdateDeleteFlatVatRate(
        int $companyId,
        int $countryId,
        string $startDate,
        float $value,
        bool $active,
        bool $delete,
        ?int $flatVatRateId,
        ?string $endDate
    ): void {
        $countryName = $this->countryRepo->getCountryById($countryId)->name;
        $langParams = ['country' => $countryName];
        $actionUpdate = true;

        if ($flatVatRateId) {
            $flatVatRate = $this->flatVatRateRepo->getFlatVatRateById($flatVatRateId);
            if ($delete) {
                History::deleted('history.Flat rates deleted for country', $flatVatRate, null, $langParams);
                $this->flatVatRateRepo->deleteFlatVatRateById($flatVatRateId);

                return;
            }
        } else {
            $flatVatRate = $this->flatVatRateRepo->getEmptyFlatVatRateModel();
            $actionUpdate = false;
        }

        $startDate = Carbon::parse($startDate)->toDateString();

        if (!is_null($endDate)) {
            $endDate = Carbon::parse($endDate)->toDateString();
        }

        $flatVatRate->company_id = $companyId;
        $flatVatRate->country_id = $countryId;
        $flatVatRate->start_date = $startDate;
        $flatVatRate->end_date = $endDate;
        $flatVatRate->value = $value;
        $flatVatRate->active = $active;

        $flatVatRate->save();

        if ($actionUpdate) {
            History::updated('history.Flat rates updated for country', $flatVatRate, null, $langParams);
        } else {
            History::created('history.Flat rates created for country', $flatVatRate, null, $langParams);
        }
    }

}
