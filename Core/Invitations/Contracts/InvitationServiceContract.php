<?php

namespace App\Core\Invitations\Contracts;

use App\Core\Data\Models\Invitation;
use App\Core\Invitations\Exceptions\InvitationExistsException;

interface InvitationServiceContract
{
    /**
     * @param int $companyId
     * @param int $invitingUserId
     * @param string $recipientEmail
     * @param string $recipientFirstName
     * @param string $recipientLastName
     * @return Invitation
     * @throws InvitationExistsException
     */
    public function storePartnerToClientInvitation(
        int $companyId,
        int $invitingUserId,
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): Invitation;

    /**
     * @param int $partnerId
     * @param int $countryId
     * @param int $invitingUserId
     * @param string $recipientEmail
     * @param string $recipientFirstName
     * @param string $recipientLastName
     * @return Invitation
     * @throws InvitationExistsException
     */
    public function storePartnerToAccountantInvitation(
        int $partnerId,
        int $countryId,
        int $invitingUserId,
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): Invitation;

    /**
     * @param int $partnerId
     * @param int $countryId
     * @param int $invitingUserId
     * @param int $invitingCompanyId
     * @param string $recipientEmail
     * @param string $recipientFirstName
     * @param string $recipientLastName
     * @return Invitation
     * @throws InvitationExistsException
     */
    public function storeClientToAccountantInvitation(
        int $partnerId,
        int $countryId,
        int $invitingUserId,
        int $invitingCompanyId,
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): Invitation;

    public function storeClientToClientInvitation(
        int $companyId,
        int $userId,
        string $email,
        string $firstName,
        string $lastName,
        int $roleId
    ): Invitation;

    /**
     * @param int $id
     */
    public function deleteInvitation(int $id): void;

    public function cleanUnacceptedInvitations(): void;

    /**
     * @param string $view
     */
    public function sendInvitationsReminders(string $view): void;
}
