<?php

namespace App\Core\Invitations;

use App\Core\Data\Models\Invitation;
use App\Core\Data\Models\InvitationType;
use App\Core\Data\Repositories\Contracts\InvitationRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use App\Core\Invitations\Contracts\InvitationServiceContract;
use App\Core\Invitations\Exceptions\InvitationExistsException;
use App\Core\System\Mails\Contracts\MailServiceContract;
use App\Core\System\Notifications\Contracts\NotificationsServiceContract;
use Carbon\Carbon;
use Illuminate\Support\Str;

class InvitationService implements InvitationServiceContract
{
    private InvitationRepositoryContract $invitationRepo;

    private NotificationsServiceContract $notificationsService;
    private UserRepositoryContract $userRepo;
    private MailServiceContract $mailService;

    public function __construct(
        InvitationRepositoryContract $invitationRepo,
        NotificationsServiceContract $notificationsService,
        UserRepositoryContract $userRepo,
        MailServiceContract $mailService
    ) {
        $this->invitationRepo = $invitationRepo;
        $this->notificationsService = $notificationsService;
        $this->userRepo = $userRepo;
        $this->mailService = $mailService;
    }

    /**
     * @inheritDoc
     */
    public function storePartnerToClientInvitation(
        int $companyId,
        int $invitingUserId,
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): Invitation {
        $invitation = $this->invitationRepo
            ->getClientInvitationByCompanyIdAndEmail($companyId, $recipientEmail);
        if (!is_null($invitation)) {
            throw new InvitationExistsException(_l('invitations.user-invitation-exists', ['email' => $recipientEmail]));
        }

        $token = $this->createInvitationToken(
            $recipientEmail,
            $recipientFirstName,
            $recipientLastName
        );

        $invitation = $this->invitationRepo
            ->getEmptyInvitationModel();

        $invitation->company_id = $companyId;
        $invitation->recipient_email = $recipientEmail;
        $invitation->inviting_user_id = $invitingUserId;
        $invitation->created_at = Carbon::now();
        $invitation->invitation_token = $token;
        $invitation->invitation_type_id = InvitationType::PARTNER_TO_CLIENT;

        $invitation->recipient_first_name = $recipientFirstName;
        $invitation->recipient_last_name = $recipientLastName;

        $invitation->save();

        return $this->checkIfUserExistsAndSendNotification($invitation);
    }

    /**
     * @inheritDoc
     */
    public function storePartnerToAccountantInvitation(
        int $partnerId,
        int $countryId,
        int $invitingUserId,
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): Invitation {
        $invitation = $this->invitationRepo
            ->getAccountantInvitationByPartnerIdAndEmail($partnerId, $recipientEmail);
        if (!is_null($invitation)) {
            throw new InvitationExistsException(_l('invitations.user-invitation-exists', ['email' => $recipientEmail]));
        }

        $token = $this->createInvitationToken(
            $recipientEmail,
            $recipientFirstName,
            $recipientLastName
        );
        $invitation = $this->invitationRepo->getEmptyInvitationModel();

        $invitation->partner_id = $partnerId;
        $invitation->country_id = $countryId;
        $invitation->recipient_email = $recipientEmail;
        $invitation->inviting_user_id = $invitingUserId;
        $invitation->created_at = Carbon::now();
        $invitation->invitation_token = $token;
        $invitation->invitation_type_id = InvitationType::PARTNER_TO_ACCOUNTANT;

        $invitation->recipient_first_name = $recipientFirstName;
        $invitation->recipient_last_name = $recipientLastName;

        $invitation->save();

        return $this->checkIfUserExistsAndSendNotification($invitation);
    }


    /**
     * @inheritDoc
     */
    public function storeClientToAccountantInvitation(
        int $partnerId,
        int $countryId,
        int $invitingUserId,
        int $invitingCompanyId,
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): Invitation {
        $invitation = $this->invitationRepo
            ->getAccountantInvitationByPartnerIdAndEmail($partnerId, $recipientEmail);
        if (!is_null($invitation)) {
            throw new InvitationExistsException(_l('invitations.user-invitation-exists', ['email' => $recipientEmail]));
        }

        $token = $this->createInvitationToken(
            $recipientEmail,
            $recipientFirstName,
            $recipientLastName
        );
        $invitation = $this->invitationRepo->getEmptyInvitationModel();

        $invitation->partner_id = $partnerId;
        $invitation->country_id = $countryId;
        $invitation->recipient_email = $recipientEmail;
        $invitation->inviting_user_id = $invitingUserId;
        $invitation->inviting_company_id = $invitingCompanyId;
        $invitation->created_at = Carbon::now();
        $invitation->invitation_token = $token;
        $invitation->invitation_type_id = InvitationType::CLIENT_TO_ACCOUNTANT;

        $invitation->recipient_first_name = $recipientFirstName;
        $invitation->recipient_last_name = $recipientLastName;

        $invitation->save();

        return $this->checkIfUserExistsAndSendNotification($invitation);
    }

    public function storeClientToClientInvitation(
        int $companyId,
        int $userId,
        string $email,
        string $firstName,
        string $lastName,
        int $roleId
    ): Invitation {
        $invitation = $this->invitationRepo
            ->getClientInvitationByCompanyIdAndEmail($companyId, $email);

        if (!is_null($invitation)) {
            /** @noinspection PhpUndefinedVariableInspection */
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new InvitationExistsException(_l('invitations.user-invitation-exists', ['email' => $recipientEmail]));
        }

        $token = $this->createInvitationToken(
            $email,
            $firstName,
            $lastName
        );

        $invitation = $this->invitationRepo->getEmptyInvitationModel();

        $invitation->company_id = $companyId;
        $invitation->recipient_email = $email;
        $invitation->inviting_user_id = $userId;
        $invitation->created_at = Carbon::now();
        $invitation->invitation_token = $token;
        $invitation->invitation_type_id = InvitationType::CLIENT_TO_CLIENT;
        $invitation->recipient_first_name = $firstName;
        $invitation->recipient_last_name = $lastName;
        $invitation->role_id = $roleId;

        $invitation->save();

        return $this->checkIfUserExistsAndSendNotification($invitation);
    }

    /**
     * @param Invitation $invitation
     * @return Invitation
     */
    private function checkIfUserExistsAndSendNotification(Invitation $invitation): Invitation
    {
        $invitation->load(
            'company',
            'invitingCompany',
            'country',
            'partner',
            'notification'
        );
        $email = $invitation->recipient_email;
        $user = $this->userRepo->getUserByEmail($email, true, true);
        if (is_null($user)) {
            return $invitation;
        }

        $partnerName = '';
        $companyName = '';
        $invitingCompanyName = '';
        $partner = $invitation->partner;
        $company = $invitation->company;
        $invitingCompany = $invitation->invitingCompany;

        if (!is_null($partner)) {
            $partnerName = $partner->name;
        }

        if (!is_null($invitingCompany)) {
            $invitingCompanyName = $invitingCompany->full_legal_name;
        }

        if (!is_null($company)) {
            $companyName = $company->full_legal_name;
        }

        $replace = [
            'partner'          => $partnerName,
            'company'          => $companyName,
            'inviting-company' => $invitingCompanyName
        ];
        $messages = [
            InvitationType::PARTNER_TO_ACCOUNTANT => 'invitations.notification.partner-to-accountant',
            InvitationType::PARTNER_TO_CLIENT     => 'invitations.notification.partner-to-client',
            InvitationType::CLIENT_TO_ACCOUNTANT  => 'invitations.notification.client-to-accountant',
            InvitationType::CLIENT_TO_CLIENT      => 'invitations.notification.client-to-client',
        ];
        $message = _l($messages[$invitation->invitation_type_id], $replace);

        $notification = $this->notificationsService
            ->promptNotificationForUser(
                $user->id,
                $message,
                route('invitations.react', [$invitation->id, 'accept' => true]),
                route('invitations.react', [$invitation->id, 'accept' => false]),
                _l('common.Accept'),
                _l('common.Reject'),
            );

        $invitation->recipient_first_name = $user->first_name;
        $invitation->recipient_last_name = $user->last_name;
        $invitation->notification_id = $notification->id;
        $invitation->save();

        $invitation->load('notification');

        return $invitation;
    }

    private function createInvitationToken(
        string $recipientEmail,
        string $recipientFirstName,
        string $recipientLastName
    ): string {
        $hash = Str::random(256);
        $data = [
            'hash'      => $hash,
            'email'     => $recipientEmail,
            'firstName' => $recipientFirstName,
            'lastName'  => $recipientLastName,
        ];
        $data = json_encode($data);

        return base64_encode($data);
    }

    /**
     * @inheritDoc
     */
    public function deleteInvitation(int $id): void
    {
        $invitation = $this->invitationRepo->getInvitationById($id);
        if (is_null($invitation)) {
            return;
        }

        $invitation->load('notification');
        if (!is_null($invitation->notification)) {
            $invitation->notification->delete();
        }
        $this->invitationRepo->deleteInvitationByIds([$id]);
    }

    public function cleanUnacceptedInvitations(): void
    {
        $invitations = $this->invitationRepo->getUnacceptedInvitations();

        foreach ($invitations as $invitation) {
            /**
             * @var Invitation $invitation
             */
            $this->deleteInvitation($invitation->id);
        }
    }

    public function sendInvitationsReminders(string $view): void
    {
        $invitations = $this->invitationRepo->getUnacceptedInvitationsForReminder();
        foreach ($invitations as $invitation) {
            /**
             * @var Invitation $invitation
             */
            $this->mailService->sendInvitationConfirmationReminder($view, $invitation);
        }
    }
}
