<?php

namespace App\Core\AmazonSeller\Contracts;

use App\Core\Data\Models\SalesChannel;
use SellingPartnerApi\Configuration;
use SellingPartnerApi\Endpoint;

abstract class AmazonApiAbstractService
{
    protected bool $test = true;

    protected function getConfiguration(SalesChannel $salesChannel): Configuration
    {
        $endpoint = $this->test ? Endpoint::EU_SANDBOX : Endpoint::EU;

        return (new Configuration([
            'lwaClientId'        => config('evat.amazon.clientId'),
            'lwaClientSecret'    => config('evat.amazon.clientSecret'),
            'lwaRefreshToken'    => $salesChannel->amazon_refresh_token,
            'awsAccessKeyId'     => config('evat.amazon.stsAccessKey'),
            'awsSecretAccessKey' => config('evat.amazon.stsSecretKey'),
            'roleArn'            => config('evat.amazon.roleArn'),
            'endpoint'           => $endpoint
        ]));
    }
}
