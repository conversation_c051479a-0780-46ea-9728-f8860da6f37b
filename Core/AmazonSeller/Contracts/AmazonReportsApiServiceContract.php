<?php

namespace App\Core\AmazonSeller\Contracts;

use App\Core\AmazonSeller\Api\AmazonApiResponseTransfer;
use App\Core\AmazonSeller\Api\Exceptions\AmazonSellerApiThrottleException;
use App\Core\AmazonSeller\Auth\LWAException;
use App\Core\Data\Models\SalesChannel;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Container\BindingResolutionException;
use SellingPartnerApi\ApiException;

interface AmazonReportsApiServiceContract
{
    /**
     * @param SalesChannel $salesChannel
     * @param Carbon $start
     * @param Carbon $end
     * @return AmazonApiResponseTransfer
     * @throws Exception
     * @throws ApiException
     */
    public function createReportForPeriod(SalesChannel $salesChannel, Carbon $start, Carbon $end): AmazonApiResponseTransfer;

    /**
     * @param SalesChannel $salesChannel
     * @param string $reportId
     * @return AmazonApiResponseTransfer
     * @throws AmazonSellerApiThrottleException
     * @throws LWAException
     * @throws GuzzleException
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function getReportById(SalesChannel $salesChannel, string $reportId): AmazonApiResponseTransfer;

    /**
     * @param SalesChannel $salesChannel
     * @param string $documentId
     * @return AmazonApiResponseTransfer
     * @throws Exception
     * @throws GuzzleException
     */
    public function getReportDocumentByDocumentId(SalesChannel $salesChannel, string $documentId): AmazonApiResponseTransfer;
}
