<?php

namespace App\Core\AmazonSeller;

use App\Core\AmazonSeller\Auth\Contracts\LWASessionContract;

class AmazonSellerSessionHandler implements LWASessionContract
{
    public function __construct()
    {
        @session_start();
    }

    /**
     * @inheritDoc
     */
    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }

    /**
     * @inheritDoc
     */
    public function put(string $key, $value): LWASessionContract
    {
        $_SESSION[$key] = $value;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function unset(string $key): LWASessionContract
    {
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }

        return $this;
    }
}