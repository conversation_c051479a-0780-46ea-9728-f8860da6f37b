<?php

namespace App\Core\AmazonSeller\Auth;

class Base64
{
    /**
     * @param string $base64Url
     * @return string
     */
    public static function base64UrlDecode(string $base64Url): string
    {
        return base64_decode(self::base64urlToBase64($base64Url));
    }

    /**
     * @param string $base64url
     * @return string
     */
    public static function base64UrlToBase64(string $base64url): string
    {
        $padding = strlen($base64url) % 4;
        if ($padding > 0) {
            $base64url .= str_repeat('=', 4 - $padding);
        }

        return strtr($base64url, '-_', '+/');
    }

    /**
     * @param string $string
     * @return string
     */
    public static function urlEncode(string $string): string
    {
        $encoded = base64_encode($string);
        $encoded = rtrim($encoded, '=');
        $encoded = strtr($encoded, '+/', '-_');

        return $encoded;
    }
}