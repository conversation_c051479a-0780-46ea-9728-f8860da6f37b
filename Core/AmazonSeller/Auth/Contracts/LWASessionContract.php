<?php

namespace App\Core\AmazonSeller\Auth\Contracts;

interface LWASessionContract
{
    /**
     * @param string $key
     * @param mixed | null $default
     * @return mixed
     */
    public function get(string $key, $default = null);

    /**
     * @param string $key
     * @param mixed $value
     * @return LWASessionContract
     */
    public function put(string $key, $value): LWASessionContract;

    /**
     * @param string $key
     * @return LWASessionContract
     */
    public function unset(string $key): LWASessionContract;
}