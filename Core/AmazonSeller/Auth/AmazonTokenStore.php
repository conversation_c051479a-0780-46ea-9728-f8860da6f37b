<?php

namespace App\Core\AmazonSeller\Auth;

use App\Core\AmazonSeller\Api\AmazonApiConfiguration;
use App\Core\Data\Models\SalesChannel;
use GuzzleHttp\Psr7\Query;
use Illuminate\Cache\Repository;
use Throwable;

class AmazonTokenStore
{
    private Repository $cache;
    private SalesChannel $salesChannel;
    private AmazonApiConfiguration $amazonApiConfiguration;
    private RequestHeaderSigner $requestHeaderSigner;
    private string $accessToken;
    private ?StsCredentials $stsCredentials = null;

    public function __construct(
        AmazonApiConfiguration $amazonApiConfiguration,
        SalesChannel $salesChannel,
        RequestHeaderSigner $requestHeaderSigner
    ) {
        $this->cache = app()->make(Repository::class);

        $this->amazonApiConfiguration = $amazonApiConfiguration;
        $this->requestHeaderSigner = $requestHeaderSigner;
        $this->salesChannel = $salesChannel;

        $this->resolveAccessToken();
        $this->resolveStsCredentials();
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    /**
     * @return string
     */
    public function getRefreshToken(): string
    {
        return $this->getSalesChannel()->amazon_refresh_token;
    }

    public function getStsCredentials(): StsCredentials
    {
        return $this->stsCredentials;
    }

    /**
     * @return SalesChannel
     */
    private function getSalesChannel(): SalesChannel
    {
        return $this->salesChannel;
    }

    /**
     * @throws LWAException
     */
    private function resolveAccessToken(): void
    {
//        $token = $this->getCacheAccessToken();
        $token = null;
        if (is_null($token)) {
            $token = $this->fetchAndResolveNewAccessToken();
        }

        $this->accessToken = $token->getAccessToken();
    }

    /**
     * @return AccessToken
     * @throws LWAException
     */
    private function fetchAndResolveNewAccessToken(): AccessToken
    {
        $url = 'https://api.amazon.com/auth/o2/token';
        $params = [
            'grant_type'    => 'refresh_token',
            'refresh_token' => $this->getRefreshToken(),
            'client_id'     => $this->amazonApiConfiguration->getClientId(),
            'client_secret' => $this->amazonApiConfiguration->getClientSecret()
        ];

        $response = LWA::request($url, 'POST', $params);
        $response = json_decode($response, true);
        $refreshToken = $response['refresh_token'];
        $token = $response['access_token'];
        $expiresIn = $response['expires_in'];

        {
            // Store new refresh token
            $salesChannel = $this->getSalesChannel();
            $salesChannel->amazon_refresh_token = $refreshToken;
            $salesChannel->save();
            $this->salesChannel = $salesChannel;
        }

        $accessToken = new AccessToken($token, $expiresIn);
        $this->setCacheAccessToken($accessToken);

        return $accessToken;
    }

    /**
     * @return string
     */
    private function getAccessTokenCacheKey(): string
    {
        return 'amazonAccessToken' . $this->salesChannel->id;
    }

//    /**
//     * @return AccessToken|null
//     */
//    private function getCacheAccessToken(): ?AccessToken
//    {
//        return $this->cache->get($this->getAccessTokenCacheKey());
//    }

    /**
     * @param AccessToken $token
     */
    private function setCacheAccessToken(AccessToken $token): void
    {
        $this->cache->put($this->getAccessTokenCacheKey(), $token, ($token->getExpiresIn() - 10));
    }

    /**
     * @throws LWAException
     */
    private function resolveStsCredentials(): void
    {
        $stsCredentials = $this->getCacheStsCredentials();
        if (is_null($stsCredentials)) {
            $stsCredentials = $this->fetchAndResolveNewStsCredentials();
        }
        $this->amazonApiConfiguration
            ->setStsAccessKey($stsCredentials->getCredentialsAccessKeyId());
        $this->amazonApiConfiguration
            ->setStsSecretKey($stsCredentials->getCredentialsSecretAccessKeyId());

        $this->stsCredentials = $stsCredentials;
    }

    /**
     * @return StsCredentials
     * @throws LWAException
     */
    private function fetchAndResolveNewStsCredentials(): StsCredentials
    {
        $configuration = $this->amazonApiConfiguration;
        $baseUri = $configuration->getStsBaseUri();
        $host = $configuration->getStsHost();

        $headers = [
            'accept'       => 'application/json',
            'content-type' => 'application/x-www-form-urlencoded; charset=utf-8',
        ];
        $params = [
            'Action'          => 'AssumeRole',
            'DurationSeconds' => 3600,
            'RoleArn'         => $configuration->getRoleArn(),
            'RoleSessionName' => 'session1',
            'Version'         => '2011-06-15',
        ];

        $payload = null;
        if (count($params) > 0) {
            $payload = Query::build($params);
        }

        $service = 'sts';
        $headers = $this->requestHeaderSigner
            ->getAuthorizationHeader(
                $host,
                '/',
                $service,
                'POST',
                $this->getAccessToken(),
                $payload,
                null,
                $headers
            )->map(function ($value, $key) {
                return $key . ': ' . $value;
            })->values()->toArray();

        $response = LWA::request($baseUri, 'POST', $params, $headers);
        try {
            $response = json_decode($response, true);

            $stsCredentials = new StsCredentials($response['AssumeRoleResponse']['AssumeRoleResult']);
            $this->setCacheStsCredentials($stsCredentials);
        } catch (Throwable) {
            dd('aaa', $response);
        }

        return $stsCredentials;
    }

    /**
     * @return string
     */
    private function getStsCredentialsCacheKey(): string
    {
        return 'stsCredentials' . $this->salesChannel->id;
    }

    /**
     * @return StsCredentials|null
     */
    private function getCacheStsCredentials(): ?StsCredentials
    {
        return $this->cache->get($this->getStsCredentialsCacheKey());
    }

    /**
     * @param StsCredentials $stsCredentials
     */
    private function setCacheStsCredentials(StsCredentials $stsCredentials): void
    {
        $this->cache->put($this->getStsCredentialsCacheKey(), $stsCredentials, ($stsCredentials->getExpireIn() - 10));
    }

    /**
     * @return AmazonApiConfiguration
     */
    public function getAmazonApiConfiguration(): AmazonApiConfiguration
    {
        return $this->amazonApiConfiguration;
    }
}
