<?php

namespace App\Core\AmazonSeller\Auth;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;

class StsCredentials implements Arrayable
{
    /**
     * @var string
     */
    private string $assumedRoleUserArn;

    /**
     * @var string
     */
    private string $assumedRoleUserAssumedRoleId;

    /**
     * @var string
     */
    private string $credentialsAccessKeyId;

    /**
     * @var float
     */
    private float $credentialsExpiration;

    /**
     * @var string
     */
    private string $credentialsSecretAccessKeyId;

    /**
     * @var string
     */
    private string $credentialsSessionToken;

    /**
     * @var int|null
     */
    private ?int $packedPolicySize;

    public function __construct(array $credentialsData)
    {
        $this->setAssumedRoleUserArn($credentialsData['AssumedRoleUser']['Arn']);
        $this->setAssumedRoleUserAssumedRoleId($credentialsData['AssumedRoleUser']['AssumedRoleId']);

        $this->setCredentialsAccessKeyId($credentialsData['Credentials']['AccessKeyId']);
        $this->setCredentialsExpiration($credentialsData['Credentials']['Expiration']);
        $this->setCredentialsSecretAccessKeyId($credentialsData['Credentials']['SecretAccessKey']);
        $this->setCredentialsSessionToken($credentialsData['Credentials']['SessionToken']);

        $this->setPackedPolicySize($credentialsData['PackedPolicySize']);
    }

    /**
     * @return string
     */
    public function getAssumedRoleUserArn(): string
    {
        return $this->assumedRoleUserArn;
    }

    /**
     * @param string $assumedRoleUserArn
     * @return StsCredentials
     */
    public function setAssumedRoleUserArn(string $assumedRoleUserArn): StsCredentials
    {
        $this->assumedRoleUserArn = $assumedRoleUserArn;

        return $this;
    }

    /**
     * @return string
     */
    public function getAssumedRoleUserAssumedRoleId(): string
    {
        return $this->assumedRoleUserAssumedRoleId;
    }

    /**
     * @param string $assumedRoleUserAssumedRoleId
     * @return StsCredentials
     */
    public function setAssumedRoleUserAssumedRoleId(string $assumedRoleUserAssumedRoleId): StsCredentials
    {
        $this->assumedRoleUserAssumedRoleId = $assumedRoleUserAssumedRoleId;

        return $this;
    }

    /**
     * @return string
     */
    public function getCredentialsAccessKeyId(): string
    {
        return $this->credentialsAccessKeyId;
    }

    /**
     * @param string $credentialsAccessKeyId
     * @return StsCredentials
     */
    public function setCredentialsAccessKeyId(string $credentialsAccessKeyId): StsCredentials
    {
        $this->credentialsAccessKeyId = $credentialsAccessKeyId;

        return $this;
    }

    /**
     * @return float
     */
    public function getCredentialsExpiration(): float
    {
        return $this->credentialsExpiration;
    }

    /**
     * @return Carbon
     */
    public function getCredentialsExpirationCarbon(): Carbon
    {
        return Carbon::createFromTimestamp($this->getCredentialsExpiration());
    }

    /**
     * TTL seconds
     *
     * @return int
     */
    public function getExpireIn(): int
    {
        $now = Carbon::now();

        return $now->diffInSeconds($this->getCredentialsExpirationCarbon());
    }

    /**
     * @param float $credentialsExpiration
     * @return StsCredentials
     */
    public function setCredentialsExpiration(float $credentialsExpiration): StsCredentials
    {
        $this->credentialsExpiration = $credentialsExpiration;

        return $this;
    }

    /**
     * @return string
     */
    public function getCredentialsSecretAccessKeyId(): string
    {
        return $this->credentialsSecretAccessKeyId;
    }

    /**
     * @param string $credentialsSecretAccessKeyId
     * @return StsCredentials
     */
    public function setCredentialsSecretAccessKeyId(string $credentialsSecretAccessKeyId): StsCredentials
    {
        $this->credentialsSecretAccessKeyId = $credentialsSecretAccessKeyId;

        return $this;
    }

    /**
     * @return string
     */
    public function getCredentialsSessionToken(): string
    {
        return $this->credentialsSessionToken;
    }

    /**
     * @param string $credentialsSessionToken
     * @return StsCredentials
     */
    public function setCredentialsSessionToken(string $credentialsSessionToken): StsCredentials
    {
        $this->credentialsSessionToken = $credentialsSessionToken;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getPackedPolicySize(): ?int
    {
        return $this->packedPolicySize;
    }

    /**
     * @param int|null $packedPolicySize
     * @return StsCredentials
     */
    public function setPackedPolicySize(?int $packedPolicySize): StsCredentials
    {
        $this->packedPolicySize = $packedPolicySize;

        return $this;
    }


    public function toArray(): array
    {
        return [
            'assumedRoleUserArn'            => $this->getAssumedRoleUserArn(),
            'assumedRoleUserAssumedRoleId'  => $this->getAssumedRoleUserAssumedRoleId(),
            'credentialsAccessKeyId'        => $this->getCredentialsAccessKeyId(),
            'credentialsExpiration'         => $this->getCredentialsExpiration(),
            'credentialsExpirationDateTime' => $this->getCredentialsExpirationCarbon()->toDateTimeString(),
            'expiresIn'                     => $this->getExpireIn(),
            'credentialsSecretAccessKeyId'  => $this->getCredentialsSecretAccessKeyId(),
            'credentialsSessionToken'       => $this->getCredentialsSessionToken(),
            'packedPolicySize'              => $this->getPackedPolicySize(),
        ];
    }
}