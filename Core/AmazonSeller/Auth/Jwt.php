<?php

namespace App\Core\AmazonSeller\Auth;

use App\Core\AmazonSeller\Auth\Contracts\LWAArrayableContract;
use stdClass;

class Jwt implements LWAArrayableContract
{
    /**
     * @var object
     */
    protected $tokenData;

    /**
     * @var string
     */
    protected $accessToken;

    /**
     * @var string
     */
    protected $refreshToken;

    /**
     * @var string
     */
    protected $tokenType;

    /**
     * @var int
     */
    protected $expiresIn;

    /**
     * @var array
     */
    protected $scopes;

    /**
     * @var Configuration
     */
    protected $configuration;

    private $debug = false;

    public function __construct($tokenData, Configuration $configuration)
    {
        if ($this->debug) {
            // DEBUG
            $tokenData = new stdClass();
            $tokenDataParts = [
                'access_token' => 'eyJ4NXQiOiJOVEF4Wm1NeE5ETXlaRGczTVRVMVpHTTBNekV6T0RKaFpXSTRORE5sWkRVMU9HRmtOakZpTVEiLCJraWQiOiJOVEF4Wm1NeE5ETXlaRGczTVRVMVpHTTBNekV6T0RKaFpXSTRORE5sWkRVMU9HRmtOakZpTVEiLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Br8N0BZ4lYwMlXczRiKJx-j1B7Q0sUJvGdzOUfKIVlgZBvSNWU43692uBpRIIziu7_nTBC5hH2ncC8CfB0Ww69Yo49MLF4lqFIfa4hgHGwn8ZOTAqQAMlcz0y5VeBcz4tZH8JnWssNABiKUGltCankdzpUOIPysHVKEM1ArJmLQyrRnu4oOLHFpcHZQRr_-BppdnOydI034x1Zm6UAchL-05JK9xtxLBknrzAqg_wFRCpKJTBdpIuBXoBuUxkpU90-I-eTzj0DSy1faCq3B-MvpoMHdYcDCTYxLkgp4uK2gvkj-_1-A_OcSkQpZWwh2rfLvLje8xQpEWC30qTTrseQ',
                'token_type'   => 'Bearer',
                'expires_in'   => 3600,
            ];

            foreach ($tokenDataParts as $key => $value) {
                $tokenData->{$key} = $value;
            }
        }

        $accessToken = $tokenData->access_token;

        $this->tokenData = $tokenData;
        $this->accessToken = $accessToken;
        $this->refreshToken = $tokenData->refresh_token;
        $this->tokenType = $tokenData->token_type;
        $this->expiresIn = $tokenData->expires_in;
//        $this->scopes = explode(' ', $tokenData->scope);
        $this->configuration = $configuration;
    }

    /**
     * @return object
     */
    public function getTokenData()
    {
        return $this->tokenData;
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    /**
     * @return string
     */
    public function getRefreshToken(): string
    {
        return $this->refreshToken;
    }

    /**
     * @return string
     */
    public function getTokenType(): string
    {
        return $this->tokenType;
    }

    /**
     * @return int
     */
    public function getExpiresIn(): int
    {
        return $this->expiresIn;
    }

    public function toArray(): array
    {
        return [
            'accessToken'   => $this->getAccessToken(),
            'refreshToken'  => $this->getRefreshToken(),
            'expiresIn'     => $this->getExpiresIn(),
            'tokenType'     => $this->getTokenType(),
            'tokenData'     => $this->getTokenData(),
            'configuration' => $this->configuration->toArray(),
        ];
    }
}
