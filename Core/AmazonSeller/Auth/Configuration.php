<?php

namespace App\Core\AmazonSeller\Auth;

use App\Core\AmazonSeller\Auth\Contracts\LWAArrayableContract;

class Configuration implements LWAArrayableContract
{
    /**
     * @var array
     */
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    /**
     * @param string $key
     * @param null $default
     * @return mixed|null
     */
    public function getConfigParameter(string $key, $default = null)
    {
        if (!$this->hasConfigParameter($key)) {
            return $default;
        }

        return $this->getConfig()[$key];
    }

    /**
     * @param string $key
     * @return bool
     */
    public function hasConfigParameter(string $key): bool
    {

        return !is_null($this->getConfig()[$key] ?? null);
    }

    /**
     * @return array
     */
    public function getConfig(): array
    {
        return $this->toArray();
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->config;
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
