<?php

namespace App\Core\AmazonSeller\Auth;

use Illuminate\Http\RedirectResponse;

class LWA
{
    /**
     * @var int
     */
    private static $cURLTimeOut = 60;

    /**
     * @var bool
     */
    private static $verifyPeer = true;

    /**
     * @var bool
     */
    private static $verifyHost = true;

    /**
     * @var int
     */
    protected static $encType = PHP_QUERY_RFC1738;

    /**
     * @var int|null
     */
    protected static $lastResponseCode;

    /**
     * @param string $url
     * @param string $method
     * @param array|null $params
     * @param array $headers
     * @param string|null $certPath
     * @return bool|string
     * @throws LWAException
     */
    public static function request(
        string $url,
        string $method = 'GET',
        array $params = null,
        array $headers = [],
        ?string $certPath = null
    ) {
        $method = strtoupper($method);

        // Init cURL
        $ch = curl_init();

        // Determine whether this is a GET or POST
        if ($method === 'POST' && !is_null($params)) {
            $paramsString = self::buildQuery($params);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $paramsString);

            // Default content type is form encoded
            $contentType = 'application/x-www-form-urlencoded;charset=UTF-8';

            // Determine if this is a JSON payload and add the appropriate content type
            if (is_string($params) && is_object(json_decode($params))) {
                $contentType = 'application/json';
            }

            // Add POST-specific headers
            $headers[] = 'Content-Type: ' . $contentType;

        }

        // If we set some headers include them
        if (count($headers) > 0) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        $params = is_array($params) ? $params : [];
        if (count($params) < 1) {
            $params = null;
        }

        if ($method === 'GET' && !is_null($params)) {
            $url = self::buildUrl($url, $params);
        }
        // Set URL to download
        curl_setopt($ch, CURLOPT_URL, $url);

        // Include header in result? (0 = yes, 1 = no)
        curl_setopt($ch, CURLOPT_HEADER, 0);

        // Allows to follow redirect
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        /**
         * Set cert
         * Otherwise ignore SSL peer verification
         */
        if (!is_null($certPath)) {
            curl_setopt($ch, CURLOPT_CAINFO, $certPath);
        }

        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, self::getVerifyHostAsValue());
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, self::getVerifyPeer());

        // Should cURL return or print out the data? (true = return, false = print)
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        // Timeout in seconds
        curl_setopt($ch, CURLOPT_TIMEOUT, self::$cURLTimeOut);

        // Download the given URL, and return output
        $output = curl_exec($ch);

        // HTTP Response code from server may be required from subclass
        $info = curl_getinfo($ch);
        self::$lastResponseCode = $info['http_code'];

        if ($output === false) {
            throw new LWAException('cURL error: (' . curl_errno($ch) . ') ' . curl_error($ch));
        }

        // Close the cURL resource, and free system resources
        curl_close($ch);

        return $output;
    }

    /**
     * @param array $params
     * @return string
     */
    public static function buildQuery(array $params): string
    {
        if (count($params) < 1) {
            return '';
        }

        return http_build_query($params, null, '&', self::$encType);
    }

    /**
     * @param string $basePath
     * @param array $params
     * @return string
     */
    public static function buildUrl(string $basePath, array $params): string
    {
        if (count($params) < 1) {
            $basePath = rtrim($basePath, '?');
            $basePath = rtrim($basePath, '&');

            return $basePath;
        }

        $separator = '?';
        if (strpos($basePath, '?')) {
            $separator = '&';
        }

        return $basePath . $separator . self::buildQuery($params);
    }

    /**
     * @return bool
     */
    public static function getVerifyPeer(): bool
    {
        return self::$verifyPeer;
    }

    /**
     * @return bool
     */
    public static function getVerifyHost(): bool
    {
        return self::$verifyHost;
    }

    /**
     * @return int
     */
    public static function getVerifyHostAsValue(): int
    {
        return self::getVerifyHost() ? 2 : 0;
    }

    /**
     * @param string $key
     * @param null $default
     * @return mixed|null
     */
    public static function get(string $key, $default = null)
    {
        return $_REQUEST[$key] ?? $default;
    }

    /**
     * @return array
     */
    public static function all(): array
    {
        return $_REQUEST ?? [];
    }

    /**
     * @return int|null
     */
    public static function getLastResponseCode(): ?int
    {
        return self::$lastResponseCode;
    }

    /**
     * @param string $url
     * @return RedirectResponse
     */
    public static function redirect(string $url): RedirectResponse
    {
        if (true || config('app.env') === 'local') {
            $url = $url . '&version=beta';
        }

        return redirect()->away($url);
    }
}
