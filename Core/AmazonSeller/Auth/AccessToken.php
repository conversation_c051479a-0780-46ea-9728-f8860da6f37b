<?php

namespace App\Core\AmazonSeller\Auth;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class AccessToken implements Arrayable, Jsonable
{
    /**
     * @var int
     */
    private int $expiresIn = 3600;

    /**
     * @var Carbon
     */
    private Carbon $expiresAt;

    /**
     * @var string
     */
    private string $accessToken;

    public function __construct(string $accessToken, ?int $expiresIn = null)
    {
        $now = Carbon::now();
        if (!is_null($expiresIn)) {
            $this->expiresIn = $expiresIn;
        }
        $this->accessToken = $accessToken;
        $this->expiresAt = $now->addSeconds($this->expiresIn);
    }

    /**
     * @return int
     */
    public function getExpiresIn(): int
    {
        return $this->expiresIn;
    }

    /**
     * @return Carbon
     */
    public function getExpiresAt(): Carbon
    {
        return $this->expiresAt;
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'accessToken' => $this->getAccessToken(),
            'expireIn'    => $this->getExpiresIn(),
            'expireAt'    => $this->getExpiresAt()->toDateTimeString()
        ];
    }

    /**
     * @param int $options
     * @return string
     */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}