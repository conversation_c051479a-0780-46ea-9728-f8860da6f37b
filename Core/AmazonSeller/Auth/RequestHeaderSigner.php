<?php

namespace App\Core\AmazonSeller\Auth;

use App\Core\AmazonSeller\Api\AmazonApiConfiguration;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;

class RequestHeaderSigner
{
    /**
     * @var AmazonApiConfiguration
     */
    private AmazonApiConfiguration $amazonApiConfiguration;

    /**
     * RequestHeaderSigner constructor.
     *
     * @param AmazonApiConfiguration $amazonApiConfiguration
     */
    public function __construct(AmazonApiConfiguration $amazonApiConfiguration)
    {
        $this->amazonApiConfiguration = $amazonApiConfiguration;
    }

    /**
     * @param string $host
     * @param string $uri
     * @param string $service
     * @param string $method
     * @param string $accessToken
     * @param string|null $payload
     * @param string|null $queryString
     * @param array|null $exitingHeaders
     * @param string|null $securityToken
     * @return Collection
     * @throws Exception
     */
    public function getAuthorizationHeader(
        string $host,
        string $uri,
        string $service,
        string $method,
        string $accessToken,
        ?string $payload = null,
        ?string $queryString = null,
        ?array $exitingHeaders = null,
        ?string $securityToken = null
    ): Collection {
        $configuration = $this->amazonApiConfiguration;

        $algo = 'sha256';
        $accessKey = $configuration->getStsAccessKey();
        $secretKey = $configuration->getStsSecretKey();
        $region = $configuration->getRegion();

        $checkValues = ['accessKey', 'secretKey', 'region'];
        foreach ($checkValues as $key) {
            $value = ${$key} ?? null;
            if (is_null($value)) {
                $errorMessage = 'DEV STS exception - key "' . $key . '" is required';
                throw new Exception($errorMessage);
            }
        }

        $userAgent = 'EvatApp/1.0 (Language=PHP/8.0; Platform=Windows/10)';
        $terminationString = 'aws4_request';
        $algorithm = 'AWS4-HMAC-SHA256';
        $exitingHeaders = $exitingHeaders ?? [];

        $queryString = $queryString ?? '';
        $payload = $payload ?? '';

        $timezone = 'Europe/London';
        $now = Carbon::now($timezone)->utc();
        $amzDate = $now->format('Ymd\THis\Z');
        $date = $now->format('Ymd');

        $hashedPayload = hash($algo, $payload);

        $canonicalHeaders = [
            'host'               => $host,
            'x-amz-access-token' => $accessToken,
            'x-amz-date'         => $amzDate,
            'user-agent'         => $userAgent,
        ];

        if (!is_null($securityToken)) {
            $canonicalHeaders['x-amz-security-token'] = $securityToken;
        }

        $canonicalHeaders = collect($canonicalHeaders)
            ->sortBy(function ($value, $key) {
                return mb_strtolower($key);
            });

        $canonicalHeadersStr = '';
        if ($canonicalHeaders->count() > 0) {
            $canonicalHeadersStr = $canonicalHeaders->map(function ($value, $key) {
                    return $key . ':' . $value;
                })->implode("\n") . "\n";
        }
        $signedHeaderKeysStr = $canonicalHeaders
            ->keys()
            ->map(function ($value) {
                return mb_strtolower($value);
            })
            ->sortBy(function ($value) {
                return $value;
            })
            ->implode(';');

        //prepare canonical request
        $canonicalRequest = [
            $method,
            $uri,
            $queryString,
            $canonicalHeadersStr,
            $signedHeaderKeysStr,
            $hashedPayload
        ];

        $canonicalRequest = implode("\n", $canonicalRequest);

        $hashedCanonicalRequest = hash($algo, $canonicalRequest);

        //Prepare credentials scope
        $credentialScope = [
            $date,
            $region,
            $service,
            $terminationString
        ];
        $credentialScope = implode('/', $credentialScope);

        //Prepare the signature payload
        $stringToSign = [
            $algorithm,
            $amzDate,
            $credentialScope,
            $hashedCanonicalRequest
        ];

        $stringToSign = implode("\n", $stringToSign);

        //Prepare lockers
        $kSecret = 'AWS4' . $secretKey;
        $kDate = hash_hmac($algo, $date, $kSecret, true);
        $kRegion = hash_hmac($algo, $region, $kDate, true);
        $kService = hash_hmac($algo, $service, $kRegion, true);
        $kSigning = hash_hmac($algo, $terminationString, $kService, true);
        /**
         * Compute the signature
         *
         * Without fourth parameter passed as true, returns lowercase hex bits as called for by docs
         */
        $signature = hash_hmac($algo, $stringToSign, $kSigning);
        $signature = trim($signature);

        $authorizationHeader = $algorithm . ' Credential=' . $accessKey . '/' . $credentialScope . ', SignedHeaders=' . $signedHeaderKeysStr . ', Signature=' . $signature;
        $authorizationHeader = [
            'Authorization' => $authorizationHeader,
        ];

        $headers = array_merge(
            $canonicalHeaders->toArray(),
            $authorizationHeader,
            $exitingHeaders
        );

        $headers = collect($headers)->mapWithKeys(function ($value, $key) {
            return [$key => $value];
        });

        return $headers;
    }
}
