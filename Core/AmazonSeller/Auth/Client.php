<?php

namespace App\Core\AmazonSeller\Auth;

use App\Core\AmazonSeller\Auth\Contracts\LWAArrayableContract;
use App\Core\AmazonSeller\Auth\Contracts\LWASessionContract;
use App\Core\Data\Repositories\Contracts\SalesChannelRepositoryContract;
use Illuminate\Http\RedirectResponse;

class Client implements LWAArrayableContract
{
    /**
     * @var ClientAuthorization
     */
    protected $authorization;

    /**
     * @var LWASessionContract
     */
    protected $session;

    /**
     * @var bool
     */
    protected $usePKCE = false;

    /**
     * @var array
     */
    protected $PKCEAlgorithms = [
        'S256'  => 'sha256',
        'plain' => false
    ];

    /**
     * @var Configuration
     */
    protected $config;

    /**
     * @var string
     */
    protected $PKCEAlgorithm = 'S256';

    /**
     * @var string full system path to the certificate
     */
    protected $certPath;

    /**
     * @var array
     */
    protected $scopes = [];

    /**
     * @var Jwt |null
     */
    protected $token;

    /**
     * @var null|static
     */
    protected $userInfo;

    private SalesChannelRepositoryContract $salesChannelRepo;

    public function __construct(
        ClientAuthorization $authorization,
        LWASessionContract $session,
        Configuration $configuration
    ) {
        $this->authorization = $authorization;
        $this->session = $session;
        $this->salesChannelRepo = app()->make(SalesChannelRepositoryContract::class);

        // Last
        $this->config = $configuration;
    }

    /**
     * @param string|null $state
     * @return RedirectResponse
     * @throws \Exception
     */
    public function requestConsent(?string $state = null): RedirectResponse
    {
        $authEndpoint = $this->getConfigParameter('consentUri');

        if (is_null($state)) {
            $state = $this->generateRandomString(32, 'lowercase');
        }
        $this->setState($state);

        $authParams = [
            'application_id' => $this->authorization->getApplicationId(),
            'redirect_uri'   => $this->getRedirectURL(),
            'state'          => $state
        ];

        // PKCE
        if ($this->isUsingPKCE()) {
            $method = $this->getPKCEMethod();
            $codeVerifier = $this->generateCodeVerifier();
            $this->setCodeVerifier($codeVerifier);
            $codeChallenge = $this->resolveCodeChallenge($codeVerifier);

            $PKCEParams = [
                'code_challenge'        => $codeChallenge,
                'code_challenge_method' => $method
            ];

            $authParams = array_merge($authParams, $PKCEParams);
        }

        $authEndpoint = LWA::buildUrl($authEndpoint, $authParams);

        return LWA::redirect($authEndpoint);
    }

    /**
     * @throws LWAException
     */
    public function authenticate(): void
    {
        $error = LWA::get('error');
        $errorDescription = LWA::get('error_description');
        if (!is_null($error)) {
            if (!is_null($errorDescription)) {
                $errorDescription = ' Description: ' . $errorDescription;
            }

            $errorDescription = $errorDescription ?? '';

            throw new LWAException('Error: ' . $error . $errorDescription);
        }

        $code = LWA::get('spapi_oauth_code');
        if (!is_null($code)) {
            $tokenData = $this->requestTokens($code);
            $error = $tokenData->error ?? null;
            $errorDescription = $tokenData->error_description ?? null;
            // Throw an error if the server returns one
            if (!is_null($error)) {
                $error = 'Got error response: ' . $error;
                if (!is_null($errorDescription)) {
                    $error = $error . '. Description: ' . $errorDescription;
                }
                throw new LWAException($error);
            }

            // Do an LWA Connect session check
            $state = LWA::get('state');
            if (is_null($state) || $state !== $this->getState()) {
                throw new LWAException('Unable to determine state');
            }

            // Do sales channel check
            $state = Base64::base64UrlDecode($state);
            $state = json_decode($state, true);
            $salesChannelId = $state['salesChannelId'] ?? 0;
            $salesChannel = $this->salesChannelRepo->getSalesChannelById($salesChannelId);
            if (is_null($salesChannel)) {
                throw new LWAException('No sales channel found');
            }

            $sellingPartnerId = LWA::get('selling_partner_id');
            if ($salesChannel->uaid !== $sellingPartnerId) {
                throw new LWAException(
                    _l('sales-channel.Sales channel UAID-s mismatch',
                        [
                            'uaidI'  => $salesChannel->uaid,
                            'uaidII' => $sellingPartnerId
                        ]
                    ), 409);
            }

            // Cleanup state
            $this->unsetState();

            $token = new Jwt($tokenData, $this->config);

            $this->setToken($token);
            $this->salesChannelRepo->setAmazonRefreshTokenForSalesChannelId($salesChannelId, $token->getRefreshToken());
        }
    }

    protected function requestTokens(string $code)
    {
        $headers = [];
        $grantType = 'authorization_code';
        $tokenEndpoint = $this->getConfigParameter('tokenEndpointUri');
        $clientId = $this->authorization->getClientId();

        $tokenParams = [
            'grant_type'    => $grantType,
            'code'          => $code,
            'redirect_uri'  => $this->getRedirectURL(),
            'client_id'     => $clientId,
            'client_secret' => $this->authorization->getClientSecret()
        ];

        if ($this->isUsingPKCE()) {
            unset($tokenParams['client_secret']);
            $PKCEParams = [
                'client_id'     => $clientId,
                'code_verifier' => $this->getCodeVerifier()
            ];

            $tokenParams = array_merge($tokenParams, $PKCEParams);
        }

        $data = LWA::request(
            $tokenEndpoint,
            'POST',
            $tokenParams,
            $headers
        );

        return json_decode($data);
    }

    /**
     * @param string $code
     * @return string
     */
    protected function resolveCodeChallenge(string $code): string
    {
        $algorithm = $this->getPKCEAlgorithm();
        $hash = hash($algorithm, $code, true);
        $base64Hash = base64_encode($hash);
        $base64HashStr = strtr($base64Hash, '+/', '-_');

        return rtrim($base64HashStr, '=');
    }

    /**
     * @return string
     * @throws \Exception
     */
    protected function generateCodeVerifier(): string
    {
        $codeVerifier = random_bytes(64);
        $codeVerifier = base64_encode($codeVerifier);
        $codeVerifier = strtr($codeVerifier, '+/', '-_');

        return rtrim($codeVerifier, '=');
    }

    /**
     * @param mixed ...$scopes
     * @return $this
     */
    public function addScope(...$scopes): Client
    {
        if (count($scopes) > 0 && is_array($scopes[0])) {
            $scopes = $scopes[0];
        }
        $scopes = (array)$scopes;
        $this->scopes = array_merge($this->scopes, $scopes);

        return $this;
    }

    /**
     * @return array
     */
    public function getScopes(): array
    {
        return $this->scopes;
    }

    /**
     * @param string $key
     * @param null $default
     * @return mixed|null
     */
    public function getConfigParameter(string $key, $default = null)
    {
        return $this->config->getConfigParameter($key, $default);
    }

    /**
     * @return string
     */
    public function getRedirectURL(): string
    {
        return $this->getConfigParameter('redirectUri');
    }

    /**
     * @return bool
     */
    public function isUsingPKCE(): bool
    {
        return $this->usePKCE;
    }

    /**
     * @return string
     */
    public function getPKCEMethod(): string
    {
        return $this->PKCEAlgorithm;
    }

    /**
     * @return string
     */
    public function getPKCEAlgorithm(): ?string
    {
        return $this->PKCEAlgorithms[$this->getPKCEMethod()] ?? null;
    }

    /**
     * @return bool
     */
    public function getVerifyPeer(): bool
    {
        return LWA::getVerifyPeer();
    }

    /**
     * @return bool
     */
    public function getVerifyHost(): bool
    {
        return LWA::getVerifyHost();
    }

    /**
     * @return string|null
     */
    public function getCertPath(): ?string
    {
        return $this->certPath;
    }

    /**
     * @param int $length
     * @param string|null $transform
     * @param string $characters
     * @return string
     */
    protected function generateRandomString(int $length = 32, ?string $transform = null, string $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'): string
    {
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        $transformTypes = [
            'uppercase',
            'lowercase',
        ];
        if (!is_null($transform) && in_array($transform, $transformTypes)) {
            if ($transform === 'uppercase') {
                $randomString = mb_strtoupper($randomString);
            }
            if ($transform === 'lowercase') {
                $randomString = mb_strtolower($randomString);
            }
        }

        return $randomString;
    }

    /**
     * @param string $key
     * @param mixed | null $default
     * @return mixed
     */
    protected function sessionGet(string $key, mixed $default = null): mixed
    {
        return $this->session->get($key, $default);
    }

    /**
     * @param string $key
     * @param mixed $value
     * @return LWASessionContract
     */
    public function sessionPut(string $key, $value): LWASessionContract
    {
        return $this->session->put($key, $value);
    }

    /**
     * @param string $key
     * @return LWASessionContract
     */
    public function sessionUnset(string $key): LWASessionContract
    {
        return $this->session->unset($key);
    }

    /**
     * @param string $state
     */
    protected function setState(string $state): void
    {
        $this->sessionPut('lwa_connect_state', $state);
    }

    public function requestUserInfo()
    {
        $userInfoEndpoint = $this->getConfigParameter('userinfo_endpoint') . '?schema=openid';
        $headers = [
            'Authorization: Bearer ' . $this->token->getAccessToken(),
            'Accept: application/json'
        ];

        $data = LWA::request($userInfoEndpoint, 'POST', null, $headers);
        $statusCode = LWA::getLastResponseCode();
        if ($statusCode !== 200) {
            throw new LWAException('The communication to retrieve user data has failed with status code ' . $statusCode);
        }
        $data = json_decode($data);
        $this->setUserInfo($data);

        return $data;
    }

    /**
     * @param string $accessToken
     * @param string|null $redirectUrl
     * @return RedirectResponse
     */
    public function logout(string $accessToken, ?string $redirectUrl = null): RedirectResponse
    {
        $logoutUrl = $this->getConfigParameter('end_session_endpoint');

        $params = [
            'id_token_hint' => $accessToken
        ];
        if (!is_null($redirectUrl)) {
            $params['post_logout_redirect_uri'] = $redirectUrl;
        }

        $logoutUrl = LWA::buildUrl($logoutUrl, $params);

        return LWA::redirect($logoutUrl);
    }

    /**
     * @return string|null
     */
    protected function getState(): ?string
    {
        return $this->sessionGet('lwa_connect_state');
    }

    protected function unsetState(): void
    {
        $this->sessionUnset('lwa_connect_state');
    }

    /**
     * @param string $codeVerifier
     */
    protected function setCodeVerifier(string $codeVerifier): void
    {
        $this->sessionPut('lwa_connect_code_verifier', $codeVerifier);
    }

    /**
     * @return string
     */
    protected function getCodeVerifier(): string
    {
        return $this->sessionGet('lwa_connect_code_verifier');
    }

    /**
     * @param Jwt|null $token
     * @return Client
     */
    public function setToken(?Jwt $token = null): Client
    {
        $this->token = $token;

        return $this;
    }

    /**
     * @param $userInfo
     * @return $this
     */
    public function setUserInfo($userInfo): Client
    {
        $this->userInfo = $userInfo;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'redirectURL'   => $this->getRedirectURL(),
            'isUsingPKCE'   => $this->isUsingPKCE(),
            'PKCEAlgorithm' => $this->getPKCEAlgorithm(),
            'configuration' => $this->config->toArray(),
            'verifyPeer'    => $this->getVerifyPeer(),
            'verifyHost'    => $this->getVerifyHost(),
            'certPath'      => $this->getCertPath(),
            'authorization' => $this->authorization->toArray()
        ];
    }
}
