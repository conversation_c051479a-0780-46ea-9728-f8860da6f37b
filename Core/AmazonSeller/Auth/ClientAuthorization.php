<?php

namespace App\Core\AmazonSeller\Auth;

use App\Core\AmazonSeller\Auth\Contracts\LWAArrayableContract;

class ClientAuthorization implements LWAArrayableContract
{
    /**
     * @var string
     */
    private string $clientId;

    /**
     * @var string
     */
    private string $applicationId;
    /**
     * @var string
     */
    private string $clientSecret;

    /**
     * ClientAuthorization constructor.
     *
     * @param string $clientId
     * @param string $clientSecret
     * @param string $applicationId
     */
    public function __construct(string $clientId, string $clientSecret, string $applicationId)
    {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->applicationId = $applicationId;
    }

    /**
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function getApplicationId(): string
    {
        return $this->applicationId;
    }

    /**
     * @return string
     */
    public function getClientSecret(): string
    {
        return $this->clientSecret;
    }

    /**
     * @param bool $full
     * @return string
     */
    public function getBaseAuthHeader(bool $full = false): string
    {
        $auth = urlencode($this->getClientId()) . ':' . urlencode($this->getClientSecret());
        $auth = base64_encode($auth);
        if ($full) {
            $auth = 'Authorization: Basic ' . $auth;
        }

        return $auth;
    }


    public function toArray(): array
    {
        return [
            'clientId'        => $this->getClientId(),
            'clientSecret'    => $this->getClientSecret(),
            'basicAuthHeader' => $this->getBaseAuthHeader()
        ];
    }
}
