<?php

namespace App\Core\AmazonSeller\Api;

use App\Core\AmazonSeller\Api\Enums\RegionEnum;
use Illuminate\Contracts\Support\Arrayable;

class AmazonApiConfiguration implements Arrayable
{
    /**
     * @var bool
     */
    private bool $debug;

    /**
     * @var string
     */
    private string $region;

    /**
     * @var string|null
     */
    private ?string $endpoint = null;

    /**
     * @var string|null
     */
    private ?string $clientId = null;

    /**
     * @var string|null
     */
    private ?string $clientSecret = null;

    /**
     * @var string|null
     */
    private ?string $stsAccessKey = null;

    /**
     * @var string|null
     */
    private ?string $roleArn = null;

    /**
     * @var string|null
     */
    private ?string $stsSecretKey = null;

    /**
     * @var string
     */
    private string $stsHost = 'sts.amazonaws.com';

    /**
     * @var string
     */
    private string $stsUri = '/';

    /**
     * @var string
     */
    private string $host;

    /**
     * AmazonApiConfiguration constructor.
     *
     * @param bool $debug
     */
    public function __construct(bool $debug = false)
    {
        $this->debug = $debug;
        $this->resolveDefaultRegion();
    }

    private function resolveDefaultRegion(): void
    {
        $region = RegionEnum::EU_WEST_1;

        $endpoint = $region['endpoint'];
        $schema = 'https://';
        if ($this->debug) {
            $endpoint = 'sandbox.' . $endpoint;
        }
        $this->host = $endpoint;
        $endpoint = $schema . $endpoint;
        $region['endpoint'] = $endpoint;

        $this->setRegion($region);
    }

    /**
     * @return string
     */
    public function getRegion(): string
    {
        return $this->region;
    }

    /**
     * @return string
     */
    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    /**
     * @param array RegionEnum::const
     * @return AmazonApiConfiguration
     */
    public function setRegion(array $region): AmazonApiConfiguration
    {
        $this->region = $region['region'];
        $this->endpoint = $region['endpoint'];
        $this->stsHost = $region['stsHost'];

        return $this;
    }

    /**
     * @return string|null
     */
    public function getClientId(): ?string
    {
        return $this->clientId;
    }

    /**
     * @param string|null $clientId
     * @return $this
     */
    public function setClientId(?string $clientId = null): AmazonApiConfiguration
    {
        $this->clientId = $clientId;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getClientSecret(): ?string
    {
        return $this->clientSecret;
    }

    /**
     * @param string|null $clientSecret
     * @return $this
     */
    public function setClientSecret(?string $clientSecret = null): AmazonApiConfiguration
    {
        $this->clientSecret = $clientSecret;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getStsAccessKey(): ?string
    {
        return $this->stsAccessKey;
    }

    /**
     * @param string|null $stsAccessKey
     * @return $this
     */
    public function setStsAccessKey(?string $stsAccessKey = null): AmazonApiConfiguration
    {
        $this->stsAccessKey = $stsAccessKey;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getRoleArn(): ?string
    {
        return $this->roleArn;
    }

    /**
     * @param string|null $roleArn
     * @return $this
     */
    public function setRoleArn(?string $roleArn = null): AmazonApiConfiguration
    {
        $this->roleArn = $roleArn;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getStsSecretKey(): ?string
    {
        return $this->stsSecretKey;
    }

    /**
     * @param string|null $stsSecretKey
     * @return $this
     */
    public function setStsSecretKey(?string $stsSecretKey = null): AmazonApiConfiguration
    {
        $this->stsSecretKey = $stsSecretKey;

        return $this;
    }

    /**
     * @return string
     */
    public function getStsHost(): string
    {
        return $this->stsHost;
    }

    /**
     * @return string
     */
    public function getStsUri(): string
    {
        return $this->stsUri;
    }

    public function getStsBaseUri(): string
    {
        return 'https://' . $this->getStsHost();
    }

    /**
     * @return string
     */
    public function getHost(): string
    {
        return $this->host;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'clientId'     => $this->getClientId(),
            'clientSecret' => $this->getClientSecret(),
            'region'       => $this->getRegion(),
            'endpoint'     => $this->getEndpoint(),
            'host'         => $this->getHost(),
            'roleArn'      => $this->getRoleArn(),
            'stsAccessKey' => $this->getStsAccessKey(),
            'stsSecretKey' => $this->getStsSecretKey(),
            'stsHost'      => $this->getStsHost(),
            'stsUri'       => $this->getStsUri(),
            'stsBaseUri'   => $this->getStsBaseUri(),
        ];
    }
}
