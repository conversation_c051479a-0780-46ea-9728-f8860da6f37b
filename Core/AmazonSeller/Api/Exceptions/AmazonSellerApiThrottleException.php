<?php

namespace App\Core\AmazonSeller\Api\Exceptions;

use Exception;

class AmazonSellerApiThrottleException extends Exception
{
    private ?array $headers = [];

    /**
     * @return array|null
     */
    public function getHeaders(): ?array
    {
        return $this->headers;
    }

    /**
     * @param array $headers
     * @return AmazonSellerApiThrottleException
     */
    public function setHeaders(array $headers): AmazonSellerApiThrottleException
    {
        $this->headers = $headers;

        return $this;
    }
}
