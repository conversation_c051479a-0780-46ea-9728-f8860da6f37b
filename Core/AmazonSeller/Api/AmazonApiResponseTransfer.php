<?php

namespace App\Core\AmazonSeller\Api;

use Illuminate\Contracts\Support\Arrayable;

class AmazonApiResponseTransfer implements Arrayable
{
    private array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    public function toArray(): array
    {
        return $this->getData();
    }
}
