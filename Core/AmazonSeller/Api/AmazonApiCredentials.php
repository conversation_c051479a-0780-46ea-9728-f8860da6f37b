<?php

namespace App\Core\AmazonSeller\Api;

use App\Core\AmazonSeller\Auth\AmazonTokenStore;
use App\Core\AmazonSeller\Auth\LWAException;
use App\Core\AmazonSeller\Auth\StsCredentials;
use App\Core\AmazonSeller\Auth\RequestHeaderSigner;
use App\Core\Data\Models\SalesChannel;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class AmazonApiCredentials implements Arrayable, Jsonable
{
    /**
     * @var SalesChannel
     */
    private SalesChannel $salesChannel;

    /**
     * @var AmazonTokenStore
     */
    private AmazonTokenStore $tokenStore;

    /**
     * @var AmazonApiConfiguration
     */
    private AmazonApiConfiguration $amazonApiConfiguration;

    /**
     * @var RequestHeaderSigner
     */
    private RequestHeaderSigner $requestHeaderSigner;

    /**
     * AmazonApiCredentials constructor.
     *
     * @param AmazonApiConfiguration $amazonApiConfiguration
     * @param SalesChannel $salesChannel
     * @throws LWAException
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function __construct(AmazonApiConfiguration $amazonApiConfiguration, SalesChannel $salesChannel)
    {
        if (is_null($salesChannel->amazon_refresh_token)) {
            throw new Exception('Sales channel does not have refresh token');
        }
        $this->setSalesChannel($salesChannel);

        $requestHeaderSigner = new RequestHeaderSigner($amazonApiConfiguration);
        $this->setRequestHeaderSigner($requestHeaderSigner);

        $tokenStore = new AmazonTokenStore(
            $amazonApiConfiguration,
            $salesChannel,
            $requestHeaderSigner
        );
        $this->setTokenStore($tokenStore);

        $this->setAmazonApiConfiguration($tokenStore->getAmazonApiConfiguration());
    }

    /**
     * @return string
     */
    public function getRefreshToken(): string
    {
        return $this->tokenStore->getRefreshToken();
    }

    /**
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->tokenStore->getAccessToken();
    }

    /**
     * @return StsCredentials
     */
    public function getStsCredentials(): StsCredentials
    {
        return $this->tokenStore->getStsCredentials();
    }

    /**
     * @return AmazonApiConfiguration
     */
    public function getAmazonApiConfiguration(): AmazonApiConfiguration
    {
        return $this->amazonApiConfiguration;
    }

    /**
     * @param AmazonApiConfiguration $amazonApiConfiguration
     * @return AmazonApiCredentials
     */
    public function setAmazonApiConfiguration(AmazonApiConfiguration $amazonApiConfiguration): AmazonApiCredentials
    {
        $this->amazonApiConfiguration = $amazonApiConfiguration;

        return $this;
    }

    /**
     * @return SalesChannel
     */
    public function getSalesChannel(): SalesChannel
    {
        return $this->salesChannel;
    }

    /**
     * @param SalesChannel $salesChannel
     * @return AmazonApiCredentials
     */
    public function setSalesChannel(SalesChannel $salesChannel): AmazonApiCredentials
    {
        $this->salesChannel = $salesChannel;

        return $this;
    }

    /**
     * @return AmazonTokenStore
     */
    public function getTokenStore(): AmazonTokenStore
    {
        return $this->tokenStore;
    }

    /**
     * @param AmazonTokenStore $tokenStore
     * @return AmazonApiCredentials
     */
    public function setTokenStore(AmazonTokenStore $tokenStore): AmazonApiCredentials
    {
        $this->tokenStore = $tokenStore;

        return $this;
    }

    /**
     * @return RequestHeaderSigner
     */
    public function getRequestHeaderSigner(): RequestHeaderSigner
    {
        return $this->requestHeaderSigner;
    }

    /**
     * @param RequestHeaderSigner $requestHeaderSigner
     * @return AmazonApiCredentials
     */
    public function setRequestHeaderSigner(RequestHeaderSigner $requestHeaderSigner): AmazonApiCredentials
    {
        $this->requestHeaderSigner = $requestHeaderSigner;

        return $this;
    }


    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'amazonApiConfiguration' => $this->getAmazonApiConfiguration()->toArray(),
            'salesChannel'           => $this->getSalesChannel()->toArray(),
            'stsCredentials'         => $this->getStsCredentials()->toArray(),
            'accessToken'            => $this->getAccessToken(),
            'refreshToken'           => $this->getRefreshToken(),
        ];
    }

    /**
     * @param int $options
     * @return string
     */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
