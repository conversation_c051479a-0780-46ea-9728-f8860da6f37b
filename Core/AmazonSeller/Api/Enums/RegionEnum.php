<?php

namespace App\Core\AmazonSeller\Api\Enums;

class RegionEnum
{
    /**
     *
     * Regions
     *
     * @link https://github.com/amzn/selling-partner-api-docs/blob/main/guides/developer-guide/SellingPartnerApiDeveloperGuide.md#selling-partner-api-endpoints
     *
     * STS
     * @link https://docs.aws.amazon.com/general/latest/gr/sts.html
     */
    const US_EAST_1 = [
        'region'   => 'us-east-1',
        'endpoint' => 'sellingpartnerapi-na.amazon.com',
        'stsHost'  => 'sts.us-east-1.amazonaws.com'
    ];

    const EU_WEST_1 = [
        'region'   => 'eu-west-1',
        'endpoint' => 'sellingpartnerapi-eu.amazon.com',
        'stsHost'  => 'sts.eu-west-1.amazonaws.com'
    ];

    const EU_WEST_2 = [
        'region'   => 'eu-west-2',
        'endpoint' => 'sellingpartnerapi-fe.amazon.com',
        'stsHost'  => 'sts.eu-west-2.amazonaws.com'
    ];
}
