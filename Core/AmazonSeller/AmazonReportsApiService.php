<?php

namespace App\Core\AmazonSeller;

use App\Core\AmazonSeller\Api\AmazonApiResponseTransfer;
use App\Core\AmazonSeller\Contracts\AmazonApiAbstractService;
use App\Core\AmazonSeller\Contracts\AmazonReportsApiServiceContract;
use App\Core\Data\Models\SalesChannel;
use Carbon\Carbon;
use Exception;
use SellingPartnerApi\Api\ReportsV20210630Api;
use SellingPartnerApi\Model\ReportsV20210630\CreateReportSpecification;
use SellingPartnerApi\ReportType;

class AmazonReportsApiService extends AmazonApiAbstractService implements AmazonReportsApiServiceContract
{
    private array $marketplacesIds = ['A1PA6795UKMFR9'];

    protected bool $test = false;

    private function getApi(SalesChannel $salesChannel): ReportsV20210630Api
    {
        return new ReportsV20210630Api($this->getConfiguration($salesChannel));
    }

    /**
     * @inheritDoc
     */
    public function createReportForPeriod(SalesChannel $salesChannel, Carbon $start, Carbon $end): AmazonApiResponseTransfer
    {
        if (is_null($salesChannel->amazon_refresh_token)) {
            throw new Exception('Sales channel does not have refresh token');
        }

        $api = $this->getApi($salesChannel);

        $body = new CreateReportSpecification();

        $body->setMarketplaceIds($this->marketplacesIds)
            ->setReportType(ReportType::GET_VAT_TRANSACTION_DATA['name'])
            ->setDataStartTime($start->toDateTimeLocalString())
            ->setDataEndTime($end->toDateTimeLocalString());

        $result = $api->createReport($body);

        return new AmazonApiResponseTransfer(['reportId' => $result->getReportId()]);
    }

    /**
     * @inheritdoc
     */
    public function getReportById(SalesChannel $salesChannel, string $reportId): AmazonApiResponseTransfer
    {
        if (is_null($salesChannel->amazon_refresh_token)) {
            throw new Exception('Sales channel does not have refresh token');
        }

        $api = $this->getApi($salesChannel);
        $result = $api->getReport($reportId);

        $data = [
            'processingStatus' => $result->getProcessingStatus(),
            'dataStartTime'    => $result->getDataStartTime(),
            'dataEndTime'      => $result->getDataEndTime(),
            'reportDocumentId' => $result->getReportDocumentId(),
        ];

        return new AmazonApiResponseTransfer($data);
    }

    /**
     * @inheritDoc
     */
    public function getReportDocumentByDocumentId(SalesChannel $salesChannel, string $documentId): AmazonApiResponseTransfer
    {

        if (is_null($salesChannel->amazon_refresh_token)) {
            throw new Exception('Sales channel does not have refresh token');
        }

        $api = $this->getApi($salesChannel);

        $result = $api->getReportDocument($documentId);

        $data = [
            'reportDocumentId' => $result->getReportDocumentId(),
            'url'              => $result->getUrl()
        ];

        return new AmazonApiResponseTransfer($data);
    }
}
