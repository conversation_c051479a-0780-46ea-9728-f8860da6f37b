<?php

namespace App\Core\CompanyCountrySales;

use App\Core\Data\Repositories\Contracts\CompanyCountrySaleRepositoryContract;
use App\Core\CompanyCountrySales\Contracts\CompanyCountrySaleServiceContract;
use Carbon\Carbon;

class CompanyCountrySaleService implements CompanyCountrySaleServiceContract
{

    /**
     * @var companyCountrySaleRepositoryContract
     */
    private $companyCountrySaleRepo;

    /**
     * CompanyCountrySaleService constructor.
     *
     * @param CompanyCountrySaleRepositoryContract $companyCountrySaleRepo
     */
    public function __construct(CompanyCountrySaleRepositoryContract $companyCountrySaleRepo)
    {

        $this->companyCountrySaleRepo = $companyCountrySaleRepo;
    }

    /**
     * @inheritDoc
     */
    public function storeUpdateDeleteCompanyCountrySale(
        int $companyId,
        int $countryId,
        ?string $firstSaleDate,
        ?int $currentYearSaleEstimate,
        ?int $nextYearSaleEstimate,
        ?int $companyCountrySaleId
    ): void {
        if (is_null($firstSaleDate) && is_null($currentYearSaleEstimate) && is_null($nextYearSaleEstimate)) {
            if (!is_null($companyCountrySaleId)) {
                $this->companyCountrySaleRepo->deleteCompanyCountrySaleById($companyCountrySaleId);
            }

            return;
        }

        if ($companyCountrySaleId) {
            $companyCountrySale = $this->companyCountrySaleRepo->getCompanyCountrySaleById($companyCountrySaleId);
            if (is_null($companyCountrySale)) {
                return;
            }
        } else {
            $companyCountrySale = $this->companyCountrySaleRepo->getEmptyCompanyCountrySaleModel();
        }

        if (!is_null($firstSaleDate)) {
            $firstSaleDate = Carbon::parse($firstSaleDate)->toDateString();
        }

        $companyCountrySale->company_id = $companyId;
        $companyCountrySale->country_id = $countryId;
        $companyCountrySale->first_sale_date = $firstSaleDate;
        $companyCountrySale->current_year_sale_estimate = $currentYearSaleEstimate;
        $companyCountrySale->next_year_sale_estimate = $nextYearSaleEstimate;

        $companyCountrySale->save();
    }
}
