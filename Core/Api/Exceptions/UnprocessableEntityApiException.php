<?php

namespace App\Core\Api\Exceptions;

use App\Core\Api\Exceptions\Contracts\ApiException;
use App\Core\Api\Responses\Helpers\HttpStatusCode;

class UnprocessableEntityApiException extends ApiException
{
    public function __construct(
        string|array|null $errors = null,
        HttpStatusCode $httpStatusCode = HttpStatusCode::UNPROCESSABLE_ENTITY,
        int|array|null $statusCode = null,
        array $data = [],
        array $meta = [],
        bool $reportable = false,
        bool $loggable = false,
        bool $success = false,
    ) {
        $finalErrors = ['Unprocessable content'];
        if (is_array($errors)) {
            $finalErrors = array_merge($finalErrors, $errors);
        }

        if (is_string($errors)) {
            $finalErrors = array_merge($finalErrors, [$errors]);
        }

        parent::__construct(
            errors: $finalErrors,
            httpStatusCode: $httpStatusCode,
            statusCode: $statusCode,
            data: $data,
            meta: $meta,
            reportable: $reportable,
            loggable: $loggable,
            success: $success,
        );
    }
}
