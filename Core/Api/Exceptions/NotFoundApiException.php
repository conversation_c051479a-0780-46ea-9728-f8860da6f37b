<?php

namespace App\Core\Api\Exceptions;

use App\Core\Api\Exceptions\Contracts\ApiException;
use App\Core\Api\Responses\Helpers\HttpStatusCode;

class NotFoundApiException extends ApiException
{
    public function __construct(
        string|array|null $errors = null,
        HttpStatusCode $httpStatusCode = HttpStatusCode::NOT_FOUND,
        int|array|null $statusCode = null,
        array $data = [],
        array $meta = [],
        bool $reportable = false,
        bool $loggable = false,
        bool $success = false,
    ) {
        $finalErrors = ['Not found'];
        if (is_array($errors)) {
            $finalErrors = array_merge($finalErrors, $errors);
        }

        if (is_string($errors)) {
            $finalErrors = array_merge($finalErrors, [$errors]);
        }

        parent::__construct(
            errors: $finalErrors,
            httpStatusCode: $httpStatusCode,
            statusCode: $statusCode,
            data: $data,
            meta: $meta,
            reportable: $reportable,
            loggable: $loggable,
            success: $success,
        );
    }
}
