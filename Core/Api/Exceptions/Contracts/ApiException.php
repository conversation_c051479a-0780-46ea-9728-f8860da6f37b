<?php

namespace App\Core\Api\Exceptions\Contracts;

use App\Core\Api\Responses\Helpers\HttpStatusCode;
use App\Core\Api\Responses\Traits\ApiResponseTrait;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Exception;

class ApiException extends Exception implements Arrayable, Jsonable
{
    use ApiResponseTrait;

    private bool $reportable;

    public function __construct(
        string|array|null $errors = null,
        HttpStatusCode $httpStatusCode = HttpStatusCode::INTERNAL_SERVER_ERROR,
        int|array|string|null $statusCode = null,
        array $data = [],
        array $meta = [],
        bool $reportable = false,
        bool $loggable = false,
        bool $success = false,
    ) {
        $statusCode = $statusCode ?? $httpStatusCode->getCode();
        $errors = $this->resolveErrors($errors);

        $this->setHttpStatusCode($httpStatusCode->getCode());
        $this->setStatusCode($statusCode);
        $this->setSuccess($success);
        $this->setErrors($errors);
        $this->setData($data);
        $this->setMeta($meta);
        $this->setReportable($reportable);
        $this->setLoggable($loggable);

        parent::__construct(implode('. ', $errors), $httpStatusCode->getCode());
    }

    private function resolveErrors(string|array|null $errors = null): array
    {
        if (is_null($errors) || (is_array($errors) && count($errors) < 1)) {
            return [HttpStatusCode::INTERNAL_SERVER_ERROR->getMessage()];
        }

        if (is_string($errors)) {
            return [$errors];
        }

        return array_values($errors);
    }

    public function isReportable(): bool
    {
        return $this->reportable;
    }

    public function setReportable(bool $reportable): ApiException
    {
        $this->reportable = $reportable;

        return $this;
    }
}
