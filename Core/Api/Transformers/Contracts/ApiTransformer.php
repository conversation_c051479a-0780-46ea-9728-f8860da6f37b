<?php

namespace App\Core\Api\Transformers\Contracts;

use League\Fractal\TransformerAbstract;

abstract class ApiTransformer extends TransformerAbstract
{
    protected function hasOptionalInclude(string $include): bool
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        $includes = request()->get('with', '');
        $includes = array_map(function ($include) {
            return trim($include);
        }, explode(',', $includes));

        $check = in_array($include, $includes);

        $scopeIdentifier = $this->currentScope->getScopeIdentifier();
        if (!is_null($scopeIdentifier)) {
            $check = $check || in_array($scopeIdentifier . '.' . $include, $includes);
        }

        return $check;
    }
}
