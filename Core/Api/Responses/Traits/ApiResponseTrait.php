<?php

namespace App\Core\Api\Responses\Traits;

use Illuminate\Http\JsonResponse;
use OpenApi\Attributes as OA;

#[OA\OpenApi(
    servers: [
        new OA\Server(url: 'https://api-test.evat.com/v1', description: 'Local development'),
        new OA\Server(url: 'https://api.evat.com/v1', description: 'Production'),
    ],
    security: [[
        'Application-Key'    => '',
        'Application-Secret' => '',
    ]],
)]
#[OA\Components(
    securitySchemes: [
        new OA\SecurityScheme(
            securityScheme: 'Application-Key',
            type: 'apiKey',
        ),
        new OA\SecurityScheme(
            securityScheme: 'Application-Secret',
            type: 'apiKey',
        ),
    ]
)]
#[
    OA\Info(
        version: '1.0.0',
        title: 'eVAT API',
        x: [
            'logo'    => [
                'url'  => '/img/e-vat_logo-shield.png',
                'href' => '/api-docs.php',
            ],
            'contact' => [
                'name'  => 'eVAT',
                'url'   => 'https://evat.com',
                'email' => '<EMAIL>',
            ],
        ]
    )
]
trait ApiResponseTrait
{
    private int $httpStatusCode = 200;
    private int|string|array $statusCode = 200;
    private bool $success = true;
    private array $errors = [];
    private array $data = [];
    private array $meta = [];
    private bool $loggable = false;

    public function getHttpStatusCode(): int
    {
        return $this->httpStatusCode;
    }

    public function getStatusCode(): array
    {
        if (!is_array($this->statusCode)) {
            return [$this->statusCode];
        }

        return $this->statusCode;
    }

    public function getSuccess(): bool
    {
        return $this->success;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function getMeta(): array
    {
        return $this->meta;
    }

    public function isLoggable(): bool
    {
        return $this->loggable;
    }

    public function setHttpStatusCode(int $httpStatusCode): self
    {
        $this->httpStatusCode = $httpStatusCode;

        return $this;
    }

    public function setStatusCode(int|array|string $statusCode): self
    {
        $this->statusCode = $statusCode;

        return $this;
    }

    public function setSuccess(bool $success): self
    {
        $this->success = $success;

        return $this;
    }

    public function setErrors(array $errors): self
    {
        $this->errors = $errors;

        return $this;
    }

    public function setData(array $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function setMeta(array $meta): self
    {
        $this->meta = $meta;

        return $this;
    }

    public function setLoggable(bool $loggable): self
    {
        $this->loggable = $loggable;

        return $this;
    }

    // ACTIONS
    public function response(): JsonResponse
    {
        return response()->json($this->toArray(), $this->getHttpStatusCode());
    }

    public function toArray(): array
    {
        return [
            'codes'   => $this->getStatusCode(),
            'success' => $this->getSuccess(),
            'errors'  => $this->getErrors(),
            'meta'    => $this->getMeta(),
            'data'    => $this->getData(),
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode(value: $this->toArray(), options: $options);
    }
}
