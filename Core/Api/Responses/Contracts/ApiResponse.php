<?php

namespace App\Core\Api\Responses\Contracts;

use App\Core\Api\Responses\Helpers\HttpStatusCode;
use App\Core\Api\Responses\Traits\ApiResponseTrait;

class ApiResponse
{
    use ApiResponseTrait;

    public function __construct(
        HttpStatusCode $httpStatusCode = HttpStatusCode::INTERNAL_SERVER_ERROR,
        int|array|null $statusCode = null,
        array $data = [],
        array $meta = [],
        bool $loggable = false
    ) {
        $statusCode = $statusCode ?? $httpStatusCode->getCode();
        $this->setHttpStatusCode($httpStatusCode->getCode());
        $this->setStatusCode($statusCode);
        $this->setData($data);
        $this->setMeta($meta);
        $this->setLoggable($loggable);
    }
}
