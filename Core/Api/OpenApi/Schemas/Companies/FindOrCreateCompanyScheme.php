<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Companies;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class FindOrCreateCompanyScheme extends AbstractSchema
{
    #[Property(example: 'Evat Ltd.')]
    public string $name;

    #[Property(example: 1)]
    public int $businessTypeId;

    #[Property(example: 'Evat', nullable: true)]
    public ?string $tradeName;

    #[Property(example: 'Ltd.')]
    public string $legalEntityType;

    #[Property(example: 'eCommerce')]
    public string $businessActivity;

    #[Property(example: 'GB000000001')]
    public string $registrationNumber;

    #[Property(example: '2022-01-01', format: 'date')]
    public string $incorporationDate;

    #[Property(example: 100000)]
    public int $shareCapitalValue;

    #[Property(example: 30)]
    public int $shareCapitalCurrencyId;

    #[Property(type: 'object', properties: [
        new Property(property: 'email', type: 'string', example: '<EMAIL>', format: 'email'),
        new Property(property: 'phoneNumber', type: 'string', example: '+44*********0', format: 'phone'),
    ])]
    public array $contact;

    #[Property(type: 'object', properties: [
        new Property(property: 'street', type: 'string', example: 'Evat House'),
        new Property(property: 'houseNumber', type: 'string', example: '1'),
        new Property(property: 'addressAddition', type: 'string', example: 'Apt. 1', nullable: true),
        new Property(property: 'city', type: 'string', example: 'London'),
        new Property(property: 'postalCode', type: 'string', example: '123456'),
        new Property(property: 'state', type: 'string', example: 'Oklahoma', nullable: true),
        new Property(property: 'countryId', type: 'integer', example: 82),
    ])]
    public array $establishmentAddress;

    #[Property(type: 'object', properties: [
        new Property(property: 'countryId', type: 'integer', example: 82),
        new Property(property: 'number', type: 'string', example: 'GB000000001'),
        new Property(property: 'registerDate', type: 'string', example: '2022-01-01', format: 'date'),
        new Property(property: 'establishmentStatusId', type: 'integer', example: 1),
    ], nullable: true)]
    public ?array $vatNumber = null;

    #[Property(type: 'object', properties: [
        new Property(property: 'countryId', type: 'integer', example: 82),
        new Property(property: 'number', type: 'string', example: 'GB00/0001/001'),
        new Property(property: 'registerDate', type: 'string', example: '2020-01-01', format: 'date'),
    ], nullable: true)]
    public ?array $taxNumber = null;

    #[Property(type: 'object', properties: [
        new Property(property: 'countryId', type: 'integer', example: 82),
        new Property(property: 'number', type: 'string', example: 'DE-*********'),
        new Property(property: 'registerDate', type: 'string', example: '2020-01-01', format: 'date'),
        new Property(property: 'ossNumberTypeId', type: 'integer', example: 1),
    ], nullable: true)]
    public ?array $ossNumber = null;

    #[Property(type: 'object', properties: [
        new Property(property: 'countryId', type: 'integer', example: 68),
        new Property(property: 'number', type: 'string', example: 'IM-*********'),
        new Property(property: 'registerDate', type: 'string', example: '2020-01-01', format: 'date'),
        new Property(property: 'iossNumberTypeId', type: 'integer', example: 3),
    ], nullable: true)]
    public ?array $iossNumber = null;

    public static function isObject(): bool
    {
        return true;
    }
}
