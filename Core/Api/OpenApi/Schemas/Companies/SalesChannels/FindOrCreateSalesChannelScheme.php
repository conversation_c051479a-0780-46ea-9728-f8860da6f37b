<?php

namespace App\Core\Api\OpenApi\Schemas\Companies\SalesChannels;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class FindOrCreateSalesChannelScheme extends AbstractSchema
{
    #[Property(example: 'Evat test sales channel')]
    public string $name;

    #[Property(example: 'ASDN34D4G5H')]
    public string $uaid;

    #[Property(example: 1)]
    public int $companyId;

    #[Property(example: 12)]
    public int $platformId;

    #[Property(example: 1, nullable: true)]
    public ?int $platformRegionId;

    public static function isObject(): bool
    {
        return true;
    }
}
