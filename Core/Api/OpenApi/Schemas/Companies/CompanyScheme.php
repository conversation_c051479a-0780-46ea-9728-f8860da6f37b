<?php

namespace App\Core\Api\OpenApi\Schemas\Companies;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema()]
class CompanyScheme extends AbstractSchema
{
    #[Property(example: 101)]
    public int $id;

    #[Property(example: 'Evat Ltd.')]
    public string $name;

    #[Property(example: 'Evat', nullable: true)]
    public ?string $shortName;

    #[Property(example: null, nullable: true)]
    public ?string $tradeName;

    #[Property(example: 'Ltd.')]
    public string $legalEntityType;

    #[Property(example: 'eCommerce')]
    public string $businessActivity;

    #[Property(example: 'GB000000001', nullable: true)]
    public ?string $registrationNumber;

    #[Property(example: 1, nullable: true)]
    public ?int $businessTypeId;

    #[Property(example: '2022-01-01', nullable: true)]
    public ?string $incorporationDate;

    public static function isObject(): bool
    {
        return true;
    }
}
