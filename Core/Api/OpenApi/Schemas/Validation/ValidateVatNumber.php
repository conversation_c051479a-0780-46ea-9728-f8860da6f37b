<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Validation;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class ValidateVatNumber extends AbstractSchema
{
    #[Property(example: 1)]
    public int $id;
    #[Property(example: 'NL12345678901')]
    public string $vatNumber;
    #[Property(example: '12345678901')]
    public string $number;
    #[Property(example: true)]
    public bool $valid;
    #[Property(example: 'success')]
    public string $status;
    #[Property(example: true)]
    public bool $formatValid;
    #[Property(example: true)]
    public bool $validated;
    #[Property(example: '2022-01-01', format: 'date')]
    public string $registerDate;
    #[Property(example: '2022-01-01', format: 'date')]
    public string $validationDate;
    #[Property(example: null, format: 'date')]
    public ?string $endDate = null;

    #[Property(type: 'object', properties: [
        new Property(property: 'id', type: 'integer', example: 74),
        new Property(property: 'name', type: 'string', example: 'France'),
        new Property(property: 'code', type: 'string', example: 'FR'),
        new Property(property: 'vatCode', type: 'string', example: 'FR'),
    ], nullable: true)]
    public ?array $country = null;

    public static function isObject(): bool
    {
        return true;
    }
}
