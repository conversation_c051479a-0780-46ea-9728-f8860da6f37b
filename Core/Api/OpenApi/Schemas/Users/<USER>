<?php

namespace App\Core\Api\OpenApi\Schemas\Users;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema()]
class UserScheme extends AbstractSchema
{
    #[Property(example: 101)]
    public int $id;
    #[Property(example: '<EMAIL>')]
    public string $email;
    #[Property(example: 'John')]
    public string $firstName;
    #[Property(example: 'Doe')]
    public string $lastName;
    #[Property(example: 'John Doe')]
    public string $fullName;
    #[Property(example: false)]
    public bool $deleted;

    public static function isObject(): bool
    {
        return true;
    }
}
