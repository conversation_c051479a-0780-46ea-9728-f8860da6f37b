<?php

namespace App\Core\Api\OpenApi\Schemas\Users;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema()]
class CreateUserScheme extends AbstractSchema
{
    #[Property(example: 'John')]
    public string $firstName;
    #[Property(example: 'Doe')]
    public string $lastName;
    #[Property(example: '<EMAIL>')]
    public string $email;
    #[Property(example: '%s343sadasdaSdad', format: 'password')]
    public string $password;

    public static function isObject(): bool
    {
        return true;
    }
}
