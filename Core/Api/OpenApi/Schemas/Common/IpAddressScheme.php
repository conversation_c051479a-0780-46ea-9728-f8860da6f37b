<?php

namespace App\Core\Api\OpenApi\Schemas\Common;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class IpAddressScheme extends AbstractSchema
{
    #[Property(
        type: 'array',
        items: new OA\Items(
            type: 'string',
            format: 'ipv4',
            example: '***********'
        )
    )]
    public array $ips;

    public static function isObject(): bool
    {
        return true;
    }
}
