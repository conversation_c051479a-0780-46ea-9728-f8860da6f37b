<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Common;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class CountriesScheme extends AbstractSchema
{
    #[Property(example: 53)]
    public int $id;

    #[Property(example: 'Croatia')]
    public string $name;

    #[Property(example: 'HR')]
    public string $code;

    #[Property(example: 'HR')]
    public string $vatCode;

    #[Property(example: 30)]
    public int $currencyId;

    #[Property(example: false)]
    public bool $isCommunity;

    #[Property(example: true)]
    public bool $inEu;

    #[Property(example: '2023-05-14', format: 'date')]
    public string $inEuStartDate;

    #[Property(example: null, format: 'date')]
    public ?string $inEuEndDate = null;

    #[Property(
        type: 'object',
        properties: [
            new Property(property: 'id', type: 'integer', example: 30),
            new Property(property: 'name', type: 'string', example: 'Euro'),
            new Property(property: 'symbol', type: 'string', example: '€'),
            new Property(property: 'code', type: 'string', example: 'EUR'),
        ],
        nullable: true
    )]
    public ?array $currency = null;

    public static function isObject(): bool
    {
        return false;
    }
}
