<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Common;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class ItemCodeCategoriesScheme extends AbstractSchema
{
    #[Property(example: 2)]
    public int $id;

    #[Property(example: 'Books & Printed Materials')]
    public string $name;
    #[Property(
        type: 'array',
        items: new OA\Items(
            properties: [
                new Property(property: 'id', type: 'integer', example: 8),
                new Property(property: 'code', type: 'string', example: 'A_BOOK_ADULT'),
                new Property(property: 'description', type: 'string', example: 'Adult Colouring or Activity Book'),
                new Property(property: 'itemTypeId', type: 'integer', example: 1),
                new Property(property: 'taxationMethodId', type: 'integer', example: 1),
                new Property(
                    property: 'itemType',
                    type: 'object',
                    example: [
                        'id'   => 1,
                        'name' => 'Goods',
                    ],
                    nullable: true
                ),
                new Property(
                    property: 'taxationMethod',
                    type: 'object',
                    example: [
                        'id'   => 1,
                        'name' => 'General',
                    ],
                    nullable: true
                ),
            ],
            type: 'object',
            nullable: true
        )
    )]
    public ?array $itemTaxCodes = null;

    public static function isObject(): bool
    {
        return false;
    }
}
