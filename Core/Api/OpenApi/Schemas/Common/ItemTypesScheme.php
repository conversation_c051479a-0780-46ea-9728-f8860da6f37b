<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Common;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class ItemTypesScheme extends AbstractSchema
{
    #[Property(example: 1)]
    public int $id;

    #[Property(example: 'Goods')]
    public string $name;

    public static function isObject(): bool
    {
        return false;
    }
}
