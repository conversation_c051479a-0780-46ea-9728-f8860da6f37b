<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Common;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class CurrenciesScheme extends AbstractSchema
{
    #[Property(example: 30)]
    public int $id;

    #[Property(example: 'Euro')]
    public string $name;

    #[Property(example: 'EUR')]
    public string $code;

    #[Property(example: '€')]
    public string $symbol;

    public static function isObject(): bool
    {
        return false;
    }
}
