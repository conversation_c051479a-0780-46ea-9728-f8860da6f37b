<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\Common;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class PlatformsScheme extends AbstractSchema
{
    #[Property(example: 1)]
    public int $id;

    #[Property(example: 'Amazon')]
    public string $name;

    #[Property(example: 1)]
    public int $platformTypeId;

    #[Property(
        type: 'object',
        properties: [
            new Property(property: 'id', type: 'integer', example: 1),
            new Property(property: 'name', type: 'string', example: 'Marketplace'),
        ],
        nullable: true
    )]
    public ?array $platformType = null;

    public static function isObject(): bool
    {
        return false;
    }
}
