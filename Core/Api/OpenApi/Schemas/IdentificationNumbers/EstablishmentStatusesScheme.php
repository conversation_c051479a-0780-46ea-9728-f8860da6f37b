<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\IdentificationNumbers;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class EstablishmentStatusesScheme extends AbstractSchema
{
    #[Property(example: 1)]
    public int $id;

    #[Property(example: 'Permanent establishment')]
    public string $name;

    #[Property(example: 'The `Permanent establishment` is a fixed place of business that generally gives rise to income or value-added tax liability in a particular jurisdiction. E.g., a branch, an office, a factory, a place of management etc.')]
    public string $description;

    public static function isObject(): bool
    {
        return false;
    }
}
