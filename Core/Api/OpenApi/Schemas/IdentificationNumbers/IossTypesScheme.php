<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\IdentificationNumbers;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class IossTypesScheme extends AbstractSchema
{
    #[Property(example: 1)]
    public int $id;

    #[Property(example: 'Import scheme IOSS (IM) number')]
    public string $name;

    #[Property(example: 'IM', nullable: true)]
    public ?string $code = null;

    public static function isObject(): bool
    {
        return false;
    }
}
