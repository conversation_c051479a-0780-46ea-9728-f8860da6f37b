<?php /** @noinspection PhpUnused */

namespace App\Core\Api\OpenApi\Schemas\IdentificationNumbers;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema]
class OssTypesScheme extends AbstractSchema
{
    #[Property(example: 2)]
    public int $id;

    #[Property(example: 'Non-Union scheme OSS (VOES, NETP) number')]
    public string $name;

    #[Property(example: 'EU', nullable: true)]
    public ?string $code = null;

    public static function isObject(): bool
    {
        return false;
    }
}
