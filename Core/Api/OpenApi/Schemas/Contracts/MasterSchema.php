<?php

namespace App\Core\Api\OpenApi\Schemas\Contracts;

use App\Core\Api\OpenApi\Others\JsonContent;
use App\Core\Api\OpenApi\Others\Property;
use OpenApi\Attributes\Items;

class MasterSchema extends JsonContent
{
    public array $codes;
    public bool $success = true;
    public array $errors = [];
    public array $meta = [];
    /**
     * @var string|\App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema|null
     */
    public string|AbstractSchema|null $data = null;

    public function __construct(
        int $httpCode,
        ?string $data = null,
        ?array $codes = null,
        ?bool $success = null,
        ?array $errors = null,
        ?array $meta = null,
    ) {
        if (is_null($codes) || count($codes) === 0) {
            $codes = [$httpCode];
        }

        $this->codes = $codes;
        $this->success = $success ?? true;
        $this->errors = $errors ?? [];
        $this->meta = $meta ?? [];
        $this->data = $data;

        parent::__construct(properties: $this->buildProperties());
    }

    private function buildProperties(): array
    {
        $data = [
            new Property(property: 'codes', type: 'array', example: $this->codes, items: new Items(type: 'integer')),
            new Property(property: 'success', type: 'boolean', example: $this->success),
            new Property(property: 'errors', type: 'array', example: $this->errors, items: new Items(type: 'string')),
            new Property(property: 'meta', type: 'object', example: $this->meta, properties: []),
        ];

        if (!is_null($this->data)) {
            if ($this->data::isObject()) {
                $data[] = new Property(property: 'data', type: 'object', ref: $this->data,);
            } else {
                $data[] = new Property(property: 'data', type: 'array', items: new Items(ref: $this->data));
            }
        } else {
            $data[] = new Property(property: 'data', type: ['array', 'object'], example: []);
        }

        return $data;
    }
}
