<?php

namespace App\Core\Api\OpenApi\Schemas\ExchangeRates;

use App\Core\Api\OpenApi\Others\Property;
use App\Core\Api\OpenApi\Schemas\Contracts\AbstractSchema;
use OpenApi\Attributes as OA;

#[OA\Schema()]
class ExchangeRateScheme extends AbstractSchema
{
    #[Property(example: 10)]
    public int $id;
    #[Property(example: '2024-04-16', format: 'date')]
    public string $date;
    #[Property(example: 1)]
    public int $currencyId;
    #[Property(example: 76.68426)]
    public float $rate;
    #[Property(type: 'object', properties: [
        new Property(property: 'id', type: 'integer', example: 74),
        new Property(property: 'name', type: 'string', example: 'Netherlands Antillian Guilder'),
        new Property(property: 'symbol', type: 'string', example: 'ƒ'),
        new Property(property: 'code', type: 'string', example: 'ANG'),
    ], nullable: true)]
    public ?array $currency = null;

    public static function isObject(): bool
    {
        return false;
    }
}
