<?php

namespace App\Core\Api\OpenApi\Others;

use OpenApi\Attributes\Items;
use OpenApi\Attributes\Property as BaseProperty;
use OpenApi\Generator;
use Attribute;

#[Attribute(Attribute::TARGET_METHOD | Attribute::TARGET_PROPERTY | Attribute::TARGET_PARAMETER | Attribute::TARGET_CLASS_CONSTANT | Attribute::IS_REPEATABLE)]
class Property extends BaseProperty
{
    public function __construct(
        ?string $property = null,
        string|array|null $type = null,
        mixed $example = Generator::UNDEFINED,
        ?Items $items = null,
        ?string $schema = null,
        ?string $title = null,
        ?string $description = null,
        ?array $required = null,
        ?array $properties = null,
        ?string $format = null,
        mixed $default = Generator::UNDEFINED,
        ?bool $nullable = null,
        string|object|null $ref = null,
    ) {
        parent::__construct(
            property: $property,
            ref: $ref,
            schema: $schema,
            title: $title,
            description: $description,
            required: $required,
            properties: $properties,
            type: $type,
            format: $format,
            items: $items,
            default: $default,
            example: $example,
            nullable: $nullable,
        );
    }
}
