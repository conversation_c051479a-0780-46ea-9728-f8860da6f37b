<?php

namespace App\Core\Api\OpenApi\Others;

use OpenApi\Attributes\Tag as OATag;
use Attribute;

#[Attribute(Attribute::TARGET_CLASS | Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class Tag extends OATag
{
    public function __construct(
        string $name,
        ?string $description = null,
    ) {
        parent::__construct(
            name: $name,
            description: $description,
            // annotation
            x: null,
            attachables: null
        );
    }
}
