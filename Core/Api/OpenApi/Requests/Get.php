<?php

namespace App\Core\Api\OpenApi\Requests;

use OpenApi\Attributes\Get as OAGet;
use OpenApi\Attributes\RequestBody;
use Attribute;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class Get extends OAGet
{
    public function __construct(
        string $path,
        string $summary,
        ?string $description = null,
        ?array $parameters = null,
        ?array $tags = null,
        ?RequestBody $requestBody = null,
        ?array $responses = null,
    ) {
        if (is_null($description)) {
            $description = $summary;
        }

        parent::__construct(
            path: $path,
            operationId: null,
            description: $description,
            summary: $summary,
            security: null,
            servers: null,
            requestBody: $requestBody,
            tags: $tags,
            parameters: $parameters,
            responses: $responses,
            callbacks: null,
            externalDocs: null,
            deprecated: null,
            x: null,
            attachables: null
        );
    }
}
