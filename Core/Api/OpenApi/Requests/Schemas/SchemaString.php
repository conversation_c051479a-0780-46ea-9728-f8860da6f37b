<?php

namespace App\Core\Api\OpenApi\Requests\Schemas;

use OpenApi\Attributes\Schema as BaseSchema;
use OpenApi\Generator;

class SchemaString extends BaseSchema
{
    public function __construct(
        mixed $text = Generator::UNDEFINED,
        ?string $format = null
    ) {
        parent::__construct(
            type: 'string',
            format: $format,
            example: $text,
        );
    }
}
