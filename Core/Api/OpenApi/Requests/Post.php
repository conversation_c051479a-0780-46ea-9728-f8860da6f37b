<?php

namespace App\Core\Api\OpenApi\Requests;

use OpenApi\Attributes\Post as OAPost;
use OpenApi\Attributes\RequestBody;
use Attribute;

#[Attribute(Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class Post extends OAPost
{
    public function __construct(
        string $path,
        string $summary,
        ?RequestBody $body = null,
        ?string $description = null,
        ?array $parameters = null,
        ?array $tags = null,
        ?array $responses = null,
    ) {
        if (is_null($description)) {
            $description = $summary;
        }

        parent::__construct(
            path: $path,
            description: $description,
            summary: $summary,
            requestBody: $body,
            tags: $tags,
            parameters: $parameters,
            responses: $responses,
        );
    }
}
