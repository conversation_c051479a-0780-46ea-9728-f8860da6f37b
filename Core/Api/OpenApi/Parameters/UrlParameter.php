<?php

namespace App\Core\Api\OpenApi\Parameters;

use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Schema;

class UrlParameter extends Parameter
{
    public function __construct(
        string $name,
        ?string $description = null,
        bool $required = true,
        string $type = 'integer',
    ) {
        parent::__construct(
            name: $name,
            description: $description ?? 'Database id of a resource',
            in: 'path',
            required: $required,
            schema: new Schema(type: $type),
        );
    }
}
