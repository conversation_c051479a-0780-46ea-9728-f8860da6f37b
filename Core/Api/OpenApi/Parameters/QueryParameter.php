<?php

namespace App\Core\Api\OpenApi\Parameters;

use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Schema;

class QueryParameter extends Parameter
{
    public function __construct(
        string $name,
        ?string $description = null,
        bool $required = false,
        ?Schema $schema = null
    ) {
        parent::__construct(
            name: $name,
            description: $description,
            in: 'query',
            required: $required,
            schema: $schema,
            explode: true
        );
    }
}
