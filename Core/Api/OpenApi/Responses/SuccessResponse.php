<?php

namespace App\Core\Api\OpenApi\Responses;

use App\Core\Api\OpenApi\Schemas\Contracts\MasterSchema;
use App\Core\Api\Responses\Helpers\HttpStatusCode;
use OpenApi\Attributes\Response;
use Attribute;

#[Attribute(Attribute::TARGET_CLASS | Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class SuccessResponse extends Response
{
    public function __construct(
        string $description,
        ?string $data = null,
        ?array $codes = null,
        ?array $meta = null,
        string|object|null $ref = null,
    ) {
        $httpCode = HttpStatusCode::OK->getCode();

        $schema = new MasterSchema(
            httpCode: $httpCode,
            data: $data,
            codes: $codes,
            success: true,
            errors: null,
            meta: $meta,
        );

        parent::__construct(
            ref: $ref,
            response: $httpCode,
            description: $description,
            content: $schema,
        );
    }
}
