<?php

namespace App\Core\Api\OpenApi\Responses;

use App\Core\Api\OpenApi\Schemas\Contracts\MasterSchema;
use App\Core\Api\Responses\Helpers\HttpStatusCode;
use OpenApi\Attributes\Response;
use Attribute;

#[Attribute(Attribute::TARGET_CLASS | Attribute::TARGET_METHOD | Attribute::IS_REPEATABLE)]
class ErrorResponse extends Response
{
    public function __construct(
        HttpStatusCode $response,
        string $description,
        array $errors,
        ?array $codes = null,
        ?array $meta = null,
        ?string $data = null,
        string|object|null $ref = null,
    ) {
        $schema = new MasterSchema(
            httpCode: $response->getCode(),
            data: $data,
            codes: $codes,
            success: false,
            errors: $errors,
            meta: $meta,
        );

        parent::__construct(
            ref: $ref,
            response: $response->getCode(),
            description: $description,
            content: $schema,
        );
    }
}
