<?php

namespace App\Core\Api;

use App\Core\Api\Contracts\ApiServiceContract;
use App\Core\Data\Models\ApiConsumer;
use App\Core\Data\Repositories\Contracts\ApiRepositoryContract;

class ApiService implements ApiServiceContract
{
    private ApiRepositoryContract $apiRepo;

    public function __construct(ApiRepositoryContract $apiRepo)
    {
        $this->apiRepo = $apiRepo;
    }

    public function validateApplication(string $applicationKey, string $applicationSecret): bool
    {
        $application = $this->getApplicationByKey($applicationKey);
        if (is_null($application)) {
            return false;
        }

        $token = $this->generateApplicationToken(
            $applicationKey,
            $application->name,
            $application->secret
        );

        $applicationSecret = base64_decode($applicationSecret);

        return password_verify($token, $applicationSecret);
    }

    public function getApplicationByKey(string $applicationKey): ?ApiConsumer
    {
        return $this->apiRepo->getApiConsumerByKey($applicationKey);
    }

    private function generateApplicationToken(
        string $applicationKey,
        string $applicationName,
        string $applicationSecret
    ): string {
        $token = [
            'key'    => $applicationKey,
            'name'   => $applicationName,
            'secret' => $applicationSecret,
        ];
        $token = json_encode($token);

        return base64_encode($token);
    }

    /** @noinspection PhpUnusedPrivateMethodInspection */
    private function generateApplicationSecret(
        string $applicationKey,
        string $applicationName,
        string $applicationSecret
    ): string {
        $token = $this->generateApplicationToken(
            $applicationKey,
            $applicationName,
            $applicationSecret
        );

        $options = [
            'memory_cost' => 1024,
            'time_cost'   => 2,
            'threads'     => 2,
        ];

        $hash = @password_hash($token, PASSWORD_ARGON2I, $options);

        return base64_encode($hash);
    }
}
