<?php

namespace App\Core\ItemTaxCodes\Contracts;

use App\Core\Data\Models\CountryItemTaxCode;
use App\Core\Data\Models\ItemTaxCode;

interface ItemTaxCodeServiceContract
{
    /**
     * @param int $itemTaxCodeId
     * @param int $countryId
     * @param int $vatRateTypeId
     * @param string $fromDate
     * @param int|null $countryItemTaxCodeId
     * @param string|null $toDate
     * @return CountryItemTaxCode|null
     */
    public function fullStoreUpdateCountryItemTaxCode(
        int $itemTaxCodeId,
        int $countryId,
        int $vatRateTypeId,
        string $fromDate,
        ?int $countryItemTaxCodeId = null,
        ?string $toDate = null
    ): ?CountryItemTaxCode;

    /**
     * @param string $code
     * @param int $itemCodeCategoryId
     * @param int $itemTypeId
     * @param int $taxationMethodId
     * @param string|null $description
     * @param int|null $id
     * @param int|null $vatRateTypeId
     * @param string|null $vatFromDate
     * @param string|null $vatToDate
     * @return ItemTaxCode
     */
    public function storeUpdateTaxCode(
        string $code,
        int $itemCodeCategoryId,
        int $itemTypeId,
        int $taxationMethodId,
        ?string $description = null,
        ?int $id = null,
        ?int $vatRateTypeId = null,
        ?string $vatFromDate = null,
        ?string $vatToDate = null
    ): ItemTaxCode;
}
