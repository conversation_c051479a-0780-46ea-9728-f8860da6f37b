<?php

namespace App\Core\ItemTaxCodes;

use App\Core\Data\Models\CountryItemTaxCode;
use App\Core\Data\Models\ItemTaxCode;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxCodesRepositoryContract;
use App\Core\ItemTaxCodes\Contracts\ItemTaxCodeServiceContract;
use Carbon\Carbon;

class ItemTaxCodeService implements ItemTaxCodeServiceContract
{
    private ItemRepositoryContract $itemRepo;
    private TaxCodesRepositoryContract $taxCodesRepo;

    /**
     * @param ItemRepositoryContract $itemRepo
     * @param TaxCodesRepositoryContract $taxCodesRepo
     */
    public function __construct(
        ItemRepositoryContract $itemRepo,
        TaxCodesRepositoryContract $taxCodesRepo,
    ) {
        $this->itemRepo = $itemRepo;
        $this->taxCodesRepo = $taxCodesRepo;
    }

    /**
     * @inheritDoc
     */
    public function fullStoreUpdateCountryItemTaxCode(
        int $itemTaxCodeId,
        int $countryId,
        int $vatRateTypeId,
        string $fromDate,
        ?int $countryItemTaxCodeId = null,
        ?string $toDate = null
    ): ?CountryItemTaxCode {
        $countryItemTaxCode = $this->itemRepo->getEmptyCountryItemTaxCodes();
        if (!is_null($countryItemTaxCodeId)) {
            $countryItemTaxCode = $this->itemRepo->getCountryItemTaxCodeById($countryItemTaxCodeId);

            $oldFromDate = $countryItemTaxCode->from_date;
            $oldVatRateTypeId = $countryItemTaxCode->vat_rate_type_id;

            if (($oldVatRateTypeId != $vatRateTypeId) && ($oldFromDate != $fromDate)) {
                $newToDate = Carbon::parse($fromDate)->addDays(-1)->format('Y-m-d');
                $this->reorderCountryItemTaxCodes(
                    $countryItemTaxCode,
                    $itemTaxCodeId,
                    $countryId,
                    $oldVatRateTypeId,
                    $oldFromDate,
                    $newToDate
                );

                $countryItemTaxCode = $this->itemRepo->getEmptyCountryItemTaxCodes();

                $this->reorderCountryItemTaxCodes(
                    $countryItemTaxCode,
                    $itemTaxCodeId,
                    $countryId,
                    $vatRateTypeId,
                    $fromDate,
                    $toDate
                );

                return $countryItemTaxCode;
            }
        }

        $this->reorderCountryItemTaxCodes(
            $countryItemTaxCode,
            $itemTaxCodeId,
            $countryId,
            $vatRateTypeId,
            $fromDate,
            $toDate
        );

        return $countryItemTaxCode;
    }

    private function reorderCountryItemTaxCodes(
        CountryItemTaxCode $countryItemTaxCode,
        int $itemTaxCodeId,
        int $countryId,
        int $vatRateTypeId,
        string $fromDate,
        ?string $toDate = null
    ): void {
        $countryItemTaxCode->item_tax_code_id = $itemTaxCodeId;
        $countryItemTaxCode->country_id = $countryId;
        $countryItemTaxCode->vat_rate_type_id = $vatRateTypeId;
        $countryItemTaxCode->from_date = $fromDate;
        $countryItemTaxCode->to_date = $toDate;
        $countryItemTaxCode->save();
    }

    public function storeUpdateTaxCode(
        string $code,
        int $itemCodeCategoryId,
        int $itemTypeId,
        int $taxationMethodId,
        ?string $description = null,
        ?int $id = null,
        ?int $vatRateTypeId = null,
        ?string $vatFromDate = null,
        ?string $vatToDate = null
    ): ItemTaxCode {
        $taxCode = $this->taxCodesRepo->storeUpdateTaxCode(
            $code,
            $itemCodeCategoryId,
            $itemTypeId,
            $taxationMethodId,
            $description,
            $id
        );

        if (!is_null($vatRateTypeId)) {
            $this->itemRepo->recreateTaxCodeForAllCountries(
                $taxCode->id,
                $vatRateTypeId,
                $vatFromDate,
                $vatToDate
            );
        }

        return $taxCode;
    }
}
