<?php

namespace App\Core\File\Contracts;

use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\File;
use Exception;
use Illuminate\Http\UploadedFile;

interface FileServiceContract
{
    public function copyResourceFile(UploadableContract $from, UploadableContract $to): ?array;

    /**
     * @param int $fileId
     * @return File|null
     */
    public function getFile(int $fileId): ?File;

    public function refreshDbFileSize(File $file): File;

    /**
     * Shortcut method for saving UploadedFile to db and filesystem
     *
     * @param UploadableContract $resource
     * @param UploadedFile $file
     * @return File
     * @throws Exception
     */
    public function storeUploadedFile(
        UploadableContract $resource,
        UploadedFile $file
    ): File;

    /**
     * @param string $name
     * @param string $mime
     * @param string $extension
     * @param int $size
     * @param UploadableContract $resource
     * @return File
     */
    public function storeFileToDatabase(
        string $name,
        string $mime,
        string $extension,
        int $size,
        UploadableContract $resource
    ): File;

    /**
     * Method saves UploadedFile to db only
     *
     * @param UploadableContract $resource
     * @param UploadedFile $file
     * @return File
     */
    public function storeUploadedFileToDatabase(
        UploadableContract $resource,
        UploadedFile $file
    ): File;


    /**
     * Method saves UploadedFile to filesystem only
     *
     * @param string $data
     * @param string $path
     */
    public function storeFileContentToFilesystem(string $data, string $path): void;

    /**
     * @param string $fromPath
     * @param string $toPath
     */
    public function copyFileToFilesystem(string $fromPath, string $toPath): void;

    public function moveFileToFilesystem(string $fromPath, string $toPath, bool $deleteIfExists = true): void;

    /**
     * @param string $fullPath
     */
    public function deleteFileFromFilesystem(string $fullPath): void;

    /**
     * @param File $file
     */
    public function deleteFile(File $file): void;

    /**
     * @param string $path
     * @return string
     */
    public function getMime(string $path): string;

    /**
     * @param string $path
     * @return string
     */
    public function getExtension(string $path): string;

    /**
     * @param string $path
     * @return int
     */
    public function getSize(string $path): int;

    /**
     * @param string $path
     */
    public function createDirIfNotExists(string $path): void;

    /**
     * @param string $path
     */
    public function deleteDirectoryFromFileSystem(string $path): void;

    /**
     * @param string $path
     * @return string
     */
    public function getContentOrCreateEmptyFile(string $path): string;
}
