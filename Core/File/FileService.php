<?php

namespace App\Core\File;

use Exception;
use Illuminate\Support\Str;
use Throwable;
use App\Core\Data\Models\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Filesystem\Filesystem;
use App\Core\File\Contracts\FileServiceContract;
use App\Core\Data\Models\Contracts\UploadableContract;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use App\Core\Data\Repositories\Contracts\FileRepositoryContract;

class FileService implements FileServiceContract
{
    private FileRepositoryContract $fileRepo;
    private Filesystem $filesystem;

    public function __construct(
        FileRepositoryContract $fileRepo,
        Filesystem $filesystem
    ) {
        $this->fileRepo = $fileRepo;
        $this->filesystem = $filesystem;
    }

    /**
     * @inheritDoc
     */
    public function getFile(int $fileId): ?File
    {
        return $this->fileRepo->getFileById($fileId);
    }

    /**
     * @inheritDoc
     */
    public function storeUploadedFile(
        UploadableContract $resource,
        UploadedFile $file
    ): File {
        $dbFile = $this->storeUploadedFileToDatabase(
            $resource,
            $file
        );

        try {
            $this->storeUploadedFileToFilesystem($dbFile, $file);
        } catch (Throwable) {
            $this->fileRepo->deleteFileById($dbFile->id);
            throw new Exception('File Not Saved');
        }

        return $dbFile;
    }

    /**
     * @inheritDoc
     */
    public function storeUploadedFileToDatabase(
        UploadableContract $resource,
        UploadedFile $file
    ): File {
        $extension = $file->getClientOriginalExtension();
        $mime = $file->getMimeType();

        $fullName = $file->getClientOriginalName();
        $name = rtrim($fullName, $extension);
        $name = rtrim($name, '.');
        $size = $file->getSize();

        $extension = mb_strtolower($extension);

        return $this->storeFileToDatabase(
            $name,
            $mime,
            $extension,
            $size,
            $resource
        );
    }

    /**
     * @param File $dbFile
     * @param UploadedFile $file
     * @throws FileNotFoundException
     */
    private function storeUploadedFileToFilesystem(
        File $dbFile,
        UploadedFile $file
    ): void {
        $path = $dbFile->file_path;
        $this->storeFileContentToFilesystem($file->get(), $path);
    }

    /**
     * @inheritDoc
     */
    public function storeFileToDatabase(
        string $name,
        string $mime,
        string $extension,
        int $size,
        UploadableContract $resource
    ): File {
        $file = $this->fileRepo->getNewFileModel();

        $file->name = $name;
        $file->mime = $mime;
        $file->size = $size;
        $file->extension = $extension;
        $file->resource_id = $resource->getUploadResourceId();
        $file->resource = $resource->getUploadResourceName();

        $file->save();

        return $file;
    }

    /**
     * @inheritDoc
     */
    public function storeFileContentToFilesystem(string $data, string $path): void
    {
        $dirPath = $this->filesystem->dirname($path);
        if (!$this->filesystem->exists($dirPath)) {
            $this->filesystem->makeDirectory($dirPath, 0755, true);
        }
        $this->filesystem->put($path, $data);
    }

    public function createFileFromData(mixed $data, string $nameWithExtension, UploadableContract $uploadable): File
    {
        $data = $data ?? '';
        $tmpPath = tmp_path(Str::uuid() . '-' . Str::remove(' ', $nameWithExtension));
        $this->storeFileContentToFilesystem($data, $tmpPath);
        $mime = $this->getMime($tmpPath);
        $size = $this->getSize($tmpPath);
        $extension = $this->getExtension($tmpPath);
        $name = rtrim($nameWithExtension, $extension);
        $name = rtrim($name, '.');
        $file = $this->storeFileToDatabase(
            $name,
            $mime,
            $extension,
            $size,
            $uploadable
        );
        $this->moveFileToFilesystem($tmpPath, $file->file_path);

        return $file;
    }

    /**
     * @inheritDoc
     */
    public function copyFileToFilesystem(string $fromPath, string $toPath): void
    {
        $dirPath = $this->filesystem->dirname($toPath);
        if (!$this->filesystem->exists($dirPath)) {
            $this->filesystem->makeDirectory($dirPath, 0755, true);
        }
        $this->filesystem->copy($fromPath, $toPath);
    }

    /**
     * @inheritDoc
     */
    public function moveFileToFilesystem(string $fromPath, string $toPath, bool $deleteIfExists = true): void
    {
        $dirPath = $this->filesystem->dirname($toPath);
        if (!$this->filesystem->exists($dirPath)) {
            $this->filesystem->makeDirectory($dirPath, 0755, true);
        }

        if ($deleteIfExists && is_file($toPath)) {
            $this->filesystem->delete($toPath);
        }

        $this->filesystem->move($fromPath, $toPath);
    }

    /**
     * @param string $fullPath
     */
    public function deleteFileFromFilesystem(string $fullPath): void
    {
        $uploadPath = upload_path() . '/';
        $relativePath = mb_substr($fullPath, mb_strlen($uploadPath));
        $pathExploded = explode('/', $relativePath);
        $pathCnt = count($pathExploded);

        for ($i = 0; $i < $pathCnt; $i++) {
            $resourcePath = $uploadPath . implode('/', $pathExploded);
            if (!$this->filesystem->isDirectory($resourcePath)) {
                if ($this->filesystem->exists($resourcePath)) {
                    $this->filesystem->delete($resourcePath);
                }
            } else {
                $files = $this->filesystem->allFiles($resourcePath);
                if (count($files) < 1) {
                    $this->filesystem->deleteDirectory($resourcePath);
                } else {
                    break;
                }
            }
            array_pop($pathExploded);
        }
    }

    /**
     * @inheritDoc
     */
    public function deleteFile(File $file): void
    {
        try {
            $this->deleteFileFromFilesystem($file->file_path);
            $this->fileRepo->deleteFileById($file->id);
        } catch (Throwable) {
        }
    }

    /**
     * @inheritDoc
     */
    public function getMime(string $path): string
    {
        return $this->filesystem->mimeType($path);
    }

    /**
     * @inheritDoc
     */
    public function getExtension(string $path): string
    {
        return $this->filesystem->extension($path);
    }

    /**
     * @inheritDoc
     */
    public function getSize(string $path): int
    {
        return $this->filesystem->size($path);
    }

    /**
     * @inheritDoc
     */
    public function createDirIfNotExists(string $path): void
    {
        $this->filesystem->ensureDirectoryExists($path, 0755, true);
    }

    public function deleteDirectoryFromFileSystem(string $path): void
    {
        $this->filesystem->deleteDirectory($path);
    }

    public function getContentOrCreateEmptyFile(string $path): string
    {
        if (!$this->filesystem->exists($path)) {
            $this->filesystem->put($path, '');
        }

        /** @noinspection PhpUnhandledExceptionInspection */
        return $this->filesystem->get($path);
    }

    public function copyResourceFile(UploadableContract $from, UploadableContract $to): ?array
    {
        /**
         * @var File $fromFile
         */
        $fromFile = $from->file ?? null;
        if (is_null($fromFile)) {
            return null;
        }

        $newFile = $this->storeFileToDatabase(
            $fromFile->name,
            $fromFile->mime,
            $fromFile->extension,
            $fromFile->size,
            $to,
        );

        $this->copyFileToFilesystem($fromFile->file_path, $newFile->file_path);

        return [
            'new' => $newFile,
            'old' => $fromFile
        ];
    }

    public function refreshDbFileSize(File $file): File
    {
        $size = $this->getSize($file->file_path);
        $file->size = $size;
        $file->save();

        return $file;
    }
}
