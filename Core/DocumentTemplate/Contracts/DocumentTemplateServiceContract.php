<?php

namespace App\Core\DocumentTemplate\Contracts;

use App\Core\Data\Models\DocumentTemplate;
use Illuminate\Support\Collection;
use App\Core\Data\Models\User;

interface DocumentTemplateServiceContract
{
    public function getAllDocumentCategories(): Collection;

    public function saveDocumentTemplate(User $user, string $storeKey): DocumentTemplate;

    public function getMappers(bool $includeData = false, ?string $resource = null): Collection;
}
