<?php

namespace App\Core\DocumentTemplate;

use App\Core\Common\Store\Store;
use App\Core\Data\Models\DocumentTemplate;
use App\Core\Data\Models\File;
use App\Core\Data\Models\User;
use App\Core\Data\Repositories\Contracts\DocumentCategoryRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentTemplateRepositoryContract;
use App\Core\DocumentTemplate\Contracts\DocumentTemplateServiceContract;
use App\Core\File\Contracts\FileServiceContract;
use App\Core\Mappers\Mappers\Company\CompanyMapper;
use App\Core\PdfTemplates\Contracts\PdfTemplateServiceContract;
use Exception;
use Illuminate\Support\Collection;

class DocumentTemplateService implements DocumentTemplateServiceContract
{
    private DocumentTemplateRepositoryContract $documentTemplateRepo;
    private FileServiceContract $fileService;
    private PdfTemplateServiceContract $pdfTemplateService;
    private DocumentCategoryRepositoryContract $documentCategoryRepo;

    public function __construct(
        DocumentTemplateRepositoryContract $documentTemplateRepo,
        PdfTemplateServiceContract $pdfTemplateService,
        FileServiceContract $fileService,
        DocumentCategoryRepositoryContract $documentCategoryRepo
    ) {

        $this->documentTemplateRepo = $documentTemplateRepo;
        $this->fileService = $fileService;
        $this->pdfTemplateService = $pdfTemplateService;
        $this->documentCategoryRepo = $documentCategoryRepo;
    }

    /**
     * @throws Exception
     */
    public function saveDocumentTemplate(User $user, string $storeKey): DocumentTemplate
    {
        $store = Store::getFirstForUserByKey($user->id, $storeKey);
        /**
         * @var File $file
         */
        $file = $store->files->first();
        $data = $store->data;

        $documentTemplateId = data_get($data, 'id');
        $documentTemplate = $this->documentTemplateRepo->getDocumentTemplateById($documentTemplateId ?? 0);
        if (!is_null($documentTemplate)) {
            $this->documentTemplateRepo->deleteDocumentTemplateFieldsByDocumentTemplateId($documentTemplate->id);
        } else {
            $documentTemplate = $this->documentTemplateRepo->getEmptyDocumentTemplateModel();
        }

        $documentTemplate->name = data_get($data, 'name');
        $documentTemplate->context = data_get($data, 'context.value');
        $documentTemplate->description = data_get($data, 'description');
        $documentTemplate->document_category_id = data_get($data, 'category.value');
        $documentTemplate->valid_from = data_get($data, 'validFrom');

        $documentTemplate->save();

        if (is_null($documentTemplateId)) {
            $newFile = $this->fileService
                ->storeFileToDatabase(
                    $file->name,
                    $file->mime,
                    $file->extension,
                    $file->size,
                    $documentTemplate
                );
            $this->fileService->copyFileToFilesystem($file->file_path, $newFile->file_path);
        }

        $fields = data_get($data, 'fields', []);

        $this->storePdfTemplateFields($fields, $documentTemplate->id);

        Store::deleteByStoreId($store->id);

        return $documentTemplate;
    }

    /**
     * @param array $fields
     * @param int $reportId
     * @return void
     */
    private function storePdfTemplateFields(array $fields, int $reportId): void
    {
        $fields = $this->pdfTemplateService
            ->mapBuilder2DbFields($fields, 'document_template_id', $reportId);

        if ($fields->count() > 0) {
            $this->documentTemplateRepo->insertDocumentTemplateFields($fields->toArray());
        }
    }

    public function getMappers(bool $includeData = false, ?string $resource = null): Collection
    {
        $mappers = [
            [
                'resource' => CompanyMapper::getClass(),
                'data'     => !$includeData ? null : CompanyMapper::getExample(),
            ]
        ];

        $mappers = collect($mappers);
        if (!is_null($resource)) {
            $mappers = $mappers->filter(function (array $mapper) use ($resource) {
                return $mapper['resource'] === $resource;
            });
        }

        return $mappers;
    }

    public function getAllDocumentCategories(): Collection
    {
        return $this->documentCategoryRepo->getAllChildDocumentCategories();
    }
}
