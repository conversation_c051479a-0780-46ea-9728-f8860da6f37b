<?php

namespace App\Core\Dev;

use App\Core\Dev\Contracts\DevServiceContract;
use App\Core\System\Slack\Contracts\SlackServiceContract;
use Illuminate\Support\Facades\DB;

class DevService implements DevServiceContract
{
    private SlackServiceContract $slackService;

    public function __construct(SlackServiceContract $slackService)
    {
        $this->slackService = $slackService;
    }

    public function checkDefaultTablePartitions(): void
    {
        $results = DB::table('pg_class')
            ->select(DB::raw('oid::regclass::text AS relation'))
            ->where('relkind', 'r')
            ->where('relispartition', true)
            ->where(DB::raw('oid::regclass'), 'ILIKE', '%_default')
            ->orderBy('relname')
            ->get();

        $defaultPartitionTables = [];
        foreach ($results as $key => $result) {
            $countRow = DB::table($result->relation)
                ->selectRaw('count(*)')
                ->get();

            if ($countRow->first()->count > 0) {
                $defaultPartitionTables[$key] = $result->relation;
            }
        }

        if (count($defaultPartitionTables) > 0) {
            $defaultPartitionTables = implode(', ', $defaultPartitionTables);
            $this->slackService->sendMessage('partition ' . $defaultPartitionTables . ' has data', 'system', ':warning:');
        } else {
            $this->slackService->sendMessage('All partitions OK', 'system', ':information_source:');
        }
    }
}
