<?php

namespace App\Core\Invoices\DataTransfer\InvoicesApi;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class ShipFromRequestData implements Arrayable, Jsonable
{
    private int $countryId;
    private string $vatNumber;

    public function __construct(int $countryId, string $vatNumber)
    {
        $this->countryId = $countryId;
        $this->vatNumber = $vatNumber;
    }

    public function getCountryId(): int
    {
        return $this->countryId;
    }

    public function getVatNumber(): string
    {
        return $this->vatNumber;
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return [
            'countryId' => $this->getCountryId(),
            'vatNumber' => $this->getVatNumber(),
        ];
    }
}
