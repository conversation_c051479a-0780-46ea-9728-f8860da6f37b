<?php

namespace App\Core\Invoices\DataTransfer\InvoicesApi;

use App\Core\Data\Models\ItemTaxCode;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class InvoiceItemRequestData implements Arrayable, Jsonable
{
    private int $qty;
    private int $invoiceItemTypeId;
    private string $description;
    private float $itemSalesPrice;
    private int $itemTaxCodeId;
    private bool $isPriceGross;
    private ?float $discountAmount;
    private ?float $discountPercentage;

    public function __construct(
        int $qty,
        int $invoiceItemTypeId,
        string $description,
        float $itemSalesPrice,
        ?int $itemTaxCodeId = null,
        ?bool $isPriceGross = null,
        ?float $discountAmount = null,
        ?float $discountPercentage = null
    ) {

        $this->qty = $qty;
        $this->invoiceItemTypeId = $invoiceItemTypeId;
        $this->description = $description;
        $this->itemSalesPrice = $itemSalesPrice;
        $this->itemTaxCodeId = $itemTaxCodeId ?? ItemTaxCode::A_GEN_STANDARD;
        $this->isPriceGross = $isPriceGross ?? true;
        $this->discountAmount = $discountAmount;
        $this->discountPercentage = $discountPercentage;
    }

    public function getQty(): int
    {
        return $this->qty;
    }

    public function getInvoiceItemTypeId(): int
    {
        return $this->invoiceItemTypeId;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getItemSalesPrice(): float
    {
        return $this->itemSalesPrice;
    }

    public function getItemTaxCodeId(): int
    {
        return $this->itemTaxCodeId;
    }

    public function isPriceGross(): bool
    {
        return $this->isPriceGross;
    }

    public function getDiscountAmount(): ?float
    {
        return $this->discountAmount;
    }

    public function getDiscountPercentage(): ?float
    {
        return $this->discountPercentage;
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return [
            'qty'                => $this->getQty(),
            'invoiceItemTypeId'  => $this->getInvoiceItemTypeId(),
            'description'        => $this->getDescription(),
            'itemSalesPrice'     => $this->getItemSalesPrice(),
            'itemTaxCodeId'      => $this->getItemTaxCodeId(),
            'isPriceGross'       => $this->isPriceGross(),
            'discountAmount'     => $this->getDiscountAmount(),
            'discountPercentage' => $this->getDiscountPercentage(),
        ];
    }
}
