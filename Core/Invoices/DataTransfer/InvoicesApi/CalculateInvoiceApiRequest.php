<?php

namespace App\Core\Invoices\DataTransfer\InvoicesApi;

use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Data\Models\ApiConsumer;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\SalesChannel;
use App\Core\VatNumbers\Api\VatNumberApi;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Contracts\Validation\Validator as ValidatorContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator as ValidatorFacade;
use Illuminate\Support\Str;
use Throwable;

class CalculateInvoiceApiRequest implements Arrayable, Jsonable
{
    private array $data;
    private ApiConsumer $apiConsumer;

    private ValidatorContract $validator;

    public function __construct(array $data, ApiConsumer $apiConsumer)
    {
        $this->data = $data;
        $this->apiConsumer = $apiConsumer;

        $this->run();
    }

    public function getSalesChannelId(): int
    {
        return $this->getData('salesChannelId');
    }

    public function getInvoiceTypeId(): int
    {
        return $this->getData('invoiceTypeId');
    }

    public function getInvoiceDate(): Carbon
    {
        return Carbon::parse($this->getData('invoiceDate'));
    }

    public function getInvoiceNumber(): string
    {
        return $this->getData('invoiceNumber');
    }

    public function getDeliveryConditionId(): int
    {
        return $this->getData('deliveryConditionId');
    }

    public function getSalesCurrencyId(): int
    {
        return $this->getData('salesCurrencyId');
    }

    public function getOrderNumber(): string
    {
        return $this->getData('orderNumber');
    }

    public function getShipFrom(): ShipFromRequestData
    {
        return new ShipFromRequestData(
            $this->getData('shipFrom.countryId'),
            $this->getData('shipFrom.vatNumber')
        );
    }

    public function getShipTo(): ?ShipToRequestData
    {
        if ($this->isShipToSameAsBillTo()) {
            return null;
        }

        return new ShipToRequestData(
            $this->getData('shipTo.countryId'),
            $this->getData('shipTo.name'),
            $this->getData('shipTo.address'),
            $this->getData('shipTo.postalCode'),
            $this->getData('shipTo.vatNumber')
        );
    }

    public function getBillTo(): BillToRequestData
    {
        return new BillToRequestData(
            $this->getData('billTo.countryId'),
            $this->getData('billTo.name'),
            $this->getData('billTo.address'),
            $this->getData('billTo.postalCode'),
            $this->getData('billTo.vatNumber')
        );
    }

    /**
     * @return Collection<InvoiceItemRequestData>
     */
    public function getItems(): Collection
    {
        return collect($this->getData('items'))
            ->map(function (array $item) {
                return new InvoiceItemRequestData(
                    $item['qty'],
                    $item['invoiceItemTypeId'],
                    $item['description'],
                    $item['itemSalesPrice'],
                    $item['itemTaxCodeId'] ?? null,
                    $item['isPriceGross'] ?? null,
                    $item['discountAmount'] ?? null,
                    $item['discountPercentage'] ?? null
                );
            });
    }

    public function isShipToSameAsBillTo(): bool
    {
        return $this->getData('shipToIsSameAsBillTo') ?? true;
    }

    public function getSalesChannel(): SalesChannel
    {
        return SalesChannel::find($this->getSalesChannelId());
    }

    public function getApiConsumer(): ApiConsumer
    {
        return $this->apiConsumer;
    }

    public function validate(): void
    {
        if ($this->validator->passes()) {
            return;
        }

        $errors = $this->resolveErrors($this->validator->errors()->toArray());
        $codes = $errors->keys();
        $errors = $errors->values();

        /** @noinspection PhpUnhandledExceptionInspection */
        throw new ValidationApiException(
            errors: $errors->toArray(),
            statusCode: $codes->toArray(),
            meta: ['codes' => $this->getAllMessages()->toArray()]
        );
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return [
            'apiConsumer' => $this->getApiConsumer()->toArray(),
            'invoice'     => [
                'invoiceTypeId'        => $this->getInvoiceTypeId(),
                'invoiceDate'          => $this->getInvoiceDate()->toDateString(),
                'invoiceNumber'        => $this->getInvoiceNumber(),
                'deliveryConditionId'  => $this->getDeliveryConditionId(),
                'salesCurrencyId'      => $this->getSalesCurrencyId(),
                'orderNumber'          => $this->getOrderNumber(),
                'shipFrom'             => $this->getShipFrom()->toArray(),
                'billTo'               => $this->getBillTo()->toArray(),
                'shipToIsSameAsBillTo' => $this->isShipToSameAsBillTo(),
                'shipTo'               => $this->getShipTo()?->toArray(),
                'salesChannel'         => $this->getSalesChannel()->toArray(),
                'items'                => $this->getItems()->toArray(),
            ]
        ];
    }

    private function run(): void
    {
        $rules = [
            'salesChannelId'      => 'required|integer|exists:ecommerce_accounts,id',
            'invoiceTypeId'       => 'required|integer|exists:invoice_types,id',
            'itemTypeId'          => 'required|integer|exists:item_types,id',
            'invoiceDate'         => 'required|date',
            'invoiceNumber'       => 'required',
            'deliveryConditionId' => 'required|integer|exists:delivery_conditions,id',
            'salesCurrencyId'     => 'required|integer|exists:currencies,id',
            'orderNumber'         => 'required',

            'shipFrom.countryId' => 'required|integer|exists:countries,id',
            'shipFrom.vatNumber' => ['required'],

            'billTo.countryId'  => 'required|integer|exists:countries,id',
            'billTo.name'       => 'required|string',
            'billTo.address'    => 'required|string',
            'billTo.postalCode' => 'required',
            'billTo.vatNumber'  => [
                'nullable',
                'string',
                function (string $attribute, mixed $value, Closure $fail) {
                    if (!$this->validateVatNumber($value)) {
                        $fail(2243);
                    }
                }
            ],

            'shipToIsSameAsBillTo' => 'nullable|boolean',

            'shipTo.countryId'  => 'required_unless:shipToIsSameAsBillTo,true|integer|exists:countries,id',
            'shipTo.name'       => 'required_unless:shipToIsSameAsBillTo,true|string',
            'shipTo.address'    => 'required_unless:shipToIsSameAsBillTo,true|string',
            'shipTo.postalCode' => 'required_unless:shipToIsSameAsBillTo,true',
            'shipTo.vatNumber'  => [
                'nullable',
                'string',
                function (string $attribute, mixed $value, Closure $fail) {
                    if (!$this->validateVatNumber($value)) {
                        $fail(2143);
                    }
                }
            ],

            'items' => 'required|array|min:1',

            'items.*.qty'               => 'required|integer|min:1',
            'items.*.invoiceItemTypeId' => 'required|integer|exists:item_types,id',
            'items.*.itemTaxCodeId'     => [
                'required_unless:items.*.invoiceItemTypeId,' . ItemType::SHIPPING . ',' . ItemType::PACKAGING,
                'integer',
                'exists:item_tax_codes,id',
            ],
            'items.*.description'       => 'required|string',
            'items.*.itemSalesPrice'    => 'required|decimal:2,4',
            'items.*.isPriceGross'      => 'nullable|boolean',

            'items.*.discountAmount'     => 'nullable|decimal:2,4|min:0',
            'items.*.discountPercentage' => 'nullable|integer|min:0|max:100',
        ];

        $codes = [
            'salesChannelId.required' => 1001,
            'salesChannelId.integer'  => 1002,
            'salesChannelId.exists'   => 1003,

            'invoiceTypeId.required' => 1101,
            'invoiceTypeId.integer'  => 1102,
            'invoiceTypeId.exists'   => 1103,

            'itemTypeId.required' => 1121,
            'itemTypeId.integer'  => 1122,
            'itemTypeId.exists'   => 1123,

            'invoiceDate.required' => 1201,
            'invoiceDate.date'     => 1202,

            'invoiceNumber.required' => 1301,

            'deliveryConditionId.required' => 1401,
            'deliveryConditionId.integer'  => 1402,
            'deliveryConditionId.exists'   => 1403,

            'salesCurrencyId.required' => 1501,
            'salesCurrencyId.integer'  => 1502,
            'salesCurrencyId.exists'   => 1503,

            'orderNumber.required' => 1601,

            'shipFrom.countryId.required' => 2001,
            'shipFrom.countryId.integer'  => 2002,
            'shipFrom.countryId.exists'   => 2003,
            'shipFrom.vatNumber.required' => 2011,
            'shipFrom.vatNumber.string'   => 2012,

            'shipTo.countryId.required_unless'  => 2101,
            'shipTo.countryId.integer'          => 2102,
            'shipTo.countryId.exists'           => 2103,
            'shipTo.name.required_unless'       => 2111,
            'shipTo.address.required_unless'    => 2121,
            'shipTo.postalCode.required_unless' => 2131,
            'shipTo.vatNumber.required_unless'  => 2141,
            'shipTo.vatNumber.string'           => 2142,

            'shipToIsSameAsBillTo.boolean' => 2190,

            'billTo.countryId.required'  => 2201,
            'billTo.countryId.integer'   => 2202,
            'billTo.countryId.exists'    => 2203,
            'billTo.name.required'       => 2211,
            'billTo.address.required'    => 2221,
            'billTo.postalCode.required' => 2231,
            'billTo.vatNumber.required'  => 2241,
            'billTo.vatNumber.string'    => 2242,

            'items.required' => 3001,
            'items.array'    => 3002,
            'items.min'      => 3003,

            'items.*.qty.required'                  => 4001,
            'items.*.qty.integer'                   => 4002,
            'items.*.qty.min'                       => 4003,
            'items.*.invoiceItemTypeId.required'    => 4101,
            'items.*.invoiceItemTypeId.integer'     => 4102,
            'items.*.invoiceItemTypeId.exists'      => 4103,
            'items.*.itemTaxCodeId.required_unless' => 4201,
            'items.*.itemTaxCodeId.integer'         => 4202,
            'items.*.itemTaxCodeId.exists'          => 4203,
            'items.*.description.required'          => 4301,
            'items.*.description.string'            => 4302,
            'items.*.itemSalesPrice.required'       => 4401,
            'items.*.itemSalesPrice.decimal'        => 4402,
            'items.*.isPriceGross.required'         => 4501,
            'items.*.isPriceGross.boolean'          => 4502,

            'items.*.discountAmount.decimal' => 4601,
            'items.*.discountAmount.min'     => 4602,

            'items.*.discountPercentage.integer' => 4603,
            'items.*.discountPercentage.min'     => 4604,
            'items.*.discountPercentage.max'     => 4605,
        ];

        $this->validator = ValidatorFacade::make(
            $this->data,
            $rules,
            $codes
        );
    }

    private function getData(?string $key = null): mixed
    {
        if (!is_null($key)) {
            return data_get($this->data, $key);
        }

        return $this->data;
    }

    private function getAllMessages(): Collection
    {
        return collect([
            1001 => 'Sales channel is required.',
            1002 => 'Sales channel must be an integer.',
            1003 => 'Sales channel does not exist.',
            1101 => 'Invoice type is required.',
            1102 => 'Invoice type must be an integer.',
            1103 => 'Invoice type does not exist.',
            1121 => 'Item type is required.',
            1122 => 'Item type must be an integer.',
            1123 => 'Item type does not exist.',
            1201 => 'Invoice date is required.',
            1202 => 'Invoice date must be a date.',
            1301 => 'Invoice number is required.',
            1401 => 'Delivery condition is required.',
            1402 => 'Delivery condition must be an integer.',
            1403 => 'Delivery condition does not exist.',
            1501 => 'Sales currency is required.',
            1502 => 'Sales currency must be an integer.',
            1503 => 'Sales currency does not exist.',
            1601 => 'Order number is required.',

            2001 => 'Ship from country is required.',
            2002 => 'Ship from country must be an integer.',
            2003 => 'Ship from country does not exist.',
            2011 => 'Ship from VAT number is required.',
            2012 => 'Ship from VAT number must be a string.',
            2013 => 'Ship from VAT number is invalid.',

            2101 => 'Ship to country is required.',
            2102 => 'Ship to country must be an integer.',
            2103 => 'Ship to country does not exist.',
            2111 => 'Ship to name is required.',
            2121 => 'Ship to address is required.',
            2131 => 'Ship to postal code is required.',
            2141 => 'Ship to VAT number is required.',
            2142 => 'Ship to VAT number must be a string.',
            2143 => 'Ship to VAT number is invalid.',

            2180 => 'shipTo is required.',
            2190 => 'shipToIsSameAsBillTo must be of type boolean.',

            2201 => 'Bill to country is required.',
            2202 => 'Bill to country must be an integer.',
            2203 => 'Bill to country does not exist.',
            2211 => 'Bill to name is required.',
            2221 => 'Bill to address is required.',
            2231 => 'Bill to postal code is required.',
            2241 => 'Bill to VAT number is required.',
            2242 => 'Bill to VAT number must be a string.',
            2243 => 'Bill to VAT number is invalid.',

            3001 => 'Invoice must have at least one item.',
            3002 => 'Items must be an array.',
            3003 => 'Invoice must have at least one item.',

            4001 => 'Items - quantity is required.',
            4002 => 'Items - quantity must be an integer.',
            4003 => 'Items - quantity must be greater than 0.',
            4101 => 'Items - invoice item type is required.',
            4102 => 'Items - invoice item type must be an integer.',
            4103 => 'Items - invoice item type does not exist.',
            4201 => 'Items - item tax code is required unless invoiceItemType is shipping (' . ItemType::SHIPPING . ') or packaging (' . ItemType::PACKAGING . ').',
            4202 => 'Items - item tax code must be an integer.',
            4203 => 'Items - item tax code does not exist.',
            4301 => 'Items - description is required.',
            4302 => 'Items - description must be a string.',
            4401 => 'Items - item sales price is required.',
            4402 => 'Items - item sales price must be a numeric.',
            4501 => 'Items - is price gross is required.',
            4502 => 'Items - is price gross must be a boolean.',

            4601 => 'Items - discount amount must be a float.',
            4602 => 'Items - discount amount must be greater than or equal to 0.',

            4603 => 'Items - discount percentage must be an integer.',
            4604 => 'Items - discount percentage must be greater than or equal to 0.',
            4605 => 'Items - discount percentage must be less than or equal to 100.',
        ]);
    }

    private function resolveErrors(array $errors): Collection
    {
        $messages = $this->getAllMessages();

        return collect($errors)
            ->mapWithKeys(function (array $error, string $key) use ($messages) {
                $error = $error[0] ?? 0;
                $stringError = Str::remove('validation.', $error);
                $error = (int)$error;
                $message = $messages->get($error);

                if (is_null($message)) {
                    return [$key => $key . ': ' . $stringError];
                }

                return [$error => $messages->get($error) ?? 'Unknown validation error: '];
            });
    }

    private function validateVatNumber(?string $vatNumber = null): bool
    {
        $vatNumber = $vatNumber ?? '';

        try {
            $validation = VatNumberApi::validateVatNumber($vatNumber);
        } catch (Throwable) {
            return false;
        }

        return $validation->valid ?? false;
    }
}
