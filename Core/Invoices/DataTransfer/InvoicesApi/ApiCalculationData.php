<?php

namespace App\Core\Invoices\DataTransfer\InvoicesApi;

use App\Core\Data\Models\Invoice;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class ApiCalculationData implements Arrayable, Jsonable
{
    private Invoice $invoice;
    private DetectedPropertiesContract $detected;

    public function __construct(Invoice $invoice, DetectedPropertiesContract $detected)
    {
        $this->invoice = $invoice;
        $this->detected = $detected;
    }

    public function getInvoice(): Invoice
    {
        return $this->invoice;
    }

    public function getDetected(): DetectedPropertiesContract
    {
        return $this->detected;
    }

    public function toArray(): array
    {
        return [
            'invoice'  => $this->getInvoice()->toArray(),
            'detected' => $this->getDetected()->toArray()
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode(value: $this->toArray(), pretty: $options);
    }
}
