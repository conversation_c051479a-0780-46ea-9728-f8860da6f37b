<?php

namespace App\Core\Invoices\DataTransfer\InvoicesApi;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class ShipToRequestData implements Arrayable, Jsonable
{
    private int $countryId;
    private string $name;
    private string $address;
    private string $postalCode;
    private string $vatNumber;

    public function __construct(
        int $countryId,
        string $name,
        string $address,
        string $postalCode,
        string $vatNumber
    ) {
        $this->countryId = $countryId;
        $this->name = $name;
        $this->address = $address;
        $this->postalCode = $postalCode;
        $this->vatNumber = $vatNumber;
    }

    public function getCountryId(): int
    {
        return $this->countryId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function getVatNumber(): string
    {
        return $this->vatNumber;
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return [
            'countryId'  => $this->getCountryId(),
            'name'       => $this->getName(),
            'address'    => $this->getAddress(),
            'postalCode' => $this->getPostalCode(),
            'vatNumber'  => $this->getVatNumber(),
        ];
    }
}
