<?php

namespace App\Core\Invoices\Contracts;

use App\Core\Data\Models\InvoiceType;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;

interface DetectedPropertiesContract
{
    public function getTaxCollectionResponsibilityId(): int;

    public function getTaxCalculationCountryId(): int;

    public function getTaxReportingSchemeId(): int;

    public function getTaxReportingCountryId(): int;

    public function getCustomerTypeId(): int;

    public function getInvoiceSubtypeId(): int;

    public function isTaxCharged(): bool;

    public function getInvoiceGoodsProperties(): ?InvoiceGoodsPropertiesContract;

    public function toArray(): array;

    public function setTaxCollectionResponsibilityId(int $taxCollectionResponsibilityId): self;

    public function setTaxCalculationCountryId(int $taxCalculationCountryId): self;

    public function setCustomerTypeId(int $customerTypeId): self;

    public function setTaxReportingSchemeId(int $taxReportingSchemeId): self;

    public function setTaxReportingCountryId(int $taxReportingCountryId): self;

    public function setTaxCharged(bool $isTaxCharged): self;

    public function setInvoiceSubtypeId(int $invoiceSubtypeId, int $invoiceTypeId = InvoiceType::SALES_INVOICE): self;

    public function getDeemedDetected(): ?DetectedInvoiceGoodsProperties;

    public function setDeemedDetected(?DetectedInvoiceGoodsProperties $deemedDetected): self;

    public function hasDeemedDetected(): bool;
}
