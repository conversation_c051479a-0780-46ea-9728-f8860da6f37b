<?php

namespace App\Core\Invoices\Contracts;

use App\Core\Data\Models\EslReport;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\OssIossReport;
use App\Core\Data\Models\VatReport;
use App\Core\Invoices\DataTransfer\InvoicesApi\ApiCalculationData;
use App\Core\Invoices\DataTransfer\InvoicesApi\CalculateInvoiceApiRequest;
use App\Core\Invoices\Invoice\DataTransfer\RecalculatedDataTransfer;
use App\Core\Invoices\Invoice\InvoiceData;
use App\Core\Mappers\MappersData\InvoicesForEslReport\EslReportInvoicesData;
use App\Core\Mappers\MappersData\InvoicesForOssIossReport\OssIossReportInvoicesData;
use App\Core\Mappers\MappersData\InvoicesForVatReport\VatReportInvoicesData;
use App\Core\System\Helpers\Arr;
use Illuminate\Support\Collection;

interface InvoiceServiceContract
{
    public function calculateAndStoreInvoiceByApi(CalculateInvoiceApiRequest $invoiceData): ApiCalculationData;

    public function getFirstSalesGroupedByCompanyCountry(array $companiesIds): Collection;

    public function getInvoiceData(Invoice $invoice, bool $isTitleAndLogoLineVisible = false): InvoiceData;

    public function getInvoicesDataForExport(int $companyId, ?int $invoiceSubtypeId = null, ?string $dateFrom = null, ?string $dateTo = null): Collection;

    public function getInvoicesMapperDataForVatReport(VatReport $report): VatReportInvoicesData;

    public function getInvoicesMapperDataForOssIossReport(OssIossReport $report): OssIossReportInvoicesData;

    public function getInvoicesMapperDataForVatReports(Collection $reports, ?int $filterVatReportId = null): Collection;

    public function getInvoicesDataForOssIossReport(Collection $reports): Collection;

    public function storeInvoiceWithItems(RecalculatedDataTransfer $calculationData, ?int $forceInvoiceStatusId = null): void;

    public function recalculateSalesInvoiceForVueComponent(Arr $invoiceData): RecalculatedDataTransfer;

    public function getInvoicesMapperDataForEslReports(Collection $reports): Collection;

    public function getInvoicesMapperDataForEslReport(EslReport $report): EslReportInvoicesData;
}
