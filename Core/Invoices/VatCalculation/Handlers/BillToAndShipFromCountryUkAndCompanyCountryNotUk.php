<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class BillToAndShipFromCountryUkAndCompanyCountryNotUk extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxCharged(true);
            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            $detected->setTaxCalculationCountryId($properties->getShipToCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
        } else {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
            $detected->setTaxCalculationCountryId($properties->getShipToCountryId());

            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCharged(true);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);
            } else {
                $detected->setTaxCharged(false);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::UK_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                $deemed = $this->createDeemedDetected(
                    TaxCollectionResponsibility::MARKETPLACE,
                    true,
                    $properties->getShipFromCountryId(),
                    TaxReportingScheme::UK_VOEC_DOMESTIC,
                    $properties->getShipFromCountryId(),
                    $properties->getShipToCountryId(),
                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                    CustomerType::B2C,
                );

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            } else {
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            }
        } else {
            if ($properties->isPlatformTypeMarketplace()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::UK_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        true,
                        $properties->getShipFromCountryId(),
                        TaxReportingScheme::UK_VOEC_DOMESTIC,
                        $properties->getBillToCountryId(),
                        $properties->getBillToCountryId(),
                        InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                        CustomerType::B2C,
                    );
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        true,
                        $properties->getCompanyCountryId(),
                        TaxReportingScheme::UK_VOEC_DOMESTIC,
                        $properties->getBillToCountryId(),
                        $properties->getBillToCountryId(),
                        InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                        CustomerType::B2C,
                    );
                }

                $detected->setDeemedDetected($deemed);
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                }
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
