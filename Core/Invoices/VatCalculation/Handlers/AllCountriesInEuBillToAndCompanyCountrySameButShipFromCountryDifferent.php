<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class AllCountriesInEuBillToAndCompanyCountrySameButShipFromCountryDifferent extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2C()) {
            if ($properties->isCompanyRegisteredForOss()) {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
            } else {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
            }

            $detected->setCustomerTypeId(CustomerType::B2C);
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCharged(true);
        } else {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCharged(false);

            if (!$properties->isShipToVatNumberValid() && $properties->getInvoiceTypeId() !== InvoiceType::PRO_FORMA_INVOICE) {
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCalculationCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(true);
            }
        }

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            }
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    if ($properties->isCompanyRegisteredForOss()) {
                        $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                        $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                    } else {
                        $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                    }
                } else {
                    if ($properties->isCompanyRegisteredForOss()) {
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                        $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                    } else {
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                    }
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());

                if ($properties->isMarketplaceIncorporatedInEu()) {
                    if ($properties->isMarketplaceRegisteredForOss()) {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS);
                            $deemed->setTaxReportingCountryId($properties->getMarketplaceOssCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS);
                            $deemed->setTaxReportingCountryId($properties->getMarketplaceOssCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        }
                    } else {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_REGULAR);
                            $deemed->setTaxReportingCountryId($properties->getBillToCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_REGULAR);
                            $deemed->setTaxReportingCountryId($properties->getBillToCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        }
                    }
                } else {
                    if ($properties->isMarketplaceRegisteredForVoes()) {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS);
                            $deemed->setTaxReportingCountryId($properties->getMarketplaceVoesRegistrationCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS);
                            $deemed->setTaxReportingCountryId($properties->getMarketplaceVoesRegistrationCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        }
                    } else {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_REGULAR);
                            $deemed->setTaxReportingCountryId($properties->getBillToCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = new DetectedInvoiceGoodsProperties($properties);
                            $deemed->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_REGULAR);
                            $deemed->setTaxReportingCountryId($properties->getBillToCountryId());
                            $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        }
                    }
                }

                $deemed->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $deemed->setTaxCharged(true);
                $deemed->setCustomerTypeId(CustomerType::B2C);
                $deemed->setTaxCalculationCountryId($properties->getBillToCountryId());

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
