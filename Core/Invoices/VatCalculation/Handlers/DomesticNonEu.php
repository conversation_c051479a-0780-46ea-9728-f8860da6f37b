<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class DomesticNonEu extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2C()) {
            $detected->setCustomerTypeId(CustomerType::B2C);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxCharged(true);
        } else {
            if (!$properties->isCompanyCountryAndShipFromCountrySame()) {
                if ($properties->isDomesticReverseChargeApplicable()) {
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                    $detected->setTaxCharged(false);
                } else {
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                }
            } else {
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
            }

            $detected->setCustomerTypeId(CustomerType::B2B);
        }

        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setTaxCharged(true);
            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
        } else {
            if ($properties->isPlatformTypeMarketplace()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                $deemed = $this->createDeemedDetected(
                    TaxCollectionResponsibility::MARKETPLACE,
                    true,
                    $properties->getCompanyCountryId(),
                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                    $properties->getBillToCountryId(),
                    $properties->getBillToCountryId(),
                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                    CustomerType::B2C
                );

                $detected->setDeemedDetected($deemed);
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
