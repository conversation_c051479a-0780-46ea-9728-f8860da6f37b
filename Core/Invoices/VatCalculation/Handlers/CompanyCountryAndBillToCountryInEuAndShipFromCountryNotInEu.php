<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class CompanyCountryAndBillToCountryInEuAndShipFromCountryNotInEu extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isSaleTransactionAbove150Euro()) {
            if ($properties->isCustomerTypeB2C()) {
                $detected->setCustomerTypeId(CustomerType::B2C);
            } else {
                $detected->setCustomerTypeId(CustomerType::B2B);
            }

            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCharged(false);
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
        } else {
            if ($properties->isCompanyRegisteredForIoss()) {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_IS_IOSS);
                    $detected->setTaxReportingCountryId($properties->getCompanyIossRegistrationCountryId());
                    $detected->setCustomerTypeId(CustomerType::B2C);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::IOSS_EXPORT_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(true);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setCustomerTypeId(CustomerType::B2B);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(false);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                }
            } else {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setCustomerTypeId(CustomerType::B2B);
                }

                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(false);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            }

            if ($properties->isPlatformTypeMarketplace() && $properties->isCustomerTypeB2C()) {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setTaxCalculationCountryId($properties->getShipToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                $deemed = new DetectedInvoiceGoodsProperties($properties);
                $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_IS_IOSS);
                $deemed->setCustomerTypeId(CustomerType::B2C);
                $deemed->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $deemed->setTaxCharged(true);
                $deemed->setTaxReportingCountryId($properties->getMarketplaceIossRegistrationCountryId());
                $deemed->setTaxCalculationCountryId($properties->getShipToCountryId());
                $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_IOSS_EXPORT_INVOICE, $properties->getInvoiceTypeId());

                $detected->setDeemedDetected($deemed);
            }
        }

        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);
            } else {
                $detected->setCustomerTypeId(CustomerType::B2B);

                if ($properties->isCompanyCountryAndBillToCountrySame()) {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                } else {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                    $detected->setTaxCharged(false);
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                }
            }
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    if ($properties->isCompanyRegisteredForOss()) {
                        $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                        $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                    } else {
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                        $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);

                        if ($properties->isCompanyCountryAndBillToCountrySame()) {
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                        } else {
                            $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                        }
                    }
                } else {
                    if ($properties->isCompanyCountryAndBillToCountrySame()) {
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                    } else {
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());

                        if ($properties->isCompanyRegisteredForOss()) {
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                            $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                        } else {
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                        }
                    }
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);

                if ($properties->isMarketplaceIncorporatedInEu()) {
                    if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                        $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        if ($properties->isMarketplaceRegisteredForOss()) {
                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        if ($properties->isCompanyCountryAndBillToCountrySame()) {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            if ($properties->isMarketplaceRegisteredForOss()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                    $properties->getMarketplaceOssCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            if ($properties->isMarketplaceRegisteredForOss()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                    $properties->getMarketplaceOssCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        }
                    }
                } else {
                    if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                        $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        if ($properties->isMarketplaceRegisteredForVoes()) {
                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        if ($properties->isCompanyCountryAndBillToCountrySame()) {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            if ($properties->isMarketplaceRegisteredForVoes()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                    $properties->getMarketplaceVoesRegistrationCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            if ($properties->isMarketplaceRegisteredForVoes()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                    $properties->getMarketplaceVoesRegistrationCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        }
                    }
                }

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
