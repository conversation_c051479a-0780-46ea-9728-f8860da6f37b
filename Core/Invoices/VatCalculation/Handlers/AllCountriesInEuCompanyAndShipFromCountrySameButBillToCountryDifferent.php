<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class AllCountriesInEuCompanyAndShipFromCountrySameButBillToCountryDifferent extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2C()) {
            if ($properties->hasCompanyPassedThreshold()) {
                if ($properties->isCompanyRegisteredForOss()) {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                    $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                }

                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            } else {
                if ($properties->isCompanyRegisteredForOss()) {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                    $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                    $detected->setCustomerTypeId(CustomerType::B2C);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setCustomerTypeId(CustomerType::B2C);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCalculationCountryId($properties->getShipFromCountryId());
                }
            }

            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCharged(true);
        } else {
            if ($properties->isCompanyRegisteredForOss()) {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
            } else {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
            }

            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCharged(false);

            if (!$properties->isShipToVatNumberValid() && $properties->getInvoiceTypeId() !== InvoiceType::PRO_FORMA_INVOICE) {
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCalculationCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(true);
            }
        }

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setTaxCharged(false);
            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);

                if ($properties->isCompanyRegisteredForOss()) {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                    $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                if ($properties->isMarketplaceIncorporatedInEu()) {
                    if ($properties->isMarketplaceRegisteredForOss()) {
                        $deemed = $this->createDeemedDetected(
                            TaxCollectionResponsibility::MARKETPLACE,
                            true,
                            $properties->getCompanyCountryId(),
                            TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                            $properties->getMarketplaceOssCountryId(),
                            $properties->getBillToCountryId(),
                            InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                            CustomerType::B2C
                        );
                    } else {
                        $deemed = $this->createDeemedDetected(
                            TaxCollectionResponsibility::MARKETPLACE,
                            true,
                            $properties->getCompanyCountryId(),
                            TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                            $properties->getBillToCountryId(),
                            $properties->getBillToCountryId(),
                            InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                            CustomerType::B2C
                        );
                    }
                } else {
                    if ($properties->isMarketplaceRegisteredForVoes()) {
                        $deemed = $this->createDeemedDetected(
                            TaxCollectionResponsibility::MARKETPLACE,
                            true,
                            $properties->getCompanyCountryId(),
                            TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                            $properties->getMarketplaceVoesRegistrationCountryId(),
                            $properties->getBillToCountryId(),
                            InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                            CustomerType::B2C
                        );
                    } else {
                        $deemed = $this->createDeemedDetected(
                            TaxCollectionResponsibility::MARKETPLACE,
                            true,
                            $properties->getCompanyCountryId(),
                            TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                            $properties->getBillToCountryId(),
                            $properties->getBillToCountryId(),
                            InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                            CustomerType::B2C
                        );
                    }
                }

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
