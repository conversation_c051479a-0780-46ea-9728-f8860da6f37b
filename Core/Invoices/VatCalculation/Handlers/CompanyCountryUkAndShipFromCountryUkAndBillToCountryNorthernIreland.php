<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class CompanyCountryUkAndShipFromCountryUkAndBillToCountryNorthernIreland extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxCharged(true);
            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId(Country::GB);
            $detected->setTaxCalculationCountryId(Country::XI);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
        } else {
            if ($properties->isPlatformTypeMarketplace()) {
                if ($properties->isCompanyCountryVatNumberPermanentEstablishment()) {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId(Country::GB);
                    $detected->setTaxCalculationCountryId(Country::XI);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                    $detected->setTaxCharged(false);
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId(Country::GB);
                    $detected->setTaxCalculationCountryId(Country::XI);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::UK_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setCustomerTypeId(CustomerType::B2B);

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        true,
                        $properties->getShipFromCountryId(),
                        TaxReportingScheme::UK_VOEC_DOMESTIC,
                        Country::GB,
                        Country::XI,
                        InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                        CustomerType::B2C
                    );

                    $detected->setDeemedDetected($deemed);
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId(Country::GB);
                $detected->setTaxCalculationCountryId(Country::XI);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);
            }
        }

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxCharged(true);
            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId(Country::GB);
            $detected->setTaxCalculationCountryId(Country::GB);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
        } else {
            if ($properties->isPlatformTypeMarketplace()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId(Country::GB);
                $detected->setTaxCalculationCountryId(Country::GB);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::UK_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                $deemed = $this->createDeemedDetected(
                    TaxCollectionResponsibility::MARKETPLACE,
                    true,
                    $properties->getCompanyCountryId(),
                    TaxReportingScheme::UK_VOEC_DOMESTIC,
                    Country::GB,
                    Country::GB,
                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                    CustomerType::B2C
                );

                $detected->setDeemedDetected($deemed);
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId(Country::GB);
                $detected->setTaxCalculationCountryId(Country::GB);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
