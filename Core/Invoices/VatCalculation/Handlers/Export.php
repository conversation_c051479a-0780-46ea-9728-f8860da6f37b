<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class Export extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2C()) {
            $detected->setCustomerTypeId(CustomerType::B2C);
        } else {
            $detected->setCustomerTypeId(CustomerType::B2B);
        }

        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
        $detected->setTaxCharged(false);

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        $detected->setTaxCharged(false);
        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            } else {
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
            }
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingCountryId($properties->getShipToCountryId());

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        false,
                        $properties->getShipFromCountryId(),
                        TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                        $properties->getBillToCountryId(),
                        $properties->getBillToCountryId(),
                        InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                        CustomerType::B2C
                    );
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        false,
                        $properties->getCompanyCountryId(),
                        TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                        $properties->getBillToCountryId(),
                        $properties->getBillToCountryId(),
                        InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                        CustomerType::B2C
                    );
                }

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }
}
