<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class BillToCountryUkAndShipFromCountryNotUkAndCompanyCountryAny extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isPlatformTypeWebshop()) {
            if ($properties->isSaleTransactionAbove135GBP()) {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setCustomerTypeId(CustomerType::B2B);
                }

                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(false);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            } else {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setCustomerTypeId(CustomerType::B2C);
                    $detected->setTaxCharged(true);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::UK_VOEC_EXPORT_INVOICE, $properties->getInvoiceTypeId());
                } else {
                    $detected->setCustomerTypeId(CustomerType::B2B);
                    $detected->setTaxCharged(false);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                }

                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
            }

            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
        } elseif ($properties->isPlatformTypeMarketplace()) {
            if ($properties->isSaleTransactionAbove135GBP()) {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setCustomerTypeId(CustomerType::B2B);
                }

                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setTaxCharged(false);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            } else {
                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(false);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);

                if ($properties->isCustomerTypeB2C()) {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

                    $deemed = new DetectedInvoiceGoodsProperties($properties);
                    $deemed->setTaxReportingSchemeId(TaxReportingScheme::UK_VOEC_IMPORT);
                    $deemed->setTaxReportingCountryId($properties->getBillToCountryId());
                    $deemed->setCustomerTypeId(CustomerType::B2C);
                    $deemed->setTaxCharged(true);
                    $deemed->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                    $deemed->setInvoiceSubtypeId(InvoiceSubtype::UK_VOEC_DEEMED_EXPORT_INVOICE, $properties->getInvoiceTypeId());
                    $deemed->setTaxCalculationCountryId($properties->getBillToCountryId());

                    $detected->setDeemedDetected($deemed);
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::UK_VOEC_IMPORT);
                    $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                }
            }
        }

        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setTaxCharged(false);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            } else {
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
            }
        } else {
            if ($properties->isPlatformTypeMarketplace()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        true,
                        $properties->getShipFromCountryId(),
                        TaxReportingScheme::UK_VOEC_DOMESTIC,
                        $properties->getBillToCountryId(),
                        $properties->getBillToCountryId(),
                        InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                        CustomerType::B2C
                    );

                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                    $deemed = $this->createDeemedDetected(
                        TaxCollectionResponsibility::MARKETPLACE,
                        true,
                        $properties->getCompanyCountryId(),
                        TaxReportingScheme::UK_VOEC_DOMESTIC,
                        $properties->getBillToCountryId(),
                        $properties->getBillToCountryId(),
                        InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                        CustomerType::B2C
                    );
                }

                $detected->setDeemedDetected($deemed);
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                }
            }
        }

        return $detected;
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
