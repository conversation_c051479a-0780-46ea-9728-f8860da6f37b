<?php

namespace App\Core\Invoices\VatCalculation\Handlers\Contracts;

use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

abstract class VatCalculationHandlerContract
{
    private DetectedInvoiceGoodsProperties $detectedInvoiceGoodsProperties;
    private InvoiceGoodsProperties $invoiceGoodsProperties;

    public function __construct(InvoiceGoodsProperties $invoiceGoodsProperties, bool $debug = false)
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
        if ($debug) {
            $this->debug();
        }

        $this->detectedInvoiceGoodsProperties = $this->run($invoiceGoodsProperties, new DetectedInvoiceGoodsProperties($invoiceGoodsProperties));
    }

    public function getInvoiceGoodsProperties(): InvoiceGoodsProperties
    {
        return $this->invoiceGoodsProperties;
    }

    public function getDetectedInvoiceGoodsProperties(): DetectedInvoiceGoodsProperties
    {
        return $this->detectedInvoiceGoodsProperties;
    }

    private function run(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isItemTypeDigitalGoods()) {
            $detected = $this->resolveDigitalGoods($properties, $detected);
        } elseif ($properties->isItemTypeServices()) {
            $detected = $this->resolveServices($properties, $detected);
        } else {
            $detected = $this->resolveGoods($properties, $detected);
        }

        return $detected;
    }

    protected abstract function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties;

    protected abstract function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties;

    protected abstract function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties;

    protected function createDeemedDetected(
        int $taxCollectionResponsibilityId,
        bool $taxCharged,
        int $suppliedFromCountryId,
        int $taxReportingSchemeId,
        int $taxReportingCountryId,
        int $taxCalculationCountryId,
        int $invoiceSubtypeId,
        int $customerTypeId,

    ): DetectedInvoiceGoodsProperties {
        $detected = new DetectedInvoiceGoodsProperties($this->getInvoiceGoodsProperties());

        $detected->setTaxCollectionResponsibilityId($taxCollectionResponsibilityId);
        $detected->setTaxCharged($taxCharged);
        $detected->setSuppliedFromCountryId($suppliedFromCountryId);
        $detected->setTaxReportingSchemeId($taxReportingSchemeId);
        $detected->setTaxReportingCountryId($taxReportingCountryId);
        $detected->setTaxCalculationCountryId($taxCalculationCountryId);
        $detected->setInvoiceSubtypeId($invoiceSubtypeId);
        $detected->setCustomerTypeId($customerTypeId);

        return $detected;
    }

    public abstract function debug(): void;
}
