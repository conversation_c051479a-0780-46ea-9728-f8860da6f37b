<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class BillToCountryInEuCompanyAndShipFromCountryNotInEu extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isPlatformTypeWebshop()) {
            if ($properties->isSaleTransactionAbove150Euro()) {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setCustomerTypeId(CustomerType::B2B);
                }

                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(false);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            } else {
                if ($properties->isCompanyRegisteredForIoss()) {
                    if ($properties->isCustomerTypeB2C()) {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_IS_IOSS);
                        $detected->setTaxReportingCountryId($properties->getCompanyIossRegistrationCountryId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::IOSS_EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setTaxCharged(true);
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    } else {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                        $detected->setCustomerTypeId(CustomerType::B2B);
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setTaxCharged(false);
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                    }
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    if ($properties->isCustomerTypeB2C()) {
                        $detected->setCustomerTypeId(CustomerType::B2C);
                    } else {
                        $detected->setCustomerTypeId(CustomerType::B2B);
                    }
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(false);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                }
            }

        } else {
            if ($properties->isSaleTransactionAbove150Euro()) {
                if ($properties->isCustomerTypeB2C()) {
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setCustomerTypeId(CustomerType::B2B);
                }

                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(false);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            } else {
                if ($properties->isMarketplaceRegisteredForIoss()) {
                    if ($properties->isCustomerTypeB2C()) {
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                        $detected->setCustomerTypeId(CustomerType::B2B);
                        $detected->setTaxCharged(false);
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                        $deemed = new DetectedInvoiceGoodsProperties($properties);
                        $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_IS_IOSS);
                        $deemed->setTaxReportingCountryId($properties->getMarketplaceIossRegistrationCountryId());
                        $deemed->setCustomerTypeId(CustomerType::B2C);
                        $deemed->setTaxCharged(true);
                        $deemed->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                        $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_IOSS_EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $deemed->setTaxCalculationCountryId($properties->getBillToCountryId());

                        $detected->setDeemedDetected($deemed);
                    } else {
                        $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                        $detected->setCustomerTypeId(CustomerType::B2B);
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setTaxCharged(false);
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                    }

                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                } else {
                    if ($properties->isCustomerTypeB2C()) {
                        $detected->setCustomerTypeId(CustomerType::B2C);
                    } else {
                        $detected->setCustomerTypeId(CustomerType::B2B);
                    }

                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(false);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                }
            }

        }

        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
            }
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2C);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                }

                if ($properties->isCompanyRegisteredForVoes()) {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS);
                    $detected->setTaxReportingCountryId($properties->getCompanyVoesRegistrationCountryId());
                } else {
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);

                if ($properties->isMarketplaceIncorporatedInEu()) {
                    if ($properties->isMarketplaceRegisteredForOss()) {
                        $detected->setTaxCharged(false);
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxCharged(false);
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    }
                } else {
                    $detected->setTaxCharged(false);
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);

                    if ($properties->isMarketplaceRegisteredForVoes()) {
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    }
                }

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
