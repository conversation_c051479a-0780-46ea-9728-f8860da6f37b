<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class AllCountriesInEuButShipFromCountryAndBillToCountryAreDifferent extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2C()) {
            if ($properties->isCompanyRegisteredForOss()) {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
            } else {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getBillToCountryId());
            }

            $detected->setCustomerTypeId(CustomerType::B2C);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setTaxCharged(true);
        } else {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
            $detected->setTaxCharged(false);

            if (!$properties->isShipToVatNumberValid() && $properties->getInvoiceTypeId() !== InvoiceType::PRO_FORMA_INVOICE) {
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCalculationCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(true);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

        if ($properties->isCustomerTypeB2B()) {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            } else {
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());

                if ($properties->isCompanyCountryAndBillToCountrySame()) {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                } else {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                    $detected->setTaxCharged(false);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                }
            }
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setCustomerTypeId(CustomerType::B2C);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());

                    if ($properties->isCompanyRegisteredForOss()) {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                        $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                    } else {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                    }
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());

                    if ($properties->isCompanyCountryAndBillToCountrySame()) {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                    } else {
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());

                        if ($properties->isCompanyRegisteredForOss()) {
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_US_OSS);
                            $detected->setTaxReportingCountryId($properties->getCompanyOssRegistrationCountryId());
                        } else {
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                        }
                    }
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);

                if ($properties->isMarketplaceIncorporatedInEu()) {
                    if ($properties->isMarketplaceRegisteredForOss()) {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipToCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            if ($properties->isCompanyCountryAndBillToCountrySame()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                    $properties->getMarketplaceOssCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                    $properties->getMarketplaceOssCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        }
                    } else {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            if ($properties->isCompanyCountryAndBillToCountrySame()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        }
                    }
                } else {
                    if ($properties->isMarketplaceRegisteredForVoes()) {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            if ($properties->isCompanyCountryAndBillToCountrySame()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                    $properties->getMarketplaceVoesRegistrationCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                    $properties->getMarketplaceVoesRegistrationCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        }

                    } else {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());

                            if ($properties->isCompanyCountryAndBillToCountrySame()) {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            } else {
                                $deemed = $this->createDeemedDetected(
                                    TaxCollectionResponsibility::MARKETPLACE,
                                    true,
                                    $properties->getCompanyCountryId(),
                                    TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                    $properties->getBillToCountryId(),
                                    $properties->getBillToCountryId(),
                                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                                    CustomerType::B2C
                                );
                            }
                        }
                    }
                }

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
