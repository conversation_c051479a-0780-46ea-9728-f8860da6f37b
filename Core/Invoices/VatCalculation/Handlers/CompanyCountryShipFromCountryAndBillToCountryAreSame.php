<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class CompanyCountryShipFromCountryAndBillToCountryAreSame extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
        $detected->setTaxReportingCountryId($properties->getBillToCountryId());
        $detected->setTaxCharged(true);

        if ($properties->isCustomerTypeB2C()) {
            $detected->setCustomerTypeId(CustomerType::B2C);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());

            $detected = $this->resolveCountrySpecificPermanentEstablishment($properties, $detected);
        } else {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
        }

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCompanyCountryInEu()) {
            if ($properties->isCustomerTypeB2B()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setCustomerTypeId(CustomerType::B2B);
            } else {
                if ($properties->isPlatformTypeWebshop()) {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setCustomerTypeId(CustomerType::B2C);
                } else {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                    $detected->setTaxCharged(false);

                    if ($properties->isMarketplaceIncorporatedInEu()) {
                        if ($properties->isMarketplaceRegisteredForOss()) {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        if ($properties->isMarketplaceRegisteredForVoes()) {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                            $detected->setCustomerTypeId(CustomerType::B2B);

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );

                        }

                    }
                    $detected->setDeemedDetected($deemed);
                }
            }
        } else {
            if ($properties->isCompanyCountryUk()) {
                if ($properties->isCustomerTypeB2B()) {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId(Country::GB);
                    $detected->setTaxCalculationCountryId(Country::GB);
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setCustomerTypeId(CustomerType::B2B);
                } else {
                    if ($properties->isPlatformTypeWebshop()) {
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                        $detected->setTaxCharged(true);
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId(Country::GB);
                        $detected->setTaxCalculationCountryId(Country::GB);
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                    } else {
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                        $detected->setTaxCharged(false);
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId(Country::GB);
                        $detected->setTaxCalculationCountryId(Country::GB);
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::UK_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        $deemed = $this->createDeemedDetected(
                            TaxCollectionResponsibility::MARKETPLACE,
                            true,
                            $properties->getCompanyCountryId(),
                            TaxReportingScheme::UK_VOEC_DOMESTIC,
                            Country::GB,
                            Country::GB,
                            InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                            CustomerType::B2C
                        );

                        $detected->setDeemedDetected($deemed);
                    }
                }
            } else {
                if ($properties->isCustomerTypeB2B()) {
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                    $detected->setTaxCharged(true);
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setCustomerTypeId(CustomerType::B2B);
                } else {
                    if ($properties->isPlatformTypeWebshop()) {
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                        $detected->setTaxCharged(true);
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2C);
                    } else {
                        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                        $detected->setTaxCharged(false);
                        $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                        $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                        $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                        $detected->setCustomerTypeId(CustomerType::B2B);

                        $deemed = $this->createDeemedDetected(
                            TaxCollectionResponsibility::MARKETPLACE,
                            true,
                            $properties->getCompanyCountryId(),
                            TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                            $properties->getBillToCountryId(),
                            $properties->getBillToCountryId(),
                            InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                            CustomerType::B2C
                        );

                        $detected->setDeemedDetected($deemed);
                    }
                }
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    private function resolveCountrySpecificPermanentEstablishment(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        $isCompanyCountryVatNumberPermanentEstablishment = $properties->isCompanyCountryVatNumberPermanentEstablishment();
        if (!$properties->isPlatformTypeMarketplace() || $isCompanyCountryVatNumberPermanentEstablishment) {
            return $detected;
        }

        $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);

        if ($properties->isCompanyCountryUk()) {
            $detected->setTaxReportingSchemeId(TaxReportingScheme::UK_VOEC_DOMESTIC);
        } elseif ($properties->isCompanyCountryInEuRaw()) {
            $detected->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_REGULAR);
        }

        return $detected;
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
