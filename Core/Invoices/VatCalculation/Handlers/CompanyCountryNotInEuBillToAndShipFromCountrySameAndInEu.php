<?php

namespace App\Core\Invoices\VatCalculation\Handlers;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\VatCalculation\Handlers\Contracts\VatCalculationHandlerContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class CompanyCountryNotInEuBillToAndShipFromCountrySameAndInEu extends VatCalculationHandlerContract
{
    protected function resolveGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isPlatformTypeWebshop()) {
            if ($properties->isCustomerTypeB2C()) {
                $detected->setCustomerTypeId(CustomerType::B2C);
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCharged(true);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
            } else {
                if ($properties->isDomesticReverseChargeApplicable()) {
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(false);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                } else {
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(true);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                }

                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setTaxCalculationCountryId($properties->getShipFromCountryId());
            }

            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxReportingCountryId($properties->getBillToCountryId());
        } else {
            if ($properties->isCustomerTypeB2C()) {
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                $detected->setTaxCharged(false);
                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);

                $deemed = new DetectedInvoiceGoodsProperties($properties);
                if ($properties->isMarketplaceRegisteredForOss()) {
                    $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS);
                    $deemed->setTaxReportingCountryId($properties->getMarketplaceOssCountryId());
                } else {
                    $deemed->setTaxReportingSchemeId(TaxReportingScheme::DEEMED_RESELLER_REGULAR);
                    $deemed->setTaxReportingCountryId($properties->getBillToCountryId());
                }

                $deemed->setInvoiceSubtypeId(InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                $deemed->setTaxCalculationCountryId($properties->getBillToCountryId());
                $deemed->setTaxCharged(true);
                $deemed->setCustomerTypeId(CustomerType::B2C);
                $deemed->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);

                $detected->setDeemedDetected($deemed);
            } else {
                if ($properties->isDomesticReverseChargeApplicable()) {
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(false);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                } else {
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
                    $detected->setTaxCharged(true);
                    $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                }

                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setTaxCalculationCountryId($properties->getShipFromCountryId());
            }
        }

        return $detected;
    }

    protected function resolveDigitalGoods(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        if ($properties->isCustomerTypeB2B()) {
            $detected->setCustomerTypeId(CustomerType::B2B);
            $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
            $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

            if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE, $properties->getInvoiceTypeId());
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::BUYER);
                $detected->setTaxCharged(false);
                $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());
            }
        } else {
            if ($properties->isPlatformTypeWebshop()) {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::SELLER);
                $detected->setTaxCharged(true);
                $detected->setCustomerTypeId(CustomerType::B2C);

                if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                    $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                    $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                    $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::DOMESTIC_SALES_INVOICE, $properties->getInvoiceTypeId());
                } else {
                    $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                    $detected->setTaxCalculationCountryId($properties->getBillToCountryId());
                    $detected->setInvoiceSubtypeId(InvoiceSubtype::CROSS_BORDER_B2C_SALES_INVOICE, $properties->getInvoiceTypeId());

                    if ($properties->isCompanyRegisteredForVoes()) {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::EU_NU_OSS);
                        $detected->setTaxReportingCountryId($properties->getCompanyVoesRegistrationCountryId());
                    } else {
                        $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                        $detected->setTaxReportingCountryId($properties->getBillToCountryId());
                    }
                }
            } else {
                $detected->setTaxCollectionResponsibilityId(TaxCollectionResponsibility::MARKETPLACE);
                $detected->setTaxCharged(false);
                $detected->setTaxReportingSchemeId(TaxReportingScheme::REGULAR);
                $detected->setCustomerTypeId(CustomerType::B2B);
                $detected->setTaxCalculationCountryId($properties->getBillToCountryId());

                if ($properties->isMarketplaceIncorporatedInEu()) {
                    if ($properties->isMarketplaceRegisteredForOss()) {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS,
                                $properties->getMarketplaceOssCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    }
                } else {
                    if ($properties->isMarketplaceRegisteredForVoes()) {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_EU_NU_OSS,
                                $properties->getMarketplaceVoesRegistrationCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    } else {
                        if ($properties->isCompanyShipFromCountryVatNumberPermanentEstablishment()) {
                            $detected->setSuppliedFromCountryId($properties->getShipFromCountryId());
                            $detected->setTaxReportingCountryId($properties->getShipFromCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EU_DEEMED_B2B_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getShipFromCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        } else {
                            $detected->setSuppliedFromCountryId($properties->getCompanyCountryId());
                            $detected->setTaxReportingCountryId($properties->getCompanyCountryId());
                            $detected->setInvoiceSubtypeId(InvoiceSubtype::EXPORT_INVOICE, $properties->getInvoiceTypeId());

                            $deemed = $this->createDeemedDetected(
                                TaxCollectionResponsibility::MARKETPLACE,
                                true,
                                $properties->getCompanyCountryId(),
                                TaxReportingScheme::DEEMED_RESELLER_REGULAR,
                                $properties->getBillToCountryId(),
                                $properties->getBillToCountryId(),
                                InvoiceSubtype::DEEMED_CROSS_BORDER_B2C_SALES_INVOICE,
                                CustomerType::B2C
                            );
                        }
                    }
                }

                $detected->setDeemedDetected($deemed);
            }
        }

        return $detected;
    }

    protected function resolveServices(InvoiceGoodsProperties $properties, DetectedInvoiceGoodsProperties $detected): DetectedInvoiceGoodsProperties
    {
        return $this->resolveGoods($properties, $detected);
    }

    public function debug(): void
    {
        dd(__CLASS__);
    }
}
