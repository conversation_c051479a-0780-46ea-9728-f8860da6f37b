<?php

namespace App\Core\Invoices\VatCalculation;

use App\Core\Invoices\VatCalculation\Handlers\AllCountriesInEuBillToAndShipFromCountrySameButCompanyCountryDifferent;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryNotInEuBillToAndShipFromCountryInEu;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryNotInEuBillToAndShipFromCountrySameAndInEu;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryAndBillToCountryInEuAndShipFromCountryNotInEu;
use App\Core\Invoices\VatCalculation\Handlers\AllCountriesInEuButShipFromCountryAndBillToCountryAreDifferent;
use App\Core\Invoices\VatCalculation\Handlers\AllCountriesInEuAndAllCountriesDifferent;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryUkAndShipFromCountryNorthernIrelandAndBillToCountryNorthernIreland;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryUkAndShipFromCountryUkAndBillToCountryNorthernIreland;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryShipFromCountryAndBillToCountryAreSame;
use App\Core\Invoices\VatCalculation\Handlers\CompanyCountryUkOrNorthernIrelandAndShipFromCountryNorthernIrelandAndBillToCountryUk;
use App\Core\Invoices\VatCalculation\Handlers\DomesticNonEu;
use App\Core\Invoices\VatCalculation\Handlers\Export;
use App\Core\Invoices\VatCalculation\Handlers\BillToCountryUkAndShipFromCountryNotUkAndCompanyCountryAny;
use App\Core\Invoices\VatCalculation\Handlers\AllCountriesInEuCompanyAndShipFromCountrySameButBillToCountryDifferent;
use App\Core\Invoices\VatCalculation\Handlers\BillToCountryInEuCompanyAndShipFromCountryNotInEu;
use App\Core\Invoices\VatCalculation\Handlers\BillToAndShipFromCountryUkAndCompanyCountryNotUk;
use App\Core\Invoices\VatCalculation\Handlers\AllCountriesInEuBillToAndCompanyCountrySameButShipFromCountryDifferent;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;

class InvoicePropertiesDetector
{
    private DetectedInvoiceGoodsProperties $detectedInvoiceGoodsProperties;
    private InvoiceGoodsProperties $invoiceGoodsProperties;

    public function setInvoiceGoodsProperties(InvoiceGoodsProperties $invoiceGoodsProperties): void
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
    }

    /**
     * @return DetectedInvoiceGoodsProperties
     */
    public function getDetectedInvoiceGoodsProperties(): DetectedInvoiceGoodsProperties
    {
        return $this->detectedInvoiceGoodsProperties;
    }

    public function getResolvedInvoiceGoodsProperties(): InvoiceGoodsProperties
    {
        return $this->invoiceGoodsProperties;
    }

    /** @noinspection PhpConditionAlreadyCheckedInspection */
    public function detect(): void
    {
        $properties = $this->getResolvedInvoiceGoodsProperties();
        $debug = false;
        $detector = null;

        if (is_null($detector) && !$properties->isCompanyCountrySameAsBillToAndShipFromCountry()) {
            if ($properties->isBillToCountryInEu()) {
                $data = null;

                if ($properties->isCompanyCountryInEu()) {
                    if ($properties->isShipFromCountryInEu()) {
                        if ($properties->isBillToCountrySameAsShipFromCountry()) {
                            $data = new AllCountriesInEuBillToAndShipFromCountrySameButCompanyCountryDifferent($properties, $debug);
                        } elseif ($properties->isCompanyCountryAndShipFromCountrySame()) {
                            $data = new AllCountriesInEuCompanyAndShipFromCountrySameButBillToCountryDifferent($properties, $debug);
                        } elseif ($properties->isCompanyCountryAndBillToCountrySame()) {
                            $data = new AllCountriesInEuBillToAndCompanyCountrySameButShipFromCountryDifferent($properties, $debug);
                        }
                    } else {
                        $data = new CompanyCountryAndBillToCountryInEuAndShipFromCountryNotInEu($properties, $debug);
                    }
                } else {
                    if ($properties->isShipFromCountryInEu()) {
                        if (!$properties->isBillToCountrySameAsShipFromCountry()) {
                            $data = new CompanyCountryNotInEuBillToAndShipFromCountryInEu($properties, $debug);
                        } elseif ($properties->isBillToCountrySameAsShipFromCountry()) {
                            if ($properties->isCompanyCountryVatNumberPermanentEstablishment() && $properties->isCompanyCountryUk() && $properties->isShipFromCountryNorthernIreland() && $properties->isBillToCountryNorthernIreland()) {
                                $data = new CompanyCountryUkAndShipFromCountryNorthernIrelandAndBillToCountryNorthernIreland($properties, $debug);
                            } else {
                                $data = new CompanyCountryNotInEuBillToAndShipFromCountrySameAndInEu($properties, $debug);
                            }
                        }
                    } else {
                        if ($properties->isCompanyCountryVatNumberPermanentEstablishment() && $properties->isCompanyCountryUk() && $properties->isShipFromCountryUk() && $properties->isBillToCountryNorthernIreland()) {
                            $data = new CompanyCountryUkAndShipFromCountryUkAndBillToCountryNorthernIreland($properties, $debug);
                        } else {
                            $data = new BillToCountryInEuCompanyAndShipFromCountryNotInEu($properties, $debug);
                        }
                    }
                }

                if (is_null($data) && $properties->isCompanyCountryBillToCountryAndShipFromCountryInEu()) {
                    if ($properties->areCompanyCountryBillToCountryAndShipFromCountryAllDifferent()) {
                        $data = new AllCountriesInEuAndAllCountriesDifferent($properties, $debug);
                    } elseif ($properties->getBillToCountryId() !== $properties->getShipFromCountryId()) {
                        $data = new AllCountriesInEuButShipFromCountryAndBillToCountryAreDifferent($properties, $debug);
                    }
                }

                $detector = $data;
            } else {
                if ($properties->isBillToCountryUk()) {
                    if ($properties->isShipFromCountryUk()) {
                        if (!$properties->isCompanyCountryUk()) {
                            $detector = new BillToAndShipFromCountryUkAndCompanyCountryNotUk($properties, $debug);
                        }
                    } else {
                        if ($properties->isCompanyCountryVatNumberPermanentEstablishment() && $properties->isShipFromCountryNorthernIreland() && ($properties->isCompanyCountryUk() || $properties->isCompanyCountryNorthernIreland())) {
                            $detector = new CompanyCountryUkOrNorthernIrelandAndShipFromCountryNorthernIrelandAndBillToCountryUk($properties, $debug);
                        } else {
                            $detector = new BillToCountryUkAndShipFromCountryNotUkAndCompanyCountryAny($properties, $debug);
                        }
                    }

                } elseif (!$properties->isBillToCountrySameAsShipFromCountry()) {
                    $detector = new Export($properties, $debug);
                } else {
                    $detector = new DomesticNonEu($properties, $debug);
                }
            }
        }

        if (is_null($detector)) {
            $detector = new CompanyCountryShipFromCountryAndBillToCountryAreSame($properties, $debug);
        }

        $this->invoiceGoodsProperties->setDetector($detector::class);
        $this->detectedInvoiceGoodsProperties = $detector->getDetectedInvoiceGoodsProperties();
//        dd($this->detectedInvoiceGoodsProperties->toArray());
    }
}
