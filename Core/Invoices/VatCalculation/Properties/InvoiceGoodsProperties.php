<?php

namespace App\Core\Invoices\VatCalculation\Properties;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\DeliveryCondition;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\PlatformType;
use App\Core\Invoices\Contracts\InvoiceGoodsPropertiesContract;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;

class InvoiceGoodsProperties implements InvoiceGoodsPropertiesContract, Arrayable
{
    private ?string $calculationDate = null;
    private ?int $billToCountryId = null;
    private ?int $shipToCountryId = null;
    private ?int $companyCountryId = null;
    private ?int $shipFromCountryId = null;
    private ?bool $isCompanyRegisteredForOss = null;
    private ?bool $isCompanyRegisteredForIoss = null;
    private ?bool $isCompanyCountryVatNumberPermanentEstablishment = null;
    private ?int $invoiceTypeId = null;
    private ?int $itemTypeId = null;
    private ?bool $isMarketplaceIncorporatedInEu = null;
    private ?bool $isMarketplaceRegisteredForOss = null;
    private ?bool $hasCompanyPassedThreshold = null;
    private ?bool $isMarketplaceRegisteredForIoss = null;
    private ?bool $isSaleTransactionAbove150Euro = null;
    private ?bool $hasOnlyVatNumberAndWarehouseInCompanyCountry = null;
    private ?bool $isSaleTransactionAbove135GBP = null;
    private ?int $platformTypeId = null;
    private ?int $marketplaceOssCountryId = null;
    private ?int $marketplaceIossRegistrationCountryId = null;
    private ?bool $isDomesticReverseChargeApplicable = null;
    private ?int $companyOssRegistrationCountryId = null;
    private ?int $companyIossRegistrationCountryId = null;
    private bool $shipToVatNumberValid = false;
    private ?string $detector = null;
    private ?bool $isCompanyRegisteredForVoes = null;
    private ?int $companyVoesRegistrationCountryId = null;
    private ?bool $isMarketplaceRegisteredForVoes = null;
    private ?int $marketplaceVoesRegistrationCountryId = null;
    private ?bool $isCompanyShipFromCountryVatNumberPermanentEstablishment = null;
    private ?bool $hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry = null;

    private ?bool $isCompanyShipToCountryVatNumberPermanentEstablishment = false;
    private ?bool $doesCompanyHaveVatNumberInShipToCountry = false;
    private ?int $deliveryConditionId = DeliveryCondition::XXX;

    public function getDeliveryConditionId(): ?int
    {
        return $this->deliveryConditionId;
    }

    public function setDeliveryConditionId(?int $deliveryConditionId): InvoiceGoodsProperties
    {
        $this->deliveryConditionId = $deliveryConditionId;

        return $this;
    }

    public function getDoesCompanyHaveVatNumberInShipToCountry(): ?bool
    {
        return $this->doesCompanyHaveVatNumberInShipToCountry;
    }

    public function setDoesCompanyHaveVatNumberInShipToCountry(?bool $doesCompanyHaveVatNumberInShipToCountry): InvoiceGoodsProperties
    {
        $this->doesCompanyHaveVatNumberInShipToCountry = $doesCompanyHaveVatNumberInShipToCountry;

        return $this;
    }

    public function getIsCompanyShipToCountryVatNumberPermanentEstablishment(): ?bool
    {
        return $this->isCompanyShipToCountryVatNumberPermanentEstablishment;
    }

    public function setIsCompanyShipToCountryVatNumberPermanentEstablishment(?bool $isCompanyShipToCountryVatNumberPermanentEstablishment): InvoiceGoodsProperties
    {
        $this->isCompanyShipToCountryVatNumberPermanentEstablishment = $isCompanyShipToCountryVatNumberPermanentEstablishment;

        return $this;
    }

    public function getCalculationDate(): ?string
    {
        return $this->calculationDate;
    }

    public function setCalculationDate(?string $calculationDate): InvoiceGoodsProperties
    {
        $this->calculationDate = $calculationDate;

        return $this;
    }

    public function getItemTypeId(): ?int
    {
        return $this->itemTypeId;
    }

    public function setItemTypeId(?int $invoiceItemTypeId): InvoiceGoodsProperties
    {
        $this->itemTypeId = $invoiceItemTypeId;

        return $this;
    }

    public function isItemTypeGoods(): bool
    {
        return $this->getItemTypeId() === ItemType::GOODS;
    }

    public function isItemTypeServices(): bool
    {
        return $this->getItemTypeId() === ItemType::SERVICES;
    }

    public function isItemTypeDigitalGoods(): bool
    {
        return $this->getItemTypeId() === ItemType::DIGITAL_GOODS;
    }

    public function setBillToCountryId(int $value): void
    {
        $this->billToCountryId = $value;
    }

    public function setCompanyCountryId(int $value): void
    {
        $this->companyCountryId = $value;
    }

    public function setShipFromCountryId(int $value): void
    {
        $this->shipFromCountryId = $value;
    }

    public function setShipToCountryId(int $value): void
    {
        $this->shipToCountryId = $value;
    }

    public function setCompanyRegisteredForOss(bool $value): void
    {
        $this->isCompanyRegisteredForOss = $value;
    }

    public function setCompanyRegisteredForIoss(bool $value): void
    {
        $this->isCompanyRegisteredForIoss = $value;
    }

    public function setCompanyPassedThreshold(bool $value): void
    {
        $this->hasCompanyPassedThreshold = $value;
    }

    public function setMarketplaceIncorporatedInEu(bool $value): void
    {
        $this->isMarketplaceIncorporatedInEu = $value;
    }

    public function setMarketplaceRegisteredForIOSS(bool $value): void
    {
        $this->isMarketplaceRegisteredForIoss = $value;
    }

    public function setMarketplaceRegisteredForOSS(bool $value): void
    {
        $this->isMarketplaceRegisteredForOss = $value;
    }

    public function setSaleTransactionAbove150Euro(bool $value): void
    {
        $this->isSaleTransactionAbove150Euro = $value;
    }

    public function setSaleTransactionAbove135GBP(bool $value): void
    {
        $this->isSaleTransactionAbove135GBP = $value;
    }

    public function setOnlyVatNumberAndWarehouseInCompanyCountry(bool $value): void
    {
        $this->hasOnlyVatNumberAndWarehouseInCompanyCountry = $value;
    }

    public function setDomesticReverseChargeApplicableInBillToCountry(bool $value): void
    {
        $this->isDomesticReverseChargeApplicable = $value;
    }

    public function getBillToCountryId(): int
    {
        $countryId = $this->getOriginalBillToCountryId();

        $itemTypes = [
            ItemType::DIGITAL_GOODS,
            ItemType::SERVICES,
        ];
        if ($this->isOriginalBillToCountryNorthernIreland() && in_array($this->getItemTypeId(), $itemTypes)) {
            $countryId = Country::GB;
        }

        return $countryId;
    }

    public function getOriginalBillToCountryId(): int
    {
        return $this->billToCountryId;
    }

    public function getCompanyCountryId(): int
    {
        $countryId = $this->getOriginalCompanyCountryId();

        $itemTypes = [
            ItemType::DIGITAL_GOODS,
            ItemType::SERVICES,
        ];
        if ($this->isOriginalCompanyCountryNorthernIreland() && in_array($this->getItemTypeId(), $itemTypes)) {
            $countryId = Country::GB;
        }

        return $countryId;
    }

    public function getOriginalCompanyCountryId(): int
    {
        return $this->companyCountryId;
    }

    public function getShipFromCountryId(): int
    {
        $countryId = $this->getOriginalShipFromCountryId();

        $itemTypes = [
            ItemType::DIGITAL_GOODS,
            ItemType::SERVICES,
        ];
        if ($this->isOriginalShipFromCountryNorthernIreland() && in_array($this->getItemTypeId(), $itemTypes)) {
            $countryId = Country::GB;
        }

        return $countryId;
    }

    public function getOriginalShipFromCountryId(): int
    {
        return $this->shipFromCountryId;
    }

    public function getShipToCountryId(): ?int
    {
        return $this->shipToCountryId;
    }

    public function hasCustomerVatNumber(): bool
    {
        return !is_null($this->shipToCountryId);
    }

    public function isBillToCountryInEu(): bool
    {
        return $this->getBillToCountry()->isInEuForDate($this->getCalculationDate());
    }

    public function isCompanyCountryInEuRaw(): bool
    {
        return $this->getCompanyCountry()->isInEuForDate($this->getCalculationDate());
    }

    public function isCompanyCountryInEu(): bool
    {
        return $this->isCompanyCountryInEuRaw() && $this->isCompanyCountryVatNumberPermanentEstablishment();
    }

    public function isShipFromCountryInEu(): bool
    {
        return $this->getShipFromCountry()->isInEuForDate($this->getCalculationDate());
    }

    public function isCompanyCountrySameAsBillToAndShipFromCountry(): bool
    {
        return $this->getCompanyCountryId() === $this->getBillToCountryId()
            && $this->getCompanyCountryId() === $this->getShipFromCountryId();
    }

    public function isCompanyCountryBillToCountryAndShipFromCountryInEu(): bool
    {
        return $this->isCompanyCountryInEu()
            && $this->isBillToCountryInEu()
            && $this->isShipFromCountryInEu();
    }

    public function areCompanyCountryBillToCountryAndShipFromCountryAllDifferent(): bool
    {
        return $this->getCompanyCountryId() !== $this->getBillToCountryId()
            && $this->getCompanyCountryId() !== $this->getShipFromCountryId()
            && $this->getBillToCountryId() !== $this->getShipFromCountryId();
    }

    public function isCompanyCountryAndBillToCountrySame(): bool
    {
        return $this->getCompanyCountryId() === $this->getBillToCountryId();
    }

    public function isCompanyCountryAndShipFromCountrySame(): bool
    {
        return $this->getCompanyCountryId() === $this->getShipFromCountryId();
    }

    public function isBillToCountrySameAsShipFromCountry(): bool
    {
        return $this->getBillToCountryId() === $this->getShipFromCountryId();
    }

    public function isDomesticReverseChargeApplicable(): bool
    {
        return $this->isDomesticReverseChargeApplicable;
    }

    public function isCompanyRegisteredForOss(): bool
    {
        return $this->isCompanyRegisteredForOss;
    }

    public function isCompanyRegisteredForIoss(): bool
    {
        return $this->isCompanyRegisteredForIoss;
    }

    public function isCustomerTypeB2B(): bool
    {
        return $this->hasCustomerVatNumber() && $this->getBillToCountryId() === $this->getShipToCountryId();
    }

    public function isCustomerTypeB2C(): bool
    {
        return !$this->isCustomerTypeB2B();
    }

    public function getCustomerTypeId(): int
    {
        return $this->isCustomerTypeB2B() ? CustomerType::B2B : CustomerType::B2C;
    }

    public function hasOnlyVatNumberAndWarehouseInCompanyCountry(): bool
    {
        return $this->hasOnlyVatNumberAndWarehouseInCompanyCountry;
    }

    public function hasCompanyPassedThresholdRaw(): bool
    {
        return $this->hasCompanyPassedThreshold;
    }

    public function hasCompanyPassedThreshold(): bool
    {
        return $this->hasCompanyPassedThreshold || !$this->hasOnlyVatNumberAndWarehouseInCompanyCountry();
    }

    public function isSaleTransactionAbove150Euro(): bool
    {
        return $this->isSaleTransactionAbove150Euro;
    }

    public function isSaleTransactionAbove135GBP(): bool
    {
        return $this->isSaleTransactionAbove135GBP;
    }

    public function isMarketplaceRegisteredForIoss(): bool
    {
        return $this->isMarketplaceRegisteredForIoss;
    }

    public function isMarketplaceIncorporatedInEu(): bool
    {
        return $this->isMarketplaceIncorporatedInEu;
    }

    public function setPlatformTypeId(int $platformTypeId): void
    {
        $this->platformTypeId = $platformTypeId;
    }

    public function getPlatformTypeId(): int
    {
        return $this->platformTypeId;
    }

    public function isPlatformTypeWebshop(): bool
    {
        return $this->getPlatformTypeId() === PlatformType::WEBSHOP;
    }

    public function isPlatformTypeMarketplace(): bool
    {
        return $this->getPlatformTypeId() === PlatformType::MARKETPLACE;
    }

    public function getCompanyOssRegistrationCountryId(): ?int
    {
        return $this->companyOssRegistrationCountryId;
    }

    public function setCompanyOssRegistrationCountryId(int $companyOssRegistrationCountryId): void
    {
        $this->companyOssRegistrationCountryId = $companyOssRegistrationCountryId;
    }

    public function getCompanyIossRegistrationCountryId(): ?int
    {
        return $this->companyIossRegistrationCountryId;
    }

    public function setCompanyIossRegistrationCountryId(int $companyIOSSRegistrationCountryId): void
    {
        $this->companyIossRegistrationCountryId = $companyIOSSRegistrationCountryId;
    }

    public function getMarketplaceIossRegistrationCountryId(): ?int
    {
        return $this->marketplaceIossRegistrationCountryId;
    }

    public function setMarketplaceIossRegistrationCountryId(int $marketplaceIOSSRegistrationCountryId): void
    {
        $this->marketplaceIossRegistrationCountryId = $marketplaceIOSSRegistrationCountryId;
    }

    public function isMarketplaceRegisteredForOss(): bool
    {
        return $this->isMarketplaceRegisteredForOss;
    }

    public function getMarketplaceOssCountryId(): ?int
    {
        return $this->marketplaceOssCountryId;
    }

    public function setMarketplaceOssCountryId(int $marketplaceOssCountryId): void
    {
        $this->marketplaceOssCountryId = $marketplaceOssCountryId;
    }

    public function isBillToCountryUk(): bool
    {
        return $this->getBillToCountryId() === Country::GB;
    }

    /** @noinspection PhpUnused */
    public function isOriginalBillToCountryUk(): bool
    {
        return $this->getOriginalBillToCountryId() === Country::GB;
    }

    public function isBillToCountryNorthernIreland(): bool
    {
        return $this->getBillToCountryId() === Country::XI;
    }

    public function isOriginalBillToCountryNorthernIreland(): bool
    {
        return $this->getOriginalBillToCountryId() === Country::XI;
    }

    public function isShipFromCountryNorthernIreland(): bool
    {
        return $this->getShipFromCountryId() === Country::XI;
    }

    public function isOriginalShipFromCountryNorthernIreland(): bool
    {
        return $this->getOriginalShipFromCountryId() === Country::XI;
    }

    public function isShipFromCountryUk(): bool
    {
        return $this->getShipFromCountryId() === Country::GB;
    }

    /** @noinspection PhpUnused */
    public function isOriginalShipFromCountryUk(): bool
    {
        return $this->getOriginalShipFromCountryId() === Country::GB;
    }

    public function isCompanyCountryUk(): bool
    {
        return $this->getCompanyCountryId() === Country::GB;
    }

    /** @noinspection PhpUnused */
    public function isOriginalCompanyCountryUk(): bool
    {
        return $this->getOriginalCompanyCountryId() === Country::GB;
    }

    public function isCompanyCountryNorthernIreland(): bool
    {
        return $this->getCompanyCountryId() === Country::XI;
    }

    public function isOriginalCompanyCountryNorthernIreland(): bool
    {
        return $this->getOriginalCompanyCountryId() === Country::XI;
    }

    public function setInvoiceTypeId(int $value): void
    {
        $this->invoiceTypeId = $value;
    }

    public function getInvoiceTypeId(): int
    {
        return $this->invoiceTypeId;
    }

    public function isCompanyCountryVatNumberPermanentEstablishment(): bool
    {
        return $this->isCompanyCountryVatNumberPermanentEstablishment;
    }

    public function setIsCompanyCountryVatNumberPermanentEstablishment(bool $isCompanyCountryVatNumberPermanentEstablishment): InvoiceGoodsProperties
    {
        $this->isCompanyCountryVatNumberPermanentEstablishment = $isCompanyCountryVatNumberPermanentEstablishment;

        return $this;
    }

    public function isShipToVatNumberValid(): bool
    {
        return $this->shipToVatNumberValid;
    }

    public function setShipToVatNumberValid(bool $buyerVatNumberValid): void
    {
        $this->shipToVatNumberValid = $buyerVatNumberValid;
    }

    public function getDetector(): ?string
    {
        return $this->detector;
    }

    public function setDetector(?string $detector): InvoiceGoodsProperties
    {
        $this->detector = $detector;

        return $this;
    }

    private ?Collection $countries = null;

    private function getCountryById(int $countryId): Country
    {
        if (is_null($this->countries)) {
            $this->countries = cache()->remember('countries', 240, function () {
                return Country::all()->keyBy('id');
            });
        }

        return $this->countries->get($countryId);
    }

    public function getBillToCountry(): Country
    {
        return $this->getCountryById($this->getBillToCountryId());
    }

    public function getShipToCountry(): ?Country
    {
        $id = $this->getShipToCountryId();
        if (is_null($id)) {
            return null;
        }

        return $this->getCountryById($id);
    }

    public function getCompanyCountry(): Country
    {
        return $this->getCountryById($this->getCompanyCountryId());
    }

    public function getShipFromCountry(): Country
    {
        return $this->getCountryById($this->getShipFromCountryId());
    }

    public function getMarketplaceOssCountry(): ?Country
    {
        $id = $this->getMarketplaceOssCountryId();
        if (is_null($id)) {
            return null;
        }

        return $this->getCountryById($id);
    }

    public function getMarketplaceIossRegistrationCountry(): ?Country
    {
        $id = $this->getMarketplaceIossRegistrationCountryId();
        if (is_null($id)) {
            return null;
        }

        return $this->getCountryById($id);
    }

    public function getCompanyOssRegistrationCountry(): ?Country
    {
        $id = $this->getCompanyOssRegistrationCountryId();
        if (is_null($id)) {
            return null;
        }

        return $this->getCountryById($id);
    }

    public function getCompanyIossRegistrationCountry(): ?Country
    {
        $id = $this->getCompanyIossRegistrationCountryId();
        if (is_null($id)) {
            return null;
        }

        return $this->getCountryById($id);
    }

    public function isMarketplaceRegisteredForVoes(): ?bool
    {
        return $this->isMarketplaceRegisteredForVoes;
    }

    public function setIsMarketplaceRegisteredForVoes(?bool $isMarketplaceRegisteredForVoes): InvoiceGoodsProperties
    {
        $this->isMarketplaceRegisteredForVoes = $isMarketplaceRegisteredForVoes;

        return $this;
    }

    public function getCompanyVoesRegistrationCountryId(): ?int
    {
        return $this->companyVoesRegistrationCountryId;
    }

    public function setCompanyVoesRegistrationCountryId(?int $companyVoesRegistrationCountryId): InvoiceGoodsProperties
    {
        $this->companyVoesRegistrationCountryId = $companyVoesRegistrationCountryId;

        return $this;
    }

    public function isCompanyRegisteredForVoes(): bool
    {
        return $this->isCompanyRegisteredForVoes ?? false;
    }

    public function setIsCompanyRegisteredForVoes(bool $isCompanyRegisteredForVoes): InvoiceGoodsProperties
    {
        $this->isCompanyRegisteredForVoes = $isCompanyRegisteredForVoes;

        return $this;
    }

    public function getMarketplaceVoesRegistrationCountryId(): ?int
    {
        return $this->marketplaceVoesRegistrationCountryId;
    }

    public function setMarketplaceVoesRegistrationCountryId(?int $marketplaceVoesRegistrationCountryId): InvoiceGoodsProperties
    {
        $this->marketplaceVoesRegistrationCountryId = $marketplaceVoesRegistrationCountryId;

        return $this;
    }

    public function isCompanyShipFromCountryVatNumberPermanentEstablishment(): bool
    {
        return $this->isCompanyShipFromCountryVatNumberPermanentEstablishment ?? false;
    }

    public function setIsCompanyShipFromCountryVatNumberPermanentEstablishment(bool $isCompanyShipFromCountryVatNumberPermanentEstablishment): InvoiceGoodsProperties
    {
        $this->isCompanyShipFromCountryVatNumberPermanentEstablishment = $isCompanyShipFromCountryVatNumberPermanentEstablishment;

        return $this;
    }

    public function hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry(): bool
    {
        return $this->hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry ?? false;
    }

    public function setHasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry(bool $hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry): InvoiceGoodsProperties
    {
        $this->hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry = $hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry;

        return $this;
    }

    public function getSuppliedFromCountryId(): ?int
    {
        return $this->getOriginalSuppliedFromCountryId();
    }

    public function getOriginalSuppliedFromCountryId(): ?int
    {
        return $this->getOriginalShipFromCountryId();
    }

    public function toArray(): array
    {
        return [
            'isCompanyRegisteredForOss'                    => $this->isCompanyRegisteredForOss(),
            'isCompanyRegisteredForIoss'                   => $this->isCompanyRegisteredForIoss(),
            'isCompanyRegisteredForVoes'                   => $this->isCompanyRegisteredForVoes(),
            'invoiceTypeId'                                => $this->getInvoiceTypeId(),
            'itemTypeId'                                   => $this->getItemTypeId(),
            'isMarketplaceIncorporatedInEu'                => $this->isMarketplaceIncorporatedInEu(),
            'isMarketplaceRegisteredForOss'                => $this->isMarketplaceRegisteredForOss(),
            'isMarketplaceRegisteredForIoss'               => $this->isMarketplaceRegisteredForIoss(),
            'isMarketplaceRegisteredForVoes'               => $this->isMarketplaceRegisteredForVoes(),
            'hasCompanyPassedThresholdRaw'                 => $this->hasCompanyPassedThresholdRaw(),
            'hasCompanyPassedThreshold'                    => $this->hasCompanyPassedThreshold(),
            'isSaleTransactionAbove150Euro'                => $this->isSaleTransactionAbove150Euro(),
            'hasOnlyVatNumberAndWarehouseInCompanyCountry' => $this->hasOnlyVatNumberAndWarehouseInCompanyCountry(),
            'isSaleTransactionAbove135GBP'                 => $this->isSaleTransactionAbove135GBP(),
            'platformTypeId'                               => $this->getPlatformTypeId(),
            'isDomesticReverseChargeApplicable'            => $this->isDomesticReverseChargeApplicable(),
            'isShipToVatNumberValid'                       => $this->isShipToVatNumberValid(),
            'isCustomerTypeB2C'                            => $this->isCustomerTypeB2C(),
            'isCustomerTypeB2B'                            => $this->isCustomerTypeB2B(),
            'customerTypeId'                               => $this->getCustomerTypeId(),

            'billToCountryId'                      => $this->getBillToCountryId(),
            'shipToCountryId'                      => $this->getShipToCountryId(),
            'companyCountryId'                     => $this->getCompanyCountryId(),
            'shipFromCountryId'                    => $this->getShipFromCountryId(),
            'suppliedCountryId'                    => $this->getSuppliedFromCountryId(),
            'marketplaceOssCountryId'              => $this->getMarketplaceOssCountryId(),
            'marketplaceIossRegistrationCountryId' => $this->getMarketplaceIossRegistrationCountryId(),
            'marketplaceVoesRegistrationCountryId' => $this->getMarketplaceVoesRegistrationCountryId(),
            'companyOssRegistrationCountryId'      => $this->getCompanyOssRegistrationCountryId(),
            'companyIossRegistrationCountryId'     => $this->getCompanyIossRegistrationCountryId(),
            'companyVoesRegistrationCountryId'     => $this->getCompanyVoesRegistrationCountryId(),

            'billToCountry'                      => $this->getBillToCountry()?->toArray(),
            'shipToCountry'                      => $this->getShipToCountry()?->toArray(),
            'companyCountry'                     => $this->getCompanyCountry()?->toArray(),
            'shipFromCountry'                    => $this->getShipFromCountry()?->toArray(),
            'marketplaceOssCountry'              => $this->getMarketplaceOssCountry()?->toArray(),
            'marketplaceIossRegistrationCountry' => $this->getMarketplaceIossRegistrationCountry()?->toArray(),
            'companyOssRegistrationCountry'      => $this->getCompanyOssRegistrationCountry()?->toArray(),
            'companyIossRegistrationCountry'     => $this->getCompanyIossRegistrationCountry()?->toArray(),

            'isBillToCountryInEu'                                       => $this->isBillToCountryInEu(),
            'isCompanyCountryInEuRaw'                                   => $this->isCompanyCountryInEuRaw(),
            'isCompanyCountryNorthernIreland'                           => $this->isCompanyCountryNorthernIreland(),
            'isOriginalCompanyCountryNorthernIreland'                   => $this->isOriginalCompanyCountryNorthernIreland(),
            'isCompanyCountryUk'                                        => $this->isCompanyCountryUk(),
            'isOriginalCompanyCountryUk'                                => $this->isOriginalCompanyCountryUk(),
            'isCompanyCountryInEu'                                      => $this->isCompanyCountryInEu(),
            'isShipFromCountryInEu'                                     => $this->isShipFromCountryInEu(),
            'isCompanyCountryVatNumberPermanentEstablishment'           => $this->isCompanyCountryVatNumberPermanentEstablishment(),
            'isCompanyShipFromCountryVatNumberPermanentEstablishment'   => $this->isCompanyShipFromCountryVatNumberPermanentEstablishment(),
            'hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry' => $this->hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry(),

            'isBillToCountryUk'                        => $this->isBillToCountryUk(),
            'isOriginalBillToCountryUk'                => $this->isOriginalBillToCountryUk(),
            'isBillToCountryNorthernIreland'           => $this->isBillToCountryNorthernIreland(),
            'isOriginalBillToCountryNorthernIreland'   => $this->isOriginalBillToCountryNorthernIreland(),
            'isShipFromCountryUk'                      => $this->isShipFromCountryUk(),
            'isOriginalShipFromCountryUk'              => $this->isOriginalShipFromCountryUk(),
            'isShipFromCountryNorthernIreland'         => $this->isShipFromCountryNorthernIreland(),
            'isOriginalShipFromCountryNorthernIreland' => $this->isOriginalShipFromCountryNorthernIreland(),

            'isItemTypeGoods'        => $this->isItemTypeGoods(),
            'isItemTypeServices'     => $this->isItemTypeServices(),
            'isItemTypeDigitalGoods' => $this->isItemTypeDigitalGoods(),

            'deliveryConditionId'                                   => $this->getDeliveryConditionId(),
            'doesCompanyHaveVatNumberInShipToCountry'               => $this->getDoesCompanyHaveVatNumberInShipToCountry(),
            'isCompanyShipToCountryVatNumberPermanentEstablishment' => $this->getIsCompanyShipToCountryVatNumberPermanentEstablishment(),

            'detector' => $this->getDetector(),
        ];
    }
}
