<?php

namespace App\Core\Invoices\VatCalculation\Properties;

use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use App\Core\Invoices\Contracts\InvoiceGoodsPropertiesContract;

class DetectedInvoiceGoodsProperties implements DetectedPropertiesContract
{
    private int $taxCollectionResponsibilityId;
    private int $taxCalculationCountryId;
    private int $customerTypeId;
    private int $taxReportingSchemeId;
    private int $taxReportingCountryId;
    private bool $isTaxCharged;
    private int $invoiceSubtypeId;
    private int $suppliedFromCountryId;

    private ?DetectedInvoiceGoodsProperties $deemedDetected = null;
    private InvoiceGoodsPropertiesContract $invoiceGoodsProperties;

    public function __construct(InvoiceGoodsPropertiesContract $invoiceGoodsProperties)
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
        $this->suppliedFromCountryId = $invoiceGoodsProperties->getSuppliedFromCountryId();
    }

    public function getTaxCollectionResponsibilityId(): int
    {
        return $this->taxCollectionResponsibilityId;
    }

    public function getTaxCalculationCountryId(): int
    {
        return $this->taxCalculationCountryId;
    }

    public function getCustomerTypeId(): int
    {
        return $this->customerTypeId;
    }

    public function getTaxReportingSchemeId(): int
    {
        return $this->taxReportingSchemeId;
    }

    public function getTaxReportingCountryId(): int
    {
        return $this->taxReportingCountryId;
    }

    public function isTaxCharged(): bool
    {
        return $this->isTaxCharged;
    }

    public function getInvoiceSubtypeId(): int
    {
        return $this->invoiceSubtypeId;
    }

    public function setTaxCollectionResponsibilityId(int $taxCollectionResponsibilityId): self
    {
        $this->taxCollectionResponsibilityId = $taxCollectionResponsibilityId;

        return $this;
    }

    public function setTaxCalculationCountryId(int $taxCalculationCountryId): self
    {
        $this->taxCalculationCountryId = $taxCalculationCountryId;

        return $this;
    }

    public function setCustomerTypeId(int $customerTypeId): self
    {
        $this->customerTypeId = $customerTypeId;

        return $this;
    }

    public function setTaxReportingSchemeId(int $taxReportingSchemeId): self
    {
        $this->taxReportingSchemeId = $taxReportingSchemeId;

        return $this;
    }

    public function setTaxReportingCountryId(int $taxReportingCountryId): self
    {
        $this->taxReportingCountryId = $taxReportingCountryId;

        return $this;
    }

    public function setTaxCharged(bool $isTaxCharged): self
    {
        $this->isTaxCharged = $isTaxCharged;

        return $this;
    }

    public function getSuppliedFromCountryId(): int
    {
        return $this->suppliedFromCountryId;
    }

    public function setSuppliedFromCountryId(int $supplementCountryId): DetectedInvoiceGoodsProperties
    {
        $this->suppliedFromCountryId = $supplementCountryId;

        return $this;
    }

    public function getDeemedDetected(): ?DetectedInvoiceGoodsProperties
    {
        return $this->deemedDetected;
    }

    public function setDeemedDetected(?DetectedInvoiceGoodsProperties $deemedDetected): DetectedInvoiceGoodsProperties
    {
        $this->deemedDetected = $deemedDetected;

        return $this;
    }

    public function hasDeemedDetected(): bool
    {
        return $this->getDeemedDetected() !== null;
    }

    public function getInvoiceGoodsProperties(): ?InvoiceGoodsPropertiesContract
    {
        return $this->invoiceGoodsProperties;
    }

    public function setInvoiceSubtypeId(int $invoiceSubtypeId, int $invoiceTypeId = InvoiceType::SALES_INVOICE): self
    {
        $subtypesMap = [
            InvoiceType::PURCHASE_INVOICE  => [
                InvoiceSubtype::DOMESTIC_SALES_INVOICE                    => InvoiceSubtype::PURCHASE_INVOICE,
                InvoiceSubtype::EU_B2B_SALES_INVOICE                      => InvoiceSubtype::EU_B2B_PURCHASE_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE                => InvoiceSubtype::DOMESTIC_B2B_PURCHASE_INVOICE,
                InvoiceSubtype::EXPORT_INVOICE                            => InvoiceSubtype::EU_CUSTOMS_DECLARATION_IMPORT_VAT,
                InvoiceSubtype::EU_B2C_SALES_INVOICE                      => InvoiceSubtype::PURCHASE_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE => InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_PURCHASE_INVOICE,
                InvoiceSubtype::IOSS_EXPORT_INVOICE                       => InvoiceSubtype::PURCHASE_INVOICE,
                InvoiceSubtype::UK_VOEC_DEEMED_EXPORT_INVOICE             => InvoiceSubtype::PURCHASE_INVOICE,
            ],
            InvoiceType::PRO_FORMA_INVOICE => [
                InvoiceSubtype::DOMESTIC_SALES_INVOICE                    => InvoiceSubtype::DOMESTIC_PRO_FORMA_INVOICE,
                InvoiceSubtype::EU_B2B_SALES_INVOICE                      => InvoiceSubtype::EU_B2B_PRO_FORMA_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE                => InvoiceSubtype::DOMESTIC_B2B_PRO_FORMA_INVOICE,
                InvoiceSubtype::EXPORT_INVOICE                            => InvoiceSubtype::EXPORT_PRO_FORMA_INVOICE,
                InvoiceSubtype::EU_B2C_SALES_INVOICE                      => InvoiceSubtype::EU_B2C_PRO_FORMA_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE => InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_PRO_FORMA_INVOICE,
                InvoiceSubtype::IOSS_EXPORT_INVOICE                       => InvoiceSubtype::IOSS_EXPORT_PRO_FORMA_INVOICE,
                InvoiceSubtype::UK_VOEC_DEEMED_EXPORT_INVOICE             => InvoiceSubtype::UK_VOEC_EXPORT_PRO_FORMA_INVOICE,
            ],
            InvoiceType::CREDIT_NOTE       => [
                InvoiceSubtype::DOMESTIC_SALES_INVOICE                    => InvoiceSubtype::CN_DOMESTIC_SALES_INVOICE,
                InvoiceSubtype::EU_B2B_SALES_INVOICE                      => InvoiceSubtype::CN_EU_B2B_SALES_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE                => InvoiceSubtype::CN_DOMESTIC_B2B_SALES_INVOICE,
                InvoiceSubtype::EXPORT_INVOICE                            => InvoiceSubtype::CN_EXPORT_INVOICE,
                InvoiceSubtype::EU_B2C_SALES_INVOICE                      => InvoiceSubtype::CN_EU_B2C_SALES_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE => InvoiceSubtype::CN_DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE,
                InvoiceSubtype::IOSS_EXPORT_INVOICE                       => InvoiceSubtype::CN_IOSS_EXPORT_INVOICE,
                InvoiceSubtype::UK_VOEC_DEEMED_EXPORT_INVOICE             => InvoiceSubtype::CN_UK_VOEC_DEEMED_EXPORT_INVOICE,
                InvoiceSubtype::EU_DEEMED_B2B_INVOICE                     => InvoiceSubtype::CN_EU_DEEMED_B2B_INVOICE,
                InvoiceSubtype::UK_DEEMED_B2B_INVOICE                     => InvoiceSubtype::CN_UK_DEEMED_B2B_INVOICE,
                InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE             => InvoiceSubtype::DEEMED_CN_DOMESTIC_SALES_INVOICE,
                InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE               => InvoiceSubtype::DEEMED_CN_EU_B2C_SALES_INVOICE,
                InvoiceSubtype::DEEMED_EXPORT_INVOICE                     => InvoiceSubtype::DEEMED_CN_EXPORT_INVOICE,
                InvoiceSubtype::DEEMED_IOSS_EXPORT_INVOICE                => InvoiceSubtype::DEEMED_CN_IOSS_EXPORT_INVOICE,
            ],
            InvoiceType::DEBIT_NOTE        => [
                InvoiceSubtype::DOMESTIC_SALES_INVOICE                    => InvoiceSubtype::DN_PURCHASE_INVOICE,
                InvoiceSubtype::EU_B2B_SALES_INVOICE                      => InvoiceSubtype::DN_EU_B2B_PURCHASE_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_SALES_INVOICE                => InvoiceSubtype::DN_DOMESTIC_B2B_PURCHASE_INVOICE,
                InvoiceSubtype::EXPORT_INVOICE                            => InvoiceSubtype::DN_PURCHASE_INVOICE,
                InvoiceSubtype::EU_B2C_SALES_INVOICE                      => InvoiceSubtype::DN_PURCHASE_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE => InvoiceSubtype::DN_DOMESTIC_B2B_REVERSE_CHARGE_PURCHASE_INVOICE,
                InvoiceSubtype::IOSS_EXPORT_INVOICE                       => InvoiceSubtype::DN_PURCHASE_INVOICE,
                InvoiceSubtype::UK_VOEC_DEEMED_EXPORT_INVOICE             => InvoiceSubtype::DN_PURCHASE_INVOICE,
            ],
        ];

        $dontMapTypes = [
            InvoiceType::SALES_INVOICE,
            InvoiceType::DEEMED_SALES_INVOICE,
            InvoiceType::DEEMED_CREDIT_NOTE,
            InvoiceType::CUSTOMS_DECLARATION,
        ];
        if (in_array($invoiceTypeId, $dontMapTypes)) {
            $this->invoiceSubtypeId = $invoiceSubtypeId;
        } else {
            $this->invoiceSubtypeId = $subtypesMap[$invoiceTypeId][$invoiceSubtypeId];
        }

        return $this;
    }

    public function toArray(): array
    {
        return [
            'customerTypeId'                => $this->getCustomerTypeId(),
            'deemedDetected'                => $this->getDeemedDetected()?->toArray(),
            'hasDeemedDetected'             => $this->hasDeemedDetected(),
            'taxCollectionResponsibilityId' => $this->getTaxCollectionResponsibilityId(),
            'taxReportingSchemeId'          => $this->getTaxReportingSchemeId(),
            'invoiceSubtypeId'              => $this->getInvoiceSubtypeId(),
            'isTaxCharged'                  => $this->isTaxCharged(),
            'suppliedFromCountryId'         => $this->getSuppliedFromCountryId(),
            'taxCalculationCountryId'       => $this->getTaxCalculationCountryId(),
            'taxReportingCountryId'         => $this->getTaxReportingCountryId(),

            'invoiceGoodsProperties' => $this->getInvoiceGoodsProperties()?->toArray()
        ];
    }
}
