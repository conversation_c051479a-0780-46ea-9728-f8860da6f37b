<?php

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\PaymentTypeStatus;
use League\Fractal\TransformerAbstract;

class PaymentTypeStatusTransformer extends TransformerAbstract
{
    public function transform(PaymentTypeStatus $paymentTypeStatus): array
    {
        return [
            'id'    => $paymentTypeStatus->id,
            'value' => $paymentTypeStatus->id,
            'name'  => _l($paymentTypeStatus->name),
            'label' => _l($paymentTypeStatus->name),
        ];
    }
}
