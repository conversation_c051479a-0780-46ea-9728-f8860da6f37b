<?php

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\InvoiceItem;
use League\Fractal\Resource\Item;
use League\Fractal\TransformerAbstract;

class InvoiceItemTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'taxCode',
        'expenseType',
    ];

    public function transform(InvoiceItem $invoiceItem): array
    {
        return [
            'id'                                   => $invoiceItem->id,
            'invoiceId'                            => $invoiceItem->invoice_id,
            'itemId'                               => $invoiceItem->item_id,
            'item'                                 => null,
            'description'                          => $invoiceItem->item_description,
            'warehouseId'                          => $invoiceItem->warehouse_id,
            'invoiceItemTypeId'                    => $invoiceItem->invoice_item_type_id,
            'qty'                                  => $invoiceItem->qty ?? 1,
            'vatPercent'                           => $invoiceItem->vat_percent,
            'itemSalesPriceNet'                    => $invoiceItem->item_sales_price_net,
            'itemSalesPriceNetWithCurrency'        => $invoiceItem->item_sales_price_net_with_currency,
            'itemSalesPriceGross'                  => $invoiceItem->item_gross_price,
            'itemSalesPriceGrossWithCurrency'      => $invoiceItem->item_gross_price_with_currency,
            'itemSalesPriceGrossTotal'             => $invoiceItem->gross_total,
            'itemSalesPriceGrossTotalWithCurrency' => $invoiceItem->gross_total_with_currency,
            'itemSalesPriceVatAmount'              => $invoiceItem->item_sales_price_vat_amount,
            'itemSalesPriceVatAmountWithCurrency'  => $invoiceItem->vat_amount_with_currency,
            'discountNet'                          => $invoiceItem->discount_net,
            'discountVatAmount'                    => $invoiceItem->discount_vat_amount,
            'itemTaxCodeId'                        => $invoiceItem->item_tax_code_id,
            'isPriceGross'                         => $invoiceItem->is_price_inserted_as_gross ?? false,
            'discountPercentage'                   => $invoiceItem->discount_percentage,
            'discountGross'                        => $invoiceItem->discount_gross,
            'taxCode'                              => null,
            'expenseType'                          => null,
        ];
    }

    /** @noinspection PhpUnused */
    public function includeTaxCode(InvoiceItem $invoiceItem): ?Item
    {
        $taxCode = $invoiceItem->taxCode;
        if (is_null($taxCode)) {
            return null;
        }

        return $this->item($taxCode, new ItemTaxCodeTransformer());
    }

    public function includeExpenseType(InvoiceItem $invoiceItem): ?Item
    {
        $expenseType = $invoiceItem->expenseType;
        if (is_null($expenseType)) {
            return null;
        }

        return $this->item($expenseType, new ExpenseTypeTransformer());
    }
}
