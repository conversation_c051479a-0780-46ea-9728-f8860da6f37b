<?php

/** @noinspection PhpParameterNameChangedDuringInheritanceInspection */

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\IdentificationNumber;
use App\Core\Data\Models\Supplier;
use App\Core\VatNumbers\Api\VatNumberApi;
use App\Web\Common\Transformers\MultiselectItemTransformerAbstract;
use Carbon\Carbon;
use Illuminate\Support\Str;

class SupplierMultiselectTransformer extends MultiselectItemTransformerAbstract
{
    protected function getValue($supplier): string|int|float|bool|null
    {
        /**
         * @var Supplier $supplier
         */
        return $supplier->id . '_' . Str::uuid();
    }

    protected function getLabel($supplier): string
    {
        /**
         * @var Supplier $supplier
         */
        $isPerson = $supplier->is_person;
        $name = $supplier->company_name;
        if ($isPerson) {
            $name = [];
            if (!is_null($supplier->first_name)) {
                $name[] = $supplier->first_name;
            }
            if (!is_null($supplier->last_name)) {
                $name[] = $supplier->last_name;
            }
            $name = implode(' ', $name);
        }

        $name = $name ?? '';
        if (!is_null($supplier->customer_number)) {
            $name = $name . ' (' . $supplier->customer_number . ')';
        }

        $country = $this->getSupplierCountry($supplier);
        if (!is_null($country)) {
            $name = $name . ' - ' . $country->name;
        }

        return $name;
    }

    public function getDescription($supplier): ?string
    {
        /**
         * @var Supplier $supplier
         */
        $descriptions = [];
        foreach ($supplier->supplierAddress as $supplierAddress) {
            $descriptions['common.Address'][] = $supplierAddress->address->display_address;

        }

        foreach ($supplier->vatNumbers as $vatNumber) {
            $descriptions['common.VAT number'][] = $vatNumber->value;
        }

        foreach ($supplier->taxNumbers as $taxNumber) {
            $descriptions['common.TAX number'][] = $taxNumber->value;

        }

        $voesNumber = $supplier->voesNumber;
        if (!is_null($voesNumber)) {
            $descriptions['supplier.VOES number'][] = $voesNumber->value;
        }
        $descriptions = collect($descriptions)
            ->map(function (array $description, string $key) {
                $description = array_unique($description);

                return '<small class="text-secondary">' . mb_strtoupper(_l($key)) . ': <span class="fst-italic text-medium-grey">' . implode(', ', $description) . '</span></small>';
            });

        $description = implode('<strong class="text-primary"> | </strong >', $descriptions->toArray());

        return '<div class="search-attributes">' . $description . '</div>';
    }

    protected function getData($supplier): array
    {
        /**
         * @var Supplier $supplier
         */
        return [
            'id'                   => $supplier->id,
            'customerBillFromData' => [
                'address'    => null,
                'country'    => $this->resolveSupplierCountry($supplier),
                'name'       => null,
                'postalCode' => $this->resolveSupplierPostalCode($supplier),
                'taxNumber'  => $this->resolveSupplierTaxNumbers($supplier),
                'vatNumber'  => $this->resolveSupplierVatNumbers($supplier),
                'voesNumber' => $supplier->voesNumber?->value
            ],
        ];
    }

    private function resolveSupplierVatNumbers(Supplier $supplier): array
    {
        $vatNumber = $supplier->vatNumbers
            ->sortByDesc(function (IdentificationNumber $vatNumber) {
                $startDate = Carbon::parse($vatNumber->start_date);

                return $startDate->timestamp;
            })->first();

        /** @noinspection PhpUnhandledExceptionInspection */
        return VatNumberApi::validateVatNumber(
            vatNumberNumber: ($vatNumber?->value ?? null),
            checkApi: false
        )->toArray();
    }

    private function resolveSupplierTaxNumbers(Supplier $supplier): ?string
    {
        $taxNumber = $supplier->taxNumbers
            ->sortByDesc(function (IdentificationNumber $taxNumber) {
                $startDate = Carbon::parse($taxNumber->start_date);

                return $startDate->timestamp;
            })->first();

        return $taxNumber?->value ?? null;
    }

    private function resolveSupplierPostalCode(Supplier $supplier): ?string
    {
        $address = null;
        foreach ($supplier->supplierAddress as $supplierAddress) {
            $address = $supplierAddress?->address ?? null;
            if (!is_null($address)) {
                break;
            }
        }
        if (is_null($address)) {
            return null;
        }

        return $address->postal_code;
    }

    private function resolveSupplierCountry(Supplier $supplier): array
    {
        $country = $this->getSupplierCountry($supplier);

        return [
            'id'    => $country?->id ?? null,
            'value' => $country?->code_with_name ?? null,
            'name'  => $country?->name ?? null,
            'label' => $country?->code_with_name ?? null,
        ];
    }

    private function getSupplierCountry(Supplier $supplier): ?Country
    {
        $country = null;
        foreach ($supplier->supplierAddress as $supplierAddress) {
            $country = $supplierAddress?->address?->country ?? null;
            if (!is_null($country)) {
                break;
            }
        }

        if (is_null($country)) {
            foreach ($supplier->vatNumbers as $vatNumber) {
                $country = $vatNumber?->country ?? null;
                if (!is_null($country)) {
                    break;
                }
            }
        }

        if (is_null($country)) {
            foreach ($supplier->taxNumbers as $taxNumber) {
                $country = $taxNumber?->country ?? null;
                if (!is_null($country)) {
                    break;
                }
            }
        }

        return $country;
    }
}
