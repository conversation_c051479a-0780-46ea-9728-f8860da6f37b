<?php

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\PaymentType;
use League\Fractal\Resource\Item;
use League\Fractal\TransformerAbstract;

class PaymentTypeTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'paymentTypeStatus'
    ];

    public function transform(PaymentType $paymentType): array
    {
        return [
            'id'    => $paymentType->id,
            'value' => $paymentType->id,
            'name'  => $paymentType->full_name,
            'label' => $paymentType->full_name,
        ];
    }

    /** @noinspection PhpUnused */
    public function includePaymentTypeStatus(PaymentType $paymentType): Item
    {
        return $this->item($paymentType->paymentTypeStatus, new PaymentTypeStatusTransformer());
    }
}
