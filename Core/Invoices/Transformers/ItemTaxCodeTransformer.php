<?php

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\ItemTaxCode;
use League\Fractal\TransformerAbstract;

class ItemTaxCodeTransformer extends TransformerAbstract
{
    public function transform(ItemTaxCode $itemTaxCode): array
    {
        return [
            'id'         => $itemTaxCode->id,
            'name'       => $itemTaxCode->full_name,
            'value'      => $itemTaxCode->id,
            'label'      => $itemTaxCode->full_name,
            'itemTypeId' => $itemTaxCode->item_type_id,
        ];
    }
}
