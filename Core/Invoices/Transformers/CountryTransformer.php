<?php

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\Country;
use League\Fractal\TransformerAbstract;

class CountryTransformer extends TransformerAbstract
{
    public function transform(Country $country)
    {
        return [
            'id'      => $country->id,
            'name'    => $country->name . '[' . $country->code . ']',
            'value'   => $country->id,
            'label'   => $country->name . '[' . $country->code . ']',
            'vatCode' => $country->vat_code,
            'code'    => $country->vat_code,
        ];
    }
}