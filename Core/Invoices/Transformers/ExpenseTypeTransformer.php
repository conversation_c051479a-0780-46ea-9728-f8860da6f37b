<?php

namespace App\Core\Invoices\Transformers;

use App\Core\Data\Models\ExpenseType;
use League\Fractal\Resource\Collection;
use League\Fractal\TransformerAbstract;
class ExpenseTypeTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'children'
    ];

    public function transform(ExpenseType $expenseType): array
    {
        return [
            'id'          => $expenseType->id,
            'value'       => $expenseType->id,
            'name'        => _l($expenseType->name),
            'label'       => _l($expenseType->name),
            'description' => _l($expenseType->description),
            'children'    => []
        ];
    }

    /** @noinspection PhpUnused */
    public function includeChildren(ExpenseType $expenseType): Collection
    {
        return $this->collection($expenseType->childrenExpenseTypes, new ExpenseTypeTransformer());
    }
}
