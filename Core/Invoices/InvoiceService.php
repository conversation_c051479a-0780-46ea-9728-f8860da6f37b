<?php

namespace App\Core\Invoices;

use App\Core\AmazonCsv\Handlers\Helpers\Calculator;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\Currency;
use App\Core\Data\Models\EslReport;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceItem;
use App\Core\Data\Models\InvoiceStatus;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\IossNumber;
use App\Core\Data\Models\ItemTaxCode;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\OssIossReport;
use App\Core\Data\Models\OssNumber;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Data\Models\VatReport;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatRateTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatReportRepositoryContract;
use App\Core\ExchangeRates\Contracts\ExchangeRatesServiceContract;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use App\Core\Invoices\Contracts\InvoiceServiceContract;
use App\Core\Invoices\DataTransfer\InvoicesApi\ApiCalculationData;
use App\Core\Invoices\DataTransfer\InvoicesApi\CalculateInvoiceApiRequest;
use App\Core\Invoices\Invoice\DataTransfer\RecalculatedDataTransfer;
use App\Core\Invoices\Invoice\InvoiceData;
use App\Core\Invoices\Invoice\VatDetection;
use App\Core\Mappers\MappersData\InvoicesForEslReport\EslReportInvoicesData;
use App\Core\Mappers\MappersData\InvoicesForOssIossReport\OssIossReportInvoicesData;
use App\Core\Mappers\MappersData\InvoicesForVatReport\InvoiceSubtypeData;
use App\Core\Mappers\MappersData\InvoicesForVatReport\VatReportInvoicesData;
use App\Core\System\Helpers\Arr;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class InvoiceService implements InvoiceServiceContract
{
    private InvoiceRepositoryContract $invoiceRepo;
    private CountryRepositoryContract $countryRepo;
    private ExchangeRatesServiceContract $exchangeRateService;
    private ItemRepositoryContract $itemRepo;
    private VatRateTypeRepositoryContract $vatRateTypeRepo;
    /**
     * @var \App\Core\Data\Repositories\Contracts\VatReportRepositoryContract
     */
    private VatReportRepositoryContract $vatReportRepo;

    public function __construct(
        InvoiceRepositoryContract $invoiceRepo,
        CountryRepositoryContract $countryRepo,
        ExchangeRatesServiceContract $exchangeRateService,
        ItemRepositoryContract $itemRepo,
        VatRateTypeRepositoryContract $vatRateTypeRepo,
        VatReportRepositoryContract $vatReportRepo
    ) {
        $this->invoiceRepo = $invoiceRepo;
        $this->countryRepo = $countryRepo;
        $this->exchangeRateService = $exchangeRateService;
        $this->itemRepo = $itemRepo;
        $this->vatRateTypeRepo = $vatRateTypeRepo;
        $this->vatReportRepo = $vatReportRepo;
    }

    public function storeInvoiceWithItems(RecalculatedDataTransfer $calculationData, ?int $forceInvoiceStatusId = null): void
    {
        $invoiceData = $calculationData->getInvoiceData();
        $detect = $calculationData->getDetected();
        $invoice = $invoiceData->getInvoice();

        $invoice->connectedDeemedSuppliesStatusInvoice?->delete();
        $invoice = $this->storeInvoiceWithItemsAction($invoice, $forceInvoiceStatusId);
        if ($detect->hasDeemedDetected()) {
            $deemedInvoice = $this->resolveDeemedInvoice($invoice, $detect->getDeemedDetected());
            $deemedInvoice = $this->storeInvoiceWithItemsAction($deemedInvoice);
            $this->resolveDeemedInvoiceItems($deemedInvoice, $detect->getDeemedDetected());
        }
    }

    private function storeInvoiceWithItemsAction(Invoice $invoice, ?int $forceInvoiceStatusId = null): Invoice
    {
        $invoiceItems = $invoice->invoiceItems;

        if (!is_null($forceInvoiceStatusId)) {
            $invoice->invoice_status_id = $forceInvoiceStatusId;
        }

        $invoice = $this->invoiceRepo->storeInvoice($invoice);
        $invoice->invoiceItems()->delete();
        $this->invoiceRepo->storeInvoiceItems($invoice, $invoiceItems);
        $invoice->load('invoiceItems');

        return $invoice;
    }

    private function resolveDeemedInvoice(Invoice $invoice, DetectedPropertiesContract $deemed): Invoice
    {
        $originalInvoiceId = $invoice->id;
        $invoice = $invoice->replicate();

        $invoice->invoice_number = 'DS-' . $invoice->invoice_number;
        $invoice->invoice_subtype_id = $deemed->getInvoiceSubtypeId();
        $invoice->customer_type_id = $deemed->getCustomerTypeId();
        $invoice->tax_collection_responsibility_id = $deemed->getTaxCollectionResponsibilityId();
        $invoice->tax_reporting_scheme_id = $deemed->getTaxReportingSchemeId();
        $invoice->tax_reporting_country_id = $deemed->getTaxReportingCountryId();
        $invoice->tax_calculation_country_id = $deemed->getTaxCalculationCountryId();
        $invoice->connected_invoice_id = $originalInvoiceId;
        $invoice->connected_invoice_type = 'DEEMED';
        $invoice->invoice_status_id = InvoiceStatus::DEEMED_SUPPLIES;

        return $invoice;
    }

    private function resolveDeemedInvoiceItems(Invoice $invoice, DetectedPropertiesContract $detected): void
    {
        $invoiceItems = $invoice->invoiceItems;
        $vatPercent = 0.0;
        foreach ($invoiceItems as $invoiceItem) {
            if ($detected->isTaxCharged()) {
                $vatPercent = $this->getVatPercentageForCountryOnDate(
                    countryId: $detected->getTaxCalculationCountryId(),
                    invoiceDate: $invoice->invoice_date,
                    itemTaxCodeId: $invoiceItem->item_tax_code_id
                );
            }

            $netPrice = $invoiceItem->item_sales_price_net;
            $grossPrice = Calculator::netToGross($netPrice, $vatPercent);

            $vatAmount = $grossPrice - $netPrice;

            $invoiceItem->vat_percent = $vatPercent;
            $invoiceItem->item_sales_price_net = $netPrice;
            $invoiceItem->item_sales_price_vat_amount = $vatAmount;

            $discountData = $this->resolveDiscountData(
                isGross: $invoiceItem->is_price_inserted_as_gross,
                qty: $invoiceItem->qty,
                vatPercent: $vatPercent,
                netPrice: $netPrice,
                grossPrice: $grossPrice,
                discountPercentage: $invoiceItem->discount_percentage,
                discountNet: $invoiceItem->discount_net,
                discountGross: $invoiceItem->discount_gross
            );

            $invoiceItem->discount_percentage = $discountData->getDiscountPercentage();
            $invoiceItem->discount_net = $discountData->getDiscountNet();
            $invoiceItem->discount_vat_amount = $discountData->getDiscountVatAmount();

            $invoiceItem->save();
        }
    }

    public function recalculateSalesInvoiceForVueComponent(Arr $invoiceData): RecalculatedDataTransfer
    {
        $invoice = $this->buildSalesInvoiceFromInvoiceData($invoiceData);
        /**
         * @var Arr $main
         */
        $main = $invoiceData->mainData;

        $invoice->load(
            'company.thresholds',
            'company.euThresholds',
            'company.address',
            'company.vatNumbers',
            'company.companyWarehouses.warehouse',
            'company.waivers',
            'marketplace.platform',
        );

        /*
         *  First pass
         *  We need two passes in the invoice calculation because of the $isSaleAbove150Euro,
         *  and $isSaleAbove135GBP, in the first pass we don't have that info yet.
         */
        $detected = $this->getDetectedInvoiceGoodsProperties(
            invoice: $invoice,
            invoiceData: $invoiceData
        );

        $invoice = $this->resolveInvoiceItems(
            invoice: $invoice,
            invoiceItems: $main->invoiceItems,
            taxCalculationCountryId: $detected->getTaxCalculationCountryId(),
            isTaxCharged: $detected->isTaxCharged()
        );
        $invoiceData = arr($this->getInvoiceData($invoice)->toArray());

        // Second pass
        $detected = $this->getDetectedInvoiceGoodsProperties(
            invoice: $invoice,
            invoiceData: $invoiceData
        );
        $invoice = $this->resolveInvoiceItems(
            invoice: $invoice,
            invoiceItems: $main->invoiceItems,
            taxCalculationCountryId: $detected->getTaxCalculationCountryId(),
            isTaxCharged: $detected->isTaxCharged()
        );

        $shippingGross = $main->get('shipping.gross');
        $wrapGross = $main->get('wrap.gross');
        $invoice = $this->resolveInvoiceShippingAndWrap(
            invoice: $invoice,
            invoiceData: $invoiceData,
            detectedGoods: $detected,
            shippingGross: $shippingGross,
            wrapGross: $wrapGross
        );

        return new RecalculatedDataTransfer($this->getInvoiceData($invoice), $detected);
    }

    private function buildSalesInvoiceFromInvoiceData(Arr $invoiceData): Invoice
    {
        $headerData = $invoiceData->get('headerData');
        $mainData = $invoiceData->get('mainData');
        $footerData = $invoiceData->get('footerData');

        $comment = $mainData->get('comment');
        $companyId = $footerData->get('companyId');

        $billToAddress = $shipToAddress = $mainData->get('billTo.address');
        $billToCountryId = $shipToCountryId = $mainData->get('billTo.country.id');
        $billToName = $shipToName = $mainData->get('billTo.name');
        $billToPostalCode = $shipToPostalCode = $mainData->get('billTo.postalCode');
        $billToVatNumber = $shipToVatNumber = $mainData->get('billTo.vatNumber.vatNumber');

        $shippingIsSameAsBillTo = (bool)$mainData->get('shippingIsSameAsBillTo', true);
        if (!$shippingIsSameAsBillTo) {
            $shipToAddress = $mainData->get('shipTo.address');
            $shipToCountryId = $mainData->get('shipTo.country.id');
            $shipToPostalCode = $mainData->get('shipTo.postalCode');
            $shipToName = $mainData->get('shipTo.name');
            $shipToVatNumber = $mainData->get('shipTo.vatNumber.vatNumber');
        }

        $invoiceDate = $headerData->get('invoiceDateRaw');
        $invoiceNumber = $headerData->get('invoiceNumber');
        $invoiceSubtypeId = $headerData->get('invoiceSubtype.id');
        $marketplaceId = $headerData->get('invoiceMarketplace.id');
        $orderDate = $headerData->get('invoiceOrderDateRaw');
        $orderNumber = $headerData->get('invoiceOrderNumber');
        $salesCurrencyId = $headerData->get('invoiceCurrency.id');
        $taxCalculationDate = $headerData->get('invoiceDateRaw');
        $itemTypeId = $headerData->get('itemType.id');

        $shipFromCountryId = $mainData->get('shipFrom.country.id');
        $shipFromVatNumber = $mainData->get('shipFrom.vatNumber.vatNumber');

        $customerTypeId = null;
        $taxCollectionResponsibilityId = null;
        $taxReportingCountryId = null;
        $taxReportingSchemeId = null;
        $taxCalculationCountryId = null;

        $invoiceId = $invoiceData->get('invoice.id');
        $invoice = null;
        if (!is_null($invoiceId)) {
            $invoice = $this->invoiceRepo->getInvoiceById($invoiceId);
        }

        if (is_null($invoice)) {
            $invoice = $this->invoiceRepo->getEmptyInvoiceModel();
        }

        $createUserId = $invoice->create_user_id ?? user()->getId();
        $createdAt = $invoice->created_at ?? now()->toDateTimeString();

        $invoice->comment = $comment;
        $invoice->description = null;
        $invoice->company_id = $companyId;
        $invoice->create_user_id = $createUserId;
        $invoice->created_at = $createdAt;

        $invoice->ship_from_country_id = $shipFromCountryId;
        $invoice->ship_from_vat_number = $shipFromVatNumber;

        $invoice->bill_to_address = $billToAddress;
        $invoice->bill_to_country_id = $billToCountryId;
        $invoice->bill_to_name = $billToName;
        $invoice->bill_to_postal_code = $billToPostalCode;
        $invoice->bill_to_vat_number = $billToVatNumber;

        $invoice->ship_to_address = $shipToAddress;
        $invoice->ship_to_country_id = $shipToCountryId;
        $invoice->ship_to_name = $shipToName;
        $invoice->ship_to_postal_code = $shipToPostalCode;
        $invoice->ship_to_vat_number = $shipToVatNumber;

        $invoice->customer_type_id = $customerTypeId;

        $invoice->invoice_date = $invoiceDate;
        $invoice->invoice_number = $invoiceNumber;
        $invoice->invoice_subtype_id = $invoiceSubtypeId;
        $invoice->item_type_id = $itemTypeId;
        $invoice->marketplace_id = $marketplaceId;
        $invoice->order_date = $orderDate;
        $invoice->order_number = $orderNumber;

        $invoice->sales_currency_id = $salesCurrencyId;
        $invoice->tax_calculation_date = $taxCalculationDate;
        $invoice->tax_collection_responsibility_id = $taxCollectionResponsibilityId;
        $invoice->tax_reporting_country_id = $taxReportingCountryId;
        $invoice->tax_reporting_scheme_id = $taxReportingSchemeId;
        $invoice->tax_calculation_country_id = $taxCalculationCountryId;
        $invoice->shipping_is_same_as_bill_to = $shippingIsSameAsBillTo;

        $invoice->load(
            'company.address',
            'marketplace.platform',
            'invoiceSubtype',
            'shipToCountry',
            'shipFromCountry',
            'company',
            'company',
            'company',
            'company',
        );


        $deemedStatusTypeIds = [
            InvoiceSubtype::UK_VOEC_DEEMED_EXPORT_INVOICE,
            InvoiceSubtype::CN_UK_VOEC_DEEMED_EXPORT_INVOICE,
            InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
            InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
            InvoiceSubtype::DEEMED_EXPORT_INVOICE,
            InvoiceSubtype::DEEMED_IOSS_EXPORT_INVOICE,
            InvoiceSubtype::DEEMED_CN_DOMESTIC_SALES_INVOICE,
            InvoiceSubtype::DEEMED_CN_EU_B2C_SALES_INVOICE,
            InvoiceSubtype::DEEMED_CN_EXPORT_INVOICE,
            InvoiceSubtype::DEEMED_CN_IOSS_EXPORT_INVOICE,
        ];
        if (in_array($invoice->invoice_subtype_id, $deemedStatusTypeIds)) {
            $invoice->invoice_status_id = InvoiceStatus::DEEMED_SUPPLIES;
        }

        return $invoice;
    }

    private function getDetectedInvoiceGoodsProperties(Invoice $invoice, Arr $invoiceData): DetectedPropertiesContract
    {
        $company = $invoice->company;
        $invoiceDate = $invoice->invoice_date;

        $companyCountryId = $company->address->country_id;
        $shipFromCountry = $invoice->shipFromCountry ?? $invoice->company->address->country;
        $shipFromCountryId = $shipFromCountry->id;
        $billToCountryId = $invoice->bill_to_country_id;
        $invoiceTypeId = $invoice->invoiceSubtype->invoice_type_id;
        $deliveryConditionId = $invoice->delivery_condition_id;

        $isMarketplaceIncorporatedInEu = (bool)$invoice->marketplace?->is_marketplace_incorporated_in_eu;

        $marketplaceOssRegistrationCountryId = $invoice->marketplace?->oss_country_id;
        $isMarketplaceRegisteredForOss = !is_null($marketplaceOssRegistrationCountryId);
        $marketplaceIossRegistrationCountryId = $invoice->marketplace?->ioss_country_id;
        $isMarketplaceRegisteredForIoss = !is_null($marketplaceIossRegistrationCountryId);
        $marketplaceVoesRegistrationCountryId = $invoice->marketplace?->voes_country_id;
        $isMarketplaceRegisteredForVoes = !is_null($marketplaceVoesRegistrationCountryId);

        $mainData = $invoiceData->mainData;
        $shippingIsSameAsBillTo = (bool)$mainData->get('shippingIsSameAsBillTo', true);
        $shipToCountryId = $invoice->ship_to_country_id;
        $vatNumberKey = 'billTo.vatNumber';
        if (!$shippingIsSameAsBillTo && !is_null($shipToCountryId)) {
            $vatNumberKey = 'shipTo.vatNumber';
            // Sketchy
            $billToCountryId = $shipToCountryId;
        }

        $buyerVatNumber = $mainData->get($vatNumberKey, arr());
        $buyerVatNumberValidated = $buyerVatNumber->get('validated', false);
        $buyerVatNumberValid = $buyerVatNumber->get('valid', false);
        $isShipToVatNumberValid = $buyerVatNumberValidated && $buyerVatNumberValid;

        $shipToCountry = $invoice->shipToCountry;

        $isDomesticReverseChargeApplicableInBillToCountry = $shipToCountry->isDomesticReverseChargeApplicableFromOnDate($invoiceDate);

        $isOnlyVatNumberAndWarehouseInCompanyCountry = $company->isOnlyVatNumberAndWarehouseInCompanyCountry($invoiceDate);
        $whetherTheCompanyHasPassedThreshold = $company->whetherTheCompanyHasPassedThresholdInCountryOnDate(
            $invoiceDate,
            $shipToCountry
        );

        $isSaleAboveThreshold = $this->isSaleAmountAboveThresholds(
            invoiceDate: $invoiceData->headerData->invoiceDateRaw,
            invoiceCurrencyId: $invoiceData->headerData->get('invoiceCurrency.id'),
            invoiceTotal: $invoiceData->mainData->get('calculation.intrinsic.value')
        );
        $isSaleAbove150Euro = $isSaleAboveThreshold->hasPassedEurThreshold();
        $isSaleAbove135GBP = $isSaleAboveThreshold->hasPassedGbpThreshold();


        $itemType = $invoiceData->headerData->itemType->id ?? ItemType::GOODS;

        $detectedGoods = new VatDetection();

        $ossNumber = $company->getActiveOssNumberOnDate($invoiceDate);
        $isCompanyRegisteredForOss = !is_null($ossNumber);
        $voesNumber = $company->getActiveVoesNumberOnDate($invoiceDate);
        $isCompanyRegisteredForVoes = !is_null($voesNumber);
        $companyVoesRegistrationCountryId = $voesNumber?->issue_country_id;
        $companyOssRegistrationCountryId = $ossNumber?->issue_country_id;
        $iossNumber = $company->getActiveIossNumberOnDate($invoiceDate);
        $isCompanyRegisteredForIoss = !is_null($iossNumber);
        $companyIossRegistrationCountryId = $iossNumber?->issue_country_id;
        $isCompanyCountryVatNumberPermanentEstablishment = $company->isCompanyCountryVatNumberPermanentEstablishmentForDate($invoiceDate);
        $isCompanyShipFromCountryVatNumberPermanentEstablishment = $company->isVatNumberInCountryOnDatePermanentEstablishment($invoiceDate, $shipFromCountryId);
        $isCompanyShipToCountryVatNumberPermanentEstablishment = $company->isVatNumberInCountryOnDatePermanentEstablishment($invoiceDate, $shipToCountryId);
        $hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry = $company->hasOnlyVatNumberAndItIsInCompanyCountry($invoiceDate);
        $doesCompanyHaveVatNumberInShipToCountry = $company->hasVatNumberInCountryForDate($shipToCountryId, $invoiceDate);

        $detectedGoods = $detectedGoods->detectAfterFirstOfJuly21(
            calculationDate: $invoiceDate,
            companyCountryId: $companyCountryId,
            isOnlyVatNumberAndWarehouseInCompanyCountry: $isOnlyVatNumberAndWarehouseInCompanyCountry,
            isCompanyCountryVatNumberPermanentEstablishment: $isCompanyCountryVatNumberPermanentEstablishment,
            whetherTheCompanyHasPassedThreshold: $whetherTheCompanyHasPassedThreshold,

            shipFromCountryId: $shipFromCountryId,

            billToCountryId: $billToCountryId,
            isDomesticReverseChargeApplicableInBillToCountry: $isDomesticReverseChargeApplicableInBillToCountry,

            platformTypeId: $invoice->marketplace->platform->platform_type_id,
            invoiceTypeId: $invoiceTypeId,
            itemTypeId: $itemType,

            isCompanyRegisteredForOss: $isCompanyRegisteredForOss,
            companyOssRegistrationCountryId: $companyOssRegistrationCountryId,
            isCompanyRegisteredForIoss: $isCompanyRegisteredForIoss,
            companyIossRegistrationCountryId: $companyIossRegistrationCountryId,

            isMarketplaceRegisteredForOss: $isMarketplaceRegisteredForOss,
            marketplaceOssRegistrationCountryId: $marketplaceOssRegistrationCountryId,
            isMarketplaceRegisteredForIoss: $isMarketplaceRegisteredForIoss,
            marketplaceIossRegistrationCountryId: $marketplaceIossRegistrationCountryId,
            isMarketplaceIncorporatedInEu: $isMarketplaceIncorporatedInEu,

            isSaleAbove150Euro: $isSaleAbove150Euro,
            isSaleAbove135GBP: $isSaleAbove135GBP,

            shipToCountryId: $shipToCountryId,
            isShipToVatNumberValid: $isShipToVatNumberValid,

            isCompanyRegisteredForVoes: $isCompanyRegisteredForVoes,
            companyVoesRegistrationCountryId: $companyVoesRegistrationCountryId,
            isMarketplaceRegisteredForVoes: $isMarketplaceRegisteredForVoes,
            marketplaceVoesRegistrationCountryId: $marketplaceVoesRegistrationCountryId,
            isCompanyShipFromCountryVatNumberPermanentEstablishment: $isCompanyShipFromCountryVatNumberPermanentEstablishment,
            hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry: $hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry,

            isCompanyShipToCountryVatNumberPermanentEstablishment: $isCompanyShipToCountryVatNumberPermanentEstablishment,
            doesCompanyHaveVatNumberInShipToCountry: $doesCompanyHaveVatNumberInShipToCountry,
            deliveryConditionId: $deliveryConditionId
        );

        $this->setDetectedProperties($invoice, $detectedGoods);

        return $detectedGoods;
    }

    public function getInvoiceData(Invoice $invoice, bool $isTitleAndLogoLineVisible = false): InvoiceData
    {
        $invoice->loadMissing(
            [
                'company.iossNumbers',
                'company.ossNumbers',
                'company.address.city',
                'company.address.country',
                'company.vatNumbers',
                'company.bank',
                'marketplace',
                'currency',
                'exchangeRates.currency',
                'customer.company',
                'customer.customerAddresses.address',
                'customer.customerAddresses.addressType',
                'customer.contacts',
                'customer.vatNumbers',
                'shipToCountry',
                'customerType',
                'deliveryCondition',
                'eventType',
                'invoiceItems' => function (HasMany $query) {
                    $query->orderBy('vat_percent', 'DESC');
                },
                'invoiceItems.item',
                'invoiceItems.taxCode',
                'invoiceItems.invoiceItemType',
                'invoiceItems.invoice.currency',
                'invoiceSubtype.invoiceType',
                'status',
                'taxCollectionResponsibility',
                'taxReportingCountry',
                'taxReportingScheme',
                'taxCalculationCountry',
                'shipFromCountry',
                'paymentType.paymentTypeStatus',
                'connectedDeemedSuppliesStatusInvoice',
                'connectedDeemedIssuedStatusInvoice',
                'itemType'
            ]
        );

        $invoiceData = new InvoiceData($invoice);
        $invoiceData->setTitleAndLogoLineVisible($isTitleAndLogoLineVisible);

        return $invoiceData;
    }

    private function getVatPercentageForCountryOnDate(int $countryId, string $invoiceDate, int $itemTaxCodeId): float
    {
        $percentage = 0.0;
        $itemTaxCode = $this->itemRepo
            ->getItemTaxCodesWithTaxRateForCountryOnDate($countryId, $invoiceDate)
            ->keyBy('id')
            ->get($itemTaxCodeId);

        if (!is_null($itemTaxCode)) {
            $percentage = (float)$itemTaxCode->rate;
        }

        return $percentage;
    }

    private function resolveInvoiceItems(
        Invoice $invoice,
        Arr $invoiceItems,
        int $taxCalculationCountryId,
        bool $isTaxCharged
    ): Invoice {
        $invoice->loadMissing('currency');
        $items = collect();
        $invoiceDate = $invoice->invoice_date;

        if (!$isTaxCharged) {
            $vatPercent = 0.0;
        }

        foreach ($invoiceItems as $invoiceItem) {
            /**
             * @var Arr $invoiceItem
             */
            $itemTaxCodeId = $invoiceItem->get('taxCode.id');
            if ($isTaxCharged) {
                $vatPercent = $this->getVatPercentageForCountryOnDate(
                    countryId: $taxCalculationCountryId,
                    invoiceDate: $invoiceDate,
                    itemTaxCodeId: $itemTaxCodeId
                );
            }

            $isGross = $invoiceItem->isPriceGross;

            $netPrice = $invoiceItem->itemSalesPriceNet;
            $grossPrice = $invoiceItem->itemSalesPriceGross;

            $hideGrossTypes = [
                InvoiceSubtype::EU_B2B_SALES_INVOICE,
                InvoiceSubtype::EXPORT_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE,
                InvoiceSubtype::EU_B2B_PRO_FORMA_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_PRO_FORMA_INVOICE,
                InvoiceSubtype::EXPORT_PRO_FORMA_INVOICE,
                InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_PRO_FORMA_INVOICE,
                InvoiceSubtype::CN_EU_B2B_SALES_INVOICE,
                InvoiceSubtype::CN_EXPORT_INVOICE,
                InvoiceSubtype::CN_DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE,
                InvoiceSubtype::EU_DEEMED_B2B_INVOICE,
                InvoiceSubtype::CN_EU_DEEMED_B2B_INVOICE,
                InvoiceSubtype::UK_DEEMED_B2B_INVOICE,
                InvoiceSubtype::CN_UK_DEEMED_B2B_INVOICE,
            ];
            if (in_array($invoice->invoice_subtype_id, $hideGrossTypes)) {
                $isGross = false;
            }

            $qty = $invoiceItem->qty;

            if ($isGross) {
                $netPrice = Calculator::grossToNet($grossPrice, $vatPercent);
            } else {
                $grossPrice = Calculator::netToGross($netPrice, $vatPercent);
            }
            $vatAmount = $grossPrice - $netPrice;

            $discountData = $this->resolveDiscountData(
                isGross: $isGross,
                qty: $qty,
                vatPercent: $vatPercent,
                netPrice: $netPrice,
                grossPrice: $grossPrice,
                discountPercentage: $invoiceItem->discountPercentage,
                discountNet: $invoiceItem->discountNet,
                discountGross: $invoiceItem->discountGross
            );
            $discountPercentage = $discountData->getDiscountPercentage();
            $discountNet = $discountData->getDiscountNet();
            $discountVatAmount = $discountData->getDiscountVatAmount();

            $invoiceItemId = $invoiceItem->id;

            $invoiceItemData = [
                'id'                          => $invoiceItemId,
                'item_id'                     => $invoiceItem->itemId,
                'invoice_id'                  => $invoice->id,
                'item_description'            => $invoiceItem->description,
                'qty'                         => $qty,
                'invoice_item_type_id'        => $invoiceItem->invoiceItemTypeId,
                'item_tax_code_id'            => $itemTaxCodeId,
                'item_sales_price_vat_amount' => $vatAmount,
                'vat_percent'                 => $vatPercent,
                'item_sales_price_net'        => $netPrice,
                'discount_net'                => $discountNet,
                'discount_vat_amount'         => $discountVatAmount,
                'discount_percentage'         => $discountPercentage,
                'is_price_inserted_as_gross'  => $isGross,
                'item_gross_price'            => $grossPrice
            ];

            $invoiceItemModel = null;
            if (!is_null($invoiceItemId)) {
                $invoiceItemModel = $invoice->invoiceItems->firstWhere('id', $invoiceItemId);
            }
            if (is_null($invoiceItemModel)) {
                $invoiceItemModel = $this->invoiceRepo->getEmptyInvoiceItemModel();
            }

            $invoiceItemModel->fill($invoiceItemData);
            if (is_null($invoice->id)) {
                $clonedInvoice = $invoice->replicate()->unsetRelation('invoiceItems');
                $invoiceItemModel->setRelation('invoice', $clonedInvoice);
            }

            $items->push($invoiceItemModel);
        }

        $invoice->setRelation('invoiceItems', $items);

        return $invoice;
    }

    private function isSaleAmountAboveThresholds(
        ?string $invoiceDate = null,
        ?int $invoiceCurrencyId = null,
        ?float $invoiceTotal = null
    ): object {
        $eur = null;
        $gbp = null;
        if (!is_null($invoiceTotal) && !is_null($invoiceCurrencyId) && !is_null($invoiceDate)) {
            $exchangeRates = $this->exchangeRateService->getExchangeRatesForDates([$invoiceDate]);
            /** @noinspection PhpUndefinedMethodInspection */
            $exchangeRate = $exchangeRates->first()->where('currency_id', $invoiceCurrencyId)->first();

            if (is_null($eur)) {
                $eur = $this->exchangeRateService->convertToEuro($invoiceTotal, $exchangeRate);
            }

            if (is_null($gbp)) {
                /** @noinspection PhpUndefinedMethodInspection */
                $exchangeRateGbp = $exchangeRates->first()->where('currency_id', Currency::GBP)->first();
                $gbp = $this->exchangeRateService->convertCurrencies($invoiceTotal, $exchangeRate, $exchangeRateGbp);
            }
        }

        return new class($eur, $gbp) {
            public function __construct(private readonly ?float $eur = null, private readonly ?float $gbp = null)
            {
            }

            public function getEurThresholdRaw(): ?float
            {
                return $this->eur;
            }

            public function getGbpThresholdRaw(): ?float
            {
                return $this->gbp;
            }

            public function getEurThreshold(): float
            {
                return $this->getEurThresholdRaw() ?? 0.0;
            }

            public function getGbpThreshold(): float
            {
                return $this->getGbpThresholdRaw() ?? 0.0;
            }

            public function hasPassedEurThreshold(): bool
            {
                return $this->getEurThreshold() > 150;
            }

            public function hasPassedGbpThreshold(): bool
            {
                return $this->getGbpThreshold() > 135;
            }
        };
    }

    private function resolveDiscountData(
        bool $isGross,
        int $qty,
        float $vatPercent,
        float $netPrice,
        float $grossPrice,
        ?float $discountPercentage,
        ?float $discountNet,
        ?float $discountGross,
    ): object {
        if (!is_null($discountPercentage)) {
            $discountNet = null;
            $discountGross = null;
        } else {
            $discountPercentage = null;
        }

        if (!is_null($discountPercentage)) {
            if ($isGross) {
                $discountGross = ($discountPercentage / 100) * $grossPrice * $qty;
                $discountNet = Calculator::grossToNet($discountGross, $vatPercent);
            } else {
                $discountNet = ($discountPercentage / 100) * $netPrice * $qty;
                $discountGross = Calculator::netToGross($discountNet, $vatPercent);
            }
        } elseif ($isGross && !is_null($discountGross)) {
            $discountNet = Calculator::grossToNet($discountGross, $vatPercent);
        } elseif (!$isGross && !is_null($discountNet)) {
            $discountGross = Calculator::netToGross($discountNet, $vatPercent);
        }

        return new class($discountPercentage, $discountNet, $discountGross) {
            public function __construct(
                private readonly ?float $discountPercentage,
                private readonly ?float $discountNet,
                private readonly ?float $discountGross
            ) {
            }

            public function getDiscountPercentage(): ?float
            {
                return $this->discountPercentage;
            }

            public function getDiscountNet(): ?float
            {
                return $this->discountNet;
            }

            private function getDiscountGross(): ?float
            {
                return $this->discountGross;
            }

            public function getDiscountVatAmount(): ?float
            {
                if (is_null($this->getDiscountGross()) || is_null($this->getDiscountNet())) {
                    return null;
                }

                return $this->getDiscountGross() - $this->getDiscountNet();
            }
        };
    }

    private function resolveInvoiceShippingAndWrap(
        Invoice $invoice,
        Arr $invoiceData,
        DetectedPropertiesContract $detectedGoods,
        ?float $shippingGross = null,
        ?float $wrapGross = null
    ): Invoice {
        if (is_null($shippingGross) && is_null($wrapGross)) {
            return $invoice;
        }

        $warehouseId = $invoiceData->get('mainData.shipFrom.country.id');
        // If country is GB we calculate shipping and wrap data with share in total
        if ($invoice->tax_reporting_country_id === Country::GB) {
            $data = $this->resolveShippingAndWrapForGb(
                invoiceData: $invoiceData,
                invoice: $invoice,
                detectedGoods: $detectedGoods,
                shippingGross: $shippingGross,
                wrapGross: $wrapGross
            );
        } else {
            $data = $this->resolveShippingOrWrapDataForOthers(
                invoice: $invoice,
                detectedGoods: $detectedGoods,
                shippingGross: $shippingGross,
                wrapGross: $wrapGross,
                warehouseId: $warehouseId
            );
        }

        $items = $invoice->invoiceItems;
        foreach ($data as $invoiceItemData) {
            $item = new InvoiceItem();
            $item->fill($invoiceItemData);
            if (is_null($invoice->id)) {
                $clonedInvoice = $invoice->replicate()->unsetRelation('invoiceItems');
                $item->setRelation('invoice', $clonedInvoice);
            }
            $item->invoice->setRelation('currency', $invoice->currency);

            $items->push($item);
        }
        $invoice->setRelation('invoiceItems', $items);

        return $invoice;
    }

    private function setDetectedProperties(Invoice $invoice, DetectedPropertiesContract $detectedProperties): void
    {
        $invoice->tax_collection_responsibility_id = $detectedProperties->getTaxCollectionResponsibilityId();
        $invoice->tax_calculation_country_id = $detectedProperties->getTaxCalculationCountryId();
        $invoice->tax_reporting_scheme_id = $detectedProperties->getTaxReportingSchemeId();
        $invoice->customer_type_id = $detectedProperties->getCustomerTypeId();
        $invoice->invoice_subtype_id = $detectedProperties->getInvoiceSubtypeId();
        $invoice->tax_reporting_country_id = $detectedProperties->getTaxReportingCountryId();

        collect([
            'taxCollectionResponsibility',
            'taxCalculationCountry',
            'taxReportingScheme',
            'customerType',
            'invoiceSubtype',
            'taxReportingCountry'
        ])->each(
            fn($relation) => $invoice->unsetRelation($relation)
        );
    }

    private function resolveShippingAndWrapForGb(
        Arr $invoiceData,
        Invoice $invoice,
        DetectedPropertiesContract $detectedGoods,
        ?float $shippingGross = null,
        ?float $wrapGross = null,
    ): Collection {
        $warehouseId = $invoiceData->get('mainData.shipFrom.country.id');

        $itemsShipping = $this->resolveShippingOrWrapWithShare(
            invoiceData: $invoiceData,
            invoice: $invoice,
            detectedGoods: $detectedGoods,
            description: _l('common.Shipping'),
            invoiceItemTypeId: ItemType::SHIPPING,
            gross: $shippingGross,
            warehouseId: $warehouseId
        );

        $itemsWrap = $this->resolveShippingOrWrapWithShare(
            invoiceData: $invoiceData,
            invoice: $invoice,
            detectedGoods: $detectedGoods,
            description: _l('common.Packaging'),
            invoiceItemTypeId: ItemType::PACKAGING,
            gross: $wrapGross,
            warehouseId: $warehouseId
        );

        return $itemsShipping->merge($itemsWrap);
    }

    private function resolveShippingOrWrapDataForOthers(
        Invoice $invoice,
        DetectedPropertiesContract $detectedGoods,
        ?float $shippingGross = null,
        ?float $wrapGross = null,
        ?int $warehouseId = null
    ): Collection {
        $items = collect();
        $shippingAndWrap = [];
        if (!is_null($shippingGross)) {
            $shippingAndWrap[ItemType::SHIPPING] = ['gross' => $shippingGross];
        }

        if (!is_null($wrapGross)) {
            $shippingAndWrap[ItemType::PACKAGING] = ['gross' => $wrapGross];
        }

        $vatPercent = $this->getVatPercentageForCountryOnDate(
            $detectedGoods->getTaxCalculationCountryId(),
            $invoice->invoice_date,
            ItemTaxCode::A_GEN_STANDARD
        ) ?? 0;

        foreach ($shippingAndWrap as $invoiceItemType => $data) {
            $grossPrice = $data['gross'];
            $netPrice = Calculator::grossToNet($grossPrice, $vatPercent);
            $netPrice = round($netPrice, 2, PHP_ROUND_HALF_EVEN);

            $description = null;
            if ($invoiceItemType === ItemType::SHIPPING) {
                $description = _l('common.Shipping');
            }

            if ($invoiceItemType === ItemType::PACKAGING) {
                $description = _l('common.Packaging');
            }

            if (!$detectedGoods->isTaxCharged()) {
                $vatAmount = 0.0;
                $vatPercent = 0.0;
                $netPrice = round($grossPrice, 2, PHP_ROUND_HALF_EVEN);
            } else {
                $vatAmount = $grossPrice - $netPrice;
            }

            $invoiceItemData = $this->addInvoiceItemData(
                qty: 1,
                invoiceItemTypeId: $invoiceItemType,
                itemTaxCodeId: ItemTaxCode::A_GEN_STANDARD,
                description: $description,
                itemSalesPriceNet: $netPrice,
                itemSalesPriceVat: round($vatAmount, 2, PHP_ROUND_HALF_EVEN),
                vatPercent: $vatPercent,
                invoiceId: $invoice->id,
                warehouseId: $warehouseId
            );

            $items->push($invoiceItemData);
        }

        return $items;
    }

    private function resolveShippingOrWrapWithShare(
        Arr $invoiceData,
        Invoice $invoice,
        DetectedPropertiesContract $detectedGoods,
        string $description,
        int $invoiceItemTypeId,
        ?float $gross = null,
        ?int $warehouseId = null
    ): Collection {
        $items = collect();
        if (is_null($gross)) {
            return $items;
        }

        $invoiceItems = $invoiceData->get('mainData.invoiceItems');
        foreach ($invoiceItems as $invoiceItem) {
            $calculation = $this->calculateNetVatAmountAndVatPercentWithShare(
                $invoiceItem,
                $invoice,
                $gross,
                $detectedGoods->isTaxCharged()
            );

            $invoiceItemData = $this->addInvoiceItemData(
                qty: 1,
                invoiceItemTypeId: $invoiceItemTypeId,
                itemTaxCodeId: $invoiceItem['itemTaxCodeId'],
                description: $description,
                itemSalesPriceNet: $calculation->getNet(),
                itemSalesPriceVat: $calculation->getVat(),
                vatPercent: $calculation->getVatPercentage(),
                invoiceId: $invoice->id,
                warehouseId: $warehouseId
            );

            $items->push($invoiceItemData);
        }

        return $items;
    }

    private function calculateNetVatAmountAndVatPercentWithShare(
        Arr $invoiceItem,
        Invoice $invoice,
        float $grossData,
        bool $isTaxCharged
    ): object {
        $itemsTotalGross = $invoice->total_without_shipping_and_wrap;

        $net = $invoiceItem->get('itemSalesPriceNet', 0.0);
        $vat = $invoiceItem->get('itemSalesPriceVatAmount', 0.0);
        $vatPercentage = $invoiceItem->get('vatPercent', 0.0);
        $discountGross = $invoiceItem->get('discountGross', 0.0);

        $invoiceItemGross = $net + $vat - $discountGross;
        if ($itemsTotalGross !== 0.0) {
            $percentage = $invoiceItemGross / $itemsTotalGross;
        } else {
            $percentage = 0.0;
        }
        $shareWithGross = $percentage * $grossData;
        $shareNet = Calculator::grossToNet($shareWithGross, $vatPercentage);

        if (!$isTaxCharged) {
            $vatAmount = 0.0;
            $vatPercent = 0.0;
            $shareNet = $shareWithGross;
        } else {
            $vatAmount = $shareWithGross - $shareNet;
            $vatPercent = $vatPercentage;
        }

        return new class ($shareNet, $vatAmount, $vatPercent) {
            public function __construct(private readonly float $net, private readonly float $vat, private readonly float $vatPercentage)
            {
            }

            public function getNet(): float
            {
                return $this->net;
            }

            public function getVat(): float
            {
                return $this->vat;
            }

            public function getVatPercentage(): float
            {
                return $this->vatPercentage;
            }
        };
    }

    /** @noinspection PhpSameParameterValueInspection */
    private function addInvoiceItemData(
        int $qty,
        int $invoiceItemTypeId,
        int $itemTaxCodeId,
        string $description,
        float $itemSalesPriceNet,
        float $itemSalesPriceVat,
        float $vatPercent,
        ?int $id = null,
        ?int $itemId = null,
        ?int $invoiceId = null,
        ?int $warehouseId = null,
    ): array {
        return [
            'id'                          => $id,
            'item_id'                     => $itemId,
            'invoice_id'                  => $invoiceId,
            'item_description'            => $description,
            'warehouse_id'                => $warehouseId,
            'qty'                         => $qty,
            'invoice_item_type_id'        => $invoiceItemTypeId,
            'item_tax_code_id'            => $itemTaxCodeId,
            'item_sales_price_vat_amount' => round($itemSalesPriceVat, 2, PHP_ROUND_HALF_EVEN),
            'vat_percent'                 => $vatPercent,
            'item_sales_price_net'        => $itemSalesPriceNet,
        ];
    }

    public function getInvoicesMapperDataForVatReport(VatReport $report): VatReportInvoicesData
    {
        $reports = $this->vatReportRepo
            ->getAllVatReportsForCompanies(
                [$report->company_id],
                Carbon::parse($report->date_from)->year
            );

        return $this->getInvoicesMapperDataForVatReports($reports, $report->id)->get($report->id);
    }

    public function getInvoicesMapperDataForVatReports(Collection $reports, ?int $filterVatReportId = null): Collection
    {
        /**
         * @var VatReport[]|Collection $reports
         */
        $reports = $reports->keyBy('id');
        if ($reports->count() < 1) {
            return $reports;
        }

        /**
         * @var Company $company
         */
        $company = $reports->first()->company;
        $ossIssueCountryId = $company->active_oss_number?->issue_country_id;
        $iossIssueCountryId = $company->active_ioss_number?->issue_country_id;

        $reportsIds = $reports->pluck('id')->toArray();
        $allData = $this->invoiceRepo
            ->getInvoicesMapperDataForVatReport(
                $reportsIds,
                $ossIssueCountryId,
                $iossIssueCountryId
            )
            ->groupBy('vat_report_id');

        $reportsData = collect();
        foreach ($reports as $report) {
            if (!is_null($filterVatReportId) && $report->id !== $filterVatReportId) {
                continue;
            }

            $data = $allData->get($report->id, collect());

            $data = $data->groupBy(function (Invoice $invoice) use ($report) {
                $percentage = (float)($invoice->vat_percent ?? 0.0);

                $subtypes = [
                    InvoiceSubtype::EU_B2C_SALES_INVOICE,
                    InvoiceSubtype::CN_EU_B2C_SALES_INVOICE,
                ];
                if (in_array($invoice->invoice_subtype_id, $subtypes)) {
                    if ($report->country_id !== $invoice->tax_calculation_country_id) {
                        $percentage = 0;
                    }
                }

                return $invoice->invoice_subtype_id . '-' . $invoice->item_type_id . '-' . $percentage;
            });

            $data = $data->map(function (Collection $invoices, $key) use ($report) {
                $data = $invoices->first();
                $data->key = $key;

                $netDiscount = $invoices->sum(function (Invoice $invoice) {
                    return (float)($invoice->net_discount ?? 0.0);
                });
                if ($netDiscount === 0.0) {
                    $netDiscount = null;
                }
                $vatDiscount = $invoices->sum(function (Invoice $invoice) use ($report) {
                    if ($report->country_id !== $invoice->tax_calculation_country_id) {
                        return 0.0;
                    }

                    return (float)($invoice->vat_discount ?? 0.0);
                });
                if ($vatDiscount === 0.0) {
                    $vatDiscount = null;
                }
                $data->net_discount = $netDiscount;
                $data->vat_discount = $vatDiscount;

                $data->net = $invoices->sum(function (Invoice $invoice) {
                    return (float)($invoice->net ?? 0.0);
                });
                $data->vat = $invoices->sum(function (Invoice $invoice) use ($report) {
                    if ($report->country_id !== $invoice->tax_calculation_country_id) {
                        return 0.0;
                    }

                    return (float)($invoice->vat ?? 0.0);
                });
                $data->cnt_invoices = $invoices->sum(function (Invoice $invoice) {
                    return (int)($invoice->cnt_invoices ?? 0);
                });
                $data->cnt_items = $invoices->sum(function (Invoice $invoice) {
                    return (int)($invoice->cnt_items ?? 0);
                });

                if ($report->country_id !== $data->tax_calculation_country_id) {
                    $data->vat_percent = null;
                }

                return $data;
            })->values();

            $reportsData->put($report->id, $data);
        }

        $reportsDataFlatten = $reportsData->flatten();
        $invoiceSubtypes = $reportsDataFlatten->pluck('invoice_subtype_id')->unique()->toArray();
        $invoiceSubtypes = $this->invoiceRepo
            ->getAllInvoiceSubtypesByIds($invoiceSubtypes)
            ->load('invoiceType')
            ->keyBy('id');

        $countries = $reportsDataFlatten->pluck('tax_calculation_country_id')->unique()->toArray();
        $countries = $this->countryRepo
            ->getAllCountriesByIds($countries)
            ->keyBy('id');

        $vatRateTypes = $reportsDataFlatten->pluck('vat_rate_type_id')->unique()->toArray();
        $vatRateTypes = $this->vatRateTypeRepo
            ->getVatRateTypesByIds($vatRateTypes)
            ->keyBy('id');

        $itemTypes = $this->itemRepo
            ->getAllItemTypes()
            ->keyBy('id');


        $data = collect();
        foreach ($reportsData as $reportId => $reportData) {
            /**
             * @var VatReport $report
             * @var Collection $reportData
             */
            $report = $reports->get($reportId);
            $mapperData = new VatReportInvoicesData($report);

            foreach ($reportData as $subtypeData) {
                $subtype = $invoiceSubtypes->get($subtypeData->invoice_subtype_id);
                $taxCalculationCountry = $countries->get($subtypeData->tax_calculation_country_id);
                $itemType = $itemTypes->get($subtypeData->item_type_id);
                $vatRateType = $vatRateTypes->get($subtypeData->vat_rate_type_id);

                $mapperData->addSubtype(new InvoiceSubtypeData(
                    invoiceSubtype: $subtype,
                    taxCalculationCountry: $taxCalculationCountry,
                    itemType: $itemType,
                    vatRateType: $vatRateType,
                    net: $subtypeData->net,
                    invoicesCount: $subtypeData->cnt_invoices,
                    invoiceItemsCount: $subtypeData->cnt_items,
                    vatPercentage: $subtypeData->vat_percent,
                    netDiscount: $subtypeData->net_discount,
                    vat: $subtypeData->vat,
                    vatDiscount: $subtypeData->vat_discount,
                    key: $subtypeData->key
                ));
            }

            $data->put($report->id, $mapperData);
        }

        return $data;
    }

    public function getInvoicesMapperDataForOssIossReport(OssIossReport $report): OssIossReportInvoicesData
    {
        return $this->getInvoicesDataForOssIossReport(collect([$report]))->first();
    }

    /**
     * @param Collection<OssIossReport> $reports
     * @return Collection|OssIossReportInvoicesData[]
     * @noinspection PhpDocSignatureInspection
     */
    public function getInvoicesDataForOssIossReport(Collection $reports): Collection
    {
        /**
         * @var Company $company
         */
        $company = $reports->first()->company;

        $schemeData = collect([
            OssIossReport::TYPE_OSS  => $company->active_oss_number,
            OssIossReport::TYPE_VOES => $company->active_voes_number,
            OssIossReport::TYPE_IOSS => $company->active_ioss_number,
        ])->filter(function (OssNumber|IossNumber|null $number = null) {
            return !is_null($number);
        })->map(function (OssNumber|IossNumber $number, string $scheme) {
            return OssIossReport::resolveSchemeData($scheme);
        });

        $countries = $this->countryRepo
            ->getAllCountries()
            ->load('currency')
            ->keyBy('id');

        $data = collect();
        foreach ($reports as $report) {
            $reportScheme = $schemeData->get($report->type);

            if (is_null($reportScheme)) {
                continue;
            }

            $reportSubtypes = $reportScheme['types'];
            $reportItemTypes = $reportScheme['itemTypes'];

            $invoices = $this->invoiceRepo
                ->getInvoiceItemsForOssIossReport(
                    companyId: $report->company_id,
                    startDate: $report->date_from,
                    endDate: $report->date_to,
                    invoiceItemTypeIds: $reportItemTypes,
                    invoiceSubtypeIds: $reportSubtypes,
                );

            $ossIoss = new OssIossReportInvoicesData(
                $report,
                $invoices,
                $countries
            );

            $data->push($ossIoss);
        }

        return $data;
    }

    public function getInvoicesMapperDataForEslReport(EslReport $report): EslReportInvoicesData
    {
        return $this->getInvoicesMapperDataForEslReports(collect([$report]))->first();
    }

    /**
     * @param Collection<EslReport> $reports
     * @return Collection
     */
    public function getInvoicesMapperDataForEslReports(Collection $reports): Collection
    {
        $years = $reports->map(function (EslReport $report) {
            return Carbon::parse($report->date_from)->year;
        })->unique()->sortBy(function (int $year) {
            return $year;
        }, SORT_NATURAL)->values();

        $companyId = $reports->first()->company_id;

        $dateFrom = Carbon::parse($years->first() . '-01-01')->startOfYear()->toDateTimeString();
        $dateTo = Carbon::parse($years->last() . '-01-01')->endOfYear()->toDateTimeString();

        $invoices = $this->invoiceRepo
            ->getInvoicesGroupedForEslReport(
                $companyId,
                $dateFrom,
                $dateTo,
                EslReport::getReportSubtypes()
            )->map(function ($invoicesData) {
                $invoicesData->net_in_tax_reporting_currency = (float)$invoicesData->net_in_tax_reporting_currency;
                $invoicesData->net_in_tax_calculation_currency = (float)$invoicesData->net_in_tax_calculation_currency;
                $invoicesData->net_in_sales_currency = (float)$invoicesData->net_in_sales_currency;

                return $invoicesData;
            })->groupBy('tax_reporting_country_id');

        $data = collect();
        foreach ($reports as $report) {
            $months = CarbonPeriod::since($report->date_from)
                ->months()
                ->until($report->date_to)
                ->map(function (Carbon $month) {
                    return $month->startOfMonth()->toDateString();
                });
            $months = iterator_to_array($months);

            /**
             * @var Collection<Invoice> $invoicesForCountry
             */
            $invoicesForReport = $invoices->get($report->country_id, collect())
                ->filter(function (Invoice $invoice) use ($months) {
                    $monthStart = Carbon::parse($invoice->month_start)->startOfMonth();

                    return in_array($monthStart->toDateString(), $months);
                });

            $reportData = new EslReportInvoicesData(
                $report->company,
                $report->country,
                $invoicesForReport,
                $report->date_from,
                $report->date_to,
                $report->id
            );

            $data->push($reportData);
        }

        return $data;
    }

    public function getFirstSalesGroupedByCompanyCountry(array $companiesIds): Collection
    {
        return $this->invoiceRepo
            ->getFirstSalesGroupedByCompanyIdCountryId($companiesIds)
            ->groupBy('company_id')
            ->map(function (Collection $data) {
                /**
                 * @var Collection<Invoice> $data
                 */

                $resolved = collect();
                foreach ($data as $dateData) {
                    $date = $dateData->invoice_date;
                    $countriesIds = [
                        $dateData->tax_reporting_country_id,
                        $dateData->tax_calculation_country_id,
                        $dateData->ship_from_country_id
                    ];
                    foreach ($countriesIds as $countryId) {
                        $countryDate = $this->resolveFirstDateForCountry($resolved, $date, $countryId);
                        if (!is_null($countryDate)) {
                            $resolved->put($countryId, $countryDate);
                        }
                    }
                }

                return $resolved->sortKeys(SORT_NATURAL);
            });
    }

    private function resolveFirstDateForCountry(Collection $resolved, string $invoiceDate, int $countryId): ?Carbon
    {
        /**
         * @var Carbon $currentDate
         */
        $currentDate = $resolved->get($countryId);
        $invoiceDate = Carbon::parse($invoiceDate);
        if (is_null($currentDate) || $invoiceDate->lt($currentDate)) {
            return $invoiceDate;
        }

        return null;
    }

    public function getInvoicesDataForExport(
        int $companyId,
        ?int $invoiceSubtypeId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): Collection {
        if (!is_null($dateFrom)) {
            $dateFrom = Carbon::parse($dateFrom);
        }
        if (!is_null($dateTo)) {
            $dateTo = Carbon::parse($dateTo);
        }

        $data = $this->invoiceRepo
            ->getInvoicesDataForExport(
                $companyId,
                $invoiceSubtypeId,
                $dateFrom?->toDateString(),
                $dateTo?->toDateString()
            );

        if ($data->count() < 1) {
            return collect();
        }

        $header = collect(array_keys($data->first()->toArray()))
            ->mapWithKeys(function (string $key) {
                return [$key => Str::headline($key)];
            })->toArray();

        $data = $data->map(function (Invoice|InvoiceItem $invoice) {
            $invoice = $invoice->toArray();

            $invoice['platform_name'] = $this->nullOrTranslate($invoice['platform_name'] ?? null);
            $invoice['delivery_condition_name'] = $this->nullOrTranslate($invoice['delivery_condition_name'] ?? null);
            $invoice['tax_reporting_scheme_name'] = $this->nullOrTranslate($invoice['tax_reporting_scheme_name'] ?? null);
            $invoice['tax_collection_responsibilities_name'] = $this->nullOrTranslate($invoice['tax_collection_responsibilities_name'] ?? null);
            $invoice['status'] = $this->nullOrTranslate($invoice['status'] ?? null);
            $invoice['invoice_subtype'] = $this->nullOrTranslate($invoice['invoice_subtype'] ?? null);
            $invoice['invoice_type'] = $this->nullOrTranslate($invoice['invoice_type'] ?? null);
            $invoice['item_tax_category'] = $this->nullOrTranslate($invoice['item_tax_category'] ?? null);
            $invoice['item_type'] = $this->nullOrTranslate($invoice['item_type'] ?? null);
            $invoice['customer_type'] = $this->nullOrTranslate($invoice['customer_type'] ?? null);

            return $invoice;
        });

        $data->prepend($header);

        return $data;
    }

    private function nullOrTranslate(?string $text = null): ?string
    {
        if (is_null($text)) {
            return null;
        }

        return _l($text);
    }

    public function calculateAndStoreInvoiceByApi(CalculateInvoiceApiRequest $invoiceData): ApiCalculationData
    {
        /**
         * Validates request and if fails throws ValidationApiException
         * which is handled in Core\System\Exceptions\Handler
         *
         * @noinspection PhpUnhandledExceptionInspection
         */
        $invoiceData->validate();

        $invoice = $this->createInvoiceFromApi($invoiceData);
        $this->createInvoiceItemsFromApi($invoice, $invoiceData);

        return $this->recalculateAndResolveInvoiceFromApi($invoice, $invoiceData);
    }

    private function recalculateAndResolveInvoiceFromApi(Invoice $invoice, CalculateInvoiceApiRequest $invoiceData): ApiCalculationData
    {
        $load = [
            'company.address',
            'marketplace',
            'shipToCountry',
            'invoiceItems' => function (HasMany $query) {
                $query->orderBy('vat_percent', 'DESC');
            },
            'invoiceItems.taxCode',
            'invoiceItems.invoiceItemType',
            'marketplace',
            'invoiceSubtype.invoiceType',
        ];
        $invoice->load($load);

        $itemTypeId = $invoice->invoiceItems->first()?->invoice_item_type_id ?? ItemType::GOODS;
        $detected = $this->detectForApi($invoice, $invoiceData->getInvoiceTypeId(), $itemTypeId);

        $taxCalculationCountryId = $detected->getTaxCalculationCountryId();
        $invoiceDate = $invoice->invoice_date;

        foreach ($invoice->invoiceItems as $item) {
            $vatRate = 0.0;
            if ($detected->isTaxCharged()) {
                $vatRate = $this->getVatPercentageForCountryOnDate(
                    countryId: $taxCalculationCountryId,
                    invoiceDate: $invoiceDate,
                    itemTaxCodeId: $item->item_tax_code_id
                );
            }

            $isGross = $item->is_price_inserted_as_gross;
            $grossPrice = $item->item_gross_price;
            $netPrice = $item->item_sales_price_net;
            $qty = $item->qty;

            if ($isGross) {
                $netPrice = Calculator::grossToNet($grossPrice, $vatRate);
            } else {
                $grossPrice = Calculator::netToGross($netPrice, $vatRate);
            }
            $vatAmount = $grossPrice - $netPrice;

            $discountData = $this->resolveDiscountData(
                isGross: $isGross,
                qty: $qty,
                vatPercent: $vatRate,
                netPrice: $netPrice,
                grossPrice: $grossPrice,
                discountPercentage: $item->discount_percentage,
                discountNet: $item->discount_net,
                discountGross: $item->discount_gross
            );
            $discountPercentage = $discountData->getDiscountPercentage();
            $discountNet = $discountData->getDiscountNet();
            $discountVatAmount = $discountData->getDiscountVatAmount();

            $item->vat_percent = $vatRate;
            $item->item_sales_price_net = $netPrice;
            $item->item_sales_price_vat_amount = $vatAmount;
            $item->discount_net = $discountNet;
            $item->discount_vat_amount = $discountVatAmount;
            $item->discount_percentage = $discountPercentage;

            $item->save();
        }

        $invoice->unsetRelations()->load($load);

        $thresholdsData = $this->isSaleAmountAboveThresholds(
            invoiceDate: $invoice->invoice_date,
            invoiceCurrencyId: $invoice->sales_currency_id,
            invoiceTotal: $invoice->total_without_shipping_and_wrap
        );

        $detected = $this->detectForApi(
            $invoice,
            $invoiceData->getInvoiceTypeId(),
            $itemTypeId,
            $thresholdsData->hasPassedEurThreshold(),
            $thresholdsData->hasPassedGbpThreshold()
        );

        $invoice->tax_collection_responsibility_id = $detected->getTaxCollectionResponsibilityId();
        $invoice->tax_calculation_country_id = $detected->getTaxCalculationCountryId();
        $invoice->tax_reporting_scheme_id = $detected->getTaxReportingSchemeId();
        $invoice->customer_type_id = $detected->getCustomerTypeId();
        $invoice->invoice_subtype_id = $detected->getInvoiceSubtypeId();
        $invoice->tax_reporting_country_id = $detected->getTaxReportingCountryId();

        $invoice->save();

        return new ApiCalculationData($invoice, $detected);
    }

    private function detectForApi(
        Invoice $invoice,
        int $invoiceTypeId,
        int $itemTypeId,
        bool $isSaleAbove150Euro = false,
        bool $isSaleAbove135GBP = false
    ): DetectedPropertiesContract {
        $invoiceDate = $invoice->invoice_date;
        $company = $invoice->company;
        $companyAddress = $company->address;

        $shipFromCountry = $invoice->shipFromCountry;
        $billToCountry = $invoice->billToCountry;
        $shipToCountry = $invoice->shipToCountry;

        $ossNumber = $company->getActiveOssNumberOnDate($invoiceDate);
        $iossNumber = $company->getActiveIossNumberOnDate($invoiceDate);
        $marketplace = $invoice->marketplace;

        $voesNumber = $company->getActiveVoesNumberOnDate($invoiceDate);

        return (new VatDetection())
            ->detectAfterFirstOfJuly21(
                calculationDate: $invoiceDate,
                companyCountryId: $companyAddress->country_id,
                isOnlyVatNumberAndWarehouseInCompanyCountry: $company->isOnlyVatNumberAndWarehouseInCompanyCountry($invoiceDate),
                isCompanyCountryVatNumberPermanentEstablishment: $company->isCompanyCountryVatNumberPermanentEstablishmentForDate($invoiceDate),
                whetherTheCompanyHasPassedThreshold: $company->whetherTheCompanyHasPassedThresholdInCountryOnDate($invoiceDate, $shipToCountry),

                shipFromCountryId: $shipFromCountry->id,

                billToCountryId: $billToCountry->id,
                isDomesticReverseChargeApplicableInBillToCountry: $billToCountry->isDomesticReverseChargeApplicableFromOnDate($invoiceDate),

                platformTypeId: $invoice->marketplace->platform->platform_type_id,
                invoiceTypeId: $invoiceTypeId,
                itemTypeId: $itemTypeId,

                isCompanyRegisteredForOss: !is_null($ossNumber),
                companyOssRegistrationCountryId: $ossNumber?->issue_country_id,
                isCompanyRegisteredForIoss: !is_null($iossNumber),
                companyIossRegistrationCountryId: $iossNumber?->issue_country_id,

                isMarketplaceRegisteredForOss: !is_null($marketplace->oss_number),
                marketplaceOssRegistrationCountryId: $marketplace->oss_country_id,
                isMarketplaceRegisteredForIoss: !is_null($marketplace->ioss_number),
                marketplaceIossRegistrationCountryId: $marketplace->ioss_country_id,
                isMarketplaceIncorporatedInEu: $marketplace->is_marketplace_incorporated_in_eu,

                isSaleAbove150Euro: $isSaleAbove150Euro,
                isSaleAbove135GBP: $isSaleAbove135GBP,

                shipToCountryId: $shipToCountry?->id,
                isShipToVatNumberValid: !is_null($shipToCountry) && !is_null($invoice->ship_to_vat_number),

                isCompanyRegisteredForVoes: !is_null($voesNumber),
                companyVoesRegistrationCountryId: $voesNumber?->issue_country_id,
                isMarketplaceRegisteredForVoes: !is_null($marketplace->voes_country_id),
                marketplaceVoesRegistrationCountryId: $marketplace->voes_country_id,
                isCompanyShipFromCountryVatNumberPermanentEstablishment: $company->isVatNumberInCountryOnDatePermanentEstablishment($invoiceDate, $shipFromCountry->id),
                hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry: $company->hasOnlyVatNumberAndItIsInCompanyCountry($invoiceDate)
            );
    }

    private function createInvoiceFromApi(CalculateInvoiceApiRequest $invoiceData): Invoice
    {
        $apiConsumer = $invoiceData->getApiConsumer();

        $invoice = $this->invoiceRepo->getEmptyInvoiceModel();

        $invoice->invoice_date = $invoiceData->getInvoiceDate()->toDateString();
        $invoice->order_date = $invoiceData->getInvoiceDate()->toDateString();
        $invoice->invoice_number = $invoiceData->getInvoiceNumber();
        $invoice->delivery_condition_id = $invoiceData->getDeliveryConditionId();
        $invoice->sales_currency_id = $invoiceData->getSalesCurrencyId();
        $invoice->order_number = $invoiceData->getOrderNumber();
        $invoice->marketplace_id = $apiConsumer->marketplace_id;
        $invoice->invoice_status_id = InvoiceStatus::DRAFT;
        $invoice->company_id = $invoiceData->getSalesChannel()->company_id;

        // WILL BE CHANGED AFTER CALCULATION
        $invoice->invoice_subtype_id = InvoiceSubtype::DOMESTIC_SALES_INVOICE;
        $invoice->tax_reporting_scheme_id = TaxReportingScheme::REGULAR;
        $invoice->tax_collection_responsibility_id = TaxCollectionResponsibility::SELLER;

        /**
         * ORDER IS IMPORTANT
         * ShipTo is billTo if shipToIsSameAsBillTo is true
         */
        $shipToIsSameAsBillTo = $invoiceData->isShipToSameAsBillTo();
        $billTo = $invoiceData->getBillTo();
        $shipTo = $invoiceData->getShipTo();
        $shipFrom = $invoiceData->getShipFrom();

        if (is_null($shipTo)) {
            $shipToIsSameAsBillTo = true;
        }

        $shipTo = $billTo;
        if (!$shipToIsSameAsBillTo) {
            $shipTo = $invoiceData->getShipTo();
        }

        $invoice->bill_to_country_id = $billTo->getCountryId();
        $invoice->bill_to_name = $billTo->getName();
        $invoice->bill_to_address = $billTo->getAddress();
        $invoice->bill_to_postal_code = $billTo->getPostalCode();
        $invoice->bill_to_vat_number = $billTo->getVatNumber();

        $invoice->ship_from_country_id = $shipFrom->getCountryId();
        $invoice->ship_from_vat_number = $shipFrom->getVatNumber();

        $invoice->ship_to_country_id = $shipTo->getCountryId();
        $invoice->ship_to_name = $shipTo->getName();
        $invoice->ship_to_address = $shipTo->getAddress();
        $invoice->ship_to_postal_code = $shipTo->getPostalCode();
        $invoice->ship_to_vat_number = $shipTo->getVatNumber();

        $invoice->save();

        return $invoice;
    }

    private function createInvoiceItemsFromApi(Invoice $invoice, CalculateInvoiceApiRequest $invoiceData): void
    {
        $items = $invoiceData->getItems();
        foreach ($items as $item) {
            $invoiceItem = $this->invoiceRepo->getEmptyInvoiceItemModel();

            $invoiceItem->invoice_id = $invoice->id;

            $invoiceItem->qty = $item->getQty();
            $invoiceItem->invoice_item_type_id = $item->getInvoiceItemTypeId();
            $invoiceItem->item_description = $item->getDescription();
            $invoiceItem->item_sales_price_net = $item->getItemSalesPrice();
            $invoiceItem->item_tax_code_id = $item->getItemTaxCodeId();
            $invoiceItem->is_price_inserted_as_gross = $item->isPriceGross();
            $invoiceItem->discount_net = $item->getDiscountAmount();
            $invoiceItem->discount_percentage = $item->getDiscountPercentage();

            $invoiceItem->save();
        }
    }
}
