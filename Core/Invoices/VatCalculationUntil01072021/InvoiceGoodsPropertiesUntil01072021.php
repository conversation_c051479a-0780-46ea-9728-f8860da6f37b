<?php


namespace App\Core\Invoices\VatCalculationUntil01072021;

use App\Core\Invoices\Contracts\InvoiceGoodsPropertiesContract;

class InvoiceGoodsPropertiesUntil01072021 implements InvoiceGoodsPropertiesContract
{
    private int $recipientCountryId;
    private int $companyCountryId;
    private int $warehouseCountryId;
    private int $invoiceTypeId;
    private bool $hasWaiverInRecipientCountry;
    private bool $hasVatNumberInRecipientCountry;
    private bool $hasWarehouseInRecipientCountry;
    private bool $hasPassedThresholdInRecipientCountry;
    private ?int $buyerVatNumberCountryId = null;
    private bool $isRecipientCountryInEu;
    private bool $isWarehouseCountryInEu;
    private bool $isDomesticReverseChargeApplicable;

    public function setInvoiceTypeId(int $value): void
    {
        $this->invoiceTypeId = $value;
    }

    public function getInvoiceTypeId(): int
    {
        return $this->invoiceTypeId;
    }

    public function setRecipientCountryId(int $value): void
    {
        $this->recipientCountryId = $value;
    }

    public function getRecipientCountryId(): int
    {
        return $this->recipientCountryId;
    }

    public function setCompanyCountryId(int $value): void
    {
        $this->companyCountryId = $value;
    }

    public function getCompanyCountryId(): int
    {
        return $this->companyCountryId;
    }

    public function setWarehouseCountryId(int $value): void
    {
        $this->warehouseCountryId = $value;
    }

    public function getWarehouseCountryId(): int
    {
        return $this->warehouseCountryId;
    }

    public function isCustomerTypeB2C(): bool
    {
        return !$this->isCustomerTypeB2B();
    }

    public function isCustomerTypeB2B(): bool
    {
        return (!is_null($this->getBuyerVatNumberCountryId()) && $this->isBuyerVatNumberCountrySameAsRecipientCountry());
    }

    public function setHasWaiverInRecipientCountry(bool $value): void
    {
        $this->hasWaiverInRecipientCountry = $value;
    }

    public function getHasWaiverInRecipientCountry(): bool
    {
        return $this->hasWaiverInRecipientCountry;
    }

    public function setHasVatNumberInRecipientCountry(bool $value): void
    {
        $this->hasVatNumberInRecipientCountry = $value;
    }

    public function getHasVatNumberInRecipientCountry(): bool
    {
        return $this->hasVatNumberInRecipientCountry;
    }

    public function setHasWarehouseInRecipientCountry(bool $value): void
    {
        $this->hasWarehouseInRecipientCountry = $value;
    }

    public function getHasWarehouseInRecipientCountry(): bool
    {
        return $this->hasWarehouseInRecipientCountry;
    }

    public function setHasPassedThresholdInRecipientCountry(bool $value): void
    {
        $this->hasPassedThresholdInRecipientCountry = $value;
    }

    public function getHasPassedThresholdInRecipientCountry(): bool
    {
        return $this->hasPassedThresholdInRecipientCountry;
    }

    public function setBuyerVatNumberCountryId(?int $value): void
    {
        $this->buyerVatNumberCountryId = $value;
    }

    public function getBuyerVatNumberCountryId(): ?int
    {
        return $this->buyerVatNumberCountryId;
    }

    public function isBuyerVatNumberCountrySameAsRecipientCountry(): bool
    {
        return $this->getBuyerVatNumberCountryId() === $this->getRecipientCountryId();
    }

    public function isRecipientCountrySameAsWarehouseCountry(): bool
    {
        return $this->getRecipientCountryId() === $this->getWarehouseCountryId();
    }

    public function setIsRecipientCountryInEu(bool $value): void
    {
        $this->isRecipientCountryInEu = $value;
    }

    public function getIsRecipientCountryInEu(): bool
    {
        return $this->isRecipientCountryInEu;
    }

    public function setIsWarehouseCountryInEu(bool $value): void
    {
        $this->isWarehouseCountryInEu = $value;
    }

    public function getIsWarehouseCountryInEu(): bool
    {
        return $this->isWarehouseCountryInEu;
    }

    public function setDomesticReverseChargeApplicableInRecipientCountry(bool $value): void
    {
        $this->isDomesticReverseChargeApplicable = $value;
    }

    public function isDomesticReverseChargeApplicable(): bool
    {
        return $this->isDomesticReverseChargeApplicable;
    }


}
