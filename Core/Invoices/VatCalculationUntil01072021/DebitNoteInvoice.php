<?php

namespace App\Core\Invoices\VatCalculationUntil01072021;

use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;

class DebitNoteInvoice
{
    private InvoiceGoodsPropertiesUntil01072021 $invoiceGoodsProperties;
    private DetectedInvoiceGoodsPropertiesUntil01072021 $detectedInvoiceGoodsProperties;

    public function __construct(InvoiceGoodsPropertiesUntil01072021 $invoiceGoodsProperties)
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
        $this->detectedInvoiceGoodsProperties = new DetectedInvoiceGoodsPropertiesUntil01072021($invoiceGoodsProperties);
        $this->run();
    }

    public function getDetectedInvoiceGoodsProperties(): DetectedInvoiceGoodsPropertiesUntil01072021
    {
        return $this->detectedInvoiceGoodsProperties;
    }

    public function run()
    {
        $this->detectedInvoiceGoodsProperties->taxReportingSchemeId = TaxReportingScheme::REGULAR;
        $this->detectedInvoiceGoodsProperties->taxCollectionResponsibilityId = TaxCollectionResponsibility::SELLER;
        $this->detectedInvoiceGoodsProperties->customerTypeId = CustomerType::B2B;
        $this->detectedInvoiceGoodsProperties->isTaxCharged = false;
        $this->detectedInvoiceGoodsProperties->taxCalculationCountryId = $this->invoiceGoodsProperties->getRecipientCountryId();
        $this->detectedInvoiceGoodsProperties->taxReportingCountryId = $this->invoiceGoodsProperties->getRecipientCountryId();

        if ($this->invoiceGoodsProperties->getWarehouseCountryId() === $this->invoiceGoodsProperties->getRecipientCountryId()) {
            if ($this->invoiceGoodsProperties->isDomesticReverseChargeApplicable()) {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::DN_DOMESTIC_B2B_REVERSE_CHARGE_PURCHASE_INVOICE;
            } else {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::DN_DOMESTIC_B2B_PURCHASE_INVOICE;
                $this->detectedInvoiceGoodsProperties->isTaxCharged = true;
            }
        } elseif ($this->invoiceGoodsProperties->getIsRecipientCountryInEu() && $this->invoiceGoodsProperties->getIsWarehouseCountryInEu()) {
            $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::DN_EU_B2B_PURCHASE_INVOICE;
        }
    }
}
