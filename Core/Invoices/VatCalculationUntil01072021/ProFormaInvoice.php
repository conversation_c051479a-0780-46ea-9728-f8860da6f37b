<?php


namespace App\Core\Invoices\VatCalculationUntil01072021;


use App\Core\Data\Models\CustomerType;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;

class ProFormaInvoice
{

    /**
     * @var InvoiceGoodsPropertiesUntil01072021
     */
    private $invoiceGoodsProperties;
    /**
     * @var DetectedInvoiceGoodsPropertiesUntil01072021
     */
    private $detectedInvoiceGoodsProperties;

    public function __construct(InvoiceGoodsPropertiesUntil01072021 $invoiceGoodsProperties)
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
        $this->detectedInvoiceGoodsProperties = new DetectedInvoiceGoodsPropertiesUntil01072021($invoiceGoodsProperties);
        $this->run();
    }

    public function getDetectedInvoiceGoodsProperties(): DetectedInvoiceGoodsPropertiesUntil01072021
    {
        return $this->detectedInvoiceGoodsProperties;
    }

    public function run()
    {
        $this->detectedInvoiceGoodsProperties->taxReportingSchemeId = TaxReportingScheme::REGULAR;
        $this->detectedInvoiceGoodsProperties->taxCollectionResponsibilityId = TaxCollectionResponsibility::SELLER;


        if ($this->invoiceGoodsProperties->getHasPassedThresholdInRecipientCountry() ||
            $this->invoiceGoodsProperties->getHasWaiverInRecipientCountry()) {
            $this->detectedInvoiceGoodsProperties->taxCalculationCountryId = $this->invoiceGoodsProperties->getRecipientCountryId();
            $this->detectedInvoiceGoodsProperties->taxReportingCountryId = $this->invoiceGoodsProperties->getRecipientCountryId();
        } else {
            $this->detectedInvoiceGoodsProperties->taxCalculationCountryId = $this->invoiceGoodsProperties->getWarehouseCountryId();
            $this->detectedInvoiceGoodsProperties->taxReportingCountryId = $this->invoiceGoodsProperties->getWarehouseCountryId();
        }


        if ($this->invoiceGoodsProperties->isCustomerTypeB2C()) {
            $this->detectedInvoiceGoodsProperties->customerTypeId = CustomerType::B2C;
            $this->detectedInvoiceGoodsProperties->isTaxCharged = true;
            if ($this->invoiceGoodsProperties->getWarehouseCountryId() === $this->invoiceGoodsProperties->getRecipientCountryId()) {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::DOMESTIC_PRO_FORMA_INVOICE;
            } elseif ($this->invoiceGoodsProperties->getIsRecipientCountryInEu() && $this->invoiceGoodsProperties->getIsWarehouseCountryInEu()) {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::EU_B2C_PRO_FORMA_INVOICE;
            } else {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::EXPORT_PRO_FORMA_INVOICE;
                $this->detectedInvoiceGoodsProperties->isTaxCharged = false;
            }
        } elseif ($this->invoiceGoodsProperties->isCustomerTypeB2B()) {
            $this->detectedInvoiceGoodsProperties->customerTypeId = CustomerType::B2B;
            $this->detectedInvoiceGoodsProperties->isTaxCharged = false;

            if ($this->invoiceGoodsProperties->getWarehouseCountryId() === $this->invoiceGoodsProperties->getRecipientCountryId()) {
                if ($this->invoiceGoodsProperties->isDomesticReverseChargeApplicable()) {
                    $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::DOMESTIC_B2B_REVERSE_CHARGE_PRO_FORMA_INVOICE;
                } else {
                    $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::DOMESTIC_B2B_PRO_FORMA_INVOICE;
                    $this->detectedInvoiceGoodsProperties->isTaxCharged = true;
                }
            } elseif ($this->invoiceGoodsProperties->getIsRecipientCountryInEu() && $this->invoiceGoodsProperties->getIsWarehouseCountryInEu()) {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::EU_B2B_PRO_FORMA_INVOICE;
                $this->detectedInvoiceGoodsProperties->taxReportingCountryId = $this->invoiceGoodsProperties->getWarehouseCountryId();
            } else {
                $this->detectedInvoiceGoodsProperties->invoiceSubtypeId = InvoiceSubtype::EXPORT_PRO_FORMA_INVOICE;
            }


        }
    }

}
