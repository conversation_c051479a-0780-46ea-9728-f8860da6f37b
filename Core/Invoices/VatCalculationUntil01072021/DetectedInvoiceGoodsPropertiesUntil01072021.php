<?php

namespace App\Core\Invoices\VatCalculationUntil01072021;

use App\Core\Data\Models\InvoiceType;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use App\Core\Invoices\Contracts\InvoiceGoodsPropertiesContract;
use App\Core\Invoices\VatCalculation\Properties\DetectedInvoiceGoodsProperties;

class DetectedInvoiceGoodsPropertiesUntil01072021 implements DetectedPropertiesContract
{
    public int $taxCollectionResponsibilityId;
    public int $taxCalculationCountryId;
    public int $customerTypeId;
    public int $taxReportingSchemeId;
    public int $taxReportingCountryId;
    public bool $isTaxCharged;
    public int $invoiceSubtypeId;

    private ?DetectedInvoiceGoodsProperties $deemedDetected = null;
    private ?InvoiceGoodsPropertiesContract $invoiceGoodsProperties;

    public function __construct(InvoiceGoodsPropertiesContract $invoiceGoodsProperties)
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
    }

    public function getTaxCollectionResponsibilityId(): int
    {
        return $this->taxCollectionResponsibilityId;
    }

    public function getTaxCalculationCountryId(): int
    {
        return $this->taxCalculationCountryId;
    }

    public function getTaxReportingSchemeId(): int
    {
        return $this->taxReportingSchemeId;
    }

    public function getTaxReportingCountryId(): int
    {
        return $this->taxReportingCountryId;
    }

    public function getCustomerTypeId(): int
    {
        return $this->customerTypeId;
    }

    public function getInvoiceSubtypeId(): int
    {
        return $this->invoiceSubtypeId;
    }

    public function isTaxCharged(): bool
    {
        return $this->isTaxCharged;
    }

    public function toArray(): array
    {
        return [
            'taxCollectionResponsibilityId' => $this->getTaxCollectionResponsibilityId(),
            'taxCalculationCountryId'       => $this->getTaxCalculationCountryId(),
            'taxReportingSchemeId'          => $this->getTaxReportingSchemeId(),
            'customerTypeId'                => $this->getCustomerTypeId(),
            'invoiceSubtypeId'              => $this->getInvoiceSubtypeId(),
            'isTaxCharged'                  => $this->isTaxCharged(),
            'taxReportingCountryId'         => $this->getTaxReportingCountryId(),
        ];
    }

    public function setTaxCollectionResponsibilityId(int $taxCollectionResponsibilityId): self
    {
        $this->taxCollectionResponsibilityId = $taxCollectionResponsibilityId;

        return $this;
    }

    public function setTaxCalculationCountryId(int $taxCalculationCountryId): self
    {
        $this->taxCalculationCountryId = $taxCalculationCountryId;

        return $this;
    }

    public function setCustomerTypeId(int $customerTypeId): self
    {
        $this->customerTypeId = $customerTypeId;

        return $this;
    }

    public function setTaxReportingSchemeId(int $taxReportingSchemeId): self
    {
        $this->taxReportingSchemeId = $taxReportingSchemeId;

        return $this;
    }

    public function setTaxReportingCountryId(int $taxReportingCountryId): self
    {
        $this->taxReportingCountryId = $taxReportingCountryId;

        return $this;
    }

    public function setTaxCharged(bool $isTaxCharged): self
    {
        $this->isTaxCharged = $isTaxCharged;

        return $this;
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function setInvoiceSubtypeId(int $invoiceSubtypeId, int $invoiceTypeId = InvoiceType::SALES_INVOICE): DetectedPropertiesContract
    {
        $this->invoiceSubtypeId = $invoiceSubtypeId;

        return $this;
    }

    public function getInvoiceGoodsProperties(): ?InvoiceGoodsPropertiesContract
    {
        return $this->invoiceGoodsProperties;
    }

    public function getDeemedDetected(): ?DetectedInvoiceGoodsProperties
    {
        return $this->deemedDetected;
    }

    public function setDeemedDetected(?DetectedInvoiceGoodsProperties $deemedDetected): DetectedPropertiesContract
    {
        $this->deemedDetected = $deemedDetected;

        return $this;
    }

    public function hasDeemedDetected(): bool
    {
        return $this->getDeemedDetected() !== null;
    }
}
