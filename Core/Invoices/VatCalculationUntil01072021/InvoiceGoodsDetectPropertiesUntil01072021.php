<?php

namespace App\Core\Invoices\VatCalculationUntil01072021;

use App\Core\Data\Models\InvoiceType;
use Exception;

class InvoiceGoodsDetectPropertiesUntil01072021
{
    private ?DetectedInvoiceGoodsPropertiesUntil01072021 $detectedInvoiceGoodsProperties;

    private ?InvoiceGoodsPropertiesUntil01072021 $invoiceGoodsProperties;

    /**
     * @param InvoiceGoodsPropertiesUntil01072021 $invoiceGoodsProperties
     */
    public function setInvoiceGoodsProperties(InvoiceGoodsPropertiesUntil01072021 $invoiceGoodsProperties): void
    {
        $this->invoiceGoodsProperties = $invoiceGoodsProperties;
    }

    /**
     * @return DetectedInvoiceGoodsPropertiesUntil01072021
     */
    public function getDetectedInvoiceGoodsProperties(): DetectedInvoiceGoodsPropertiesUntil01072021
    {
        return $this->detectedInvoiceGoodsProperties;
    }

    /**
     * @throws Exception
     */
    public function detect(): void
    {
        $invoiceTypeId = $this->invoiceGoodsProperties->getInvoiceTypeId();

        $types = [
            InvoiceType::SALES_INVOICE     => SalesInvoice::class,
            InvoiceType::CREDIT_NOTE       => CreditNoteInvoice::class,
            InvoiceType::PURCHASE_INVOICE  => PurchaseInvoice::class,
            InvoiceType::PRO_FORMA_INVOICE => ProFormaInvoice::class,
            InvoiceType::DEBIT_NOTE        => DebitNoteInvoice::class
        ];

        if (!array_key_exists($invoiceTypeId, $types)) {
            throw new Exception('Non-exiting InvoiceType');
        }

        $data = new $types[$invoiceTypeId]($this->invoiceGoodsProperties);

        $this->detectedInvoiceGoodsProperties = $data->getDetectedInvoiceGoodsProperties();
    }
}
