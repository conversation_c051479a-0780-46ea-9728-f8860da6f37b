<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\Currency;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class InvoiceSubtotalData implements Arrayable, Jsonable
{
    private Invoice $invoice;

    private float $sumNet = 0.0;
    private float $sumVat = 0.0;
    private string $prefix;

    private Collection $subtotals;

    private Collection $exchangeRates;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->prefix = $invoice->invoiceSubtype->invoice_type_id === InvoiceType::CREDIT_NOTE ? '-' : '';
        $this->exchangeRates = $invoice->exchangeRates->keyBy('currency_id');

        $this->resolve();
    }

    private function resolve(): void
    {
        $this->subtotals = $this->getInvoice()
            ->invoiceItems
            ->groupBy('vat_percent')
            ->map(function (Collection $invoiceItems) {
                /**
                 * @var Collection<\App\Core\Data\Models\InvoiceItem> $invoiceItems
                 */
                $percentageData = new InvoiceSubtotalPercentageData(
                    $this->invoice,
                    $invoiceItems,
                );
                $this->sumNet = $this->sumNet + $percentageData->getNet();
                $this->sumVat = $this->sumVat + $percentageData->getVat();

                return $percentageData;
            });
    }

    /**
     * @return float
     */
    public function getInvoiceTotal(): float
    {
        return $this->invoice->total;
    }

    /**
     * @return string
     */
    public function getInvoiceTotalWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getInvoiceTotal(), $this->invoice->currency->code);
    }

    /**
     * @return Invoice
     */
    public function getInvoice(): Invoice
    {
        return $this->invoice;
    }

    /**
     * @return float
     */
    public function getSumNet(): float
    {
        return $this->sumNet;
    }

    /**
     * @return float
     */
    public function getSumVat(): float
    {
        return $this->sumVat;
    }

    /**
     * @return string
     */
    public function getSumNetWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getSumNet(), $this->invoice->currency->code);
    }

    /**
     * @return string
     */
    public function getSumVatWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getSumVat(), $this->invoice->currency->code);
    }

    /**
     * @return bool
     */
    public function exchangeToEurVisible(): bool
    {
        return !is_null($this->getSumNetExchangedToEurWithCurrency());
    }

    /**
     * @return string|null
     */
    public function getSumNetExchangedToEurWithCurrency(): ?string
    {
        if ($this->invoice->sales_currency_id === Currency::EUR) {
            return null;
        }

        if (!$this->invoice->invoiceSubtype->convert_to_eur) {
            return null;
        }

        $value = $this->getSumNet();
        $value = $this->convertToEur($value);
        $code = $this->exchangeRates->get(Currency::EUR)->currency->code;

        return $this->prefix . evat_money($value, $code);
    }

    /**
     * @return string|null
     */
    public function getSumVatExchangedToEurWithCurrency(): ?string
    {
        if ($this->invoice->sales_currency_id === Currency::EUR) {
            return null;
        }

        if (!$this->invoice->invoiceSubtype->convert_to_eur) {
            return null;
        }

        $value = $this->getSumVat();
        $value = $this->convertToEur($value);
        $code = $this->exchangeRates->get(Currency::EUR)->currency->code;

        return $this->prefix . evat_money($value, $code);
    }

    /**
     * @return bool
     */
    public function exchangeToGbpVisible(): bool
    {
        return !is_null($this->getSumNetExchangedToGbpWithCurrency());
    }

    /**
     * @return string|null
     */
    public function getSumNetExchangedToGbpWithCurrency(): ?string
    {
        if ($this->invoice->sales_currency_id === Currency::GBP) {
            return null;
        }

        if (!$this->invoice->invoiceSubtype->convert_to_gbp) {
            return null;
        }

        if ($this->invoice->tax_reporting_country_id !== Country::GB) {
            return null;
        }

        $value = $this->getSumNet();
        $value = $this->convertToEur($value);
        $rate = $this->exchangeRates->get(Currency::GBP)->value;
        $value = $value * $rate;
        $code = $this->exchangeRates->get(Currency::GBP)->currency->code;

        return $this->prefix . evat_money($value, $code);
    }

    /**
     * @return string|null
     */
    public function getSumVatExchangedToGbpWithCurrency(): ?string
    {
        if ($this->invoice->sales_currency_id === Currency::GBP) {
            return null;
        }

        if (!$this->invoice->invoiceSubtype->convert_to_gbp) {
            return null;
        }

        if ($this->invoice->tax_reporting_country_id !== Country::GB) {
            return null;
        }

        $value = $this->getSumVat();
        $value = $this->convertToEur($value);
        $code = $this->exchangeRates->get(Currency::GBP)->currency->code;

        return $this->prefix . evat_money($value, $code);
    }

    private function convertToEur(float $value): float
    {
        $rate = $this->exchangeRates
            ->get($this->invoice->sales_currency_id);

        return $value / $rate->value;
    }

    /**
     * @return Collection<InvoiceSubtotalPercentageData>
     */
    public function getSubtotals(): Collection
    {
        return $this->subtotals;
    }

    public function toArray(): array
    {
        return [
            'sumVat'                           => $this->getSumVat(),
            'sumNet'                           => $this->getSumNet(),
            'sumVatWithCurrency'               => $this->getSumVatWithCurrency(),
            'sumNetWithCurrency'               => $this->getSumNetWithCurrency(),
            'subtotals'                        => $this->getSubtotals()->toArray(),
            'invoiceTotal'                     => $this->getInvoiceTotal(),
            'invoiceTotalWithCurrency'         => $this->getInvoiceTotalWithCurrency(),
            'exchangeToEurVisible'             => $this->exchangeToEurVisible(),
            'sumNetExchangedToEurWithCurrency' => $this->getSumNetExchangedToEurWithCurrency(),
            'sumVatExchangedToEurWithCurrency' => $this->getSumVatExchangedToEurWithCurrency(),
            'exchangeToGbpVisible'             => $this->exchangeToGbpVisible(),
            'sumNetExchangedToGbpWithCurrency' => $this->getSumNetExchangedToGbpWithCurrency(),
            'sumVatExchangedToGbpWithCurrency' => $this->getSumVatExchangedToGbpWithCurrency()
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
