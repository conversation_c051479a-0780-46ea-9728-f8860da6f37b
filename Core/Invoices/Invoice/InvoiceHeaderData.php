<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Currency;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\Marketplace;
use App\Core\Data\Models\PaymentType;
use App\Core\Data\Models\Platform;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Web\Invoices\Transformers\ItemTypeTransformer;
use App\Web\Invoices\Transformers\MarketplaceTransformer;
use App\Web\Invoices\Transformers\PlatformTransformer;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class InvoiceHeaderData implements Arrayable, Jsonable
{
    private Invoice $invoice;

    private bool $isTitleAndLogoLineVisible = false;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function getInvoiceSubtypeName(): string
    {
        return $this->invoice->invoiceSubtype->name;
    }

    public function getInvoiceSubtype(): ?InvoiceSubtype
    {
        return $this->invoice->invoiceSubtype;
    }

    public function getInvoiceType(): ?InvoiceType
    {
        return $this->getInvoiceSubtype()?->invoiceType;
    }

    public function getPaymentType(): ?PaymentType
    {
        return $this->invoice->paymentType;
    }

    public function getPaymentTypeArray(): ?array
    {
        $type = $this->getPaymentType();
        if (is_null($type)) {
            return null;
        }

        return [
            'id'    => $type->id,
            'name'  => $type->full_name,
            'value' => $type->id,
            'label' => $type->full_name,
        ];
    }

    public function getInvoiceTypeArray(): ?array
    {
        $type = $this->getInvoiceType();
        if (is_null($type)) {
            return null;
        }

        return [
            'id'    => $type->id,
            'name'  => $type->name,
            'value' => $type->id,
            'label' => $type->name,
        ];
    }

    public function getInvoiceSubtypeArray(): ?array
    {
        $subtype = $this->getInvoiceSubtype();
        if (is_null($subtype)) {
            return null;
        }

        return [
            'id'    => $subtype->id,
            'name'  => _l($subtype->name),
            'value' => $subtype->id,
            'label' => _l($subtype->name),
        ];
    }

    public function getInvoiceTypeNumber(): string
    {
        $name = mb_strtolower($this->getInvoiceTypeName());
        $name = ucfirst($name) . ' number';

        return _l($name);
    }

    public function getInvoiceTypeName(): string
    {
        return $this->getInvoiceType()?->name ?? '';
    }

    public function getItemTypeName(): string
    {
        return _l($this->getItemType()->name);
    }

    public function getItemType(): ItemType
    {
        return $this->invoice->itemType;
    }

    public function getItemTypeArray(): array
    {
        return transform_data($this->getItemType(), new ItemTypeTransformer())->toArray();
    }

    public function getInvoiceCurrency(): Currency
    {
        return $this->invoice->currency;
    }

    public function getInvoiceCurrencyArray(): array
    {
        $currency = $this->getInvoiceCurrency();

        return [
            'id'     => $currency->id,
            'name'   => $currency->name,
            'value'  => $currency->id,
            'label'  => $currency->name,
            'symbol' => $currency->symbol,
            'code'   => $currency->code
        ];
    }

    /**
     * @return string
     */
    public function getInvoiceCurrencyWithCode(): string
    {
        $currency = $this->invoice->currency;

        return $currency->name . ' (' . $currency->code . '/' . $currency->symbol . ')';
    }

    public function getInvoiceLabel(): string
    {
        if ($this->invoice->tax_collection_responsibility_id === TaxCollectionResponsibility::MARKETPLACE && $this->getItemType()?->id === ItemType::GOODS) {
            return _l('invoices.invoice tax collection responsibility marketplace');
        }

        return $this->getInvoiceTypeName();
    }

    public function getInvoiceNumber(): ?string
    {
        return $this->invoice->invoice_number;
    }

    /**
     * @return string
     */
    public function getInvoiceDate(): string
    {
        return $this->parseDateCarbon($this->invoice->invoice_date)
            ->format(_l_date_format('date'));
    }

    public function getInvoiceDateRaw(): string
    {
        return $this->parseDateCarbon($this->invoice->invoice_date)->toDateString();
    }

    /**
     * @return ?string
     */
    public function getInvoiceOrderNumber(): ?string
    {
        return $this->invoice->order_number;
    }

    /**
     * @return string
     */
    public function getInvoiceOrderDate(): string
    {
        return $this->parseDateCarbon($this->invoice->order_date)
            ->format(_l_date_format('date'));
    }

    public function getInvoiceOrderDateRaw(): string
    {
        return $this->parseDateCarbon($this->invoice->order_date);
    }

    /**
     * @return string
     */
    public function getInvoiceDueDate(): string
    {
        return $this->parseDateCarbon($this->invoice->invoice_date)
            ->format(_l_date_format('date'));
    }

    public function getInvoiceDueDateRaw(): string
    {
        return $this->parseDateCarbon($this->invoice->invoice_date)->toDateString();
    }

    public function getInvoiceMarketplace(): ?Marketplace
    {
        return $this->invoice->marketplace;
    }

    public function getInvoiceMarketplaceArray(): ?array
    {
        $marketplace = $this->getInvoiceMarketplace();
        if (is_null($marketplace)) {
            return null;
        }

        return transform_data($marketplace, new MarketplaceTransformer())->toArray();
    }

    public function getInvoicePlatform(): ?Platform
    {
        return $this->getInvoiceMarketplace()?->platform;
    }

    public function getInvoicePlatformArray(): ?array
    {
        $platform = $this->getInvoicePlatform();
        if (is_null($platform)) {
            return null;
        }

        return transform_data($platform, new PlatformTransformer())->toArray();
    }

    /**
     * @return bool
     */
    public function isTitleAndLogoLineVisible(): bool
    {
        return $this->isTitleAndLogoLineVisible;
    }

    /**
     * @param bool $isTitleAndLogoBarVisible
     */
    public function setTitleAndLogoLineVisible(bool $isTitleAndLogoBarVisible): void
    {
        $this->isTitleAndLogoLineVisible = $isTitleAndLogoBarVisible;
    }

    public function getConnectedDeemedIssuedStatusInvoice(): ?Invoice
    {
        return $this->invoice->connectedDeemedIssuedStatusInvoice;
    }

    public function hasConnectedDeemedIssuedStatusInvoice(): bool
    {
        return $this->getConnectedDeemedIssuedStatusInvoice() !== null;
    }

    public function getConnectedDeemedSuppliesStatusInvoice(): ?Invoice
    {
        return $this->invoice->connectedDeemedSuppliesStatusInvoice;
    }

    public function hasConnectedDeemedSuppliesStatusInvoice(): bool
    {
        return $this->getConnectedDeemedSuppliesStatusInvoice() !== null;
    }

    private function parseDateCarbon(string $date): Carbon
    {
        return Carbon::parse($date);
    }

    public function toArray(): array
    {
        return [
            'invoiceLabel'                         => $this->getInvoiceLabel(),
            'invoiceNumber'                        => $this->getInvoiceNumber(),
            'invoiceDate'                          => $this->getInvoiceDate(),
            'invoiceDateRaw'                       => $this->getInvoiceDateRaw(),
            'invoiceDueDate'                       => $this->getInvoiceDueDate(),
            'invoiceDueDateRaw'                    => $this->getInvoiceDueDateRaw(),
            'invoiceSubtype'                       => $this->getInvoiceSubtypeArray(),
            'invoiceOrderDate'                     => $this->getInvoiceOrderDate(),
            'invoiceOrderDateRaw'                  => $this->getInvoiceOrderDateRaw(),
            'invoiceCurrency'                      => $this->getInvoiceCurrencyArray(),
            'invoiceOrderNumber'                   => $this->getInvoiceOrderNumber(),
            'invoicePlatform'                      => $this->getInvoicePlatformArray(),
            'invoiceMarketplace'                   => $this->getInvoiceMarketplaceArray(),
            'invoiceType'                          => $this->getInvoiceTypeArray(),
            'itemType'                             => $this->getItemTypeArray(),
            'paymentType'                          => $this->getPaymentTypeArray(),
            'connectedDeemedSuppliesStatusInvoice' => $this->getConnectedDeemedSuppliesStatusInvoice()?->toArray(),
            'connectedDeemedIssuedStatusInvoice'   => $this->getConnectedDeemedIssuedStatusInvoice()?->toArray(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
