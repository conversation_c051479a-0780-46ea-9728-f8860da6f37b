<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class InvoicePromotionData implements Arrayable, Jsonable
{
    private Invoice $invoice;

    private float $net = 0;
    private float $gross = 0;
    private int $multiplicator;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->multiplicator = $invoice->invoiceSubtype->invoice_type_id === InvoiceType::CREDIT_NOTE ? 1 : -1;
    }

    /**
     * @return float
     */
    public function getNet(): float
    {
        if ($this->net <= 0) {
            return $this->net;
        }

        return $this->net * $this->multiplicator;
    }

    /**
     * @return float
     */
    public function getGross(): float
    {
        if ($this->gross <= 0) {
            return $this->gross;
        }

        return $this->gross * $this->multiplicator;
    }

    /**
     * @param float $net
     * @return InvoicePromotionData
     */
    public function setNet(float $net): InvoicePromotionData
    {
        $this->net = $net;

        return $this;
    }

    /**
     * @param float $gross
     * @return InvoicePromotionData
     */
    public function setGross(float $gross): InvoicePromotionData
    {
        $this->gross = $gross;

        return $this;
    }

    /**
     * @return string
     */
    public function getNetWithCurrency(): string
    {
        return evat_money($this->getNet(), $this->invoice->currency->code);
    }

    /**
     * @return string
     */
    public function getGrossWithCurrency(): string
    {
        return evat_money($this->getGross(), $this->invoice->currency->code);
    }

    public function toArray(): array
    {
        return [
            'grossWithCurrency' => $this->getGrossWithCurrency(),
            'netWithCurrency'   => $this->getNetWithCurrency(),
            'gross'             => $this->getGross(),
            'net'               => $this->getNet(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
