<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Invoice;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class InvoiceData implements Arrayable, Jsonable
{
    private Invoice $invoice;
    private InvoiceHeaderData $invoiceHeaderData;
    private InvoiceMainData $invoiceMainData;
    private InvoiceFooterData $invoiceFooterData;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->invoiceHeaderData = new InvoiceHeaderData($invoice);
        $this->invoiceMainData = new InvoiceMainData($invoice);
        $this->invoiceFooterData = new InvoiceFooterData($invoice);
    }

    /**
     * @return InvoiceHeaderData
     */
    public function getHeaderData(): InvoiceHeaderData
    {
        return $this->invoiceHeaderData;
    }

    /**
     * @return InvoiceMainData
     */
    public function getMainData(): InvoiceMainData
    {
        return $this->invoiceMainData;
    }

    /**
     * @return InvoiceFooterData
     */
    public function getFooterData(): InvoiceFooterData
    {
        return $this->invoiceFooterData;
    }

    /**
     * @param bool $isTitleAndLogoBarVisible
     * @return $this
     */
    public function setTitleAndLogoLineVisible(bool $isTitleAndLogoBarVisible): InvoiceData
    {
        $this->invoiceHeaderData->setTitleAndLogoLineVisible($isTitleAndLogoBarVisible);

        return $this;
    }

    public function getInvoice(): Invoice
    {
        return $this->invoice;
    }

    private function getInvoiceArray(): array
    {
        $invoice = $this->getInvoice()->toArray();
        $fields = Invoice::getAllTableColumns();

        foreach ($fields as $field) {
            $invoice[$field] = $invoice[$field] ?? null;
        }

        return $invoice;
    }

    public function toArray(): array
    {
        return [
            'invoice'    => $this->getInvoiceArray(),
            'headerData' => $this->getHeaderData()->toArray(),
            'mainData'   => $this->getMainData()->toArray(),
            'footerData' => $this->getFooterData()->toArray(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
