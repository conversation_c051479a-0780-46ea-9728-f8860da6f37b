<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Invoice;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class InvoiceFooterData implements Arrayable, Jsonable
{
    private Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function getCompanyId(): int
    {
        return $this->invoice->company_id;
    }

    /**
     * @return string
     */
    public function getCompanyFullLegalName(): string
    {
        return $this->invoice->company->full_legal_name;
    }

    /**
     * @return string|null
     */
    public function getCompanyRegistrationNumber(): ?string
    {
        return $this->invoice->company->registration_number;
    }

    /**
     * @return bool
     */
    public function hasCompanyRegistrationNumber(): bool
    {
        return !is_null($this->getCompanyRegistrationNumber());
    }

    /**
     * @return string|null
     */
    public function getCompanyVatNumber(): ?string
    {
        $vatNumbers = $this->invoice
            ->company
            ->vatNumbers
            ->keyBy('country_id');
        $addressCountryId = $this->invoice
            ->company
            ->address
            ->country_id;

        return $vatNumbers->get($addressCountryId)?->vat_number;
    }

    /**
     * @return string
     */
    public function getCompanyAddress(): string
    {
        return $this->invoice->company->address->display_address;
    }

    /**
     * @return bool
     */
    public function hasCompanyVatNumber(): bool
    {
        return !is_null($this->getCompanyVatNumber());
    }

    /**
     * @return string|null
     */
    public function getBankName(): ?string
    {
        return $this->invoice->company->bank?->name;
    }

    /**
     * @return string|null
     */
    public function getBankIban(): ?string
    {
        return $this->invoice->company->bank?->iban;
    }

    /**
     * @return string|null
     */
    public function getBankSwift(): ?string
    {
        return $this->invoice->company->bank?->swift;
    }

    /**
     * @return bool
     */
    public function hasBank(): bool
    {
        return !is_null($this->invoice->company->bank);
    }

    public function toArray()
    {
        return [
            'companyId'                    => $this->getCompanyId(),
            'companyFullLegalName'         => $this->getCompanyFullLegalName(),
            'companyRegistrationNumber'    => $this->getCompanyRegistrationNumber(),
            'hasCompanyRegistrationNumber' => $this->hasCompanyRegistrationNumber(),
            'companyVatNumber'             => $this->getCompanyVatNumber(),
            'companyAddress'               => $this->getCompanyAddress(),
            'hasCompanyVatNumber'          => $this->hasCompanyVatNumber(),
            'bankName'                     => $this->getBankName(),
            'bankIban'                     => $this->getBankIban(),
            'bankSwift'                    => $this->getBankSwift(),
            'hasBank'                      => $this->hasBank(),
        ];
    }

    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }
}
