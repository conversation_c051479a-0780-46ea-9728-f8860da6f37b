<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceItem;
use App\Core\Data\Models\InvoiceType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class InvoiceSubtotalPercentageData implements Arrayable, Jsonable
{
    private Invoice $invoice;
    private float $net;
    private float $vat;
    private string $percentage;
    private string $prefix;

    /**
     * @param \App\Core\Data\Models\Invoice $invoice
     * @param \Illuminate\Support\Collection<InvoiceItem> $invoiceItemsByVatRate
     */
    public function __construct(Invoice $invoice, Collection $invoiceItemsByVatRate)
    {
        $this->invoice = $invoice;
        $this->prefix = $invoice->invoiceSubtype->invoice_type_id === InvoiceType::CREDIT_NOTE ? '-' : '';

        $this->percentage = $invoiceItemsByVatRate
            ->first()
            ->vat_percentage ?? '0%';

        $net = 0;
        $vat = 0;
        foreach ($invoiceItemsByVatRate as $invoiceItem) {
            /**
             * @var InvoiceItem $invoiceItem
             */
            $net = $net + $invoiceItem->getNetTotalDiscountApplied();
            $vat = $vat + $invoiceItem->getVatAmountTotalDiscountApplied();
        }

        $this->net = $net;
        $this->vat = $vat;
    }

    /**
     * @return float
     */
    public function getNet(): float
    {
        return $this->net;
    }

    /**
     * @return float
     */
    public function getVat(): float
    {
        return $this->vat;
    }

    /**
     * @return string
     */
    public function getNetWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getNet(), $this->invoice->currency->code);
    }

    /**
     * @return string
     */
    public function getVatWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getVat(), $this->invoice->currency->code);
    }

    /**
     * @return string
     */
    public function getPercentage(): string
    {
        return $this->percentage;
    }

    public function toArray(): array
    {
        return [
            'percentage'        => $this->getPercentage(),
            'grossWithCurrency' => $this->getVatWithCurrency(),
            'netWithCurrency'   => $this->getNetWithCurrency(),
            'gross'             => $this->getVat(),
            'net'               => $this->getNet(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
