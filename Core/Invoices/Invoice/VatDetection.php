<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\ItemType;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use App\Core\Invoices\VatCalculation\InvoicePropertiesDetector;
use App\Core\Invoices\VatCalculation\Properties\InvoiceGoodsProperties;
use App\Core\Invoices\VatCalculationUntil01072021\InvoiceGoodsDetectPropertiesUntil01072021;
use App\Core\Invoices\VatCalculationUntil01072021\InvoiceGoodsPropertiesUntil01072021;
use Illuminate\Support\Collection;

/**
 * This class must be instantiated on every use
 *
 * @warning - Methods only
 */
final class VatDetection
{
    public function detectAfterFirstOfJuly21(
        string $calculationDate,
        int $companyCountryId,
        bool $isOnlyVatNumberAndWarehouseInCompanyCountry,
        bool $isCompanyCountryVatNumberPermanentEstablishment,
        bool $whetherTheCompanyHasPassedThreshold,

        int $shipFromCountryId,

        int $billToCountryId,
        bool $isDomesticReverseChargeApplicableInBillToCountry,

        int $platformTypeId,
        int $invoiceTypeId,
        int $itemTypeId = ItemType::GOODS,

        ?bool $isCompanyRegisteredForOss = null,
        ?int $companyOssRegistrationCountryId = null,
        ?bool $isCompanyRegisteredForIoss = null,
        ?int $companyIossRegistrationCountryId = null,

        ?bool $isMarketplaceRegisteredForOss = null,
        ?int $marketplaceOssRegistrationCountryId = null,
        ?bool $isMarketplaceRegisteredForIoss = null,
        ?int $marketplaceIossRegistrationCountryId = null,
        ?bool $isMarketplaceIncorporatedInEu = null,

        ?bool $isSaleAbove150Euro = null,
        ?bool $isSaleAbove135GBP = null,

        ?int $shipToCountryId = null,
        ?bool $isShipToVatNumberValid = null,

        ?bool $isCompanyRegisteredForVoes = null,
        ?int $companyVoesRegistrationCountryId = null,
        ?bool $isMarketplaceRegisteredForVoes = null,
        ?int $marketplaceVoesRegistrationCountryId = null,

        ?bool $isCompanyShipFromCountryVatNumberPermanentEstablishment = null,
        ?bool $hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry = null,

        ?bool $isCompanyShipToCountryVatNumberPermanentEstablishment = null,
        ?bool $doesCompanyHaveVatNumberInShipToCountry = null,
        ?int $deliveryConditionId = null,
    ): DetectedPropertiesContract {
        $isShipToVatNumberValid = $isShipToVatNumberValid ?? false;

        $invoiceGoodsProperties = new InvoiceGoodsProperties();

        $invoiceGoodsProperties->setCalculationDate($calculationDate);
        $invoiceGoodsProperties->setCompanyCountryId($companyCountryId);
        $invoiceGoodsProperties->setShipFromCountryId($shipFromCountryId);
        $invoiceGoodsProperties->setBillToCountryId($billToCountryId);
        $invoiceGoodsProperties->setDomesticReverseChargeApplicableInBillToCountry($isDomesticReverseChargeApplicableInBillToCountry);
        $invoiceGoodsProperties->setOnlyVatNumberAndWarehouseInCompanyCountry($isOnlyVatNumberAndWarehouseInCompanyCountry);
        $invoiceGoodsProperties->setCompanyPassedThreshold($whetherTheCompanyHasPassedThreshold);
        $invoiceGoodsProperties->setPlatformTypeId($platformTypeId);
        $invoiceGoodsProperties->setInvoiceTypeId($invoiceTypeId);
        $invoiceGoodsProperties->setItemTypeId($itemTypeId);
        $invoiceGoodsProperties->setIsCompanyCountryVatNumberPermanentEstablishment($isCompanyCountryVatNumberPermanentEstablishment);
        $invoiceGoodsProperties->setShipToVatNumberValid($isShipToVatNumberValid);

        if (!is_null($deliveryConditionId)) {
            $invoiceGoodsProperties->setDeliveryConditionId($deliveryConditionId);
        }

        if (!is_null($doesCompanyHaveVatNumberInShipToCountry)) {
            $invoiceGoodsProperties->setDoesCompanyHaveVatNumberInShipToCountry($doesCompanyHaveVatNumberInShipToCountry);
        }

        if (!is_null($isCompanyShipToCountryVatNumberPermanentEstablishment)) {
            $invoiceGoodsProperties->setIsCompanyShipToCountryVatNumberPermanentEstablishment($isCompanyShipToCountryVatNumberPermanentEstablishment);
        }

        if (!is_null($isCompanyRegisteredForOss)) {
            $invoiceGoodsProperties->setCompanyRegisteredForOss($isCompanyRegisteredForOss);
            if ($isCompanyRegisteredForOss && !is_null($companyOssRegistrationCountryId)) {
                $invoiceGoodsProperties->setCompanyOssRegistrationCountryId($companyOssRegistrationCountryId);
            }
        }

        if (!is_null($isMarketplaceRegisteredForOss)) {
            $invoiceGoodsProperties->setMarketplaceRegisteredForOSS($isMarketplaceRegisteredForOss);
            if ($isMarketplaceRegisteredForOss && !is_null($marketplaceOssRegistrationCountryId)) {
                $invoiceGoodsProperties->setMarketplaceOssCountryId($marketplaceOssRegistrationCountryId);
            }
        }

        if (!is_null($isCompanyRegisteredForIoss)) {
            $invoiceGoodsProperties->setCompanyRegisteredForIoss($isCompanyRegisteredForIoss);
            if ($isCompanyRegisteredForIoss && !is_null($companyIossRegistrationCountryId)) {
                $invoiceGoodsProperties->setCompanyIossRegistrationCountryId($companyIossRegistrationCountryId);
            }
        }

        if (!is_null($isMarketplaceRegisteredForIoss)) {
            $invoiceGoodsProperties->setMarketplaceRegisteredForIOSS($isMarketplaceRegisteredForIoss);
            if ($isMarketplaceRegisteredForIoss && !is_null($marketplaceIossRegistrationCountryId)) {
                $invoiceGoodsProperties->setMarketplaceIossRegistrationCountryId($marketplaceIossRegistrationCountryId);
            }
        }

        if (!is_null($isSaleAbove150Euro)) {
            $invoiceGoodsProperties->setSaleTransactionAbove150Euro($isSaleAbove150Euro);
        }

        if (!is_null($isSaleAbove135GBP)) {
            $invoiceGoodsProperties->setSaleTransactionAbove135GBP($isSaleAbove135GBP);
        }

        if (!is_null($isMarketplaceIncorporatedInEu)) {
            $invoiceGoodsProperties->setMarketplaceIncorporatedInEu($isMarketplaceIncorporatedInEu);
        }

        if (!is_null($shipToCountryId) && $isShipToVatNumberValid) {
            $invoiceGoodsProperties->setShipToCountryId($shipToCountryId);
        }

        if (!is_null($isCompanyRegisteredForVoes)) {
            $invoiceGoodsProperties->setIsCompanyRegisteredForVoes($isCompanyRegisteredForVoes);
        }

        if (!is_null($companyVoesRegistrationCountryId)) {
            $invoiceGoodsProperties->setCompanyVoesRegistrationCountryId($companyVoesRegistrationCountryId);
        }

        if (!is_null($isMarketplaceRegisteredForVoes)) {
            $invoiceGoodsProperties->setIsMarketplaceRegisteredForVoes($isMarketplaceRegisteredForVoes);
        }

        if (!is_null($marketplaceVoesRegistrationCountryId)) {
            $invoiceGoodsProperties->setMarketplaceVoesRegistrationCountryId($marketplaceVoesRegistrationCountryId);
        }

        if (!is_null($isCompanyShipFromCountryVatNumberPermanentEstablishment)) {
            $invoiceGoodsProperties->setIsCompanyShipFromCountryVatNumberPermanentEstablishment($isCompanyShipFromCountryVatNumberPermanentEstablishment);
        }

        if (!is_null($hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry)) {
            $invoiceGoodsProperties->setHasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry($hasOnlyVatNumberAndPermanentEstablishmentInCompanyCountry);
        }

        $invoiceGoodsDetectProperties = new InvoicePropertiesDetector();
        $invoiceGoodsDetectProperties->setInvoiceGoodsProperties($invoiceGoodsProperties);

        $invoiceGoodsDetectProperties->detect();

//        dd(
//            $invoiceGoodsProperties->toArray(),
//            $invoiceGoodsDetectProperties->getDetectedInvoiceGoodsProperties()->toArray()
//        );

        return $invoiceGoodsDetectProperties->getDetectedInvoiceGoodsProperties();
    }

    public function detectBeforeFirstOfJuly21(
        int $companyCountryId,
        int $warehouseCountryId,
        int $recipientCountryId,
        bool $isDomesticReverseChargeApplicableInRecipientCountry,
        bool $isRecipientCountryInEu,
        bool $isWarehouseCountryInEu,
        int $invoiceTypeId,
        bool $whetherTheCompanyHasPassedThreshold,
        bool $hasWaiverInRecipientCountry,
        bool $hasVatNumberInRecipientCountry,
        bool $hasWarehouseInRecipientCountry,
        ?int $buyerVatNumberCountryId = null,
    ): DetectedPropertiesContract {
        $invoiceGoodsProperties = new InvoiceGoodsPropertiesUntil01072021();

        $invoiceGoodsProperties->setCompanyCountryId($companyCountryId);
        $invoiceGoodsProperties->setWarehouseCountryId($warehouseCountryId);
        $invoiceGoodsProperties->setRecipientCountryId($recipientCountryId);
        $invoiceGoodsProperties->setInvoiceTypeId($invoiceTypeId);
        $invoiceGoodsProperties->setHasWaiverInRecipientCountry($hasWaiverInRecipientCountry);
        $invoiceGoodsProperties->setHasVatNumberInRecipientCountry($hasVatNumberInRecipientCountry);
        $invoiceGoodsProperties->setHasWarehouseInRecipientCountry($hasWarehouseInRecipientCountry);
        $invoiceGoodsProperties->setHasPassedThresholdInRecipientCountry($whetherTheCompanyHasPassedThreshold);
        $invoiceGoodsProperties->setIsRecipientCountryInEu($isRecipientCountryInEu);
        $invoiceGoodsProperties->setIsWarehouseCountryInEu($isWarehouseCountryInEu);
        $invoiceGoodsProperties->setDomesticReverseChargeApplicableInRecipientCountry($isDomesticReverseChargeApplicableInRecipientCountry);

        if (!is_null($buyerVatNumberCountryId)) {
            $invoiceGoodsProperties->setBuyerVatNumberCountryId($buyerVatNumberCountryId);
        }

        $invoiceGoodsDetectProperties = new InvoiceGoodsDetectPropertiesUntil01072021();
        $invoiceGoodsDetectProperties->setInvoiceGoodsProperties($invoiceGoodsProperties);

        /** @noinspection PhpUnhandledExceptionInspection */
        $invoiceGoodsDetectProperties->detect();

        return $invoiceGoodsDetectProperties->getDetectedInvoiceGoodsProperties();
    }

    /**
     * @param int $taxCalculationDate - invoice date
     * @param int $companyCountryId
     * @param Collection $vatNumbers
     * @param Collection $warehouses
     * @return bool
     */
    public function isOnlyVatNumberAndWarehouseInCompanyCountry(
        int $taxCalculationDate,
        int $companyCountryId,
        Collection $vatNumbers,
        Collection $warehouses,
    ): bool {

        $vatNumbers = $vatNumbers
            ->filter(function ($vatNumber) use ($taxCalculationDate) {
                $startDate = $vatNumber['register_date'];
                if (is_null($startDate)) {
                    $startDate = $vatNumber['first_time_in_report'];
                }
                if (is_null($startDate)) {
                    return false;
                }

                $endDate = $vatNumber['end_date'];
                $startDate = strtotime($startDate);
                $check = $taxCalculationDate >= $startDate;
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                    $check = $check && ($taxCalculationDate < $endDate);
                }

                return $check;
            });

        if ($vatNumbers->count() !== 1) {
            return false;
        }

        $vatNumberCountryId = $vatNumbers->first()['country_id'];
        if ($vatNumberCountryId !== $companyCountryId) {
            return false;
        }

        $warehouses = $warehouses
            ->filter(function (array $warehouse) use ($taxCalculationDate) {
                if (is_null($warehouse['first_time_used'])) {
                    return false;
                }
                $firstTimeUsed = strtotime($warehouse['first_time_used']);

                return $taxCalculationDate >= $firstTimeUsed;
            });

        if ($warehouses->count() !== 1) {
            return false;
        }

        $warehouseCountryId = $warehouses->first()['warehouse']['country_id'];
        if ($warehouseCountryId !== $companyCountryId) {
            return false;
        }

        return true;
    }

    /**
     * @param int $taxCalculationDate - invoice date
     * @param int $breakpoint
     * @param Collection $thresholds
     * @param array $recipientCountry
     * @return bool
     */
    public function whetherTheCompanyHasPassedThreshold(int $taxCalculationDate, int $breakpoint, Collection $thresholds, array $recipientCountry): bool
    {
        if ($thresholds->count() < 1) {
            return false;
        }

        if ($taxCalculationDate >= $breakpoint) {
            $inEuStartDate = $recipientCountry['in_eu_start_date'];
            $inEuEndDate = $recipientCountry['in_eu_end_date'];
            if (is_null($inEuStartDate)) {
                return true;
            }
            $inEuStartDate = strtotime($inEuStartDate);
            $checkCountryInEu = $taxCalculationDate >= $inEuStartDate;
            if (!is_null($inEuEndDate)) {
                $inEuEndDate = strtotime($inEuEndDate);
                $checkCountryInEu = $checkCountryInEu && ($taxCalculationDate < $inEuEndDate);
            }
            if (!$checkCountryInEu) {
                return true;
            }

            return $thresholds->filter(function ($threshold) use ($taxCalculationDate) {
                    $date = strtotime($threshold['date']);

                    return $taxCalculationDate >= $date;
                })->count() > 0;
        } else {
            $thresholds = $thresholds->filter(function ($threshold) use ($recipientCountry, $taxCalculationDate) {
                if ($threshold['country_id'] !== $recipientCountry['id']) {
                    return false;
                }

                $date = strtotime($threshold['date']);

                return $taxCalculationDate >= $date;
            });

            return $thresholds->count() > 0;
        }
    }

    /** - invoice date
     *
     * @param int $taxCalculationDate
     * @param int $recipientCountryId
     * @param Collection $waivers
     * @return bool
     */
    public function hasWaiverInRecipientCountry(int $taxCalculationDate, int $recipientCountryId, Collection $waivers): bool
    {
        $waivers = $waivers
            ->filter(function (array $waiver) use ($taxCalculationDate) {
                $startDate = strtotime($waiver['start_date']);
                $endDate = $waiver['end_date'];
                $check = $taxCalculationDate >= $startDate;
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                    $check = $check && ($taxCalculationDate < $endDate);
                }

                return $check;
            })
            ->pluck('country_id')
            ->toArray();

        return in_array($recipientCountryId, $waivers);
    }

    /** - invoice date
     *
     * @param int $taxCalculationDate
     * @param int $recipientCountryId
     * @param Collection $vatNumbers
     * @return bool
     */
    public function hasVatNumberInRecipientCountry(int $taxCalculationDate, int $recipientCountryId, Collection $vatNumbers): bool
    {
        $vatNumbers = $vatNumbers
            ->filter(function (array $vatNumber) use ($taxCalculationDate) {
                $startDate = $vatNumber['register_date'];
                if (is_null($startDate)) {
                    $startDate = $vatNumber['first_time_in_report'];
                }
                if (is_null($startDate)) {
                    return false;
                }

                $startDate = strtotime($startDate);
                $endDate = $vatNumber['end_date'];
                $check = $taxCalculationDate >= $startDate;
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                    $check = $check && ($taxCalculationDate < $endDate);
                }

                return $check;
            })
            ->pluck('country_id')
            ->toArray();

        return in_array($recipientCountryId, $vatNumbers);
    }

    /**
     *
     * @param int $taxCalculationDate
     * @param int $recipientCountryId
     * @param array $warehouses
     * @return bool
     */
    public function hasWarehouseInRecipientCountry(int $taxCalculationDate, int $recipientCountryId, array $warehouses): bool
    {
        $warehouses = array_filter($warehouses, function (array $warehouse) use ($taxCalculationDate) {
            $startDate = strtotime($warehouse['first_time_used']);

            return $taxCalculationDate >= $startDate;
        });

        return in_array($recipientCountryId, $warehouses);
    }
}
