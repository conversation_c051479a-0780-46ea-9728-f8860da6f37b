<?php

namespace App\Core\Invoices\Invoice\DataTransfer;

use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use App\Core\Invoices\Invoice\InvoiceData;
use Illuminate\Contracts\Support\Arrayable;

readonly class RecalculatedDataTransfer implements Arrayable
{
    public function __construct(private InvoiceData $invoiceData, private DetectedPropertiesContract $detected)
    {
    }

    public function getInvoiceData(): InvoiceData
    {
        return $this->invoiceData;
    }

    public function getDetected(): DetectedPropertiesContract
    {
        return $this->detected;
    }

    public function toArray(): array
    {
        return [
            'invoiceData' => $this->getInvoiceData()->toArray(),
            'detected'    => $this->getDetected()->toArray(),
        ];
    }
}
