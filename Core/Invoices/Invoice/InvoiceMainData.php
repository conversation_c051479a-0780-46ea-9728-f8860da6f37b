<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Customer;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceItem;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\IossNumber;
use App\Core\Data\Models\IossNumberType;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\OssNumber;
use App\Core\Data\Models\OssNumberType;
use App\Core\Data\Models\PlatformType;
use App\Core\Data\Models\Supplier;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Invoices\Transformers\CountryTransformer;
use App\Core\Invoices\Transformers\InvoiceItemTransformer;
use App\Core\Invoices\Transformers\SupplierMultiselectTransformer;
use App\Core\VatNumbers\Api\VatNumberApi;
use App\Web\Invoices\Transformers\CustomerTransformer;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class InvoiceMainData implements Arrayable, Jsonable
{
    private Invoice $invoice;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function getCustomer(): ?Customer
    {
        return $this->invoice->customer;
    }

    public function getCustomerArray(): ?array
    {
        $customer = $this->getCustomer();
        if (is_null($customer)) {
            return null;
        }

        return transform_data($customer, new CustomerTransformer())->toArray();
    }

    /**
     * @return string|null
     */
    public function getBillToName(): ?string
    {
        if ($this->invoice->invoiceSubtype->invoice_type_id === InvoiceType::PRO_FORMA_INVOICE) {
            return $this->getBillFromCompanyFullName();
        }

        return $this->invoice->bill_to_name;
    }

    /**
     * @return bool
     */
    public function hasBillToName(): bool
    {
        return !is_null($this->getBillToName());
    }

    /**
     * @return string|null
     */
    public function getBillToAddress(): ?string
    {
        return $this->invoice->bill_to_address;
    }

    /**
     * @return bool
     */
    public function hasBillToAddress(): bool
    {
        return !is_null($this->getBillToAddress());
    }

    /**
     * @return string|null
     */
    public function getBillingAddressCustomerVatNumber(): ?string
    {
        return $this->invoice->bill_to_vat_number;
    }

    public function getShipToVatNumber(): array
    {
        return $this->getTransformedVatNumber($this->invoice->ship_to_vat_number);
    }

    public function getTaxReportingScheme(): ?TaxReportingScheme
    {
        return $this->invoice->taxReportingScheme;
    }

    public function getTaxReportingSchemeArray(): ?array
    {
        $scheme = $this->getTaxReportingScheme();
        if (is_null($scheme)) {
            return null;
        }
        $description = $scheme->description;
        if (!is_null($description)) {
            $description = _l($description);
        }

        return [
            'id'          => $scheme->id,
            'name'        => _l($scheme->name),
            'value'       => $scheme->id,
            'label'       => _l($scheme->name),
            'description' => $description,
            'amazonCode'  => $scheme->amazon_code,
        ];
    }

    public function getInvoiceDescription(): ?string
    {
        return $this->invoice->description;
    }

    public function getTaxReportingSchemeId(): ?int
    {
        return $this->invoice->tax_reporting_scheme_id;
    }

    public function getIntrinsicValue(): float
    {
        $intrinsicValue = $this->getInvoiceItems()->reduce(function ($carry, $invoiceItem) {
            return $carry - ($invoiceItem['discount_net'] ?? 0.0) + $invoiceItem['qty'] * $invoiceItem['item_sales_price_net'];
        }, 0);

        return round($intrinsicValue, 2, PHP_ROUND_HALF_EVEN);
    }

    public function showIntrinsicValue(): ?string
    {
        if ($this->shouldDisplayIossNumber() || ($this->getTaxReportingSchemeId() === TaxReportingScheme::UK_VOEC_IMPORT && $this->getPlatformTypeId() === PlatformType::MARKETPLACE)) {
            return evat_money($this->getIntrinsicValue(), $this->invoice->currency->code);
        }

        return null;
    }

    public function getPlatformTypeId(): ?int
    {
        return $this->invoice->marketplace?->platform->platform_type_id;
    }

    public function getItemTypeId(): int
    {
        return $this->invoice->item_type_id;
    }

    public function ossNumber(): ?string
    {
        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::AMAZON_DEEMED_RESELLER && $this->getPlatformTypeId() === PlatformType::MARKETPLACE) {
            return $this->invoice->marketplace->oss_number;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::EU_US_OSS) {
            return $this->invoice->company->active_oss_number?->number;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::EU_NU_OSS) {
            return $this->invoice->company->active_voes_number?->number;
        }

        return null;
    }

    public function iossNumber(): ?string
    {
        if ($this->getItemTypeId() !== ItemType::GOODS) {
            return null;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::EU_IS_IOSS) {
            return $this->invoice->company->active_ioss_number?->number;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::AMAZON_DEEMED_RESELLER_IOSS && $this->getPlatformTypeId() === PlatformType::MARKETPLACE) {
            return $this->invoice->marketplace->ioss_number;
        }

        return null;
    }

    public function marketplaceVatNumber(): ?string
    {
        return $this->invoice->marketplace?->vat_number;
    }

    public function shouldDisplayOssNumber(): bool
    {
        if (in_array($this->getTaxReportingSchemeId(), [TaxReportingScheme::EU_NU_OSS, TaxReportingScheme::EU_US_OSS])) {
            return true;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::AMAZON_DEEMED_RESELLER && $this->getPlatformTypeId() === PlatformType::MARKETPLACE) {
            return true;
        }

        return false;
    }

    public function shouldDisplayMarketplaceVat(): bool
    {
        if ($this->getPlatformTypeId() !== PlatformType::MARKETPLACE) {
            return false;
        }

        if (in_array($this->getTaxReportingSchemeId(), [TaxReportingScheme::UK_VOEC_IMPORT, TaxReportingScheme::UK_VOEC_DOMESTIC])) {
            return true;
        }

        return false;
    }

    public function shouldDisplayIossNumber(): bool
    {
        if ($this->getItemTypeId() !== ItemType::GOODS) {
            return false;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::EU_IS_IOSS) {
            return true;
        }

        if ($this->getTaxReportingSchemeId() === TaxReportingScheme::AMAZON_DEEMED_RESELLER_IOSS && $this->getPlatformTypeId() === PlatformType::MARKETPLACE) {
            return true;
        }

        return false;
    }

    public function getShipFromCountry(): ?array
    {
        if (is_null($this->invoice->ship_from_country_id)) {
            return null;
        }

        return transform_data($this->invoice->shipFromCountry, new CountryTransformer())->toArray();
    }

    private function getShipFromAddress(): ?string
    {
        return $this->invoice->ship_from_address;
    }

    public function getShipFromName(): ?string
    {
        return $this->invoice->ship_from_name;
    }

    public function getShipFromPostalCode(): ?string
    {
        return $this->invoice->ship_from_postal_code;
    }

    public function getShipFromTaxNumber(): ?string
    {
        return $this->invoice->ship_from_tax_number;
    }

    public function getShipFromVoesNumber(): ?string
    {
        return $this->invoice->ship_from_voes_number;
    }

    /**
     * @return bool
     */
    public function hasBillingAddressCustomerVatNumber(): bool
    {
        return !is_null($this->getBillingAddressCustomerVatNumber());
    }

    /**
     * @return string|null
     */
    public function getShipToAddress(): ?string
    {
        return $this->invoice->ship_to_address;
    }

    public function getBillFromCompanyFullName(): ?string
    {
        if (in_array($this->invoice->invoiceSubtype->invoice_type_id, [InvoiceType::PURCHASE_INVOICE, InvoiceType::DEBIT_NOTE])) {
            return null;
        }

        if (in_array($this->invoice->invoiceSubtype->invoice_type_id, [InvoiceType::DEEMED_SALES_INVOICE, InvoiceType::DEEMED_CREDIT_NOTE])) {
            return $this->invoice->marketplace?->name;
        }

        return $this->invoice->company->full_legal_name;
    }

    /**
     * @return string|null
     */
    public function getBillFromCompanyRegistrationNumber(): ?string
    {
        return $this->invoice->company->registration_number;
    }

    public function hasBillFromCompanyRegistrationNumber(): bool
    {
        if ($this->invoice->invoiceSubtype->invoice_type_id === InvoiceType::PURCHASE_INVOICE) {
            return false;
        }


        if (in_array($this->invoice->invoiceSubtype->invoice_type_id, [InvoiceType::DEEMED_SALES_INVOICE, InvoiceType::DEEMED_CREDIT_NOTE])) {
            return false;
        }

        return !is_null($this->getBillFromCompanyRegistrationNumber());
    }

    /**
     * @return string|null
     */
    public function getBillFromCompanyVatNumber(): ?string
    {
        $vatNumber = null;
        $taxReportingCountryId = $this->invoice->tax_reporting_country_id;
        $taxReportingSchemeId = $this->invoice->tax_reporting_scheme_id;
        $invoiceSubtypeId = $this->invoice->invoice_subtype_id;
        $date = Carbon::parse($this->invoice->invoice_date);
        if (in_array($this->invoice->invoiceSubtype->invoice_type_id, [InvoiceType::PURCHASE_INVOICE, InvoiceType::DEBIT_NOTE])) {
            return $this->invoice->ship_from_vat_number;
        }

        if (in_array($this->invoice->invoiceSubtype->invoice_type_id, [InvoiceType::DEEMED_SALES_INVOICE, InvoiceType::DEEMED_CREDIT_NOTE])) {
            if (in_array($taxReportingSchemeId, [TaxReportingScheme::DEEMED_RESELLER_EU_US_OSS, TaxReportingScheme::DEEMED_RESELLER_REGULAR])) {
                $ossNumberCheck = [
                    InvoiceSubtype::DEEMED_DOMESTIC_SALES_INVOICE,
                    InvoiceSubtype::DEEMED_CN_DOMESTIC_SALES_INVOICE,
                    InvoiceSubtype::DEEMED_EU_B2C_SALES_INVOICE,
                    InvoiceSubtype::DEEMED_CN_EU_B2C_SALES_INVOICE,
                ];
                if (in_array($invoiceSubtypeId, $ossNumberCheck)) {
                    return $this->invoice->marketplace?->oss_number;
                }
            }

            if (in_array($invoiceSubtypeId, [InvoiceSubtype::DEEMED_IOSS_EXPORT_INVOICE, InvoiceSubtype::DEEMED_CN_IOSS_EXPORT_INVOICE])) {
                return $this->invoice->marketplace?->ioss_number;
            }

            return null;
        }

        if (!is_null($taxReportingCountryId)) {
            $vatNumber = $this->invoice
                ->company
                ->getActiveVatNumbersOnDate($date->toDateString(), $taxReportingCountryId)
                ->first()
                ?->vat_number;
        }

        if ($taxReportingSchemeId === TaxReportingScheme::EU_NU_OSS) {
            $vatNumber = $this->invoice
                ->company
                ->ossNumbers
                ->filter(function (OssNumber $ossNumber) use ($date) {
                    if ($ossNumber->type_id !== OssNumberType::NON_UNION_SCHEME) {
                        return false;
                    }

                    $registerDate = Carbon::parse($ossNumber->register_date);
                    $check = $date->gte($registerDate);
                    $endDate = $ossNumber->end_date;
                    if (!is_null($endDate)) {
                        $endDate = Carbon::parse($endDate);
                        $check = $check && $date->lt($endDate);
                    }

                    return $check;
                })->first()?->number;
        }

        if ($taxReportingSchemeId === TaxReportingScheme::AMAZON_UNION_OSS) {
            $vatNumber = $this->invoice
                ->company
                ->ossNumbers
                ->filter(function (OssNumber $ossNumber) use ($date) {
                    if ($ossNumber->type_id !== OssNumberType::UNION_SCHEME) {
                        return false;
                    }

                    $registerDate = Carbon::parse($ossNumber->register_date);
                    $check = $date->gte($registerDate);
                    $endDate = $ossNumber->end_date;
                    if (!is_null($endDate)) {
                        $endDate = Carbon::parse($endDate);
                        $check = $check && $date->lt($endDate);
                    }

                    return $check;
                })->first()?->number;
        }

        if ($taxReportingSchemeId === TaxReportingScheme::EU_IS_IOSS) {
            $vatNumber = $this->invoice
                ->company
                ->iossNumbers
                ->filter(function (IossNumber $iossNumber) use ($date) {
                    $checkTypes = [
                        IossNumberType::IMPORT_SCHEME,
                        IossNumberType::TAXABLE_PERSON,
                    ];
                    if (!in_array($iossNumber->type_id, $checkTypes, true)) {
                        return false;
                    }

                    $registerDate = Carbon::parse($iossNumber->register_date);
                    $check = $date->gte($registerDate);
                    $endDate = $iossNumber->end_date;
                    if (!is_null($endDate)) {
                        $endDate = Carbon::parse($endDate);
                        $check = $check && $date->lt($endDate);
                    }

                    return $check;
                })->first()?->number;
        }

        return $vatNumber;
    }

    /**
     * @return bool
     */
    public function hasBillFromCompanyVatNumber(): bool
    {
        return !is_null($this->getBillFromCompanyVatNumber());
    }

    /**
     * @return Collection<InvoiceItem>
     */
    public function getInvoiceItems(): Collection
    {
        return $this->invoice
            ->invoiceItems
            ->filter(function (InvoiceItem $invoiceItem) {
                return in_array($invoiceItem->invoice_item_type_id, [
                    ItemType::GOODS,
                    ItemType::SERVICES,
                    ItemType::DIGITAL_GOODS,
                ]);
            });
    }

    public function getInvoiceItemsArray(): array
    {
        return $this->getInvoiceItems()->transformData(new InvoiceItemTransformer())->toArray();
    }

    /**
     * @return ?InvoicePromotionData
     */
    public function getInvoicePromotionData(): ?InvoicePromotionData
    {
        /**
         * @var InvoiceItem[]|Collection $invoiceItems
         */
        $invoiceItems = $this->invoice->invoiceItems;

        if ($invoiceItems->count() < 1) {
            return null;
        }

        $net = 0.0;
        $gross = 0.0;
        foreach ($invoiceItems as $invoiceItem) {
            $net += $invoiceItem->getDiscountNet();
            $gross += $invoiceItem->getDiscountGross();
        }

        if ($gross <= 0) {
            return null;
        }

        $promotionData = new InvoicePromotionData($this->invoice);
        $promotionData->setGross($gross);
        $promotionData->setNet($net);

        return $promotionData;
    }

    /**
     * @return InvoiceWrapData|null
     */
    public function getInvoiceWrapData(): ?InvoiceWrapData
    {
        /**
         * @var InvoiceItem[]|Collection $invoiceItems
         */
        $invoiceItems = $this->invoice
            ->invoiceItems
            ->filter(function (InvoiceItem $invoiceItem) {
                if ($invoiceItem->invoice_item_type_id !== ItemType::PACKAGING) {
                    return false;
                }

                return (!is_null($invoiceItem->getNet()) && $invoiceItem->getNet() !== 0.0);
            });

        if ($invoiceItems->count() < 1) {
            return null;
        }

        $net = 0.0;
        $gross = 0.0;
        foreach ($invoiceItems as $invoiceItem) {
            $net += $invoiceItem->getNet();
            $gross += $invoiceItem->getGross();
        }

        $promotionData = new InvoiceWrapData($this->invoice);
        $promotionData->setGross($gross);
        $promotionData->setNet($net);

        return $promotionData;
    }

    /**
     * @return InvoiceShippingData|null
     */
    public function getInvoiceShippingData(): ?InvoiceShippingData
    {
        /**
         * @var InvoiceItem[]|Collection $invoiceItems
         */
        $invoiceItems = $this->invoice
            ->invoiceItems
            ->filter(function (InvoiceItem $invoiceItem) {
                if ($invoiceItem->invoice_item_type_id !== ItemType::SHIPPING) {
                    return false;
                }

                return (!is_null($invoiceItem->getNet()) && $invoiceItem->getNet() != 0.0);
            });

        if ($invoiceItems->count() < 1) {
            return null;
        }

        $net = 0.0;
        $gross = 0.0;
        foreach ($invoiceItems as $invoiceItem) {
            $net += $invoiceItem->getNet();
            $gross += $invoiceItem->getGross();
        }

        $promotionData = new InvoiceShippingData($this->invoice);
        $promotionData->setGross($gross);
        $promotionData->setNet($net);

        return $promotionData;
    }

    /**
     * @return InvoiceSubtotalData
     */
    public function getInvoiceSubtotalData(): InvoiceSubtotalData
    {
        return new InvoiceSubtotalData($this->invoice);
    }

    /**
     * @return string
     */
    public function getInvoiceTotalText(): string
    {
        $name = mb_strtolower($this->invoice->invoiceSubtype->invoiceType->name);
        $name = ucfirst($name) . ' total';

        return _l($name);
    }

    /**
     * @return string|null
     */
    public function getInvoiceNote(): ?string
    {
        $note = collect();
        $items = $this->invoice->invoiceItems->filter(function (InvoiceItem $invoiceItem) {
            return in_array($invoiceItem->invoice_item_type_id, [ItemType::GOODS, ItemType::SERVICES]);
        })->groupBy('invoice_item_type_id');
        if ($items->has(ItemType::GOODS)) {
            $note->push($this->invoice->invoiceSubtype->note_goods);
        }
        if ($items->has(ItemType::SERVICES)) {
            $note->push($this->invoice->invoiceSubtype->note_services);
        }

        return $note->filter(function ($value) {
            return !is_null($value);
        })
            ->map(function (string $value) {
                return mb_ucfirst(_l($value));
            })
            ->implode('. ');
    }

    /**
     * @return bool
     */
    public function hasInvoiceNote(): bool
    {
        return !is_null($this->getInvoiceNote());
    }

    public function getShipToCountry(): ?array
    {
        if (!$this->invoice->shipToCountry) {
            return null;
        }

        return transform_data($this->invoice->shipToCountry, new CountryTransformer())
            ->toArray();
    }

    public function getShipToPostalCode(): ?string
    {
        return $this->invoice->ship_to_postal_code;
    }

    public function getInvoiceComment(): ?string
    {
        return $this->invoice->comment;
    }

    public function getBillToPostalCode(): ?string
    {
        return $this->invoice->bill_to_postal_code;
    }

    public function getBillToCountry(): ?array
    {
        if (!$this->invoice->billToCountry) {
            return null;
        }

        return transform_data($this->invoice->billToCountry, new CountryTransformer())->toArray();
    }

    public function getBillToVatNumber(): array
    {
        return $this->getTransformedVatNumber($this->invoice->bill_to_vat_number);
    }

    public function getBillToTaxNumber(): ?string
    {
        return $this->invoice->bill_to_tax_number;
    }

    public function getBillToVoesNumber(): ?string
    {
        return $this->invoice->bill_to_voes_number;
    }

    public function getShipFromVatNumber(): array
    {
        return $this->getTransformedVatNumber($this->invoice->ship_from_vat_number);
    }

    public function getShipToName(): ?string
    {
        return $this->invoice->ship_to_name;
    }

    public function getShipToTaxNumber(): ?string
    {
        return $this->invoice->ship_to_tax_number;
    }

    public function getShipToVoesNumber(): ?string
    {
        return $this->invoice->ship_to_voes_number;
    }

    private function getBillFromAddress(): ?string
    {
        return $this->invoice->bill_from_address;
    }

    private function getBillFromName(): ?string
    {
        return $this->invoice->bill_from_name;
    }

    public function getBillFromPostalCode(): ?string
    {
        return $this->invoice->bill_from_postal_code;
    }

    public function getBillFromCountry(): ?array
    {
        if (!$this->invoice->billFromCountry) {
            return null;
        }

        return transform_data($this->invoice->billFromCountry, new CountryTransformer())->toArray();
    }

    public function getBillFromVatNumber(): array
    {
        return $this->getTransformedVatNumber($this->invoice->bill_from_vat_number);
    }

    public function getBillFromTaxNumber(): ?string
    {
        return $this->invoice->bill_from_tax_number;
    }

    public function getBillFromVoesNumber(): ?string
    {
        return $this->invoice->bill_from_voes_number;
    }

    private function getBillToArray(): array
    {
        return [
            'name'       => $this->getBillToName(),
            'address'    => $this->getBillToAddress(),
            'postalCode' => $this->getBillToPostalCode(),
            'country'    => $this->getBillToCountry(),
            'vatNumber'  => $this->getBillToVatNumber(),
            'taxNumber'  => $this->getBillToTaxNumber(),
            'voesNumber' => $this->getBillToVoesNumber(),
        ];
    }

    private function getBillFromArray(): array
    {
        return [
            'name'       => $this->getBillFromName(),
            'address'    => $this->getBillFromAddress(),
            'postalCode' => $this->getBillFromPostalCode(),
            'country'    => $this->getBillFromCountry(),
            'vatNumber'  => $this->getBillFromVatNumber(),
            'taxNumber'  => $this->getBillFromTaxNumber(),
            'voesNumber' => $this->getBillFromVoesNumber(),
        ];
    }

    private function getShipToArray(): array
    {
        return [
            'name'       => $this->getShipToName(),
            'address'    => $this->getShipToAddress(),
            'postalCode' => $this->getShipToPostalCode(),
            'country'    => $this->getShipToCountry(),
            'vatNumber'  => $this->getShipToVatNumber(),
            'taxNumber'  => $this->getShipToTaxNumber(),
            'voesNumber' => $this->getShipToVoesNumber(),
        ];
    }

    private function getShipFromArray(): array
    {
        return [
            'name'       => $this->getShipFromName(),
            'address'    => $this->getShipFromAddress(),
            'postalCode' => $this->getShipFromPostalCode(),
            'country'    => $this->getShipFromCountry(),
            'vatNumber'  => $this->getShipFromVatNumber(),
            'taxNumber'  => $this->getShipFromTaxNumber(),
            'voesNumber' => $this->getShipFromVoesNumber(),
        ];
    }

    private function shippingIsSameAsBillTo(): bool
    {
        return $this->invoice->shipping_is_same_as_bill_to ?? true;
    }

    private function shippingIsSameAsBillFrom(): bool
    {
        return $this->invoice->shipping_is_same_as_bill_from ?? true;
    }

    private function getOssArray(): array
    {
        return [
            'number'  => $this->ossNumber(),
            'visible' => $this->shouldDisplayOssNumber(),
        ];
    }

    private function getIossArray(): array
    {
        return [
            'number'  => $this->ossNumber(),
            'visible' => $this->shouldDisplayIossNumber(),
        ];
    }

    private function getMarketplaceDataArray(): array
    {
        return [
            'number'  => $this->marketplaceVatNumber(),
            'visible' => $this->shouldDisplayMarketplaceVat()
        ];
    }

    private function getNoteArray(): array
    {
        return [
            'note'    => $this->getInvoiceNote(),
            'visible' => $this->hasInvoiceNote(),
        ];
    }

    private function getCalculationDataArray(): array
    {
        return [
            'totalText'    => $this->getInvoiceTotalText(),
            'visible'      => $this->getInvoiceItems()->count() > 0,
            'subtotalData' => $this->getInvoiceSubtotalData()->toArray(),
            'intrinsic'    => [
                'value'   => $this->getIntrinsicValue(),
                'visible' => $this->showIntrinsicValue(),
            ]
        ];
    }

    public function getSupplier(): ?Supplier
    {
        return $this->invoice->supplier;
    }

    public function getSupplierArray(): ?array
    {
        $supplier = $this->getSupplier();
        if (is_null($supplier)) {
            return null;
        }

        return $supplier->transformData(new SupplierMultiselectTransformer())->toArray();
    }

    private function getSupplierId(): ?int
    {
        return $this->getSupplier()?->id ?? null;
    }

    public function toArray(): array
    {
        return [
            'customer'                 => $this->getCustomerArray(),
            'supplier'                 => $this->getSupplierArray(),
            'supplierId'               => $this->getSupplierId(),
            'billTo'                   => $this->getBillToArray(),
            'billFrom'                 => $this->getBillFromArray(),
            'shipTo'                   => $this->getShipToArray(),
            'shipFrom'                 => $this->getShipFromArray(),
            'shippingIsSameAsBillTo'   => $this->shippingIsSameAsBillTo(),
            'shippingIsSameAsBillFrom' => $this->shippingIsSameAsBillFrom(),
            'oss'                      => $this->getOssArray(),
            'ioss'                     => $this->getIossArray(),
            'taxReportingScheme'       => $this->getTaxReportingSchemeArray(),
            'marketplaceData'          => $this->getMarketplaceDataArray(),
            'note'                     => $this->getNoteArray(),
            'comment'                  => $this->getInvoiceComment(),
            'description'              => $this->getInvoiceDescription(),
            'calculation'              => $this->getCalculationDataArray(),
            'promotion'                => $this->getInvoicePromotionData()?->toArray(),
            'wrap'                     => $this->getInvoiceWrapData()?->toArray(),
            'shipping'                 => $this->getInvoiceShippingData()?->toArray(),
            'invoiceItems'             => $this->getInvoiceItemsArray(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    private function getTransformedVatNumber(?string $vatNumber): array
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        return VatNumberApi::validateVatNumber($vatNumber)->toArray();
    }
}
