<?php

namespace App\Core\Invoices\Invoice;

use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class InvoiceShippingData implements Arrayable, Jsonable
{
    private Invoice $invoice;

    private float $net = 0;
    private float $gross = 0;
    private string $prefix;

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->prefix = $invoice->invoiceSubtype->invoice_type_id === InvoiceType::CREDIT_NOTE ? '-' : '';
    }

    /**
     * @return float
     */
    public function getNet(): float
    {
        return round($this->net, 2);
    }

    /**
     * @return float
     */
    public function getGross(): float
    {
        return round($this->gross, 2);
    }

    /**
     * @param float $net
     * @return $this
     */
    public function setNet(float $net): InvoiceShippingData
    {
        $this->net = $net;

        return $this;
    }

    /**
     * @param float $gross
     * @return $this
     */
    public function setGross(float $gross): InvoiceShippingData
    {
        $this->gross = $gross;

        return $this;
    }

    /**
     * @return string
     */
    public function getNetWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getNet(), $this->invoice->currency->code);
    }

    /**
     * @return string
     */
    public function getGrossWithCurrency(): string
    {
        return $this->prefix . evat_money($this->getGross(), $this->invoice->currency->code);
    }


    public function toArray(): array
    {
        return [
            'grossWithCurrency' => $this->getGrossWithCurrency(),
            'netWithCurrency'   => $this->getNetWithCurrency(),
            'gross'             => $this->getGross(),
            'net'               => $this->getNet(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
