<?php

namespace App\Core\Iban;

use App\Core\Iban\Contracts\IbanServiceContract;
use PHP_IBAN\IBAN;

class IbanService implements IbanServiceContract
{
    /**
     * @inheritDoc
     */
    public function validateIban(?string $iban = null): bool
    {
        if (is_null($iban)) {
            return false;
        }
        $iban = trim($iban);
        $ibanData = new IBAN($iban);

        return $ibanData->Verify();
    }

}
