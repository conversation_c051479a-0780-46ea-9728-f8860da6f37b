<?php

namespace App\Core\CheckUkVatApi;

use App\Core\CheckUkVatApi\Contracts\CheckUkVatApiServiceContract;
use App\Core\CheckUkVatApi\Responses\CheckUkVatApiServiceResponse;
use GuzzleHttp\Client;
use Throwable;

class CheckUkVatApiService implements CheckUkVatApiServiceContract
{
    /**
     * @return string
     */
    private function getCheckUkVatBaseUrl(): string
    {
        return config('evat.checkUkVatApi.url');
    }

    /**
     * @inheritDoc
     */
    public function checkIfUkVatNumberValid(
        string $ukVatNumber
    ): ?CheckUkVatApiServiceResponse {
        $options = [
            'base_uri' => $this->getCheckUkVatBaseUrl(),
            'headers'  => [
                'Accept' => 'application/vnd.hmrc.1.0+json'
            ]
        ];

        $client = new Client($options);

        $targetVrn = substr($ukVatNumber, 2);

        $relativeUrl = $targetVrn . '/' . $targetVrn;

        try {
            $response = $client->request(
                'GET',
                $relativeUrl
            );
        } catch (Throwable $exception) {
            $response = $exception->getResponse();

            if ($exception->getResponse()->getStatusCode() === 500) {
                return null;
            }
        }

        $response = $response->getBody()->getContents();

        $response = json_decode($response, true);

        return new CheckUkVatApiServiceResponse($response);
    }
}
