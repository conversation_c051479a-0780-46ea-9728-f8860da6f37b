<?php

namespace App\Core\CheckUkVatApi\Responses;

class CheckUkVatApiServiceResponse
{
    /**
     * @var array
     */
    private $rawData;

    /**
     * @var CheckUkVatResult
     */
    private $result;

    public function __construct(array $rawData)
    {
        $this->rawData = $rawData;
        $this->resolveResponse();
    }

    private function resolveResponse(): void
    {
        $result = new CheckUkVatResult();
        if (isset($this->rawData['consultationNumber'])) {
            $result->setValid(true);
            $result->setReferenceNumber($this->rawData['consultationNumber']);
        } else {
            $result->setValid(false);
            $result->setReferenceNumber(null);
        }

        $this->result = $result;
    }

    /**
     * @return CheckUkVatResult
     */
    public function getResult(): CheckUkVatResult
    {
        return $this->result;
    }
}
