<?php

namespace App\Core\CheckUkVatApi\Responses;

class CheckUkVatResult
{
    /**
     * @var bool
     */
    private $valid;

    /**
     * @var string |null
     */
    private $referenceNumber;

    /**
     * @return bool
     */
    public function isValid(): bool
    {
        return $this->valid;
    }

    /**
     * @param bool $valid
     * @return CheckUkVatResult
     */
    public function setValid(bool $valid): CheckUkVatResult
    {
        $this->valid = $valid;

        return $this;
    }

    /**
     * @return string |null
     */
    public function getReferenceNumber(): ?string
    {
        return $this->referenceNumber;
    }

    /**
     * @param string $referenceNumber |null
     * @return CheckUkVatResult
     */
    public function setReferenceNumber(?string $referenceNumber): CheckUkVatResult
    {
        $this->referenceNumber = $referenceNumber;

        return $this;
    }
}
