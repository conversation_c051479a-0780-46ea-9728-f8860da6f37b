<?php

namespace App\Core\Common\History;

use App\Core\Data\Models\History as HistoryModel;
use App\Core\Data\Models\User;
use Illuminate\Database\Eloquent\Model;

class History
{
    /**
     * @param string $description
     * @param Model $resource
     * @param int|null $forceUserId
     * @param array $langParams
     * @return HistoryModel
     */
    public static function created(
        string $description,
        Model $resource,
        ?int $forceUserId = null,
        array $langParams = []
    ): HistoryModel {
        return self::store($description, $resource, 'CREATE', $forceUserId, $langParams);
    }

    /**
     * @param string $description
     * @param Model $resource
     * @param int|null $forceUserId
     * @param array $langParams
     * @param array $extraData
     * @return HistoryModel
     */
    public static function updated(
        string $description,
        Model $resource,
        ?int $forceUserId = null,
        array $langParams = [],
        array $extraData = [],
    ): HistoryModel {
        return self::store($description, $resource, 'UPDATE', $forceUserId, $langParams, $extraData);
    }

    /**
     * @param string $description
     * @param Model $resource
     * @param int|null $forceUserId
     * @param array $langParams
     * @return HistoryModel
     */
    public static function deleted(
        string $description,
        Model $resource,
        ?int $forceUserId = null,
        array $langParams = []
    ): HistoryModel {
        return self::store($description, $resource, 'DELETE', $forceUserId, $langParams);
    }

    /**
     * @param string $description
     * @param Model $resource
     * @param string $action
     * @param int|null $forceUserId
     * @param array $langParams
     * @param array $extraData
     * @return HistoryModel
     */
    private static function store(
        string $description,
        Model $resource,
        string $action,
        ?int $forceUserId = null,
        array $langParams = [],
        array $extraData = []
    ): HistoryModel {

        $resource->unsetRelations();
        // #969
        if ($action !== 'DELETE') {
            $resource->refresh();
        }

        $resourceClass = get_class($resource);

        $userId = User::USER_SYSTEM;
        if (!is_null(user())) {
            $userId = user()->getId();
        }

        if (!is_null($forceUserId)) {
            $userId = $forceUserId;
        }

        $historyData = $resource->toArray();

        if (count($extraData) > 0) {
            $historyData['extraData'] = $extraData;
        }

        $history = new HistoryModel();

        $history->description = $description;
        $history->change_user_id = $userId;
        $history->resource_id = $resource->id;
        $history->resource_class = $resourceClass;
        $history->action = $action;
        $history->data = $historyData;
        $history->lang_params = $langParams;

        $history->save();

        return $history;
    }
}
