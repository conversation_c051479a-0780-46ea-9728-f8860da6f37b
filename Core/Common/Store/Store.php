<?php

namespace App\Core\Common\Store;

use App\Core\Data\Models\Store as StoreModel;
use App\Core\Data\Repositories\Contracts\StoreRepositoryContract;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class Store
{
    public static function getByStoreId(int $storeId): ?StoreModel
    {
        $storeRepo = self::getRepo();

        return $storeRepo->getStoreById($storeId);
    }

    private static function getRepo(): StoreRepositoryContract
    {
        return app()->make(StoreRepositoryContract::class);
    }

    public static function getFirstForUserByKey(int $userId, string $key, ?string $channel = null): ?StoreModel
    {
        return self::getAll($userId, $key, $channel)->first();
    }

    public static function getAllForUserByKey(int $userId, string $key, ?string $channel = null): Collection
    {
        return self::getAll($userId, $key, $channel);
    }

    public static function getAllForUserByChannel(int $userId, string $channel, ?string $key = null): Collection
    {
        return self::getAll($userId, $key, $channel);
    }

    private static function getAll(int $userId, ?string $key = null, ?string $channel = null): Collection
    {
        $storeRepo = self::getRepo();

        return $storeRepo->get(
            $userId,
            $key,
            $channel
        );
    }

    public static function add(
        int $userId,
        string $key,
        array $data,
        ?string $channel = null,
        ?int $ttl = null
    ): StoreModel {
        return self::saveStore(
            self::getRepo()->getEmptyStoreModel(),
            $data,
            $channel,
            $ttl,
            $userId,
            $key
        );
    }

    public static function update(
        int $userId,
        string $key,
        array $data,
        ?string $channel = null,
        ?int $ttl = null
    ): StoreModel {
        $store = self::getFirstForUserByKey($userId, $key, $channel);

        return self::saveStore($store, $data, $channel, $ttl);
    }

    public static function deleteForUserByKey(int $userId, string $key): void
    {
        $storeRepo = self::getRepo();

        $storeRepo->deleteAllByKeyAndUser($key, $userId);
    }

    public static function deleteByStoreId(int $storeId): void
    {
        $storeRepo = self::getRepo();

        $storeRepo->deleteById($storeId);
    }

    public static function upsert(
        int $userId,
        string $key,
        array $data,
        ?string $channel = null,
        ?int $ttl = null
    ): StoreModel {
        $store = self::getFirstForUserByKey($userId, $key, $channel);
        if (is_null($store)) {
            $store = self::getRepo()->getEmptyStoreModel();
        }

        return self::saveStore(
            $store,
            $data,
            $channel,
            $ttl,
            $userId,
            $key
        );
    }

    private static function saveStore(
        StoreModel $store,
        array $data,
        ?string $channel = null,
        ?int $ttl = null,
        ?int $userId = null,
        ?string $key = null
    ): StoreModel {
        $store->data = $data;
        $store->channel = $channel;
        $store->last_modification_time = Carbon::now()->toDateTimeString();
        $store->ttl = $ttl;

        if (is_null($store->id)) {
            if (!is_null($userId)) {
                $store->user_id = $userId;
            }
            if (!is_null($key)) {
                $store->key = $key;
            }
        }

        $store->save();

        return $store;
    }
}
