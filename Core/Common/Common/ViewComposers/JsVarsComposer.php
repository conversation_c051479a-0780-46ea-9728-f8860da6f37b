<?php

namespace App\Core\Common\Common\ViewComposers;

use App\Core\SalesChannels\Contracts\SalesChannelServiceContract;
use App\Core\SalesChannels\Exceptions\SalesChannelForbiddenException;
use App\Core\System\Notifications\Contracts\NotificationsServiceContract;
use Carbon\Carbon;
use Illuminate\View\View;

class JsVarsComposer
{
    public function compose(View $view): void
    {
        $nowTime = Carbon::now();
        $now = $nowTime->timestamp * 1000;
        $nowDate = $nowTime->startOfDay()->toISOString(true);
        $timezone = config('app.timezone');

        $locale = app()->getLocale();
        $shortLocale = mb_strtolower(explode('_', $locale)[0]);
        $timeFormats = config('date-formats.js.' . $shortLocale);
        $notificationsPeriod = config('evat.notificationsPeriod');
        $showNotificationsToastPeriod = config('evat.showNotificationsToastPeriod');

        $config = [
            'debug'                               => config('app.debug'),
            'csrfToken'                           => csrf_token(),
            'serverTimestamp'                     => $now,
            'serverDate'                          => $nowDate,
            'serverTimezone'                      => $timezone,
            'messages'                            => sd()->getMessages(),
            'locale'                              => $locale,
            'shortLocale'                         => $shortLocale,
            'timeFormats'                         => $timeFormats,
            'notificationsPeriod'                 => $notificationsPeriod,
            'showNotificationsToastPeriod'        => $showNotificationsToastPeriod,
            'notifications'                       => [],
            'loggedOut'                           => false,
            'currentInstitutionInstitutionTypeId' => sd()->getCurrentInstitutionInstitutionTypeId(),
            'isClient'                            => false,
            'isDeveloper'                         => user()?->isAdministrative() ?? false,
            'isAccountant'                        => false,
            'isPartner'                           => false,
            'viewUUID'                            => uniqid('', true),
            'currentUrl'                          => url()->current(),
            'stripe'                              => [
                'key'                 => config('evat.stripe.key'),
                'intentRoute'         => route('payment.stripe.setup-intent'),
                'makePaymentRoute'    => route('payment.stripe.make-payment'),
                'paymentMethodsRoute' => route('payment.stripe.payment-methods'),
            ],
            'externalAppSalesChannel'             => $this->resolveExternalAppSalesChannelInstall()
        ];

        if (!is_null(user())) {
            $config['user_id'] = user()->getId();
            $config['notifications'] = $this->resolveNotifications();
            $config['isClient'] = user()->isClientType();
            $config['isAccountant'] = user()->isServiceProviderType();
            $config['isPartner'] = user()->isCrmType();

            // Product tour data
            $config['productTour'] = $this->productTourData();
        }
        if (config('app.env', 'production') === 'local') {
            $config['isDevelopmentEnvironment'] = true;
        }

        $config['routes'] = [
            'vatValidation' => route('validation.vat')
        ];

        $config = json_encode($config, JSON_UNESCAPED_UNICODE);
        $view->with('jsVars', $config);
    }

    private function resolveNotifications(): array
    {
        /**
         * @var NotificationsServiceContract $notificationsService
         */
        /** @noinspection PhpUnhandledExceptionInspection */
        $notificationsService = app()->make(NotificationsServiceContract::class);

        return $notificationsService->resolveNotificationsForUser(user()->getId());
    }

    /**
     * @return array|null
     */
    private function resolveExternalAppSalesChannelInstall(): ?array
    {
        $key = 'externalAppUserConfirmation';
        /**
         * @var SalesChannelServiceContract $salesChannelService
         */
        /** @noinspection PhpUnhandledExceptionInspection */
        $salesChannelService = app()->make(SalesChannelServiceContract::class);
        $salesChannelData = sd()->get($key);
        $user = user();

        if (!is_null($salesChannelData) && !$user->isAdministrative()) {
            try {
                return $salesChannelService->resolveExternalAppSalesChannelInstall($user, $salesChannelData);
            } catch (SalesChannelForbiddenException) {
                sd()->forget($key);
            }
        }

        return null;
    }

    private function productTourData(): array
    {
        /*
         * Will be ordered by sequence, doesn't need to bi on order, and it doesn't need
         * to be +1, it just needs to be higher or lower
         */
        $steps = [
            [
                'name'     => 'initial',
                'sequence' => 0
            ],
            [
                'name'     => 'myProfile',
                'sequence' => 2
            ],
            [
                'name'     => 'companyYear',
                'sequence' => 4
            ],
        ];

        if (user()->isClientType()) {
            $steps['myCompanySetup'] = [
                'name'     => 'myCompany',
                'sequence' => 6
            ];
        }

        return [
            'done'      => user()->is_product_tour_done,
            'initial'   => 'initial',
            'submitUrl' => route('product-tour.store'),
            'steps'     => $this->resolveSteps($steps),
        ];
    }

    private function resolveSteps(array $steps): array
    {
        $steps = collect($steps)
            ->sortBy('sequence', SORT_NATURAL)
            ->values();

        return $steps->map(function (array $step, $index) use ($steps) {
            $next = $steps[$index + 1] ?? null;
            if (!is_null($next)) {
                $next = $next['name'];
            }
            $previous = $steps[$index - 1] ?? null;
            if (!is_null($previous)) {
                $previous = $previous['name'];
            }

            $step['next'] = $next;
            $step['previous'] = $previous;

            return $step;
        })->toArray();
    }
}
