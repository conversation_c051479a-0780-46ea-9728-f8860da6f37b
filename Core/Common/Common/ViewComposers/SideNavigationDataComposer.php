<?php

namespace App\Core\Common\Common\ViewComposers;

use App\Core\Data\Models\ShopifyMandatoryWebhookRequest;
use App\Core\Data\Models\Warehouse;
use Illuminate\View\View;

class SideNavigationDataComposer
{
    public function compose(View $view)
    {
        $warehouses = cache()->rememberForever(Warehouse::ALL_VIEWS_PROPOSED_CACHE_KEY, function () {
            return Warehouse::where('confirmed', false)
                ->where('source', 'U')
                ->get();
        });

        $view->with(Warehouse::ALL_VIEWS_PROPOSED_CACHE_KEY, $warehouses->count());

//        $unapprovedRequests = cache()->rememberForever(ShopifyMandatoryWebhookRequest::HAS_UNAPPROVED_REQUESTS_CACHE_KEY, function () {
//            return ShopifyMandatoryWebhookRequest::whereNull('request_completed_at')
//                ->get();
//        });

        $view->with(ShopifyMandatoryWebhookRequest::HAS_UNAPPROVED_REQUESTS_CACHE_KEY, 0);
    }
}
