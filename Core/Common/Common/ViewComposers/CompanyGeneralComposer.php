<?php

namespace App\Core\Common\Common\ViewComposers;

use App\Core\Data\Models\BusinessType;
use App\Core\Data\Models\Company;
use App\Core\Data\Repositories\Contracts\CompanyContactPersonRepositoryContract;
use Illuminate\View\View;

class CompanyGeneralComposer
{
    protected ?Company $company;
    protected CompanyContactPersonRepositoryContract $companyContactPersonRepo;

    public function __construct(CompanyContactPersonRepositoryContract $companyContactPersonRepo)
    {
        $this->company = sd()->getFilterCompany();
        if (is_null($this->company)) {
            redirect()->route('admin.home')->send();
        }

        $this->company->refresh();

        $this->company->load([
            'contactPeople.person',
            'shareholders',
            'legalRepresentatives',
            'bank'
        ]);

        $this->companyContactPersonRepo = $companyContactPersonRepo;
    }

    public function compose(View $view)
    {
        return $view
            ->with('isGeneralDataMissing', $this->isGeneralDataMissing())
            ->with('passedShareholdersThreshold', $this->passedShareholdersThreshold())
            ->with('hasBank', $this->hasBank())
            ->with('isContactMissingData', $this->isContactMissing())
            ->with('shouldHideShareholders', $this->shouldHideShareholders())
            ->with('hasLegalRepresentatives', $this->hasLegalRepresentatives());
    }

    private function isContactMissing()
    {
        $contactPersonTypeIds = $this->companyContactPersonRepo->getAllContactPersonTypes()->pluck('id');
        $companyContactsTypeIds = $this->company->contactPeople->pluck('contact_person_type_id')->unique();

        return $contactPersonTypeIds->diff($companyContactsTypeIds)->isNotEmpty();
    }

    private function passedShareholdersThreshold()
    {
        return $this->company->shareholders->sum('capital_value_percent') < 100;
    }

    private function hasLegalRepresentatives()
    {
        return $this->company->legalRepresentatives->count() < 1;
    }

    private function hasBank()
    {
        return is_null($this->company->bank?->id);
    }

    private function isGeneralDataMissing()
    {
        $companyGeneralDataRequiredFields = collect([
            $this->company->full_legal_name,
            $this->company->legal_type,
            $this->company->business_type_id,
            $this->company->legal_type,
            $this->company->email,
            $this->company->phone,
            $this->company->registration_number,
            $this->company->incorporation_date,
            $this->company->business_activity,
            $this->company->address?->street,
            $this->company->address?->house_number,
            $this->company->address?->city_id,
            $this->company->address?->postal_code,
            $this->company->address?->country_id
        ]);

        $businessTypeId = $this->company->business_type_id;
        if (
            $businessTypeId === BusinessType::LIMITED
            || $businessTypeId === BusinessType::CORPORATION
            || $businessTypeId === BusinessType::BRANCH
        ) {
            $companyGeneralDataRequiredFields = $companyGeneralDataRequiredFields->merge([
                $this->company->share_capital,
                $this->company->share_capital_currency_id,
            ]);
        }

        return $companyGeneralDataRequiredFields->contains(null);
    }

    private function shouldHideShareholders(): bool
    {
        $businessTypeId = $this->company->business_type_id;

        $noShareholderBusinessTypes = [
            BusinessType::SOLE_TRADER,
            BusinessType::FREELANCER,
            BusinessType::PARTNERSHIP,
            BusinessType::CHARITY,
        ];

        return !in_array($businessTypeId, $noShareholderBusinessTypes);
    }

}
