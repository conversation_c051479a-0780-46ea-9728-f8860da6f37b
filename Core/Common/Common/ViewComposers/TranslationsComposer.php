<?php

namespace App\Core\Common\Common\ViewComposers;

use App\Core\Translations\Contracts\TranslationServiceContract;
use Illuminate\View\View;

class TranslationsComposer
{
    private TranslationServiceContract $translationService;

    public function __construct(TranslationServiceContract $translationService)
    {
        $this->translationService = $translationService;
    }

    public function compose(View $view)
    {
        $locale = app()->getLocale();
        $translations = $this->translationService->resolveTranslationForLocale($locale);

        $translation = [
            'showLangKey'  => sd()->showLangKey(),
            'translations' => $translations
        ];

        $translation = json_encode($translation, JSON_FORCE_OBJECT);

        $view->with('translation', $translation);
    }
}
