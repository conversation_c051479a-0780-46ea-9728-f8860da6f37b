<?php

namespace App\Core\Common\Common\ViewComposers;

use App\Core\UserManual\Contracts\UserManualServiceContract;
use Exception;
use Illuminate\Support\Facades\Route;
use Illuminate\View\View;

class UserManualDataComposer
{
    private UserManualServiceContract $manualService;

    public function __construct(UserManualServiceContract $manualService)
    {
        $this->manualService = $manualService;
    }

    /**
     * @throws Exception
     */
    public function compose(View $view)
    {
        $locale = app()->getLocale() ?? 'en';
        $page = Route::getCurrentRoute()?->getName();
        $params = Route::getCurrentRoute()?->parameters() ?? [];
        if (is_null($page)) {
            throw new Exception('Route name not defined');
        }

        $userManual = $this->manualService
            ->resolveUserManualForPage(
                user(),
                $page,
                $locale,
                $params
            );

        $view->with('userManual', $userManual);
    }
}
