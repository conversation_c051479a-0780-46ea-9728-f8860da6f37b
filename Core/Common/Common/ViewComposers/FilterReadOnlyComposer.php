<?php

namespace App\Core\Common\Common\ViewComposers;

use Illuminate\View\View;

class FilterReadOnlyComposer
{
    public function compose(View $view)
    {
        $readOnly = [
            'year'    => false,
            'company' => false,
        ];

        foreach ($readOnly as $key => $filter) {
            $key = $key . 'ReadOnlyStatus';
            $view->with($key, $filter);
        }
    }
}
