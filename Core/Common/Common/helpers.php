<?php

use App\Core\Common\Common\Transformers\ArraySerializer;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Common\Common\Transformers\TransformedObject;
use App\Core\Data\Models\Contracts\CurrentUserContract;
use App\Core\File\Contracts\FileServiceContract;
use App\Core\System\Session\Contracts\SessionContract;
use App\Core\System\Session\SessionManager;
use Barryvdh\Debugbar\ServiceProvider as DebugbarServiceProvider;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Session\Store;
use League\Fractal\Manager;
use League\Fractal\Resource\Collection as FractalCollection;
use League\Fractal\Resource\Item as FractalItem;
use App\Core\System\Helpers\Arr;
use Illuminate\Support\Str;

if (!function_exists('sd')) {
    /**
     * @return SessionContract
     * @noinspection PhpDocSignatureInspection
     */
    function sd(): SessionContract|SessionManager|Store
    {
        return session();
    }
}

if (!function_exists('user')) {

    /**
     * @return CurrentUserContract|null
     */
    function user(): ?CurrentUserContract
    {
        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return auth()->user();
    }
}


if (!function_exists('_l')) {
    /**
     * @param string|null $text
     * @param array|null $replace
     * @param string|null $forceLocale
     * @return string
     */
    function _l(?string $text = null, ?array $replace = null, ?string $forceLocale = null): string
    {
        $key = $text;
        $replace = $replace ?? [];
        if (!is_null($text)) {
            if (is_null($forceLocale)) {
                $text = __($text, $replace);
            } else {
                $currentLocale = app()->getLocale();
                app()->setLocale($forceLocale);
                Carbon::setLocale($forceLocale);
                setlocale(LC_TIME, $forceLocale);

                $text = __($text, $replace);

                app()->setLocale($currentLocale);
                setlocale(LC_TIME, $currentLocale);
                Carbon::setLocale($currentLocale);
            }
        }

        if (is_null($text)) {
            return '';
        }


        if (sd()->showLangKey()) {
            $text = $text . ' ⟨' . $key . '⟩';
        }

        return $text;
    }
}

if (!function_exists('upload_path')) {
    /**
     * @param string|null $path
     * @return string
     */
    function upload_path(?string $path = null): string
    {
        if (!is_null($path)) {
            $path = '/' . trim($path, '/');

            if ($path === '/') {
                $path = null;
            }
        }

        if (is_null($path)) {
            $path = '';
        }

        return storage_path('upload' . $path);
    }
}

if (!function_exists('tmp_path')) {
    function tmp_path(?string $path = null, bool $isDirectory = false): string
    {
        if (!is_null($path)) {
            $path = '/' . trim($path, '/');
            if ($path === '/') {
                $path = null;
            }
        }

        if (is_null($path) && !$isDirectory) {
            $path = '/' . Str::uuid();
        }

        $path = storage_path('tmp' . $path);

        if ($isDirectory) {
            $path = rtrim($path, '/') . '/';
        }

        if (config('app.env') !== 'production') {
            /**
             * @var \App\Core\File\Contracts\FileServiceContract $fileService
             */
            /** @noinspection PhpUnhandledExceptionInspection */
            $fileService = app()->make(FileServiceContract::class);
            $fileService->createDirIfNotExists(storage_path('tmp'));
        }

        return $path;
    }
}

if (!function_exists('transform_data')) {
    /**
     * Transforms data
     *
     * @param mixed $data
     * @param mixed $transformer
     * @return TransformedObjectContract
     */
    function transform_data(mixed $data, mixed $transformer): TransformedObjectContract
    {
        $availableTransformTypes = [
            'item',
            'collection',
        ];
        $transformAs = $transformer->transformAs ?? null;
        if (!is_null($transformAs)) {
            $transformAs = mb_strtolower($transformAs);
            if (!in_array($transformAs, $availableTransformTypes)) {
                $transformAs = null;
            }
        }

        if (is_null($transformAs)) {
            $transformAs = is_iterable($data) ? 'collection' : 'item';
        }

        $manager = new Manager();
        $includes = '';
        try {
            $includes = request()->get('with') ?? '';
        } catch (Throwable) {
        }

        $includes = explode(',', $includes);
        $includes = collect($includes)
            ->map(function ($include) {
                return trim($include);
            })
            ->filter(function ($include) {
                return !empty($include);
            })
            ->toArray();

        $manager->parseIncludes($includes);
        $manager->setSerializer(new ArraySerializer());
        if ($transformAs === 'collection') {
            $resource = new FractalCollection($data, $transformer);
        } else {
            $resource = new FractalItem($data, $transformer);
        }

        $resource = new TransformedObject($manager->createData($resource)->toArray());
        unset($manager);

        return $resource;
    }
}

if (!function_exists('_l_date_format')) {
    /**
     * @param string $format
     * @return string
     */
    function _l_date_format(string $format): string
    {
        $format = explode('date-formats.', $format);
        $format = 'date-formats.' . app()->getLocale() . '.' . end($format);

        return config($format);
    }
}

if (!function_exists('bytes_to_human')) {
    /**
     * @param int $bytes
     * @param int|null $precision
     * @return string
     */
    function bytes_to_human(int $bytes, ?int $precision = 2): string
    {
        $units = ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('random_string')) {
    /**
     * @param int $length
     * @return string
     */
    function random_string(int $length = 8): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomString;
    }
}

if (!function_exists('evat_money')) {
    /**
     * @param int|float|string $value
     * @param string $currencyCode
     * @return string
     */
    function evat_money(int|float|string $value, string $currencyCode): string
    {
        if (is_float($value) || is_int($value)) {
            $value = (float)$value;
        }

        if (is_string($value)) {
            $value = trim($value);
            $value = str_replace(',', '.', $value);
            $valueData = explode('.', $value);
            $count = count($valueData);
            if ($count !== 1) {
                $decimals = end($valueData);
                unset($valueData[$count - 1]);
                $value = '';
                foreach ($valueData as $digits) {
                    $value = $value . trim($digits);
                }
                $value = $value . '.' . $decimals;
            }
            $value = (float)$value;
        }

        return money($value, $currencyCode, true);
    }
}

if (!function_exists('evat_json_encode')) {
    /**
     * @param mixed $value
     * @param bool $pretty
     * @param array|null $options
     * @return string
     */
    function evat_json_encode(mixed $value, bool $pretty = false, ?array $options = null): string
    {
        $jsonOptions = [
            JSON_UNESCAPED_UNICODE,
            JSON_HEX_TAG,
            JSON_HEX_APOS,
            JSON_HEX_QUOT,
            JSON_HEX_AMP,
            JSON_PRESERVE_ZERO_FRACTION
        ];

        if ($pretty) {
            $jsonOptions[] = JSON_PRETTY_PRINT;
        }

        if (!is_null($options)) {
            $jsonOptions = $options;
        }

        $jsonOptions = array_sum($jsonOptions);

        return json_encode($value, $jsonOptions);
    }
}

if (!function_exists('eloquent_to_raw')) {
    /**
     * @param Builder $builder
     * @param bool $dump
     * @return string
     */
    function eloquent_to_raw(Builder $builder, bool $dump = true): string
    {
        $bindings = $builder->getBindings();
        $bindings = collect($bindings)
            ->map(function ($binding) {
                if (is_numeric($binding)) {
                    return $binding;
                }

                if (is_null($binding)) {
                    return 'null';
                }

                if ($binding === true || $binding === false) {
                    return $binding ? 'true' : 'false';
                }

                return (string)str($binding)
                    ->prepend("'")
                    ->append("'");
            })->toArray();

        $raw = (string)str($builder->toSql())->replaceArray('?', $bindings);
        if ($dump) {
            dd($raw);
        }

        return $raw;
    }
}

if (!function_exists('debugbar_message')) {
    /**
     * @param string $message
     * @param string $label
     * @return void
     */
    function debugbar_message(string $message, string $label = 'info'): void
    {
        if (!app()->environment('local') || !class_exists(DebugbarServiceProvider::class)) {
            return;
        }

        Debugbar::addMessage($message, $label);
    }
}

if (!function_exists('arr')) {

    function arr(array $data = []): Arr
    {
        return new Arr($data);
    }
}

if (!function_exists('is_api_domain')) {
    function is_api_domain(): bool
    {
        $host = request()?->getHost() ?? '';

        return Str::startsWith($host, 'api');
    }
}

if (!function_exists('is_string_binary')) {
    function is_string_binary(?string $string = null): bool
    {
        if (is_null($string)) {
            return false;
        }

        return !mb_check_encoding($string, 'UTF-8');
    }
}
