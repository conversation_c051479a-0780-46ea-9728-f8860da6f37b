<?php

namespace App\Core\Common\Common\DataTransfer;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use Throwable;

class DataTransferObject implements Arrayable, Jsonable
{
    protected array $data;

    public function __construct(?array $data = null)
    {
        $this->data = $data ?? [];
    }

    public function __set($name, $val)
    {
        $this->data[$name] = $val;
    }

    public function __get($name)
    {
        return $this->data[$name] ?? null;
    }

    public function toArray(): array
    {
        return $this->resolveArray($this->data);
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function __toString(): string
    {
        return $this->toJson();
    }

    public function toCollection(): Collection
    {
        return collect($this->toArray());
    }

    private function resolveArray($sets): array
    {
        $data = [];
        foreach ($sets as $key => $set) {
            if ($set instanceof Arrayable) {
                $set = $set->toArray();
                $set = $this->resolveArray($set);
            }

            if (is_object($set) && method_exists($set, '__toString')) {
                try {
                    $set = (string)$set;
                } catch (Throwable $e) {
                }
            }

            if (is_object($set) && method_exists($set, 'toArray')) {
                try {
                    $set = $set->toArray();
                } catch (Throwable) {
                }
            }

            $data[$key] = $set;
        }

        return $data;
    }

    public function transform(callable $function): self
    {
        $this->data = $function($this->data);

        return $this;
    }

    public function count(): int
    {
        return count($this->data);
    }

    public function hasData(): bool
    {
        return $this->count() > 0;
    }

    public function isEmpty(): bool
    {
        return !$this->hasData();
    }
}
