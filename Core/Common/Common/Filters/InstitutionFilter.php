<?php

namespace App\Core\Common\Common\Filters;

use App\Core\Common\Common\Filters\Contracts\FilterContract;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Web\Main\Transformers\SearchInstitutionTransformer;

class InstitutionFilter implements FilterContract
{
    public function isFilterItemVisible(): bool
    {
        return !is_null(user()) && user()->isCrmOrServiceProviderType();
    }

    public function getSelectAllFilterItem(): TransformedObjectContract
    {
        $selectAllInstitutions = new InstitutionInstitutionType();

        return transform_data($selectAllInstitutions, new SearchInstitutionTransformer(url()->current()));
    }

    public function toJson($options = 0)
    {
        $filterInstitution = $this->getSelectedFilterItem();
        if (is_null($filterInstitution)) {
            return json_encode(null);
        }

        return $filterInstitution->toJson();
    }

    public function getSelectedFilterItem(): ?TransformedObjectContract
    {
        $filterInstitution = sd()->getCurrentInstitutionInstitutionType();
        if (is_null($filterInstitution)) {
            return null;
        }

        return transform_data($filterInstitution, new SearchInstitutionTransformer(url()->current()));
    }

    public function toArray()
    {
        $filterInstitution = $this->getSelectedFilterItem();
        if (is_null($filterInstitution)) {
            return [];
        }

        return $filterInstitution->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getVueData(): array
    {
        $currentItem = $this->getSelectedFilterItem();
        if (!is_null($currentItem)) {
            $currentItem = $currentItem->toArray();
        }

        return [
            'isVisible'     => $this->isFilterItemVisible(),
            'order'         => 1,
            'allItemsItem'  => $this->getSelectAllFilterItem()->toArray(),
            'url'           => route('nav-filter.search.institutions'),
            'title'         => _l('common.Institution'),
            'showSearch'    => false,
            'showSelectAll' => false,
            'disabled'      => false,
            'storeKey'      => 'institutionFilter',
            'currentItem'   => $currentItem
        ];
    }
}
