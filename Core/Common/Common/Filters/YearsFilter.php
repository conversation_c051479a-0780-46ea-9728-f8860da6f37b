<?php

namespace App\Core\Common\Common\Filters;

use App\Core\Common\Common\Filters\Contracts\FilterContract;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Data\Models\Year;
use App\Web\Main\Transformers\SearchYearTransformer;

class YearsFilter implements FilterContract
{

    public function isFilterItemVisible(): bool
    {
        return true;
    }

    public function getSelectAllFilterItem(): TransformedObjectContract
    {
        $selectAllYears = new Year();

        return transform_data($selectAllYears, new SearchYearTransformer(url()->current()));
    }

    public function getSelectedFilterItem(): ?TransformedObjectContract
    {
        $filterYear = sd()->getFilterYear();
        if (is_null($filterYear)) {
            return null;
        }

        return transform_data($filterYear, new SearchYearTransformer(url()->current()));
    }

    public function toJson($options = 0)
    {
        $filterYear = $this->getSelectedFilterItem();
        if (is_null($filterYear)) {
            return json_encode(null);
        }

        return $filterYear->toJson();
    }

    public function toArray()
    {
        $filterYear = $this->getSelectedFilterItem();
        if (is_null($filterYear)) {
            return [];
        }

        return $filterYear->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getVueData(): array
    {
        $currentItem = $this->getSelectedFilterItem();
        if (!is_null($currentItem)) {
            $currentItem = $currentItem->toArray();
        }

        return [
            'isVisible'     => $this->isFilterItemVisible(),
            'order'         => 2,
            'allItemsItem'  => $this->getSelectAllFilterItem()->toArray(),
            'url'           => \route('nav-filter.search.years'),
            'title'         => \_l('common.Year'),
            'showSearch'    => false,
            'showSelectAll' => false,
            'disabled'      => false,
            'storeKey'      => 'yearsFilter',
            'currentItem'   => $currentItem
        ];
    }
}
