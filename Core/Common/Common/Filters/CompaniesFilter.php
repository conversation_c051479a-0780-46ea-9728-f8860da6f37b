<?php

namespace App\Core\Common\Common\Filters;

use App\Core\Common\Common\Filters\Contracts\FilterContract;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Data\Models\Company;
use App\Web\Main\Transformers\SearchCompanyTransformer;

class CompaniesFilter implements FilterContract
{

    public function isFilterItemVisible(): bool
    {
        return !is_null(user()) && user()->isCrmOrServiceProviderType();
    }

    public function getSelectAllFilterItem(): TransformedObjectContract
    {
        $selectAllCompanies = new Company();

        return transform_data($selectAllCompanies, new SearchCompanyTransformer(url()->current()));
    }

    public function getSelectedFilterItem(): ?TransformedObjectContract
    {
        $filterCompany = sd()->getFilterCompany();
        if (is_null($filterCompany)) {
            return null;
        }

        return transform_data($filterCompany, new SearchCompanyTransformer(url()->current()));
    }

    public function toJson($options = 0)
    {
        $filterCompany = $this->getSelectedFilterItem();
        if (is_null($filterCompany)) {
            return json_encode(null);
        }

        return $filterCompany->toJson();
    }

    public function toArray()
    {
        $filterCompany = $this->getSelectedFilterItem();
        if (is_null($filterCompany)) {
            return [];
        }

        return $filterCompany->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getVueData(): array
    {
        $currentItem = $this->getSelectedFilterItem();
        if (!is_null($currentItem)) {
            $currentItem = $currentItem->toArray();
        }

        return [
            'isVisible'     => $this->isFilterItemVisible(),
            'order'         => 3,
            'allItemsItem'  => $this->getSelectAllFilterItem()->toArray(),
            'url'           => route('nav-filter.search.companies'),
            'title'         => _l('common.Company'),
            'showSearch'    => true,
            'showSelectAll' => user()->isAdministrative() || count(user()->getAvailableCompaniesIds()) > 1,
            'disabled'      => false,
            'storeKey'      => 'companyFilter',
            'currentItem'   => $currentItem
        ];
    }
}
