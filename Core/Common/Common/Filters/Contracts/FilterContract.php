<?php

namespace App\Core\Common\Common\Filters\Contracts;

use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

interface FilterContract extends Jsonable, Arrayable
{
    /**
     * @return bool
     */
    public function isFilterItemVisible(): bool;

    /**
     * @return TransformedObjectContract
     */
    public function getSelectAllFilterItem(): TransformedObjectContract;

    /**
     * @return TransformedObjectContract|null
     */
    public function getSelectedFilterItem(): ?TransformedObjectContract;

    /**
     * @return array
     */
    public function getVueData(): array;
}
