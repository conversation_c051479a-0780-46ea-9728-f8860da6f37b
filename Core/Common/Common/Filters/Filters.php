<?php

namespace App\Core\Common\Common\Filters;

use App\Core\Common\Common\Filters\Contracts\FilterContract;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Filters implements Jsonable, Arrayable
{
    public function getCompanyFilter(): FilterContract
    {
        return new CompaniesFilter();
    }

    public function getInstitutionFilter(): FilterContract
    {
        return new InstitutionFilter();
    }

    public function getClientCompaniesFilter(): FilterContract
    {
        return new ClientCompaniesFilter();
    }

    public function getYearsFilter(): FilterContract
    {
        return new YearsFilter();
    }

    public function getVueData(): string
    {
        $data = [
            '_1' => $this->getInstitutionFilter()->getVueData(),
            '_2' => $this->getYearsFilter()->getVueData(),
            '_3' => $this->getClientCompaniesFilter()->getVueData(),
            '_4' => $this->getCompanyFilter()->getVueData(),
        ];

        return evat_json_encode($data);
    }

    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }

    public function toArray()
    {
        return [
            'institutionFilter'   => $this->getInstitutionFilter()->toArray(),
            'companyFilter'       => $this->getCompanyFilter()->toArray(),
            'userCompaniesFilter' => $this->getClientCompaniesFilter()->toArray(),
            'yearsFilter'         => $this->getYearsFilter()->toArray()
        ];
    }
}
