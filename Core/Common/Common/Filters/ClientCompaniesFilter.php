<?php

namespace App\Core\Common\Common\Filters;

use App\Core\Common\Common\Filters\Contracts\FilterContract;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Data\Models\Company;
use App\Web\Main\Transformers\SearchClientCompanyTransformer;

class ClientCompaniesFilter implements FilterContract
{

    public function isFilterItemVisible(): bool
    {
        return !is_null(user()) && user()->isClientType();
    }

    public function getSelectAllFilterItem(): TransformedObjectContract
    {
        $selectAllClientCompanies = new Company();

        return transform_data($selectAllClientCompanies, new SearchClientCompanyTransformer(url()->current()));
    }

    public function getSelectedFilterItem(): ?TransformedObjectContract
    {
        $filterClientCompany = sd()->getFilterCompany();
        if (is_null($filterClientCompany)) {
            return null;
        }

        return transform_data($filterClientCompany, new SearchClientCompanyTransformer(url()->current()));
    }

    public function toJson($options = 0)
    {
        $filterClientCompany = $this->getSelectedFilterItem();
        if (is_null($filterClientCompany)) {
            return json_encode(null);
        }

        return $filterClientCompany->toJson();
    }

    public function toArray()
    {
        $filterClientCompany = $this->getSelectedFilterItem();
        if (is_null($filterClientCompany)) {
            return [];
        }

        return $filterClientCompany->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getVueData(): array
    {
        $currentItem = $this->getSelectedFilterItem();
        if (!is_null($currentItem)) {
            $currentItem = $currentItem->toArray();
        }

        return [
            'isVisible'     => $this->isFilterItemVisible(),
            'order'         => 3,
            'allItemsItem'  => $this->getSelectAllFilterItem()->toArray(),
            'url'           => route('nav-filter.search.client-companies'),
            'title'         => _l('common.Company'),
            'showSearch'    => false,
            'showSelectAll' => sd()->getAllCompaniesRoles()->flatten()->keyBy('company_id')->count() > 1,
            'disabled'      => false,
            'storeKey'      => 'userCompaniesFilter',
            'currentItem'   => $currentItem
        ];
    }
}
