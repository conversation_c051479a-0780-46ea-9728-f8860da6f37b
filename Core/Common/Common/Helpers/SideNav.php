<?php

namespace App\Core\Common\Common\Helpers;

use App\Core\Data\Models\InstitutionType;
use App\Core\Data\Models\Warehouse;
use Illuminate\Support\Facades\Cache;

class SideNav
{
    public static function showAmazonReports(): bool
    {
        $currentInstitutionTypeId = sd()->getCurrentInstitutionTypeId();
        $isPartnerOrAccountant = ($currentInstitutionTypeId === InstitutionType::TYPE_PARTNER || $currentInstitutionTypeId === InstitutionType::TYPE_ACCOUNTANT);
        $hasRequiredRoles = user()->hasEvatAdminRoles() || user()->hasCustomerSupportRole() || user()->hasPartnerRegistrationTeamRole() || user()->hasAccountantRegistrationTeamRole();

        return ($isPartnerOrAccountant && $hasRequiredRoles) && !user()->isClientType();
    }

    public static function showCrmSpReports(): bool
    {
        $currentInstitutionTypeId = sd()->getCurrentInstitutionTypeId();
        $isPartnerOrAccountant = ($currentInstitutionTypeId === InstitutionType::TYPE_PARTNER || $currentInstitutionTypeId === InstitutionType::TYPE_ACCOUNTANT);
        $hasRequiredRoles = user()->hasEvatAdminRoles() || user()->hasCustomerSupportRole();

        return ($isPartnerOrAccountant && $hasRequiredRoles) && !user()->isClientType();
    }

    public static function showRegistration(): bool
    {
        $currentInstitutionTypeId = sd()->getCurrentInstitutionTypeId();
        $isPartnerOrAccountant = ($currentInstitutionTypeId === InstitutionType::TYPE_PARTNER || $currentInstitutionTypeId === InstitutionType::TYPE_ACCOUNTANT);
        $hasRequiredRoles = user()->hasEvatAdminRoles() || user()->hasCustomerSupportRole() || user()->hasPartnerRegistrationTeamRole() || user()->hasAccountantRegistrationTeamRole();
        $isClientType = user()->isClientType();

        return ($isPartnerOrAccountant && $hasRequiredRoles) || $isClientType;
    }

    public static function showRegistrationClass(): string
    {
        return request()->is('registration-process')
        && request()->route()->getName() !== 'registration-process.single-company-index' ? 'active' : '';
    }

    public static function showTeams(): bool
    {
        return user()->isAdministrative() || user()->hasOneOfAdminRoles() || user()->hasTeamLeaderRole();
    }

    public static function showUsers(): bool
    {
        return user()->isAdministrative() || user()->hasOneOfAdminRoles();
    }

    public static function showMyCompanySubmenu(): bool
    {
        return !is_null(sd()->getFilterCompany());
    }

    public static function showMyCompanyRegistration(): bool
    {
        $allowedInstitutions = [
            InstitutionType::TYPE_PARTNER,
            InstitutionType::TYPE_ACCOUNTANT,
        ];

        return user()->isClientType()
            || (in_array(sd()->getCurrentInstitutionTypeId(), $allowedInstitutions))
            && (user()->hasEvatAdminRoles() || user()->hasCustomerSupportRole() || user()->hasPartnerRegistrationTeamRole() || user()->hasAccountantRegistrationTeamRole());
    }

    public static function showMyCompanyInvoices(): bool
    {
        return true;
    }

    public static function showMyCompanyIdentificationNumbersClassActive(): string
    {
        return request()->is('companies/tax-data*') ? 'active' : '';
    }

    public static function showMyCompanySetupClassActive(): string
    {
        return request()->is('companies/setup*')
        || request()->is('users/client-users*') ? 'active' : '';
    }

    public static function showMyCompanySetupClassShow(): string
    {
        return request()->is('companies/setup*')
        || request()->is('users/client-users*') ? 'show' : '';
    }

    public static function showSetupClassActive(): string
    {
        return request()->is('teams*')
        || request()->is('users*')
        || request()->is('crm/report-templates*') ? 'active' : '';
    }

    public static function showSetupClassShow(): string
    {
        return request()->is('teams*')
        || request()->is('users*')
        || request()->is('crm/report-templates*') ? 'show' : '';
    }

    public static function isCrmSpReportsRoute(): bool
    {
        return request()->is('crm-sp/reports*');
    }

    public static function isCompanyReportsRoute(): bool
    {
        return request()->is('reports*');
    }

    public static function showMyCompanySetupAccountants(): bool
    {
        $isClient = sd()->isClient();
        $clientCompany = sd()->getFilterCompany();
        $isPullusCompany = false;
        if (!is_null($clientCompany)) {
            $isPullusCompany = $clientCompany->partner_id === config('evat.register.partnerId');
        }

        return (!$isClient && (user()->isCrmType()) || user()->isServiceProviderType()) || ($isClient && $isPullusCompany);
    }

    public static function showMyCompanySetupSalesChannels(): bool
    {
        return sd()->isClient() || user()?->isAdministrative() || user()->isCrmType() || user()->isServiceProviderType();
    }

    public static function showMyCompanySetupItems(): bool
    {
        return user()->isClientType() || user()->isDeveloper() || user()->isServiceProviderType();
    }

    public static function showMyCompanySetupWarehouses(): bool
    {
        return user()->isClientType() || user()->hasAnyPartnerRole() || user()->isServiceProviderType();
    }

    public static function showMyCompanySetupUsers(): bool
    {
        return user()->isAdministrative() || (user()->isClientAdmin() && sd()->isClient());
    }

    public static function showAdministration(): bool
    {
        return user()->isAdministrative();
    }

    public static function showAdministrationWarehouseNotifyClass(): string
    {
        return Cache::get(Warehouse::ALL_VIEWS_PROPOSED_CACHE_KEY)->count() > 0 ? 'notify' : '';
    }

    public static function showAdministrationHelpersActive(): string
    {
        $show = '';
        if (request()->is('developers/import-companies*') || request()->is('brands*') || request()->is('tax-codes*')) {
            $show = 'active';
        }

        return $show;
    }

    public static function showAdministrationHelpersShow(): string
    {
        $show = '';
        if (request()->is('developers/import-companies*') || request()->is('brands*') || request()->is('tax-codes*')) {
            $show = 'show';
        }

        return $show;
    }

    public static function showAdministrationDocumentsActive(): string
    {
        return self::showDocuments() ? 'active' : '';
    }

    public static function showAdministrationDocumentsShow(): string
    {
        return self::showDocuments() ? 'show' : '';
    }

    private static function showDocuments(): bool
    {
        return request()->is('documents/sample-files*')
            || request()->is('documents/categories*')
            || request()->is('documents/document-types*')
            || request()->is('documents/document-template*');
    }

    public static function showDevelopers(): bool
    {
        return user()->isDeveloper();
    }

    public static function isDeveloperSystemRoute(): bool
    {
        return request()->is('developers/system*');
    }

    public static function isDeveloperDevelopersRoute(): bool
    {
        return request()->is('developers/developer/*');
    }

    public static function isDeveloperUiRoute(): bool
    {
        return request()->is('developers/ui/*');
    }

    public static function showCompanyReportTemplates(): bool
    {
        return false;

//        return user()->isDeveloper() || user()->isClientAdmin();
    }

    public static function showCrmReportTemplates(): bool
    {
        return false;

//        return user()->isDeveloper() || user()->isCrmAdmin();
    }
}
