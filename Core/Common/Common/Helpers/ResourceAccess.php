<?php

namespace App\Core\Common\Common\Helpers;

use App\Core\Data\Models\RegistrationProcess;

class ResourceAccess
{
    public static function showRegistrationProcess(RegistrationProcess $registrationProcess): bool
    {
        if (user()->isDeveloper()) {
            return true;
        }
        if (sd()->isServiceProvider()) {
            $allowedCountryIds = $registrationProcess
                ->company
                ->accountants
                ->where('institution_institution_type_id', sd()->getCurrentInstitutionInstitutionTypeId())
                ->pluck('country_id')
                ->toArray();

            return in_array($registrationProcess->country_id, $allowedCountryIds) ? true : abort(403);
        }

        if (sd()->isCrm()) {
            $partnerId = $registrationProcess
                ->company
                ->partner_id;

            return sd()->getCurrentInstitutionInstitutionType()?->id === $partnerId ? true : abort(403);
        }

        if (user()->isClientType()) {
            return in_array($registrationProcess->company_id, user()->getAvailableCompaniesIds()) ? true : abort(403);
        }

        abort(403);
    }
}
