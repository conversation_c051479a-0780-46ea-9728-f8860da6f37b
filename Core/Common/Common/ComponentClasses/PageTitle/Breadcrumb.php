<?php

namespace App\Core\Common\Common\ComponentClasses\PageTitle;

class Breadcrumb
{
    public ?string $title;
    public ?string $link;
    public ?string $icon;
    public ?bool $newTab;

    public function __construct(?string $title = null, ?string $link = null, ?string $icon = null, ?bool $newTab = null)
    {
        $this->title = $title;
        $this->link = $link;
        $this->icon = $icon;
        $this->newTab = $newTab;
    }

}
