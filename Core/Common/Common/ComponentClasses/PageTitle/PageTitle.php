<?php

namespace App\Core\Common\Common\ComponentClasses\PageTitle;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;

class PageTitle implements Arrayable
{
    public string $title;
    public ?Collection $breadcrumbs;
    public ?Collection $subtitles;

    public function __construct(string $title, ?Collection $breadcrumbs = null, ?Collection $subtitles = null)
    {
        $this->title = $title;
        $this->breadcrumbs = $breadcrumbs;
        $this->subtitles = $subtitles;
    }

    public function setBreadcrumb(string $title, ?string $link = null, ?string $icon = null, ?bool $newTab = null): PageTitle
    {
        if (is_null($this->breadcrumbs)) {
            $this->breadcrumbs = collect();
        }
        $breadcrumb = new Breadcrumb($title, $link, $icon, $newTab);

        $this->breadcrumbs->push($breadcrumb);

        return $this;
    }

    public static function make(string $title, ?Collection $breadcrumbs = null, ?Collection $subtitles = null): self
    {
        return new self($title, $breadcrumbs, $subtitles);
    }

    public function setSubtitle(string $title, ?string $link = null, ?bool $newTab = null): PageTitle
    {
        if (is_null($this->subtitles)) {
            $this->subtitles = collect();
        }
        $subtitle = new Subtitle($title, $link, $newTab);

        $this->subtitles->push($subtitle);

        return $this;
    }

    public function toJson(): string
    {
        return evat_json_encode($this->toArray());
    }

    public function toArray(): array
    {
        return [
            'title'       => $this->title,
            'breadcrumbs' => $this->breadcrumbs?->toArray(),
            'subtitles'   => $this->subtitles?->toArray(),
        ];
    }
}
