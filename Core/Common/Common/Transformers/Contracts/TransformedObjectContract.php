<?php

namespace App\Core\Common\Common\Transformers\Contracts;

use Illuminate\Support\Collection;

interface TransformedObjectContract
{
    public function toArray(): array;

    public function toCollection(): Collection;

    public function toJson(int $options = 0): string;

    public function transform(callable $function): self;

    public function count(): int;

    public function hasData(): bool;

    public function isEmpty(): bool;
}
