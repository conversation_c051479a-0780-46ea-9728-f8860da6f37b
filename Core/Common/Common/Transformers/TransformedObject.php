<?php

namespace App\Core\Common\Common\Transformers;

use App\Core\Common\Common\DataTransfer\DataTransferObject;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;

class TransformedObject extends DataTransferObject implements TransformedObjectContract
{
    public function transform(callable $function): self
    {
        parent::transform($function);

        return $this;
    }
}
