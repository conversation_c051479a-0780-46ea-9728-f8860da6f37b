<?php

namespace App\Core\Contacts;

use App\Core\Contacts\Contracts\ContactServiceContract;
use App\Core\Data\Models\Contact;
use App\Core\Data\Repositories\Contracts\ContactRepositoryContract;

class ContactService implements ContactServiceContract
{
    private ContactRepositoryContract $contactRepo;

    public function __construct(ContactRepositoryContract $contactRepo)
    {
        $this->contactRepo = $contactRepo;
    }

    public function storeContact(
        string $value,
        int $contactTypeId,
        ?int $personId = null,
        ?int $companyId = null,
        ?int $institutionId = null,
    ): Contact {
        $contact = $this->contactRepo->getEmptyContactModel();
        $contact->value = $value;
        $contact->person_id = $personId;
        $contact->company_id = $companyId;
        $contact->institution_id = $institutionId;
        $contact->contact_type_id = $contactTypeId;

        $contact->save();

        return $contact;
    }

    public function storeUpdateDeleteContact(
        int $contactTypeId,
        ?int $contactId = null,
        ?string $value = null,
        ?int $personId = null,
        ?int $companyId = null,
        ?int $institutionId = null
    ): string|null {
        $actionUpdate = true;

        if ($contactId) {
            if ($value == null) {
                $this->contactRepo->deleteContactById($contactId);

                return 'delete';
            }
            $contact = $this->contactRepo->getContactById($contactId);
            if (is_null($contact)) {
                return null;
            }

        } elseif ($value) {
            $contact = $this->contactRepo->getEmptyContactModel();
            $actionUpdate = false;
        } else {
            return null;
        }

        if (
            $actionUpdate &&
            $contact->value === $value &&
            $contact->person_id === $personId &&
            $contact->company_id === $companyId &&
            $contact->institution_id === $institutionId &&
            $contact->contact_type_id === $contactTypeId
        ) {
            return null;
        }

        $contact->value = $value;
        $contact->person_id = $personId;
        $contact->company_id = $companyId;
        $contact->institution_id = $institutionId;
        $contact->contact_type_id = $contactTypeId;

        $contact->save();

        if ($actionUpdate) {
            return 'update';
        } else {
            return 'store';
        }
    }
}
