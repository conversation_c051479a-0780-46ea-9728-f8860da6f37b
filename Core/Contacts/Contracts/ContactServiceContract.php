<?php

namespace App\Core\Contacts\Contracts;

use App\Core\Data\Models\Contact;

interface ContactServiceContract
{
    public function storeContact(
        string $value,
        int $contactTypeId,
        ?int $personId = null,
        ?int $companyId = null,
        ?int $institutionId = null,
    ): Contact;

    public function storeUpdateDeleteContact(
        int $contactTypeId,
        ?int $contactId = null,
        ?string $value = null,
        ?int $personId = null,
        ?int $companyId = null,
        ?int $institutionId = null
    ): string|null;
}
