<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use stdClass;

class FranceVatStatementApiTransfer implements Arrayable, Jsonable
{
    /**
     * @var FranceVatStatementApiAuthTransfer
     */
    private $apiAuthTransfer;
    /**
     * @var FranceVatStatementApiIdentityTransfer
     */
    private $apiIdentityTransfer;
    /**
     * @var FranceVatStatementApiPeriodTransfer
     */
    private $apiPeriodTransfer;
    /**
     * @var FranceVatStatementApi3310CA3Transfer
     */
    private $api3310CA3Transfer;

    public function __construct(
        FranceVatStatementApiAuthTransfer $apiAuthTransfer,
        FranceVatStatementApiIdentityTransfer $apiIdentityTransfer,
        FranceVatStatementApiPeriodTransfer $apiPeriodTransfer,
        FranceVatStatementApi3310CA3Transfer $api3310CA3Transfer
    ) {
        $this->apiAuthTransfer = $apiAuthTransfer;
        $this->apiIdentityTransfer = $apiIdentityTransfer;
        $this->apiPeriodTransfer = $apiPeriodTransfer;
        $this->api3310CA3Transfer = $api3310CA3Transfer;
    }

    /**
     * @return FranceVatStatementApiAuthTransfer
     */
    public function getApiAuthTransfer(): FranceVatStatementApiAuthTransfer
    {
        return $this->apiAuthTransfer;
    }

    /**
     * @return FranceVatStatementApiIdentityTransfer
     */
    public function getApiIdentityTransfer(): FranceVatStatementApiIdentityTransfer
    {
        return $this->apiIdentityTransfer;
    }

    /**
     * @return FranceVatStatementApiPeriodTransfer
     */
    public function getApiPeriodTransfer(): FranceVatStatementApiPeriodTransfer
    {
        return $this->apiPeriodTransfer;
    }

    /**
     * @return FranceVatStatementApi3310CA3Transfer
     */
    public function getApi3310CA3Transfer(): FranceVatStatementApi3310CA3Transfer
    {
        return $this->api3310CA3Transfer;
    }

    public function toArray()
    {
        return [
            'auth'     => $this->getApiAuthTransfer()->toArray(),
            'identity' => $this->getApiIdentityTransfer()->toArray(),
            'period'   => $this->getApiPeriodTransfer()->toArray(),
            '3310CA3'  => $this->getApi3310CA3Transfer()->toArray(),
        ];
    }

    public function toJson($options = 0)
    {
        $dataSet = $this->toArray();
        $data = [];
        foreach ($dataSet as $key => $value) {
            if (is_array($value) && count($value) < 1) {
                $value = new stdClass();
            }
            $data[$key] = $value;
        }

        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
