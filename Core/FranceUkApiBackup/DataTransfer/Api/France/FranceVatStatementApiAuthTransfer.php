<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class FranceVatStatementApiAuthTransfer implements Arrayable, Jsonable
{
    private string $email;
    private string $timestamp;
    private string $timestampFormat = 'Y-m-d\TH:i:s';

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return $this
     */
    public function setEmail(string $email): FranceVatStatementApiAuthTransfer
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @return string
     */
    public function getTimestamp(): string
    {
        return $this->timestamp;
    }

    /**
     * @param string $timestamp
     * @return FranceVatStatementApiAuthTransfer
     */
    public function setTimestamp(string $timestamp): FranceVatStatementApiAuthTransfer
    {
        $timestamp = Carbon::parse($timestamp);

        return $this->setTimestampFromCarbon($timestamp);
    }

    /**
     * @param Carbon $timestamp
     * @return FranceVatStatementApiAuthTransfer
     */
    public function setTimestampFromCarbon(Carbon $timestamp): FranceVatStatementApiAuthTransfer
    {
        $this->timestamp = $timestamp->format($this->timestampFormat);

        return $this;
    }

    public function toArray(): array
    {
        return [
            'email'     => $this->getEmail(),
            'timestamp' => $this->getTimestamp()
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
