<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class FranceVatStatementApiPeriodTransfer implements Arrayable, Jsonable
{
    /**
     * @var string
     */
    private $begin;

    /**
     * @var string
     */
    private $end;

    /**
     * @var int
     */
    private $year;

    /**
     * @var bool | null
     */
    private $suspension;

    /**
     * @var string
     */
    private $reference;

    /**
     * @var string | null
     */
    private $dueDate;

    /**
     * @var int | null
     */
    private $amount;

    /**
     * @var bool | null
     */
    private $noPayment;

    /**
     * @return string
     */
    public function getBegin(): string
    {
        return $this->begin;
    }

    /**
     * @param string $begin
     * @return $this
     */
    public function setBegin(string $begin): FranceVatStatementApiPeriodTransfer
    {
        $this->begin = $begin;

        return $this;
    }

    /**
     * @return string
     */
    public function getEnd(): string
    {
        return $this->end;
    }

    /**
     * @param string $end
     * @return $this
     */
    public function setEnd(string $end): FranceVatStatementApiPeriodTransfer
    {
        $this->end = $end;

        return $this;
    }

    /**
     * @return int
     */
    public function getYear(): int
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return $this
     */
    public function setYear(int $year): FranceVatStatementApiPeriodTransfer
    {
        $this->year = $year;

        return $this;
    }

    /**
     * @return bool|null
     */
    public function isSuspended(): ?bool
    {
        return $this->suspension;
    }

    /**
     * @param bool|null $suspension
     * @return $this
     */
    public function setSuspension(?bool $suspension = null): FranceVatStatementApiPeriodTransfer
    {
        $this->suspension = $suspension;

        return $this;
    }

    /**
     * @return string
     */
    public function getReference(): string
    {
        return $this->reference;
    }

    /**
     * @param string $reference
     * @return $this
     */
    public function setReference(string $reference): FranceVatStatementApiPeriodTransfer
    {
        $this->reference = $reference;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getDueDate(): ?string
    {
        return $this->dueDate;
    }

    /**
     * @param string|null $dueDate
     * @return $this
     */
    public function setDueDate(?string $dueDate = null): FranceVatStatementApiPeriodTransfer
    {
        if (!is_null($dueDate)) {
            $dueDate = trim($dueDate);
            if (mb_strlen($dueDate) < 1) {
                $dueDate = null;
            }
        }
        $this->dueDate = $dueDate;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getAmount(): ?int
    {
        return $this->amount;
    }

    /**
     * @param int|null $amount
     * @return $this
     */
    public function setAmount(?int $amount = null): FranceVatStatementApiPeriodTransfer
    {
        $this->amount = $amount;

        return $this;
    }

    /**
     * @return bool|null
     */
    public function getNoPayment(): ?bool
    {
        return $this->noPayment;
    }

    /**
     * @param bool|null $noPayment
     * @return FranceVatStatementApiPeriodTransfer
     */
    public function setNoPayment(?bool $noPayment = null): FranceVatStatementApiPeriodTransfer
    {
        $this->noPayment = $noPayment;

        return $this;
    }

    public function toArray()
    {
        $amount = $this->getAmount();
        $suspension = $this->isSuspended();
        $noPayment = $this->getNoPayment();

        $data = [
            'begin'     => $this->getBegin(),
            'end'       => $this->getEnd(),
            'millesime' => $this->getYear(),
            'reference' => $this->getReference(),
        ];

        if (!is_null($amount)) {
            $data['amount'] = $amount;
        }

        if (!is_null($noPayment)) {
            $data['noPayment'] = $noPayment;
        }

        if (!is_null($suspension)) {
            $data['suspension'] = $suspension;
        }

        return $data;
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
