<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class FranceVatStatementApiResponse implements Arrayable, Jsonable
{
    /**
     * @var string|null
     */
    private $message;
    /**
     * @var bool
     */
    private $success;

    public function __construct(bool $success, ?string $message = null)
    {
        $this->message = $message;
        $this->success = $success;
    }

    /**
     * @return string|null
     */
    public function getMessage(): ?string
    {
        return $this->message;
    }

    /**
     * @return bool
     */
    public function isSuccessful(): bool
    {
        return $this->success;
    }

    public function toArray()
    {
        return [
            'success' => $this->isSuccessful(),
            'message' => $this->getMessage()
        ];
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
