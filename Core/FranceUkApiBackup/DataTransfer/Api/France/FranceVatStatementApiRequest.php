<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use App\Core\Data\Models\TeledecResponseType;
use ErrorException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use Throwable;

class FranceVatStatementApiRequest
{
    /**
     * @var FranceVatStatementApiTransfer
     */
    private $requestData;

    /**
     * @var string
     */
    private $apiEndpointUrl;

    /**
     * @var string
     */
    private $privateKey;

    /**
     * @var int
     */
    private $timeout = 5;

    /**
     * @var int
     */
    private $connectionTimeout = 5;

    public function __construct(
        FranceVatStatementApiTransfer $requestData,
        string $privateKey
    ) {
        $this->requestData = $requestData;
        $this->privateKey = $privateKey;
        $this->apiEndpointUrl = config('evat.france.teledec.apiEndpointUrl');
    }

    /**
     * @return FranceVatStatementApiResponse
     * @throws ErrorException
     * @throws FranceApiException
     */
    public function make(): FranceVatStatementApiResponse
    {
        $hash = $this->makeHash();
        $clientOptions = ['base_uri' => $this->getTeledecApiBaseUrl()];
        $client = new Client($clientOptions);
        $endpoint = $this->getApiEndpoint();

        try {
            $requestOptions = [
                'body'            => $this->requestData->toJson(),
                // Timeout seconds
                'connect_timeout' => $this->connectionTimeout,
                'timeout'         => $this->timeout,
                'query'           => [
                    'hash' => $hash
                ]
            ];
            $response = $client->request(
                'POST',
                $endpoint,
                $requestOptions
            );
            $contents = $response->getBody()->getContents();
            $data = json_decode($contents, true);
            if (is_null($data) && is_string($contents)) {
                throw new ErrorException($contents, 0, E_USER_ERROR);
            }
            $success = ($data['status'] ?? 'not ok') === 'ok';
            $message = $data['message'] ?? null;

            if (!is_null($message)) {
                $message = ucfirst($message);
            }
            if (!$success) {
//                throw new FranceApiException(false, TeledecResponseType::TYPE_FAILED, $message);
                throw new FranceApiException(false, TeledecResponseType::TYPE_FAILED_SUBMIT, $message);
            }

            return new FranceVatStatementApiResponse(true, $message);
        } catch (ConnectException $exception) {
            $errorCode = (int)$exception->getHandlerContext()['errno'] ?? 0;
            if ($errorCode === 28) {
                return $this->make();
            }
            throw $exception;
        } catch (ErrorException $exception) {
            throw $exception;
        } catch (FranceApiException $exception) {
            throw $exception;
        } catch (Throwable $exception) {
            throw new FranceApiException(false, TeledecResponseType::TYPE_FAILED_SUBMIT, $exception->getMessage());
        }
    }

    /**
     * @return string
     */
    private function getApiEndpoint(): string
    {
        $url = $this->resolveApiEndpointUrl();

        return $url['route'];
    }

    /**
     * @return string
     */
    private function getTeledecApiBaseUrl(): string
    {
        $url = $this->resolveApiEndpointUrl();

        return $url['protocol'] . $url['base'];
    }

    private function resolveApiEndpointUrl(): array
    {
        $url = rtrim($this->apiEndpointUrl, '/');
        $url = explode('//', $url);
        $protocol = trim($url[0]) . '//';
        $url = $url[1];
        $url = explode('/', $url);
        $base = trim($url[0]);
        unset($url[0]);
        $route = '/' . implode('/', $url);
        $url = [
            'protocol' => $protocol,
            'base'     => $base,
            'route'    => $route,
        ];

        return $url;
    }

    /**
     * @return string
     */
    private function makeHash(): string
    {
        return hash_hmac('sha256', $this->requestData->toJson(), $this->privateKey);
    }
}
