<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use App\Core\Data\Models\TeledecResponseType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class FranceVatStatementApiIdentityTransfer implements Arrayable, Jsonable
{

    /**
     * @var string
     */
    private $siret;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $fullRegimeFiscal;

    /**
     * @var string
     */
    private $regimeFiscalTVA;

    /**
     * @var int
     */
    private $yearEndMonth;

    /**
     * @var int
     */
    private $yearEndDay;

    /**
     * @var string
     */
    private $addressStreet;

    /**
     * @var string | null
     */
    private $addressComplement;

    /**
     * @var string | null
     */
    private $addressNeighbourhood;

    /**
     * @var string
     */
    private $addressPostalCode;

    /**
     * @var string
     */
    private $addressCity;

    /**
     * @var string
     */
    private $addressCountry = '';

    /**
     * @var string
     */
    private $legalForm;

    /**
     * @var string
     */
    private $legalRepresentative;

    /**
     * @var string
     */
    private $legalRepresentativeAs;

    /**
     * @var string
     */
    private $legalRepresentativeTitle;

    /**
     * @var string | null
     */
    private $telephone;

    /**
     * @var string
     */
    private $email;

    /**
     * @var string | null
     */
    private $BIC;

    /**
     * @var string | null
     */
    private $IBAN;

    /**
     * @return string
     */
    public function getSiret(): string
    {
        return $this->siret;
    }

    /**
     * @param string $siret
     * @return $this
     */
    public function setSiret(string $siret): FranceVatStatementApiIdentityTransfer
    {
        $siret = preg_replace('/\s+/', '', $siret);
        $this->siret = $siret;

        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return $this
     */
    public function setName(string $name): FranceVatStatementApiIdentityTransfer
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return string
     */
    public function getFullRegimeFiscal(): string
    {
        return $this->fullRegimeFiscal;
    }

    /**
     * @param string $fullRegimeFiscal
     * @return $this
     */
    public function setFullRegimeFiscal(string $fullRegimeFiscal): FranceVatStatementApiIdentityTransfer
    {
        $this->fullRegimeFiscal = $fullRegimeFiscal;

        return $this;
    }

    /**
     * @return string
     */
    public function getRegimeFiscalTVA(): string
    {
        return $this->regimeFiscalTVA;
    }

    /**
     * @param string $regimeFiscalTVA
     * @return $this
     */
    public function setRegimeFiscalTVA(string $regimeFiscalTVA): FranceVatStatementApiIdentityTransfer
    {
        $this->regimeFiscalTVA = $regimeFiscalTVA;

        return $this;
    }

    /**
     * @return int
     */
    public function getYearEndMonth(): int
    {
        return $this->yearEndMonth;
    }

    /**
     * @param int $yearEndMonth
     * @return $this
     */
    public function setYearEndMonth(int $yearEndMonth): FranceVatStatementApiIdentityTransfer
    {
        $this->yearEndMonth = $yearEndMonth;

        return $this;
    }

    /**
     * @return int
     */
    public function getYearEndDay(): int
    {
        return $this->yearEndDay;
    }

    /**
     * @param int $yearEndDay
     * @return $this
     */
    public function setYearEndDay(int $yearEndDay): FranceVatStatementApiIdentityTransfer
    {
        $this->yearEndDay = $yearEndDay;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddressStreet(): string
    {
        return $this->addressStreet;
    }

    /**
     * @param string $addressStreet
     * @return $this
     */
    public function setAddressStreet(string $addressStreet): FranceVatStatementApiIdentityTransfer
    {
        $this->addressStreet = $addressStreet;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getAddressComplement(): ?string
    {
        return $this->addressComplement;
    }

    /**
     * @param string|null $addressComplement
     * @return $this
     */
    public function setAddressComplement(?string $addressComplement = null): FranceVatStatementApiIdentityTransfer
    {
        if (!is_null($addressComplement)) {
            $addressComplement = trim($addressComplement);
            if (mb_strlen($addressComplement) < 1) {
                $addressComplement = null;
            }
        }
        $this->addressComplement = $addressComplement;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getAddressNeighbourhood(): ?string
    {
        return $this->addressNeighbourhood;
    }

    /**
     * @param string|null $addressNeighbourhood
     * @return $this
     */
    public function setAddressNeighbourhood(?string $addressNeighbourhood = null): FranceVatStatementApiIdentityTransfer
    {
        if (!is_null($addressNeighbourhood)) {
            $addressNeighbourhood = trim($addressNeighbourhood);
            if (mb_strlen($addressNeighbourhood) < 1) {
                $addressNeighbourhood = null;
            }
        }
        $this->addressNeighbourhood = $addressNeighbourhood;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddressPostalCode(): string
    {
        return $this->addressPostalCode;
    }

    /**
     * @param string $addressPostalCode
     * @return $this
     */
    public function setAddressPostalCode(string $addressPostalCode): FranceVatStatementApiIdentityTransfer
    {
        $this->addressPostalCode = $addressPostalCode;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddressCity(): string
    {
        return $this->addressCity;
    }

    /**
     * @param string $addressCity
     * @return $this
     */
    public function setAddressCity(string $addressCity): FranceVatStatementApiIdentityTransfer
    {
        $this->addressCity = $addressCity;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddressCountry(): string
    {
        return $this->addressCountry;
    }

    /**
     * @param string $addressCountry
     * @return $this
     */
    public function setAddressCountry(string $addressCountry): FranceVatStatementApiIdentityTransfer
    {
        $this->addressCountry = $addressCountry;

        return $this;
    }

    /**
     * @return string
     */
    public function getLegalForm(): string
    {
        return $this->legalForm;
    }

    /**
     * @param string $legalForm
     * @return $this
     */
    public function setLegalForm(string $legalForm): FranceVatStatementApiIdentityTransfer
    {
        $this->legalForm = $legalForm;

        return $this;
    }

    /**
     * @return string
     */
    public function getLegalRepresentative(): string
    {
        return $this->legalRepresentative;
    }

    /**
     * @param string $legalRepresentative
     * @return $this
     */
    public function setLegalRepresentative(string $legalRepresentative): FranceVatStatementApiIdentityTransfer
    {
        $this->legalRepresentative = $legalRepresentative;

        return $this;
    }

    /**
     * @return string
     */
    public function getLegalRepresentativeAs(): string
    {
        return $this->legalRepresentativeAs;
    }

    /**
     * @param string $legalRepresentativeAs
     * @return $this
     */
    public function setLegalRepresentativeAs(string $legalRepresentativeAs): FranceVatStatementApiIdentityTransfer
    {
        $this->legalRepresentativeAs = $legalRepresentativeAs;

        return $this;
    }

    /**
     * @return string
     */
    public function getLegalRepresentativeTitle(): string
    {
        return $this->legalRepresentativeTitle;
    }

    /**
     * @param string $legalRepresentativeTitle
     * @return $this
     */
    public function setLegalRepresentativeTitle(string $legalRepresentativeTitle): FranceVatStatementApiIdentityTransfer
    {
        $this->legalRepresentativeTitle = $legalRepresentativeTitle;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    /**
     * @param string|null $telephone
     * @return $this
     */
    public function setTelephone(?string $telephone = null): FranceVatStatementApiIdentityTransfer
    {
        if (!is_null($telephone)) {
            $telephone = trim($telephone);
            if (mb_strlen($telephone) < 1) {
                $telephone = null;
            }
        }
        $this->telephone = $telephone;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return $this
     */
    public function setEmail(string $email): FranceVatStatementApiIdentityTransfer
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getBIC(): ?string
    {
        return $this->BIC;
    }

    /**
     * @param string|null $BIC
     * @return $this
     */
    public function setBIC(?string $BIC = null): FranceVatStatementApiIdentityTransfer
    {
        if (!is_null($BIC)) {
            $BIC = trim($BIC);
            if (mb_strlen($BIC) < 1) {
                $BIC = null;
            }
        }
        $this->BIC = $BIC;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getIBAN(): ?string
    {
        return $this->IBAN;
    }

    /**
     * @param string|null $IBAN
     * @return $this
     */
    public function setIBAN(?string $IBAN = null): FranceVatStatementApiIdentityTransfer
    {
        if (!is_null($IBAN)) {
            $IBAN = trim($IBAN);
            if (mb_strlen($IBAN) < 1) {
                $IBAN = null;
            }
        }
        $this->IBAN = $IBAN;

        return $this;
    }

    /**
     * @return array
     * @throws FranceApiException
     */
    public function toArray()
    {
        $iban = $this->getIBAN();
        $bic = $this->getBIC();
        $telephone = $this->getTelephone();
        $addressComplement = $this->getAddressComplement();
        $addressNeighbourhood = $this->getAddressNeighbourhood();

        if ((is_null($iban) && !is_null($bic)) || (!is_null($iban) && is_null($bic))) {
            throw new FranceApiException(false, TeledecResponseType::TYPE_FAILED_SUBMIT, _l('reports.france-submit.bic-iban-error'));
        }

        $data = [
            'siret'                    => $this->getSiret(),
            'name'                     => $this->getName(),
            'fullRegimeFiscal'         => $this->getFullRegimeFiscal(),
            'regimeFiscalTVA'          => $this->getRegimeFiscalTVA(),
            'yearEndMonth'             => $this->getYearEndMonth(),
            'yearEndDay'               => $this->getYearEndDay(),
            'addressStreet'            => $this->getAddressStreet(),
            'addressPostalCode'        => $this->getAddressPostalCode(),
            'addressCity'              => $this->getAddressCity(),
            'addressCountry'           => $this->getAddressCountry(),
            'legalForm'                => $this->getLegalForm(),
            'legalRepresentative'      => $this->getLegalRepresentative(),
            'legalRepresentativeAs'    => $this->getLegalRepresentativeAs(),
            'legalRepresentativeTitle' => $this->getLegalRepresentativeTitle(),
            'email'                    => $this->getEmail(),
        ];

        if (!is_null($iban)) {
            $data['BIC'] = $bic;
            $data['IBAN'] = $iban;
        }

        if (!is_null($telephone)) {
            $data['telephone'] = $telephone;
        }

        if (!is_null($addressComplement)) {
            $data['addressComplement'] = $addressComplement;
        }

        if (!is_null($addressNeighbourhood)) {
            $data['addressNeighbourhood'] = $addressNeighbourhood;
        }

        return $data;
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
