<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use Exception;
use Illuminate\Contracts\Support\Arrayable;

class FranceApiException extends Exception implements Arrayable
{
    /**
     * @var string
     */
    protected $apiMessage;

    /**
     * @var bool
     */
    protected $success;
    /**
     * @var int
     */
    private $teledecResponseType;

    public function __construct(bool $success, int $teledecResponseType, ?string $message = null)
    {
        $this->apiMessage = $message;
        $this->success = $success;
        parent::__construct($message ?? '');
        $this->teledecResponseType = $teledecResponseType;
    }

    /**
     * @return bool
     */
    public function isSuccessful(): bool
    {
        return $this->success;
    }

    /**
     * @return string|null
     */
    public function getErrorMessage(): ?string
    {
        return $this->apiMessage;
    }

    /**
     * @return int
     */
    public function getTeledecResponseTypeId(): int
    {
        return $this->teledecResponseType;
    }

    public function toArray()
    {
        return [
            'success' => $this->isSuccessful(),
            'message' => $this->getErrorMessage(),
        ];
    }
}
