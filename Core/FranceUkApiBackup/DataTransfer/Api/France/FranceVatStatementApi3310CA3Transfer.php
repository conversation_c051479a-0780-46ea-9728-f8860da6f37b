<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\France;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Str;

class FranceVatStatementApi3310CA3Transfer implements Arrayable, Jsonable
{
    private  $compliance;

    public function __construct($compliance)
    {
        $this->compliance = $compliance;
    }

    private ?int $CA;
    private ?int $CB;
    private ?int $KH;
    private ?int $CC;
    private ?int $CF;
    private ?int $CG;
    private ?int $CE;
    private ?int $DA;
    private ?int $DB;
    private ?int $DH;
    private ?int $DC;
    private ?int $DF;
    private ?int $DD;
    private ?int $DG;
    private ?int $DE;
    private ?int $FP;
    private ?int $GP;
    private ?int $FB;
    private ?int $GB;
    private ?int $FR;
    private ?int $GR;
    private ?int $FM;
    private ?int $GM;
    private ?int $FN;
    private ?int $GN;
    private ?int $FC;
    private ?int $GC;
    private ?int $FD;
    private ?int $GD;
    private ?int $GG;
    private ?int $KS;
    private ?int $GH;
    private ?int $GJ;
    private ?int $GK;
    private ?int $HA;
    private ?int $HB;
    private ?int $HH;
    private ?int $HC;
    private ?int $HD;
    private ?int $KU;
    private ?float $HE;
    private ?int $HG;
    /**
     * Nil submit
     */
    private bool $KF = false;
    private ?int $KG;
    private ?int $JA;
    private ?int $JB;
    private ?int $KJ;
    private ?int $JC;
    private ?int $KA;
    private ?int $KB;
    private ?int $KL;
    private ?int $KE;

    // Za brisnje
    private ?int $KR;
    private ?int $FJ;
    private ?int $FK;
    private ?int $KT;

    // NOVO
    private ?int $DK;
    private ?int $KV;
    private ?int $CH;
    private ?int $KW;
    private ?int $KX;
    private ?int $KY;
    private ?int $KZ;
    private ?int $DJ;
    private ?int $LA;
    private ?int $GS;
    private ?int $GT;
    private ?int $GU;
    private ?int $GV;
    private ?int $LB;
    private ?int $LC;
    private ?int $LD;
    private ?int $LE;
    private ?int $LF;
    private ?int $LG;
    private ?int $LH;
    private ?int $LJ;
    private ?int $LK;
    private ?int $LL;
    private ?int $LM;
    private ?int $LN;
    private ?int $GA;
    private ?int $HK;
    private ?int $LP;
    private ?int $HL;

    private ?string $messageToTaxAuthority = null;

    /**
     * @return string|null
     */
    public function getMessageToTaxAuthority(): ?string
    {
        return $this->messageToTaxAuthority;
    }

    /**
     * @param string|null $messageToTaxAuthority
     * @return FranceVatStatementApi3310CA3Transfer
     */
    public function setMessageToTaxAuthority(?string $messageToTaxAuthority): FranceVatStatementApi3310CA3Transfer
    {
        $this->messageToTaxAuthority = $messageToTaxAuthority;

        return $this;
    }

    public function get0056(): ?int
    {
        return $this->DK;
    }

    public function set0056(?int $DK = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DK = $DK;

        return $this;
    }

    public function get0051(): ?int
    {
        return $this->KV;
    }

    public function set0051(?int $KV = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KV = $KV;

        return $this;
    }

    public function get0048(): ?int
    {
        return $this->CH;
    }

    public function set0048(?int $CH = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CH = $CH;

        return $this;
    }

    public function get0052(): ?int
    {
        return $this->KW;
    }

    public function set0052(?int $KW = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KW = $KW;

        return $this;
    }

    public function get0053(): ?int
    {
        return $this->KX;
    }

    public function set0053(?int $KX = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KX = $KX;

        return $this;
    }

    public function get0054(): ?int
    {
        return $this->KY;
    }

    public function set0054(?int $KY = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KY = $KY;

        return $this;
    }

    public function get0055(): ?int
    {
        return $this->KZ;
    }

    public function set0055(?int $KZ = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KZ = $KZ;

        return $this;
    }

    public function get0049(): ?int
    {
        return $this->DJ;
    }

    public function set0049(?int $DJ = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DJ = $DJ;

        return $this;
    }

    public function get0050(): ?int
    {
        return $this->LA;
    }

    public function set0050(?int $LA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LA = $LA;

        return $this;
    }

    public function get0208Net(): ?int
    {
        return $this->GS;
    }

    public function set0208Net(?int $GS = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GS = $GS;

        return $this;
    }

    public function get0208VAT(): ?int
    {
        return $this->GT;
    }

    public function set0208VAT(?int $GT = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GT = $GT;

        return $this;
    }

    public function get0152Net(): ?int
    {
        return $this->GU;
    }

    public function set0152Net(?int $GU = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GU = $GU;

        return $this;
    }

    public function get0152VAT(): ?int
    {
        return $this->GV;
    }

    public function set0152VAT(?int $GV = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GV = $GV;

        return $this;
    }

    public function get0210Net(): ?int
    {
        return $this->LB;
    }

    public function set0210Net(?int $LB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LB = $LB;

        return $this;
    }

    public function get0210VAT(): ?int
    {
        return $this->LC;
    }

    public function set0210VAT(?int $LC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LC = $LC;

        return $this;
    }

    public function get0211Net(): ?int
    {
        return $this->LD;
    }

    public function set0211Net(?int $LD = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LD = $LD;

        return $this;
    }

    public function get0211VAT(): ?int
    {
        return $this->LE;
    }

    public function set0211VAT(?int $LE = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LE = $LE;

        return $this;
    }

    public function get0212Net(): ?int
    {
        return $this->LF;
    }

    public function set0212Net(?int $LF = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LF = $LF;

        return $this;
    }

    public function get0212VAT(): ?int
    {
        return $this->LG;
    }

    public function set0212VAT(?int $LG = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LG = $LG;

        return $this;
    }

    public function get0213Net(): ?int
    {
        return $this->LH;
    }

    public function set0213Net(?int $LH = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LH = $LH;

        return $this;
    }

    public function get0213VAT(): ?int
    {
        return $this->LJ;
    }

    public function set0213VAT(?int $LJ = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LJ = $LJ;

        return $this;
    }

    public function get0214Net(): ?int
    {
        return $this->LK;
    }

    public function set0214Net(?int $LK = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LK = $LK;

        return $this;
    }

    public function get0214VAT(): ?int
    {
        return $this->LL;
    }

    public function set0214VAT(?int $LL = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LL = $LL;

        return $this;
    }

    public function get0215Net(): ?int
    {
        return $this->LM;
    }

    public function set0215Net(?int $LM = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LM = $LM;

        return $this;
    }

    public function get0215VAT(): ?int
    {
        return $this->LN;
    }

    public function set0215VAT(?int $LN = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LN = $LN;

        return $this;
    }

    public function get15a(): ?int
    {
        return $this->GA;
    }

    public function set15a(?int $GA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GA = $GA;

        return $this;
    }

    public function get21a(): ?int
    {
        return $this->HK;
    }

    public function set21a(?int $HK = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HK = $HK;

        return $this;
    }

    public function get21b(): ?int
    {
        return $this->LP;
    }

    public function set21b(?int $LP = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->LP = $LP;

        return $this;
    }

    public function get0711(): ?int
    {
        return $this->HL;
    }

    public function set0711(?int $HL = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HL = $HL;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0979(): ?int
    {
        return $this->CA;
    }

    /**
     * @param int | null $CA
     * @return $this
     */
    public function set0979(?int $CA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CA = $CA;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0981(): ?int
    {
        return $this->CB;
    }

    /**
     * @param int | null $CB
     * @return $this
     */
    public function set0981(?int $CB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CB = $CB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0044(): ?int
    {
        return $this->KH;
    }

    /**
     * @param int | null $KH
     * @return $this
     */
    public function set0044(?int $KH = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KH = $KH;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0045(): ?int
    {
        return $this->KR;
    }

    /**
     * @param int | null $KR
     * @return $this
     */
    public function set0045(?int $KR = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KR = $KR;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0031(): ?int
    {
        return $this->CC;
    }

    /**
     * @param int | null $CC
     * @return $this
     */
    public function set0031(?int $CC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CC = $CC;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0030(): ?int
    {
        return $this->CF;
    }

    /**
     * @param int | null $CF
     * @return $this
     */
    public function set0030(?int $CF = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CF = $CF;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0040(): ?int
    {
        return $this->CG;
    }

    /**
     * @param int | null $CG
     * @return $this
     */
    public function set0040(?int $CG = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CG = $CG;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0036(): ?int
    {
        return $this->CE;
    }

    /**
     * @param int | null $CE
     * @return $this
     */
    public function set0036(?int $CE = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->CE = $CE;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0032(): ?int
    {
        return $this->DA;
    }

    /**
     * @param int | null $DA
     * @return $this
     */
    public function set0032(?int $DA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DA = $DA;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0033(): ?int
    {
        return $this->DB;
    }

    /**
     * @param int | null $DB
     * @return $this
     */
    public function set0033(?int $DB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DB = $DB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0047(): ?int
    {
        return $this->DH;
    }

    /**
     * @param int | null $DH
     * @return $this
     */
    public function set0047(?int $DH = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DH = $DH;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0034(): ?int
    {
        return $this->DC;
    }

    /**
     * @param int | null $DC
     * @return $this
     */
    public function set0034(?int $DC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DC = $DC;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0029(): ?int
    {
        return $this->DF;
    }

    /**
     * @param int | null $DF
     * @return $this
     */
    public function set0029(?int $DF = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DF = $DF;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0037(): ?int
    {
        return $this->DD;
    }

    /**
     * @param int | null $DD
     * @return $this
     */
    public function set0037(?int $DD = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DD = $DD;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0043(): ?int
    {
        return $this->DG;
    }

    /**
     * @param int | null $DG
     * @return $this
     */
    public function set0043(?int $DG = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DG = $DG;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0039(): ?int
    {
        return $this->DE;
    }

    /**
     * @param int | null $DE
     * @return $this
     */
    public function set0039(?int $DE = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->DE = $DE;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0207Net(): ?int
    {
        return $this->FP;
    }

    /**
     * @param int | null $FP
     * @return $this
     */
    public function set0207Net(?int $FP = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FP = $FP;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0207Vat(): ?int
    {
        return $this->GP;
    }

    /**
     * @param int | null $GP
     * @return $this
     */
    public function set0207Vat(?int $GP = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GP = $GP;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0105Net(): ?int
    {
        return $this->FB;
    }

    /**
     * @param int | null $FB
     * @return $this
     */
    public function set0105Net(?int $FB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FB = $FB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0105Vat(): ?int
    {
        return $this->GB;
    }

    /**
     * @param int | null $GB
     * @return $this
     */
    public function set0105Vat(?int $GB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GB = $GB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0151Net(): ?int
    {
        return $this->FR;
    }

    /**
     * @param int | null $FR
     * @return $this
     */
    public function set0151Net(?int $FR = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FR = $FR;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0151Vat(): ?int
    {
        return $this->GR;
    }

    /**
     * @param int | null $GR
     * @return $this
     */
    public function set0151Vat(?int $GR = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GR = $GR;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0201Net(): ?int
    {
        return $this->FM;
    }

    /**
     * @param int | null $FM
     * @return $this
     */
    public function set0201Net(?int $FM = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FM = $FM;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0201Vat(): ?int
    {
        return $this->GM;
    }

    /**
     * @param int | null $GM
     * @return $this
     */
    public function set0201Vat(?int $GM = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GM = $GM;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0100Net(): ?int
    {
        return $this->FN;
    }

    /**
     * @param int | null $FN
     * @return $this
     */
    public function set0100Net(?int $FN = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FN = $FN;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0100Vat(): ?int
    {
        return $this->GN;
    }

    /**
     * @param int | null $GN
     * @return $this
     */
    public function set0100Vat(?int $GN = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GN = $GN;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get12Net(): ?int
    {
        return $this->FJ;
    }

    /**
     * @param int | null $FJ
     * @return $this
     */
    public function set12Net(?int $FJ = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FJ = $FJ;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get12Vat(): ?int
    {
        return $this->FK;
    }

    /**
     * @param int | null $FK
     * @return $this
     */
    public function set12Vat(?int $FK = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FK = $FK;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0900Net(): ?int
    {
        return $this->FC;
    }

    /**
     * @param int | null $FC
     * @return $this
     */
    public function set0900Net(?int $FC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FC = $FC;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0900Vat(): ?int
    {
        return $this->GC;
    }

    /**
     * @param int | null $GC
     * @return $this
     */
    public function set0900Vat(?int $GC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GC = $GC;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0950Net(): ?int
    {
        return $this->FD;
    }

    /**
     * @param int | null $FD
     * @return $this
     */
    public function set0950Net(?int $FD = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->FD = $FD;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0950Vat(): ?int
    {
        return $this->GD;
    }

    /**
     * @param int | null $GD
     * @return $this
     */
    public function set0950Vat(?int $GD = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GD = $GD;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0600(): ?int
    {
        return $this->GG;
    }

    /**
     * @param int | null $GG
     * @return $this
     */
    public function set0600(?int $GG = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GG = $GG;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0602(): ?int
    {
        return $this->KS;
    }

    /**
     * @param int | null $KS
     * @return $this
     */
    public function set0602(?int $KS = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KS = $KS;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get16(): ?int
    {
        return $this->GH;
    }

    /**
     * @param int | null $GH
     * @return $this
     */
    public function set16(?int $GH = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GH = $GH;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0046(): ?int
    {
        return $this->KT;
    }

    /**
     * @param int | null $KT
     * @return $this
     */
    public function set0046(?int $KT = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KT = $KT;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0035(): ?int
    {
        return $this->GJ;
    }

    /**
     * @param int | null $GJ
     * @return $this
     */
    public function set0035(?int $GJ = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GJ = $GJ;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0038(): ?int
    {
        return $this->GK;
    }

    /**
     * @param int | null $GK
     * @return $this
     */
    public function set0038(?int $GK = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->GK = $GK;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0703(): ?int
    {
        return $this->HA;
    }

    /**
     * @param int | null $HA
     * @return $this
     */
    public function set0703(?int $HA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HA = $HA;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0702(): ?int
    {
        return $this->HB;
    }

    /**
     * @param int | null $HB
     * @return $this
     */
    public function set0702(?int $HB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HB = $HB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get21(): ?int
    {
        return $this->HH;
    }

    /**
     * @param int | null $HH
     * @return $this
     */
    public function set21(?int $HH = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HH = $HH;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0059(): ?int
    {
        return $this->HC;
    }

    /**
     * @param int | null $HC
     * @return $this
     */
    public function set0059(?int $HC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HC = $HC;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get8001(): ?int
    {
        return $this->HD;
    }

    /**
     * @param int | null $HD
     * @return $this
     */
    public function set8001(?int $HD = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HD = $HD;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0603(): ?int
    {
        return $this->KU;
    }

    /**
     * @param int | null $KU
     * @return $this
     */
    public function set0603(?int $KU = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KU = $KU;

        return $this;
    }

    /**
     * @return float|null
     */
    public function get22A(): ?float
    {
        return $this->HE;
    }

    /**
     * @param float | null $HE
     * @return $this
     */
    public function set22A(?float $HE = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HE = $HE;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get23(): ?int
    {
        return $this->HG;
    }

    /**
     * @param int | null $HG
     * @return $this
     */
    public function set23(?int $HG = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->HG = $HG;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0710(): ?int
    {
        return $this->KG;
    }

    /**
     * @param int | null $KG
     * @return $this
     */
    public function set0710(?int $KG = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KG = $KG;

        return $this;
    }

    /**
     * @return bool
     */
    public function getNilSubmit(): bool
    {
        return $this->KF;
    }

    /**
     * @param bool $nilSubmit
     * @return $this
     */
    public function setNilSubmit(bool $nilSubmit = true): FranceVatStatementApi3310CA3Transfer
    {
        $this->KF = $nilSubmit;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get0705(): ?int
    {
        return $this->JA;
    }

    /**
     * @param int | null $JA
     * @return $this
     */
    public function set0705(?int $JA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->JA = $JA;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get8002(): ?int
    {
        return $this->JB;
    }

    /**
     * @param int | null $JB
     * @return $this
     */
    public function set8002(?int $JB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->JB = $JB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get8005(): ?int
    {
        return $this->KJ;
    }

    /**
     * @param int | null $KJ
     * @return $this
     */
    public function set8005(?int $KJ = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KJ = $KJ;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get8003(): ?int
    {
        return $this->JC;
    }

    /**
     * @param int | null $JC
     * @return $this
     */
    public function set8003(?int $JC = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->JC = $JC;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get28(): ?int
    {
        return $this->KA;
    }

    /**
     * @param int | null $KA
     * @return $this
     */
    public function set28(?int $KA = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KA = $KA;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get9979(): ?int
    {
        return $this->KB;
    }

    /**
     * @param int | null $KB
     * @return $this
     */
    public function set9979(?int $KB = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KB = $KB;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get9991(): ?int
    {
        return $this->KL;
    }

    /**
     * @param int | null $KL
     * @return $this
     */
    public function set9991(?int $KL = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KL = $KL;

        return $this;
    }

    /**
     * @return int | null
     */
    public function get32(): ?int
    {
        return $this->KE;
    }

    /**
     * @param int | null $KE
     * @return $this
     */
    public function set32(?int $KE = null): FranceVatStatementApi3310CA3Transfer
    {
        $this->KE = $KE;

        return $this;
    }

    public function toArray()
    {
        if ($this->getNilSubmit()) {
            $data = [
                'KF' => $this->getNilSubmit()
            ];
        } else {
            $data = [
                'CA' => $this->get0979(),
                'CB' => $this->get0981(),
                'KH' => $this->get0044(),
                'KR' => $this->get0045(),
                'CC' => $this->get0031(),
                'CF' => $this->get0030(),
                'CG' => $this->get0040(),
                'CE' => $this->get0036(),
                'DA' => $this->get0032(),
                'DB' => $this->get0033(),
                'DH' => $this->get0047(),
                'DC' => $this->get0034(),
                'DF' => $this->get0029(),
                'DD' => $this->get0037(),
                'DG' => $this->get0043(),
                'DE' => $this->get0039(),
                'FP' => $this->get0207Net(),
                'GP' => $this->get0207Vat(),
                'FB' => $this->get0105Net(),
                'GB' => $this->get0105Vat(),
                'FR' => $this->get0151Net(),
                'GR' => $this->get0151Vat(),
                'FM' => $this->get0201Net(),
                'GM' => $this->get0201Vat(),
                'FN' => $this->get0100Net(),
                'GN' => $this->get0100Vat(),
                'FJ' => $this->get12Net(),
                'FK' => $this->get12Vat(),
                'FC' => $this->get0900Net(),
                'GC' => $this->get0900Vat(),
                'FD' => $this->get0950Net(),
                'GD' => $this->get0950Vat(),
                'GG' => $this->get0600(),
                'KS' => $this->get0602(),
                'GH' => $this->get16(),
                'KT' => $this->get0046(),
                'GJ' => $this->get0035(),
                'GK' => $this->get0038(),
                'HA' => $this->get0703(),
                'HB' => $this->get0702(),
                'HH' => $this->get21(),
                'HC' => $this->get0059(),
                'HD' => $this->get8001(),
                'KU' => $this->get0603(),
                'HE' => $this->get22A(),
                'HG' => $this->get23(),
                'KG' => $this->get0710(),
                'JA' => $this->get0705(),
                'JB' => $this->get8002(),
                'KJ' => $this->get8005(),
                'JC' => $this->get8003(),
                'KA' => $this->get28(),
                'KB' => $this->get9979(),
                'KL' => $this->get9991(),
                'KE' => $this->get32()
            ];
            if ($this->compliance->year_id >= 2022) {
                // Novo
                $newData = [
                    'DK' => $this->get0056(),
                    'KV' => $this->get0051(),
                    'CH' => $this->get0048(),
                    'KW' => $this->get0052(),
                    'KX' => $this->get0053(),
                    'KY' => $this->get0054(),
                    'KZ' => $this->get0055(),
                    'DJ' => $this->get0049(),
                    'LA' => $this->get0050(),
                    'GS' => $this->get0208Net(),
                    'GT' => $this->get0208VAT(),
                    'GU' => $this->get0152Net(),
                    'GV' => $this->get0152VAT(),
                    'LB' => $this->get0210Net(),
                    'LC' => $this->get0210VAT(),
                    'LD' => $this->get0211Net(),
                    'LE' => $this->get0211VAT(),
                    'LF' => $this->get0212Net(),
                    'LG' => $this->get0212VAT(),
                    'LH' => $this->get0213Net(),
                    'LJ' => $this->get0213VAT(),
                    'LK' => $this->get0214Net(),
                    'LL' => $this->get0214VAT(),
                    'LM' => $this->get0215Net(),
                    'LN' => $this->get0215VAT(),
                    'GA' => $this->get15a(),
                    'HK' => $this->get21a(),
                    'LP' => $this->get21b(),
                    'HL' => $this->get0711(),
                ];
                $data = array_merge($data, $newData);
            }
        }
        $message = $this->getMessageToTaxAuthority();
        if (!is_null($message)) {
            $message = Str::limit($message, 497);
            $data['BA_4440_1'] = $message;
        }

        return $this->cleanData($data);
    }

    private function cleanData(array $data): array
    {
        $clean = [];
        foreach ($data as $key => $value) {
            if (is_null($value)) {
                continue;
            }
            if ($value === 0) {
                continue;
            }
            if ($value === 0.0) {
                continue;
            }

            $clean[$key] = $value;
        }

        return $clean;
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
