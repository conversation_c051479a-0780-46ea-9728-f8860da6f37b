<?php
declare(strict_types=1);

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\UK;

use Exception;

class ApiErrorResponseException extends Exception
{
    private $multipleErrors;

    private $apiErrorCode;

    private $responseBody;

    public function __construct(string $message, ?string $apiErrorCode, string $responseBody, array $multipleErrors = [])
    {
        $this->multipleErrors = $multipleErrors;
        $this->apiErrorCode = $apiErrorCode;
        $this->responseBody = $responseBody;

        parent::__construct($message);
    }

    public function getResponseBody()
    {
        return $this->responseBody;
    }

    public function getApiErrorCode()
    {
        return $this->apiErrorCode;
    }

    public function getApiErrors()
    {
        return $this->multipleErrors;
    }
}
