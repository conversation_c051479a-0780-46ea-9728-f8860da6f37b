<?php
declare(strict_types=1);

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\UK\Responses;

class VATReturnResponse
{
    /** @var string The time that the message was processed in the system. */
    private string $processingDate;

    /** @var string Unique number that represents the form bundle. The system stores VAT Return data in forms, which are held in a unique form bundle. */
    private string $formBundleNumber;

    /** @var string|null Is DD if the netVatDue value is a debit and HMRC holds a Direct Debit Instruction for the client. Is BANK if the netVatDue value is a credit and HMRC holds the client’s bank data. Otherwise not present. */
    private ?string $paymentIndicator;

    /** @var string|null The charge reference number is returned, only if the netVatDue value is a debit. Between 1 and 16 characters. */
    private ?string $chargeRefNumber;

    public function __construct(array $response)
    {
        $this->processingDate = $response->processingDate;
        $this->formBundleNumber = $response->formBundleNumber;
        $this->paymentIndicator = $response->paymentIndicator ?? null;
        $this->chargeRefNumber = $response->chargeRefNumber ?? null;
    }

    public function getProcessingDate()
    {
        return $this->processingDate;
    }

    public function getPaymentIndicator()
    {
        return $this->paymentIndicator;
    }

    public function getFormBundleNumber()
    {
        return $this->formBundleNumber;
    }

    public function getChargeRefNumber()
    {
        return $this->chargeRefNumber;
    }

    public function toJson()
    {
        return json_encode([
            'processingDate'   => $this->processingDate,
            'paymentIndicator' => $this->paymentIndicator,
            'formBundleNumber' => $this->formBundleNumber,
            'chargeRefNumber'  => $this->chargeRefNumber
        ]);
    }
}
