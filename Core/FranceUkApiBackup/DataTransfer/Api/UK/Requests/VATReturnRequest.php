<?php
declare(strict_types=1);

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\UK\Requests;

class VATReturnRequest
{
    /** @var string The ID code for the period that this obligation belongs to. */
    public $periodKey;

    /** @var float VAT due on sales and other outputs. This corresponds to box 1 on the VAT Return form. */
    public $vatDueSales;

    /** @var float VAT due on acquisitions from other EC Member States. This corresponds to box 2 on the VAT Return form. */
    public $vatDueAcquisitions;

    /** @var float Total VAT due (the sum of vatDueSales and vatDueAcquisitions). This corresponds to box 3 on the VAT Return form. */
    public $totalVatDue;

    /** @var float VAT reclaimed on purchases and other inputs (including acquisitions from the EC). This corresponds to box 4 on the VAT Return form. */
    public $vatReclaimedCurrPeriod;

    /** @var float The difference between totalVatDue and vatReclaimedCurrPeriod. This corresponds to box 5 on the VAT Return form. */
    public $netVatDue;

    /** @var int Total value of sales and all other outputs excluding any VAT. This corresponds to box 6 on the VAT Return form. The value must be in pounds (no pence). */
    public $totalValueSalesExVAT;

    /** @var int Total value of purchases and all other inputs excluding any VAT (including exempt purchases). This corresponds to box 7 on the VAT Return form. The value must be in pounds (no pence). */
    public $totalValuePurchasesExVAT;

    /** @var int Total value of all supplies of goods and related costs, excluding any VAT, to other EC member states. This corresponds to box 8 on the VAT Return form. The value must be in pounds (no pence). */
    public $totalValueGoodsSuppliedExVAT;

    /** @var int Total value of acquisitions of goods and related costs excluding any VAT, from other EC member states. This corresponds to box 9 on the VAT Return form. The value must be in pounds (no pence). */
    public $totalAcquisitionsExVAT;

    /** @var int Declaration that the user has finalised their VAT return. */
    public $finalised;

    public function toArray(): array
    {
        return [
            'periodKey'                    => $this->periodKey,
            'vatDueSales'                  => $this->vatDueSales,
            'vatDueAcquisitions'           => $this->vatDueAcquisitions,
            'totalVatDue'                  => $this->totalVatDue,
            'vatReclaimedCurrPeriod'       => $this->vatReclaimedCurrPeriod,
            'netVatDue'                    => $this->netVatDue,
            'totalValueSalesExVAT'         => $this->totalValueSalesExVAT,
            'totalValuePurchasesExVAT'     => $this->totalValuePurchasesExVAT,
            'totalValueGoodsSuppliedExVAT' => $this->totalValueGoodsSuppliedExVAT,
            'totalAcquisitionsExVAT'       => $this->totalAcquisitionsExVAT,
            'finalised'                    => $this->finalised,
        ];
    }
}
