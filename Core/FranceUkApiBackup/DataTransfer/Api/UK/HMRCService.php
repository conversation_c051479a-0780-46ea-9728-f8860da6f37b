<?php
declare(strict_types=1);

namespace App\Core\FranceUkApiBackup\DataTransfer\Api\UK;

use GuzzleHttp\Exception\GuzzleException;
use Throwable;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Hash;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Exception\RequestException;
use League\OAuth2\Client\Token\AccessToken;
use App\Core\FranceUkApiBackup\DataTransfer\Api\UK\Requests\VATReturnRequest;
use App\Core\FranceUkApiBackup\DataTransfer\Api\UK\Responses\VATReturnResponse;

class HMRCService
{
    protected Client $client;

    protected AccessToken $accessToken;

    protected string $version;

    protected string $contentType;

    protected array $clientDeviceInfo;

    /**
     * Create HMRC Service
     *
     * @param AccessToken $accessToken
     * @param array $clientDeviceInfo
     * @param string $version
     * @param string $contentType
     */
    public function __construct(
        AccessToken $accessToken,
        array $clientDeviceInfo,
        string $version = '1.0',
        string $contentType = 'json'
    ) {
        $this->accessToken = $accessToken;
        $this->clientDeviceInfo = $clientDeviceInfo;
        $this->version = $version;
        $this->contentType = $contentType;

        $headers = array_merge([
            'Accept'        => $this->getAcceptHeader(),
            'Authorization' => $this->getAuthorizationHeader(),
        ], $this->getFraudProtectionHeaders());

        $this->client = new Client([
            'base_uri' => config('evat.uk.hmrc.url'),
            'headers'  => $headers,
        ]);
    }

    /**
     * Retrieve VAT obligations.
     *
     * @param string $vrn VAT registration number.
     * @param string|null $from Date from which to return obligations. Mandatory unless the status is O.
     * @param string|null $to Date to which to return obligations. Mandatory unless the status is O.
     * @param string|null $status Obligation status to return: O=Open, F= Fulfilled. Omit status to retrieve all obligations.
     * @param array $headers Gov-Test-Scenario headers
     * @return object
     * @throws GuzzleException
     */
    public function getVATObligations(
        string $vrn,
        ?string $from = null,
        ?string $to = null,
        ?string $status = 'O',
        array $headers = []
    ): object {
        $queryParams = [];
        if ($status) {
            $queryParams['status'] = $status;
        }

        if ($from) {
            $queryParams['from'] = $from;
        }

        if ($to) {
            $queryParams['to'] = $to;
        }

        try {
            $response = $this->client->get("organisations/vat/{$vrn}/obligations", [
                'query'   => $queryParams,
                'headers' => $headers
            ]);
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    public function helloWorld(): string
    {
        $response = $this->client->get('hello/world');

        return data_get(json_decode($response->getBody()->getContents(), true), 'message');
    }

    public function helloUser(): string
    {
        $response = $this->client->get('hello/user');

        return data_get(json_decode($response->getBody()->getContents(), true), 'message');
    }

    public function helloApp(): string
    {
        $response = $this->client->get('hello/application');

        return data_get(json_decode($response->getBody()->getContents(), true), 'message');
    }

    /**
     * Submit VAT return for period.
     *
     * @param string $vrn VAT registration number.
     * @param VATReturnRequest $requestObj
     * @return VATReturnResponse
     * @throws ApiErrorResponseException
     * @throws Throwable
     * @throws GuzzleException
     */
    public function submitVATReturn(string $vrn, VATReturnRequest $requestObj): VATReturnResponse
    {
        try {
            $response = $this->client->post("organisations/vat/{$vrn}/returns", [
                'json' => $requestObj->toArray(),
            ]);
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return new VATReturnResponse($this->parseResponse($response));
    }

    /**
     * Retrieve a submitted VAT return
     *
     * @param string $vrn VAT registration number.
     * @param string $periodKey The ID code for the period that this obligation belongs to. The format is a string of four alphanumeric characters. Occasionally the format includes the “#” symbol, which must be URL-encoded.
     * @return array
     * @throws ApiErrorResponseException
     * @throws Throwable
     * @throws GuzzleException
     */
    public function viewVATReturn(string $vrn, string $periodKey): array
    {
        try {
            $response = $this->client->get("organisations/vat/{$vrn}/returns/{$periodKey}");
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    /**
     * Retrieve VAT liabilities
     *
     * @param string $vrn VAT registration number.
     * @param string $from Liabilities to return from date, the minimum 'from' date is 2017-12-01
     * @param string $to Liabilities to return up to date, the maximum 'to' date is the current date
     * @return array
     * @throws ApiErrorResponseException
     * @throws Throwable
     * @throws GuzzleException
     */
    public function getVATLiabilities(string $vrn, string $from, string $to): array
    {
        try {
            $response = $this->client->get("organisations/vat/{$vrn}/liabilities", [
                'query' => [
                    'from' => $from,
                    'to'   => $to
                ]
            ]);
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    /**
     * Retrieve VAT payments
     *
     * @param string $vrn VAT registration number.
     * @param string $from Payments to return from date, the minimum 'from' date is 2017-12-01
     * @param string $to Payments to return up to date, the maximum 'to' date is the current date
     * @return array
     * @throws ApiErrorResponseException
     * @throws Throwable
     * @throws GuzzleException
     */
    public function getVATPayments(string $vrn, string $from, string $to): array
    {
        try {
            $response = $this->client->get("organisations/vat/{$vrn}/payments", [
                'query' => [
                    'from' => $from,
                    'to'   => $to
                ]
            ]);
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    /**
     * This resource validates fraud prevention headers submitted with this HTTP request.
     *
     * @return object
     * @throws ApiErrorResponseException
     * @throws Throwable
     * @throws GuzzleException
     */
    public function testFraudProtectionHeaders(): array
    {
        try {
            $response = $this->client->get('test/fraud-prevention-headers/validate');
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    /**
     * Parse response object
     *
     * @param ResponseInterface $response
     * @return array
     */
    private function parseResponse(ResponseInterface $response): array
    {
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * Throw custom exception on API error response
     *
     * @param Throwable $e
     * @return void
     * @throws ApiErrorResponseException
     * @throws Throwable
     */
    private function parseException(Throwable $e): void
    {
        throw $e;
        if ($e instanceof RequestException && $e->hasResponse()) {
            $response = $this->parseResponse($e->getResponse());

            throw new ApiErrorResponseException($response->message, $response->code ?? null, json_encode($response), $response->errors ?? []);
        }

        throw $e;
    }

    /**
     * Format Authorization header
     *
     * @return string
     */
    private function getAuthorizationHeader(): string
    {
        return 'Bearer ' . $this->accessToken->getToken();
    }

    /**
     * Format Accept header
     *
     * @return string
     */
    private function getAcceptHeader(): string
    {
        return "application/vnd.hmrc.{$this->version}+{$this->contentType}";
    }

    /**
     * Fill fraud protection headers
     *
     * @see https://developer.service.hmrc.gov.uk/guides/fraud-prevention/
     * @return array
     */
    public function getFraudProtectionHeaders(): array
    {
        $serverIP = gethostbyname('app.evat.com.');//$_SERVER['SERVER_ADDR'];
        $clientPublicIP = request()->ip();
        $timezoneOffset = 'UTC' . date('P');
        $forward = sprintf('by=%s&for=%s', $serverIP, $clientPublicIP);
        $vendorVersions = ['frontend' => '1.0.0', 'app' => '1.0.0'];
        $clientWindowSize = ['width' => e($this->clientDeviceInfo['window_width']), 'height' => e($this->clientDeviceInfo['window_height'])];
        $clientScreen = [
            'width'          => e($this->clientDeviceInfo['screen_width']),
            'height'         => e($this->clientDeviceInfo['screen_height']),
            'scaling-factor' => e($this->clientDeviceInfo['display_scaling']),
            'colour-depth'   => e($this->clientDeviceInfo['color_depth']),
        ];
        $plugins = collect($this->clientDeviceInfo['plugins'])->map(function ($plugin) {
            return rawurlencode(e($plugin));
        });

        $milliseconds = substr((string)round(microtime(true) * 1000), -3);

        $headers = [
            'Gov-Vendor-Product-Name'          => rawurlencode('eVAT'),
            'Gov-Vendor-Version'               => http_build_query($vendorVersions),
            'Gov-Client-Connection-Method'     => 'WEB_APP_VIA_SERVER',
            'Gov-Client-Local-IPs-Timestamp'   => gmdate('Y-m-d\TH:i:s.\\' . $milliseconds . '\Z'),
            'Gov-Client-Public-IP-Timestamp'   => gmdate('Y-m-d\TH:i:s.\\' . $milliseconds . '\Z'),
            'Gov-Client-Local-IPs'             => $this->clientDeviceInfo['local_ip'],
            'Gov-Client-Public-IP'             => $clientPublicIP,
            'Gov-Vendor-Public-IP'             => $serverIP,
            'Gov-Client-Public-Port'           => $_SERVER['REMOTE_PORT'],
            'Gov-Client-Device-ID'             => $this->clientDeviceInfo['hmrc_device_id'],
            'Gov-Client-User-IDs'              => sprintf('%s=%s', 'evat_web_app', user()->getId()),
            'Gov-Client-Timezone'              => $timezoneOffset,
            'Gov-Client-Browser-Do-Not-Track'  => 'true',
            'Gov-Client-Browser-JS-User-Agent' => $_SERVER['HTTP_USER_AGENT'],
            'Gov-Client-Browser-Plugins'       => $plugins->isEmpty() ? '' : $plugins->implode(','),
            'Gov-Vendor-Forwarded'             => $forward,
            'Gov-Client-Window-Size'           => http_build_query($clientWindowSize),
            'Gov-Client-Screens'               => http_build_query($clientScreen),
            'Gov-Vendor-License-IDs'           => '',
        ];

        if (user()->has_token) {
            $multiFactor = [
                'type'             => 'TOTP',
                'timestamp'        => Carbon::parse(user()->last_login)->format('Y-m-d\TH:i\Z'),
                'unique-reference' => Hash::make(user()->seed),
            ];
            $headers['Gov-Client-Multi-Factor'] = http_build_query($multiFactor);
        }

        return $headers;
    }

    public function createTestUser(): object|array
    {
        try {
            $response = $this->client
                ->post('/create-test-user/organisations', [
                    'json' => [
                        'serviceNames' => [
                            'corporation-tax',
                            'paye-for-employers',
                            'submit-vat-returns',
                            'national-insurance',
                            'self-assessment',
                            'mtd-income-tax',
                            'mtd-vat',
                            'lisa',
                            'secure-electronic-transfer',
                            'relief-at-source',
                            'customs-services',
                            'goods-vehicle-movements',
                            'safety-and-security',
                            'common-transit-convention-traders',
                            'common-transit-convention-traders-legacy'
                        ]
                    ]
                ]);
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    public function createTestAgent(): array
    {
        try {
            $response = $this->client
                ->post('/create-test-user/agents', [
                    'json' => [
                        'serviceNames' => [
                            'agent-services',
                        ]
                    ]
                ]);
        } catch (RequestException $e) {
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }

    public function getAvailableServices(): array
    {
        try {
            $response = $this->client
                ->get('/create-test-user/services');
        } catch (RequestException $e) {
            throw $e;
            $this->parseException($e);
        }

        return $this->parseResponse($response);
    }
}
