<?php

namespace App\Core\FranceUkApiBackup\DataTransfer\AmazonExportedVatStatement;

use App\Core\Data\Models\Company;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\SalesChannel;
use App\Core\Data\Models\TaxPeriod;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;

class AmazonExportedVatStatementFile implements Arrayable, Jsonable
{
    private ?SalesChannel $salesChannel = null;
    private ?TaxPeriod $taxPeriod = null;
    private ?Carbon $dateFrom = null;
    private ?Carbon $dateTo = null;
    private Collection $errors;
    private string $fileName;
    private ?string $key = null;
    private array $content = [];
    private ?Country $country = null;
    private ?UploadedFile $file = null;

    public function __construct()
    {
        $this->errors = collect();
    }

    /**
     * @return UploadedFile|null
     */
    public function getFile(): ?UploadedFile
    {
        return $this->file;
    }

    /**
     * @param UploadedFile|null $file
     * @return $this
     */
    public function setFile(?UploadedFile $file = null): AmazonExportedVatStatementFile
    {
        $this->file = $file;

        return $this;
    }

    /**
     * @return Country|null
     */
    public function getCountry(): ?Country
    {
        return $this->country;
    }

    /**
     * @param Country $country
     * @return AmazonExportedVatStatementFile
     */
    public function setCountry(Country $country): AmazonExportedVatStatementFile
    {
        $this->country = $country;

        return $this;
    }

    /**
     * @return array
     */
    public function getContent(): array
    {
        return $this->content;
    }

    /**
     * @param array $content
     * @return AmazonExportedVatStatementFile
     */
    public function setContent(array $content): AmazonExportedVatStatementFile
    {
        $this->content = $content;

        return $this;
    }

    /**
     * @param string $key
     * @return AmazonExportedVatStatementFile
     */
    public function setKey(string $key): AmazonExportedVatStatementFile
    {
        $this->key = $key;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getKey(): ?string
    {
        return $this->key;
    }

    /**
     * @param Carbon|null $dateFrom
     * @return AmazonExportedVatStatementFile
     */
    public function setDateFrom(?Carbon $dateFrom): AmazonExportedVatStatementFile
    {
        $this->dateFrom = $dateFrom;

        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getDateFrom(): ?Carbon
    {
        return $this->dateFrom;
    }

    /**
     * @param Carbon|null $dateTo
     * @return AmazonExportedVatStatementFile
     */
    public function setDateTo(?Carbon $dateTo): AmazonExportedVatStatementFile
    {
        $this->dateTo = $dateTo;

        return $this;
    }

    /**
     * @return Carbon|null
     */
    public function getDateTo(): ?Carbon
    {
        return $this->dateTo;
    }

    /**
     * @return SalesChannel|null
     */
    public function getSalesChannel(): ?SalesChannel
    {
        return $this->salesChannel;
    }

    /**
     * @return Company|null
     */
    public function getCompany(): ?Company
    {
        return $this->salesChannel?->company;
    }

    /**
     * @param SalesChannel $salesChannel
     * @return AmazonExportedVatStatementFile
     */
    public function setSalesChannel(SalesChannel $salesChannel): AmazonExportedVatStatementFile
    {
        $this->salesChannel = $salesChannel;

        return $this;
    }

    /**
     * @return TaxPeriod|null
     */
    public function getTaxPeriod(): ?TaxPeriod
    {
        return $this->taxPeriod;
    }

    /**
     * @param TaxPeriod $taxPeriod
     * @return $this
     */
    public function setTaxPeriod(TaxPeriod $taxPeriod): AmazonExportedVatStatementFile
    {
        $this->taxPeriod = $taxPeriod;

        return $this;
    }

    /**
     * @return Collection
     */
    private function getErrors(): Collection
    {
        return $this->errors;
    }

    /**
     * @return bool
     */
    public function hasErrors(): bool
    {
        return $this->errors->count() > 0;
    }

    /**
     * @param string $error
     * @return AmazonExportedVatStatementFile
     */
    public function addError(string $error): AmazonExportedVatStatementFile
    {
        if (!$this->errors->contains($error)) {
            $this->errors->push($error);
        }

        return $this;
    }

    /**
     * @return string
     */
    public function getFileName(): string
    {
        return $this->fileName;
    }

    /**
     * @param string $fileName
     * @return AmazonExportedVatStatementFile
     */
    public function setFileName(string $fileName): AmazonExportedVatStatementFile
    {
        $this->fileName = $fileName;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        $salesChannel = $this->getSalesChannel();
        $salesChannelData = null;
        if (!is_null($salesChannel)) {
            $salesChannelData = [
                'name'     => $salesChannel->account_name,
                'platform' => _l($salesChannel->platform_name),
                'uaid'     => $salesChannel->uaid
            ];
        }

        $period = $this->getTaxPeriod();
        if (!is_null($period)) {
            $period = _l($period->lang_key);
        }

        return [
            'key'               => $this->getKey(),
            'salesChannel'      => $salesChannelData,
            'salesChannelModel' => $this->getSalesChannel()?->toArray(),
            'company'           => $salesChannel?->company->name,
            'companyModel'      => $this->getCompany()?->toArray(),
            'period'            => $period,
            'periodModel'       => $this->getTaxPeriod()?->toArray(),
            'countryModel'      => $this->getCountry()->toArray(),
            'content'           => $this->getContent(),
            'dateFrom'          => $this->getDateFrom()?->format(_l_date_format('date')),
            'dateTo'            => $this->getDateTo()?->format(_l_date_format('date')),
            'fileName'          => $this->getFileName(),
            'hasErrors'         => $this->hasErrors(),
            'errors'            => $this->getErrors()->toArray(),
        ];
    }
}
