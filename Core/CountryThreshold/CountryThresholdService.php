<?php

namespace App\Core\CountryThreshold;

use App\Core\CountryThreshold\Contracts\CountryThresholdServiceContract;
use App\Core\Data\Models\CountryThreshold;
use App\Core\Data\Repositories\Contracts\CountryThresholdRepositoryContract;

class CountryThresholdService implements CountryThresholdServiceContract
{
    private CountryThresholdRepositoryContract $countryThresholdRepo;

    public function __construct(CountryThresholdRepositoryContract $countryThresholdRepo)
    {
        $this->countryThresholdRepo = $countryThresholdRepo;
    }

    public function storeCountryThreshold(
        int $countryId,
        int $value,
        string $effectiveFrom
    ): CountryThreshold {
        $countryThreshold = $this->countryThresholdRepo->getEmptyCountryThresholdModel();
        $countryThreshold->country_id = $countryId;
        $countryThreshold->value = $value;
        $countryThreshold->effective_from = $effectiveFrom;

        $countryThreshold->save();

        return $countryThreshold;
    }

    public function updateCountryThreshold(
        int $countryThresholdId,
        int $countryId,
        int $value,
        string $effectiveFrom
    ): CountryThreshold {
        $countryThreshold = $this->countryThresholdRepo->getCountryThresholdById($countryThresholdId);
        $countryThreshold->country_id = $countryId;
        $countryThreshold->value = $value;
        $countryThreshold->effective_from = $effectiveFrom;

        $countryThreshold->save();

        return $countryThreshold;
    }
}
