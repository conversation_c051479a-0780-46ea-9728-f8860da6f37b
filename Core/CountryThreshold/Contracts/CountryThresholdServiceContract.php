<?php

namespace App\Core\CountryThreshold\Contracts;

use App\Core\Data\Models\CountryThreshold;

interface CountryThresholdServiceContract
{
    public function storeCountryThreshold(
        int $countryId,
        int $value,
        string $effectiveFrom
    ): CountryThreshold;

    public function updateCountryThreshold(
        int $countryThresholdId,
        int $countryId,
        int $value,
        string $effectiveFrom
    ): CountryThreshold;
}
