<?php

namespace App\Core\Auth;

use App\Core\Auth\Contracts\AuthServiceContract;
use App\Core\Auth\Exception\PasswordValidationException;
use App\Core\Data\Models\User;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use Carbon\Carbon;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Contracts\Hashing\Hasher;
use OTPHP\TOTP;

class AuthService implements AuthServiceContract
{
    /**
     * @var UserRepositoryContract
     */
    private $userRepo;
    /**
     * @var Hasher
     */
    private $hasher;

    public function __construct(UserRepositoryContract $userRepo, Hasher $hasher)
    {
        $this->userRepo = $userRepo;
        $this->hasher = $hasher;
    }

    /**
     * @inheritDoc
     */
    public function login(string $username, string $secret): ?User
    {
        if (mb_strlen($username) === 0 || mb_strlen($secret) === 0) {
            return null;
        }

        $user = $this->userRepo->getUserByEmail($username, true);
        if (is_null($user) || !$user->active) {
            return null;
        }

        if (is_null($user->email_verified_at)) {
            return null;
        }

        $env = app()->environment() ?? 'production';
        if ($env !== 'local') {
            $password = $user->password;
            if (!$this->hasher->check($secret, $password)) {
                return null;
            }
        }

        $user->load(
            'institutionInstitutionTypeUserRoles.role',
            'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionTypePartners.country',
            'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionTypePartners.institutionInstitutionType.institution',
            'institutionInstitutionTypeUserRoles.institutionInstitutionType.institution',
            'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionType',
            'companiesUserRoles.company',
            'companiesUserRoles.role'
        );

        if (!$user->isClientType() && $user->institutionInstitutionTypeUserRoles->count() < 1) {
            return null;
        }

        return $user;
    }

    /**
     * @inheritDoc
     */
    public function resetPassword(User $user): string
    {
        $password = $this->generatePassword();

        $user->password = $this->hasher->make($password);
        $user->password_reset_token = null;
        $user->password_reset_time = Carbon::now();
        $preferences = $user->preferences ?? [];
        $preferences['passwordAutoGenerated'] = true;
        $user->preferences = $preferences;

        $user->save();

        return $password;
    }

//    Generate a random 16 character long password.
//    Newly generated password will have at least: 1 capital letter, 1 non-capital letter and 1 digit
    public function generatePassword(): string
    {
        $upperCaseLetter = chr(65 + rand(0, 25));
        $lowerCaseLetter = chr(97 + rand(0, 25));
        $number = chr(48 + rand(0, 9));

        $password = random_string(16);
        $password = str_replace(substr($password, rand(0, strlen($password) - 1), 1), $upperCaseLetter, $password);
        $password = str_replace(substr($password, rand(0, strlen($password) - 1), 1), $lowerCaseLetter, $password);

        return str_replace(substr($password, rand(0, strlen($password) - 1), 1), $number, $password);
    }

    /**
     * @inheritDoc
     */
    public function create2FASeedAndGenerateQrCodeForUser(int $userId): string
    {
        $user = $this->userRepo->getUserById($userId);
        if (!is_null($user->seed)) {
            $totp = TOTP::create($user->seed);
        } else {
            $totp = TOTP::create();
        }
        $totp->setIssuer(config('app.name'));
        $totp->setLabel($user->full_name);

        $user->seed = $totp->getSecret();

        $user->save();

        $qrCode = QrCode::create($totp->getProvisioningUri())
            ->setSize(250);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);

        return 'data:image/png;base64,' . base64_encode($result->getString());
    }

    /**
     * @inheritDoc
     */
    public function validateTokenForUser(int $userId, ?string $token = null): bool
    {
        $valid = false;
        $user = $this->userRepo->getUserById($userId);
        if (!is_null($token) && !is_null($user) && mb_strlen($token) === 6) {
            $token = (int)$token;
            $token = str_pad($token, 6, '0', STR_PAD_LEFT);
            $totp = TOTP::create($user->seed);
            $valid = $totp->verify($token);
        }

        return $valid;
    }

    /**
     * @inheritDoc
     */
    public function enable2FAForUser(int $userId): void
    {
        $user = $this->userRepo->getUserById($userId);
        $user->has_token = true;

        $user->save();
    }

    /**
     * @inheritDoc
     */
    public function disable2FAForUser(int $userId): void
    {
        $user = $this->userRepo->getUserById($userId);
        $user->seed = null;
        $user->has_token = false;

        $user->save();
    }

    public function checkPasswordLetterValidation(?string $password = null): bool
    {
        $password = $password ?? '';
        $hasSpaces = preg_replace('/\s+/', '_', $password);
        if ($password !== $hasSpaces) {
            throw new PasswordValidationException(_l('validation.String that you entered contains spaces'));
        }

        if (mb_strlen($password) < 8) {
            throw new PasswordValidationException(_l('validation.Must contain at least 8 letters'));
        }

        if (mb_strtolower($password) === $password) {
            throw new PasswordValidationException(_l('validation.Must contain at least one uppercase letter'));
        }

        if (mb_strtoupper($password) === $password) {
            throw new PasswordValidationException(_l('validation.Must contain at least one lowercase letter'));
        }

        preg_match_all('!\d+!', $password, $matches);
        $matches = $matches[0] ?? [];
        if (count($matches) < 1) {
            throw new PasswordValidationException(_l('validation.Must contain at least one digit'));
        }

        return true;
    }
}
