<?php

namespace App\Core\Auth\Contracts;

use App\Core\Auth\Exception\PasswordValidationException;
use App\Core\Data\Models\User;

interface AuthServiceContract
{
    /**
     * @param string $username
     * @param string $secret
     * @return User|null
     */
    public function login(string $username, string $secret): ?User;

    /**
     * @param User $user
     * @return string
     */
    public function resetPassword(User $user): string;

    /**
     * @return string
     */
    public function generatePassword(): string;

    /**
     * @param int $userId
     * @return string
     */
    public function create2FASeedAndGenerateQrCodeForUser(int $userId): string;

    /**
     * @param int $userId
     * @param string|null $token
     * @return bool
     */
    public function validateTokenForUser(int $userId, ?string $token = null): bool;

    /**
     * @param int $userId
     */
    public function enable2FAForUser(int $userId): void;

    /**
     * @param int $userId
     */
    public function disable2FAForUser(int $userId): void;

    /**
     * @param string|null $password
     * @return bool
     * @throws PasswordValidationException
     */
    public function checkPasswordLetterValidation(?string $password = null): bool;
}
