<?php

namespace App\Core\Institutions;

use App\Core\Banks\Contracts\BankServiceContract;
use App\Core\Data\Models\Address;
use App\Core\Data\Models\Institution;
use App\Core\Data\Models\InstitutionBranch;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Models\InstitutionType;
use App\Core\Data\Models\Person;
use App\Core\Data\Repositories\Contracts\AddressRepositoryContract;
use App\Core\Data\Repositories\Contracts\BankRepositoryContract;
use App\Core\Data\Repositories\Contracts\CityRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\ContactRepositoryContract;
use App\Core\Data\Repositories\Contracts\InstitutionRepositoryContract;
use App\Core\Data\Repositories\Contracts\PersonRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatNumberRepositoryContract;
use App\Core\Institutions\Contracts\InstitutionServiceContract;
use App\Core\Institutions\DataTransfer\AddressTransferObject;
use App\Core\Institutions\DataTransfer\BankTransferObject;
use App\Core\Institutions\DataTransfer\InstitutionBranchTransferObject;
use App\Core\Institutions\DataTransfer\InstitutionTransferObject;
use App\Core\Institutions\DataTransfer\PersonTransferObject;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use stdClass;
use Throwable;

class InstitutionService implements InstitutionServiceContract
{
    private InstitutionRepositoryContract $institutionRepo;
    private AddressRepositoryContract $addressRepo;
    private PersonRepositoryContract $personRepo;
    private ContactRepositoryContract $contactRepo;
    private CityRepositoryContract $cityRepo;
    private CompanyRepositoryContract $companyRepo;
    private TaxNumberRepositoryContract $taxNumberRepo;
    private VatNumberRepositoryContract $vatNumberRepo;
    private BankRepositoryContract $bankRepo;
    private BankServiceContract $bankService;
    private UserRepositoryContract $userRepo;

    public function __construct(
        InstitutionRepositoryContract $institutionRepo,
        AddressRepositoryContract $addressRepo,
        PersonRepositoryContract $personRepo,
        ContactRepositoryContract $contactRepo,
        CityRepositoryContract $cityRepo,
        CompanyRepositoryContract $companyRepo,
        TaxNumberRepositoryContract $taxNumberRepo,
        VatNumberRepositoryContract $vatNumberRepo,
        BankRepositoryContract $bankRepo,
        BankServiceContract $bankService,
        UserRepositoryContract $userRepo
    ) {
        $this->institutionRepo = $institutionRepo;
        $this->addressRepo = $addressRepo;
        $this->personRepo = $personRepo;
        $this->contactRepo = $contactRepo;
        $this->cityRepo = $cityRepo;
        $this->companyRepo = $companyRepo;
        $this->taxNumberRepo = $taxNumberRepo;
        $this->vatNumberRepo = $vatNumberRepo;
        $this->bankRepo = $bankRepo;
        $this->bankService = $bankService;
        $this->userRepo = $userRepo;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    public function fullStoreUpdateInstitution(
        InstitutionTransferObject $institutionTransfer,
        PersonTransferObject $authorisedPersonTransfer,
        AddressTransferObject $addressTransfer,
        bool $isAdministrative,
        bool $isPartnerAdmin,
        ?PersonTransferObject $contactPersonTransfer = null,
        ?BankTransferObject $bankTransfer = null,
        ?AddressTransferObject $authorisedPersonAddressTransfer = null
    ): Institution {
        $institution = $this->institutionRepo->getEmptyInstitutionModel();
        if ($institutionTransfer->isSaved()) {
            $institution = $this->institutionRepo->getInstitutionById($institutionTransfer->getId());
        }

        $address = $this->addressRepo->getEmptyAddressModel();
        if ($addressTransfer->isSaved()) {
            $address = $this->addressRepo->getAddressById($addressTransfer->getId());
        }

        $authorisedPersonAddress = null;
        if (!is_null($authorisedPersonAddressTransfer)) {
            $authorisedPersonAddress = $this->addressRepo->getEmptyAddressModel();
            if ($authorisedPersonAddressTransfer->isSaved()) {
                $authorisedPersonAddress = $this->addressRepo->getAddressById($authorisedPersonAddressTransfer->getId());
            }
        }


        $authorisedPerson = $this->personRepo->getEmptyPersonModel();
        if ($authorisedPersonTransfer->isSaved()) {
            $authorisedPerson = $this->personRepo->getPersonById($authorisedPersonTransfer->getId());
        }

        $contactPerson = null;
        if (!is_null($contactPersonTransfer)) {
            $contactPerson = $this->personRepo->getEmptyPersonModel();
            if ($contactPersonTransfer->isSaved()) {
                $contactPerson = $this->personRepo->getPersonById($contactPersonTransfer->getId());
            }
        }

        if (is_null($institution) || is_null($address) || is_null($authorisedPerson) || (!is_null($contactPersonTransfer) && is_null($contactPerson)) || (!is_null($authorisedPersonAddressTransfer) && is_null($authorisedPersonAddress))) {
            throw new Exception('Institution, address, authorized person or contact person not found in database');
        }

        $isAccountant = $institutionTransfer->isAccountant();
        $isPartner = $institutionTransfer->isPartner();

        if (!$isAccountant && !$isPartner) {
            throw new Exception('At least one institution type must be given');
        }

        if ($isPartner) {
            foreach ($institutionTransfer->getAccountants() as $partner) {
                $countries = $partner['countries'] ?? [];
                if (count($countries) < 1) {
                    throw new Exception('At least one country must be given');
                }
            }
        }

        {
            //save addresses
            $address = $this->saveAddress($address, $addressTransfer);

            $authorisedPersonAddressId = null;
            if (is_null($authorisedPersonAddress)) {
                $existingAddress = $institution->authorisedPerson->address ?? null;
                if (!is_null($existingAddress)) {
                    $existingAddress->delete();
                }
            } else {
                $authorisedPersonAddress = $this->saveAddress($authorisedPersonAddress, $authorisedPersonAddressTransfer);
                $authorisedPersonAddressId = $authorisedPersonAddress->id;
            }

            // Save authorized and contact person
            $authorisedPerson = $this->savePerson($authorisedPerson, $authorisedPersonTransfer, $authorisedPersonAddressId);
            if (!is_null($contactPerson)) {
                $contactPerson = $this->savePerson($contactPerson, $contactPersonTransfer);
            }
        }

        {
            try {

                DB::beginTransaction();
                // Save institution
                $institution->full_legal_name = $institutionTransfer->getFullLegalName();
                $institution->short_name = $institutionTransfer->getShortName();
                $institution->registration_number = $institutionTransfer->getRegistrationNumber();
                $institution->legal_type = $institutionTransfer->getLegalType();
                $institution->web = $institutionTransfer->getWeb();
                $institution->default_tax_office_id = $institutionTransfer->getDefaultTaxOfficeId();
                $institution->address_id = $address->id;
                $institution->authorised_person_id = $authorisedPerson->id;
                if (!is_null($contactPerson)) {
                    $institution->contact_person_id = $contactPerson->id;
                }

                $institution->save();

                $institution->load(
                    'institutionInstitutionTypes.teams',
                    'institutionInstitutionTypes.institutionTypeAccountants.institutionInstitutionType.teams',
                    'taxNumbers',
                    'vatNumbers',
                    'bank'
                );

                {
                    // Save institution contacts
                    foreach ($institutionTransfer->getContacts() as $contactData) {
                        $this->saveContact($contactData, 'institution_id', $institution->id);
                    }
                }

                if ($isAccountant) {
                    $this->resolveInstitutionInstitutionType($institution->id, InstitutionType::TYPE_ACCOUNTANT);
                }

                if ($isPartner) {
                    $institutionInstitutionType = $this->resolveInstitutionInstitutionType($institution->id, InstitutionType::TYPE_PARTNER);
                    if ($isAdministrative || $isPartnerAdmin) {
                        $this->resolveAccountants($institutionInstitutionType, $institutionTransfer->getAccountants());
                    }
                }

                $this->resolveTaxNumbers($institution, $institutionTransfer->getTaxNumbers());
                $this->resolveVatNumbers($institution, $institutionTransfer->getVatNumbers());
                DB::commit();
            } catch (Throwable $exception) {
                /** @noinspection PhpUnhandledExceptionInspection */
                DB::rollBack();
                /** @noinspection PhpUnhandledExceptionInspection */
                throw $exception;
            }
        }
        {
            // Save bank details

            if (is_null($bankTransfer)) {
                $bank = $institution->bank;
                if (!is_null($bank)) {
                    $this->bankRepo->deleteBankById($bank->id);
                }
            } else {
                $bankId = null;
                if ($bankTransfer->isSaved()) {
                    $bankId = $bankTransfer->getId();
                }
                $this->bankService->storeUpdateBank(
                    $bankTransfer->getName(),
                    $bankTransfer->getBeneficiary(),
                    $bankTransfer->getSwift(),
                    $bankTransfer->getIban(),
                    $bankTransfer->getAccountNumber(),
                    $bankTransfer->getRoutingNumber(),
                    $bankId,
                    null,
                    $institution->id,
                    null,
                    null,
                    $bankTransfer->getBranchName()
                );
            }
        }

        return $institution;
    }

    /**
     * @param InstitutionInstitutionType $institutionInstitutionType
     * @param Collection $accountants
     */
    private function resolveAccountants(InstitutionInstitutionType $institutionInstitutionType, Collection $accountants): void
    {
        $exitingAccountants = $institutionInstitutionType
            ->load(
                'institutionTypePartners',
                'institutionTypeAccountants'
            )
            ->institutionTypeAccountants
            ->groupBy('institution_institution_type_id')
            ->transform(function (Collection $countries) {
                return $countries->keyBy('country_id');
            });

        $accountants = $accountants->keyBy('institution.id')
            ->transform(function ($accountant) {
                $countries = collect($accountant['countries'])
                    ->keyBy('id');
                $accountant['countries'] = $countries;

                return $accountant;
            });


        $forDeleteIds = $this->resolveInstitutionTypeAccountantsForDelete($accountants, $exitingAccountants);
        $accountantsForInsert = $this->resolveInstitutionTypeAccountantsForInsert($accountants, $exitingAccountants, $institutionInstitutionType);

        try {
            $this->institutionRepo->deleteInstitutionTypePartnersByIds($forDeleteIds);
        } catch (Throwable) {
        }
        $this->institutionRepo->insertInstitutionTypePartners($accountantsForInsert);
    }

    private function resolveInstitutionTypeAccountantsForInsert(Collection $accountants, Collection $exitingAccountants, InstitutionInstitutionType $institutionInstitutionType): array
    {
        $forInsert = [];

        foreach ($accountants as $accountantId => $accountant) {
            $countries = $accountant['countries'] ?? collect();
            foreach ($countries as $countryId => $country) {
                $country = [
                    'institution_institution_type_id' => $accountantId,
                    'partner_id'                      => $institutionInstitutionType->id,
                    'country_id'                      => $countryId,
                ];
                if (!$exitingAccountants->has($accountantId)) {
                    $forInsert[] = $country;
                    continue;
                }
                /** @var Collection $exitingAccountantCountries */
                $exitingAccountantCountries = $exitingAccountants->get($accountantId);
                if (!$exitingAccountantCountries->has($countryId)) {
                    $forInsert[] = $country;
                }
            }
        }

        return $forInsert;
    }

    private function resolveInstitutionTypeAccountantsForDelete(Collection $accountants, Collection $exitingAccountants): array
    {
        $forDeleteIds = [];
        foreach ($exitingAccountants as $exitingAccountantId => $exitingAccountantCountry) {
            foreach ($exitingAccountantCountry as $country) {
                if (!$accountants->has($exitingAccountantId)) {
                    $forDeleteIds[] = $country->id;
                    continue;
                }
                $partnerCountries = $accountants->get($exitingAccountantId)['countries'] ?? collect();
                if (!$partnerCountries->has($country->country_id)) {
                    $forDeleteIds[] = $country->id;
                }
            }
        }

        return $forDeleteIds;
    }

    /**
     * @param int $institutionId
     * @param int $institutionTypeId
     * @return InstitutionInstitutionType
     */
    private function resolveInstitutionInstitutionType(int $institutionId, int $institutionTypeId): InstitutionInstitutionType
    {
        $institutionInstitutionType = $this->institutionRepo
            ->getInstitutionInstitutionTypeByInstitutionIdAndInstitutionTypeId($institutionId, $institutionTypeId);
        if (is_null($institutionInstitutionType)) {
            $institutionInstitutionType = $this->institutionRepo
                ->getEmptyInstitutionInstitutionTypeModel();

            $institutionInstitutionType->institution_id = $institutionId;
            $institutionInstitutionType->institution_type_id = $institutionTypeId;

            $institutionInstitutionType->save();
        }

        return $institutionInstitutionType;
    }

    /**
     * @param Person $person
     * @param PersonTransferObject $personTransfer
     * @param int|null $addressId
     * @return Person
     */
    private function savePerson(Person $person, PersonTransferObject $personTransfer, ?int $addressId = null): Person
    {
        $person->first_name = $personTransfer->getFirstName();
        $person->last_name = $personTransfer->getLastName();
        $person->position = $personTransfer->getPosition();
        $person->birth_country_id = $personTransfer->getBirthCountryId();
        $person->place_of_birth = $personTransfer->getPlaceOfBirth();
        $person->passport_number = $personTransfer->getPassportNumber();
        $person->passport_issued_by = $personTransfer->getPassportIssuedBy();
        $person->passport_country_id = $personTransfer->getPassportCountryId();
        $person->passport_is_id_card = $personTransfer->getPassportIsIdCard();
        $person->gender_type_id = $personTransfer->getGenderTypeId();
        $person->nationality = $personTransfer->getNationality();
        $person->address_id = $addressId;
        $person->pesel_number = $personTransfer->getPeselNumber();
        $person->nip_number = $personTransfer->getNipNumber();
        $person->nif_number = $personTransfer->getNifNumber();
        $person->nie_number = $personTransfer->getNieNumber();

        $birthday = $personTransfer->getBirthday();
        $passportValidFrom = $personTransfer->getPassportValidFrom();
        $passportValidUntil = $personTransfer->getPassportValidUntil();

        if (!is_null($passportValidFrom)) {
            $passportValidFrom = Carbon::parse($passportValidFrom)->toDateString();
        }
        if (!is_null($passportValidUntil)) {
            $passportValidUntil = Carbon::parse($passportValidUntil)->toDateString();
        }
        if (!is_null($birthday)) {
            $birthday = Carbon::parse($birthday)->toDateString();
        }

        $person->passport_valid_from = $passportValidFrom;
        $person->passport_valid_until = $passportValidUntil;
        $person->birth_date = $birthday;

        $person->save();

        foreach ($personTransfer->getContacts() as $contactData) {
            $this->saveContact($contactData, 'person_id', $person->id);
        }

        return $person;
    }

    private function saveAddress(Address $address, AddressTransferObject $addressTransfer): Address
    {
        $city = $this->cityRepo->getCityByNameAndCountry(
            $addressTransfer->getCity(),
            $addressTransfer->getCountryId()
        );
        if (is_null($city)) {
            $city = $this->cityRepo->getEmptyCityModel();
            $city->name = $addressTransfer->getCity();
            $city->country_id = $addressTransfer->getCountryId();

            $city->save();
        }

        $address->street = $addressTransfer->getStreet();
        $address->house_number = $addressTransfer->getNumber();
        $address->city_id = $city->id;
        $address->postal_code = $addressTransfer->getZipCode();
        $address->country_id = $addressTransfer->getCountryId();
        $address->state = $addressTransfer->getState();
        $address->addition = $addressTransfer->getAddition();

        $address->save();

        return $address;
    }

    private function saveContact(array $contactData, string $resourceKey, int $resourceId): void
    {
        $value = $contactData['value'] ?? '';
        $value = trim($value);

        $contact = $this->contactRepo->getEmptyContactModel();
        if (!is_null($contactData['id'])) {
            $contact = $this->contactRepo->getContactById($contactData['id']);
        }
        if (is_null($contact)) {
            return;
        }

        $contact->contact_type_id = $contactData['contactTypeId'];
        $contact->value = $value;
        $contact->{$resourceKey} = $resourceId;

        $contact->save();
    }

    /**
     * @inheritDoc
     */
    public function deleteInstitution(int $institutionId): void
    {
        $this->institutionRepo->deleteInstitutionInstitutionTypeByInstitutionId($institutionId);
        $this->institutionRepo->deleteInstitutionById($institutionId);
    }

    /**
     * @inheritDoc
     */
    public function getPartnerAccountantsByCountry(int $partnerId): Collection
    {
        return $this->institutionRepo
            ->getInstitutionTypePartnersByPartnerId($partnerId)
            ->load('country', 'institutionInstitutionType.institution')
            ->groupBy('country_id')
            ->map(function (Collection $institutionTypePartners) {
                $country = $institutionTypePartners->first()->country;

                $data = new stdClass();
                $data->country = $country;
                $data->institutionTypePartners = $institutionTypePartners;

                return $data;
            });
    }

    /**
     * @inheritDoc
     */
    public function getAllCompaniesIdsForInstitutionInstitutionType(InstitutionInstitutionType $institutionInstitutionType): array
    {
        $institutionInstitutionType->load(
            'companies',
            'institutionTypePartners.companyAccountants.accountant',
            'institutionTypePartners.companyAccountants.company'
        );

        if ($institutionInstitutionType->institution_type_id === InstitutionType::TYPE_PARTNER) {
            return $institutionInstitutionType->companies->pluck('id')->toArray();
        }

        return $this->companyRepo->getAllCompaniesIdsByAccountantInstitutionInstitutionTypeId($institutionInstitutionType->id);
    }

    /**
     * @inheritDoc
     */
    public function fullStoreUpdateInstitutionBranch(
        InstitutionBranchTransferObject $institutionBranchTransfer,
        PersonTransferObject $authorisedPersonTransfer,
        AddressTransferObject $addressTransfer,
        ?AddressTransferObject $authorisedPersonAddressTransfer
    ): InstitutionBranch {
        $institutionBranch = $this->institutionRepo->getEmptyInstitutionBranchModel();

        if ($institutionBranchTransfer->isSaved()) {
            $institutionBranch = $this->institutionRepo->getInstitutionBranchById($institutionBranchTransfer->getId());
        }

        $address = $this->addressRepo->getEmptyAddressModel();
        if ($addressTransfer->isSaved()) {
            $address = $this->addressRepo->getAddressById($addressTransfer->getId());
        }

        $authorisedPerson = $this->personRepo->getEmptyPersonModel();
        if ($authorisedPersonTransfer->isSaved()) {
            $authorisedPerson = $this->personRepo->getPersonById($authorisedPersonTransfer->getId());
        }

        $authorisedPersonAddress = null;
        if (!is_null($authorisedPersonAddressTransfer)) {
            $authorisedPersonAddress = $this->addressRepo->getEmptyAddressModel();
            if ($authorisedPersonAddressTransfer->isSaved()) {
                $authorisedPersonAddress = $this->addressRepo->getAddressById($authorisedPersonAddressTransfer->getId());
            }
        }

        if (is_null($institutionBranch) || is_null($address) || is_null($authorisedPerson) || (!is_null($authorisedPersonAddressTransfer) && is_null($authorisedPersonAddress))) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new Exception('Institution branch, address, authorized person or authorised person address not found in database');
        }
        {
            //save addresses
            $address = $this->saveAddress($address, $addressTransfer);

            $authorisedPersonAddressId = null;
            $deleteAuthorisedPersonAddress = false;
            if (is_null($authorisedPersonAddress)) {
                $existingAddress = $institution->authorisedPerson->address ?? null;
                if (!is_null($existingAddress)) {
                    $deleteAuthorisedPersonAddress = true;
                }

                if ($deleteAuthorisedPersonAddress && !is_null($existingAddress)) {
                    $existingAddress->delete();
                }
            } else {
                $authorisedPersonAddress = $this->saveAddress($authorisedPersonAddress, $authorisedPersonAddressTransfer);
                $authorisedPersonAddressId = $authorisedPersonAddress->id;
            }

            // Save authorized person
            $authorisedPerson = $this->savePerson($authorisedPerson, $authorisedPersonTransfer, $authorisedPersonAddressId);
        }

        {
            // Save institution
            $institutionBranch->institution_id = $institutionBranchTransfer->getInstitutionId();
            $institutionBranch->branch_name = $institutionBranchTransfer->getBranchName();
            $institutionBranch->branch_code = $institutionBranchTransfer->getBranchCode();
            $institutionBranch->branch_short_name = $institutionBranchTransfer->getShortName();
            $institutionBranch->default_tax_office_id = $institutionBranchTransfer->getDefaultTaxOfficeId();
            $institutionBranch->address_id = $address->id;
            $institutionBranch->authorised_person_id = $authorisedPerson->id;

            $institutionBranch->save();

        }

        return $institutionBranch;

    }

    /**
     * @param Institution $institution
     * @param Collection $taxNumbers
     */
    private function resolveTaxNumbers(Institution $institution, Collection $taxNumbers): void
    {
        $existingTaxNumberIds = $institution->taxNumbers->pluck('id')->toArray();
        $newTaxNumberIds = $taxNumbers->pluck('id')->toArray();
        $idsToDelete = array_diff($existingTaxNumberIds, $newTaxNumberIds);

        $this->taxNumberRepo->deleteTaxNumbersByIds($idsToDelete);

        foreach ($taxNumbers as $taxNum) {
            if (!is_null($taxNum['id'])) {
                $taxNumber = $this->taxNumberRepo->getTaxNumberById($taxNum['id']);
            } else {
                $taxNumber = $this->taxNumberRepo->getEmptyTaxNumberModel();
            }
            $taxNumber->country_id = $taxNum['country']['id'];
            $taxNumber->number = $taxNum['number'];
            $taxNumber->institution_id = $institution->id;

            $taxNumber->save();
        }
    }

    private function resolveVatNumbers(Institution $institution, Collection $vatNumbers): void
    {
        $existingVatNumberIds = $institution->vatNumbers->pluck('id')->toArray();
        $newVatNumberIds = $vatNumbers->pluck('id')->toArray();
        $idsToDelete = array_diff($existingVatNumberIds, $newVatNumberIds);

        $this->vatNumberRepo->deleteVatNumbersByIds($idsToDelete);

        foreach ($vatNumbers as $vatNum) {
            if (!is_null($vatNum['id'])) {
                $vatNumber = $this->vatNumberRepo->getVatNumberById($vatNum['id']);
            } else {
                $vatNumber = $this->vatNumberRepo->getEmptyVatNumberModel();
            }
            $registerDate = null;
            if (!is_null($vatNum['registerDate'])) {
                $registerDate = Carbon::parse($vatNum['registerDate'])->toDateString();
            }

            $vatNumber->country_id = $vatNum['country']['id'];
            $vatNumber->vat_number = $vatNum['number'];
            $vatNumber->institution_id = $institution->id;
            $vatNumber->register_date = $registerDate;
            $vatNumber->save();
        }
    }

    /**
     * @inheritDoc
     */
    public function isUserOnInstitution(int $userId, int $institutionInstitutionTypeId): bool
    {
        $usersIds = $this->userRepo
            ->getAllUsersForInstitution($institutionInstitutionTypeId)
            ->keyBy('id')
            ->keys()
            ->toArray();

        return in_array($userId, $usersIds);
    }
}
