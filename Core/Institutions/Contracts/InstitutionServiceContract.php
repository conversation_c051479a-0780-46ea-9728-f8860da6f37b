<?php

namespace App\Core\Institutions\Contracts;

use App\Core\Data\Models\Institution;
use App\Core\Data\Models\InstitutionBranch;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Institutions\DataTransfer\AddressTransferObject;
use App\Core\Institutions\DataTransfer\InstitutionTransferObject;
use App\Core\Institutions\DataTransfer\InstitutionBranchTransferObject;
use App\Core\Institutions\DataTransfer\PersonTransferObject;
use App\Core\Institutions\DataTransfer\BankTransferObject;
use Illuminate\Support\Collection;

interface InstitutionServiceContract
{
    public function fullStoreUpdateInstitution(
        InstitutionTransferObject $institutionTransfer,
        PersonTransferObject $authorisedPersonTransfer,
        AddressTransferObject $addressTransfer,
        bool $isAdministrative,
        bool $isPartnerAdmin,
        ?PersonTransferObject $contactPersonTransfer = null,
        ?BankTransferObject $bankTransfer = null,
        ?AddressTransferObject $authorisedPersonAddressTransfer = null
    ): Institution;

    /**
     * @param int $institutionId
     */
    public function deleteInstitution(int $institutionId): void;

    /**
     * @param int $partnerId
     * @return Collection
     */
    public function getPartnerAccountantsByCountry(int $partnerId): Collection;

    /**
     * @param InstitutionInstitutionType $institutionInstitutionType
     * @return array
     */
    public function getAllCompaniesIdsForInstitutionInstitutionType(InstitutionInstitutionType $institutionInstitutionType): array;

    /**
     * @param InstitutionBranchTransferObject $institutionBranchTransfer
     * @param PersonTransferObject $authorisedPersonTransfer
     * @param AddressTransferObject $addressTransfer
     * @param AddressTransferObject|null $authorisedPersonAddressTransfer
     * @return InstitutionBranch
     */
    public function fullStoreUpdateInstitutionBranch(
        InstitutionBranchTransferObject $institutionBranchTransfer,
        PersonTransferObject $authorisedPersonTransfer,
        AddressTransferObject $addressTransfer,
        ?AddressTransferObject $authorisedPersonAddressTransfer
    ): InstitutionBranch;

    /**
     * @param int $userId
     * @param int $institutionInstitutionTypeId
     * @return bool
     */
    public function isUserOnInstitution(int $userId, int $institutionInstitutionTypeId): bool;
}
