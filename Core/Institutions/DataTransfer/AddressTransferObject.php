<?php

namespace App\Core\Institutions\DataTransfer;

use Illuminate\Contracts\Support\Arrayable;

class AddressTransferObject implements Arrayable
{
    /**
     * @var string
     */
    private $street;
    /**
     * @var string
     */
    private $number;
    /**
     * @var string
     */
    private $city;
    /**
     * @var string
     */
    private $zipCode;
    /**
     * @var int
     */
    private $countryId;
    /**
     * @var int|null
     */
    private $id;
    /**
     * @var string|null
     */
    private $state;
    /**
     * @var string|null
     */
    private $addition;

    /**
     * AddressTransferObject constructor.
     *
     * @param string $street
     * @param string $number
     * @param string $city
     * @param string $zipCode
     * @param int $countryId
     * @param int|null $id
     * @param string|null $state
     * @param string|null $addition
     */
    public function __construct(
        string $street,
        string $number,
        string $city,
        string $zipCode,
        int $countryId,
        ?int $id = null,
        ?string $state = null,
        ?string $addition = null
    ) {
        $this->street = $street;
        $this->number = $number;
        $this->city = trim($city);
        $this->zipCode = $zipCode;
        $this->countryId = $countryId;
        $this->id = $id;
        $this->state = $state;
        $this->addition = $addition;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'id'        => $this->getId(),
            'isSaved'   => $this->isSaved(),
            'street'    => $this->getStreet(),
            'number'    => $this->getNumber(),
            'city'      => $this->getCity(),
            'zipCode'   => $this->getZipCode(),
            'countryId' => $this->getCountryId(),
            'state'     => $this->getState(),
            'addition'  => $this->getAddition(),
        ];
    }

    public function isSaved(): bool
    {
        return !is_null($this->getId());
    }

    /**
     * @return string
     */
    public function getStreet(): string
    {
        return $this->street;
    }

    /**
     * @return string
     */
    public function getNumber(): string
    {
        return $this->number;
    }

    /**
     * @return string
     */
    public function getCity(): string
    {
        return $this->city;
    }

    /**
     * @return string
     */
    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    /**
     * @return int
     */
    public function getCountryId(): int
    {
        return $this->countryId;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @return string|null
     */
    public function getAddition(): ?string
    {
        return $this->addition;
    }
}
