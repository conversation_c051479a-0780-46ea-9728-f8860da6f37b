<?php

namespace App\Core\Institutions\DataTransfer;

use Illuminate\Contracts\Support\Arrayable;

class BankTransferObject implements Arrayable
{
    /**
     * @var string
     */
    private $name;
    /**
     * @var string
     */
    private $beneficiary;
    /**
     * @var string|null
     */
    private $iban;
    /**
     * @var string|null
     */
    private $accountNumber;
    /**
     * @var string|null
     */
    private $routingNumber;
    /**
     * @var string
     */
    private $swift;
    /**
     * @var string|null
     */
    private $branchName;
    /**
     * @var int|null
     */
    private $id;


    /**
     * BankTransferObject constructor.
     *
     * @param string $name
     * @param string|null $branchName
     * @param string $beneficiary
     * @param string $swift
     * @param string|null $iban
     * @param int|null $id
     */
    public function __construct(
        string $name,
        string $beneficiary,
        string $swift,
        ?string $iban = null,
        ?string $accountNumber = null,
        ?string $routingNumber = null,
        ?string $branchName = null,
        ?int $id = null
    ) {
        $this->name = $name;
        $this->branchName = $branchName;
        $this->beneficiary = $beneficiary;
        $this->swift = $swift;
        $this->iban = $iban;
        $this->accountNumber = $accountNumber;
        $this->routingNumber = $routingNumber;
        $this->id = $id;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'id'            => $this->getId(),
            'isSaved'       => $this->isSaved(),
            'name'          => $this->getName(),
            'branchName'    => $this->getBranchName(),
            'beneficiary'   => $this->getBeneficiary(),
            'iban'          => $this->getIban(),
            'accountNumber' => $this->getAccountNumber(),
            'routingNumber' => $this->getRoutingNumber(),
            'swift'         => $this->getSwift()
        ];
    }

    public function isSaved(): bool
    {
        return !is_null($this->getId());
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getBranchName(): ?string
    {
        return $this->branchName;
    }

    /**
     * @return string
     */
    public function getBeneficiary(): string
    {
        return $this->beneficiary;
    }

    /**
     * @return string|null
     */
    public function getIban(): string|null
    {
        return $this->iban;
    }

    /**
     * @return string|null
     */
    public function getAccountNumber(): string|null
    {
        return $this->accountNumber;
    }

    /**
     * @return string|null
     */
    public function getRoutingNumber(): string|null
    {
        return $this->routingNumber;
    }

    /**
     * @return string
     */
    public function getSwift(): string
    {
        return $this->swift;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }
}
