<?php

namespace App\Core\Institutions\DataTransfer;

use Illuminate\Contracts\Support\Arrayable;

class InstitutionBranchTransferObject implements Arrayable
{
    /**
     * @var int
     */
    private $institutionId;
    /**
     * @var string
     */
    private $branchName;
    /**
     * @var string|null
     */
    private $branchCode;
    /**
     * @var int|null
     */
    private $id;
    /**
     * @var string|null
     */
    private $shortName;
    /**
     * @var int|null
     */
    private $defaultTaxOfficeId;

    /**
     * InstitutionTransferObject constructor.
     *
     * @param int $institutionId
     * @param string $branchName
     * @param string|null $branchCode
     * @param int|null $id
     * @param string|null $shortName
     * @param int|null $defaultTaxOfficeId
     */
    public function __construct(
        int $institutionId,
        string $branchName,
        string $branchCode = null,
        ?int $id = null,
        ?string $shortName = null,
        ?int $defaultTaxOfficeId = null
    ) {
        $this->institutionId = $institutionId;
        $this->branchName = $branchName;
        $this->branchCode = $branchCode;
        $this->id = $id;
        $this->shortName = $shortName;
        $this->defaultTaxOfficeId = $defaultTaxOfficeId;
    }


    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'institutionId'      => $this->getInstitutionId(),
            'branchName'         => $this->getBranchName(),
            'branchCode'         => $this->getBranchCode(),
            'id'                 => $this->getId(),
            'shortName'          => $this->getShortName(),
            'defaultTaxOfficeId' => $this->getDefaultTaxOfficeId(),
            'isSaved'            => $this->isSaved()
        ];
    }

    public function isSaved(): bool
    {
        return !is_null($this->getId());
    }

    /**
     * @return int
     */
    public function getInstitutionId(): int
    {
        return $this->institutionId;
    }

    /**
     * @return string
     */
    public function getBranchName(): string
    {
        return $this->branchName;
    }

    /**
     * @return string|null
     */
    public function getBranchCode(): ?string
    {
        return $this->branchCode;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getShortName(): ?string
    {
        return $this->shortName;
    }

    /**
     * @return int|null
     */
    public function getDefaultTaxOfficeId(): ?int
    {
        return $this->defaultTaxOfficeId;
    }

}
