<?php

namespace App\Core\Institutions\DataTransfer;

use App\Core\Data\Models\InstitutionType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;

class InstitutionTransferObject implements Arrayable
{
    /**
     * @var string
     */
    private $fullLegalName;
    /**
     * @var Collection
     */
    private $types;
    /**
     * @var Collection
     */
    private $contacts;
    /**
     * @var int|null
     */
    private $id;
    /**
     * @var string|null
     */
    private $shortName;
    /**
     * @var string|null
     */
    private $web;
    /**
     * @var Collection
     */
    private $accountants;

    /**
     * @var int|null
     */
    private $defaultTaxOfficeId;
    /**
     * @var Collection
     */
    private $taxNumbers;
    /**
     * @var Collection
     */
    private $vatNumbers;
    /**
     * @var string|null
     */
    private $registrationNumber;
    /**
     * @var string|null
     */
    private $legalType;

    public function __construct(
        string $fullLegalName,
        array $types,
        array $contacts,
        array $accountants,
        array $taxNumbers,
        array $vatNumbers,
        ?int $id = null,
        ?string $shortName = null,
        ?string $web = null,
        ?int $defaultTaxOfficeId = null,
        ?string $registrationNumber = null,
        ?string $legalType = null,
    ) {
        $this->fullLegalName = $fullLegalName;
        $this->types = collect($types);
        $this->accountants = collect($accountants);
        $this->taxNumbers = collect($taxNumbers);
        $this->vatNumbers = collect($vatNumbers);
        $this->contacts = collect($contacts);
        $this->id = $id;
        $this->shortName = $shortName;
        $this->web = $web;
        $this->defaultTaxOfficeId = $defaultTaxOfficeId;
        $this->registrationNumber = $registrationNumber;
        $this->legalType = $legalType;
    }


    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'fullLegalName'      => $this->getFullLegalName(),
            'registrationNumber' => $this->getRegistrationNumber(),
            'legalType'          => $this->getLegalType(),
            'types'              => $this->getTypes()->toArray(),
            'contacts'           => $this->getContacts()->toArray(),
            'id'                 => $this->getId(),
            'shortName'          => $this->getShortName(),
            'web'                => $this->getWeb(),
            'defaultTaxOfficeId' => $this->getDefaultTaxOfficeId(),
            'accountants'        => $this->getAccountants()->toArray(),
            'taxNumbers'         => $this->getTaxNumbers()->toArray(),
            'vatNumbers'         => $this->getVatNumbers()->toArray(),
            'isSaved'            => $this->isSaved()
        ];
    }

    public function isSaved(): bool
    {
        return !is_null($this->getId());
    }

    public function isPartner(): bool
    {
        return in_array(InstitutionType::TYPE_PARTNER, $this->getTypes()->toArray());
    }

    public function isAccountant(): bool
    {
        return in_array(InstitutionType::TYPE_ACCOUNTANT, $this->getTypes()->toArray());
    }

    /**
     * @return string
     */
    public function getFullLegalName(): string
    {
        return $this->fullLegalName;
    }

    /**
     * @return Collection
     */
    public function getTypes(): Collection
    {
        return $this->types;
    }

    /**
     * @return Collection
     */
    public function getContacts(): Collection
    {
        return $this->contacts;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getShortName(): ?string
    {
        return $this->shortName;
    }

    /**
     * @return string|null
     */
    public function getWeb(): ?string
    {
        return $this->web;
    }

    /**
     * @return Collection
     */
    public function getAccountants(): Collection
    {
        return $this->accountants;
    }

    /**
     * @return int|null
     */
    public function getDefaultTaxOfficeId(): ?int
    {
        return $this->defaultTaxOfficeId;
    }

    /**
     * @return Collection
     */
    public function getTaxNumbers(): Collection
    {
        return $this->taxNumbers;
    }

    /**
     * @return Collection
     */
    public function getVatNumbers(): Collection
    {
        return $this->vatNumbers;
    }

    /**
     * @return string|null
     */
    public function getLegalType(): ?string
    {
        return $this->legalType;
    }

    /**
     * @return string|null
     */
    public function getRegistrationNumber(): ?string
    {
        return $this->registrationNumber;
    }
}
