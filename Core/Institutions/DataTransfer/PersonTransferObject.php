<?php

namespace App\Core\Institutions\DataTransfer;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;

class PersonTransferObject implements Arrayable
{
    /**
     * @var string
     */
    private $firstName;
    /**
     * @var string
     */
    private $lastName;
    /**
     * @var Collection
     */
    private $contacts;
    /**
     * @var int|null
     */
    private $id;
    /**
     * @var string|null
     */
    private $position;
    /**
     * @var int|null
     */
    private $birthCountryId;
    /**
     * @var string|null
     */
    private $placeOfBirth;
    /**
     * @var string|null
     */
    private $passportNumber;
    /**
     * @var string|null
     */
    private $passportValidFrom;
    /**
     * @var string|null
     */
    private $passportValidUntil;
    /**
     * @var string|null
     */
    private $passportIssuedBy;
    /**
     * @var int|null
     */
    private $passportCountryId;
    /**
     * @var bool|null
     */
    private $passportIsIdCard;
    /**
     * @var int|null
     */
    private $genderTypeId;
    /**
     * @var string|null
     */
    private $nationality;
    /**
     * @var string|null
     */
    private $birthday;
    /**
     * @var string|null
     */
    private $peselNumber;
    /**
     * @var string|null
     */
    private $nipNumber;
    /**
     * @var string|null
     */
    private $nifNumber;
    /**
     * @var string|null
     */
    private $nieNumber;

    public function __construct(
        string $firstName,
        string $lastName,
        array $contacts,
        ?int $id = null,
        ?string $position = null,
        ?int $birthCountryId = null,
        ?string $placeOfBirth = null,
        ?string $passportNumber = null,
        ?string $passportValidFrom = null,
        ?string $passportValidUntil = null,
        ?string $passportIssuedBy = null,
        ?int $passportCountryId = null,
        ?bool $passportIsIdCard = false,
        ?int $genderTypeId = null,
        ?string $nationality = null,
        ?string $birthday = null,
        ?string $peselNumber = null,
        ?string $nipNumber = null,
        ?string $nifNumber = null,
        ?string $nieNumber = null
    ) {
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->contacts = collect($contacts);
        $this->id = $id;
        $this->position = $position;
        $this->birthCountryId = $birthCountryId;
        $this->placeOfBirth = $placeOfBirth;
        $this->passportNumber = $passportNumber;
        $this->passportValidFrom = $passportValidFrom;
        $this->passportValidUntil = $passportValidUntil;
        $this->passportIssuedBy = $passportIssuedBy;
        $this->passportCountryId = $passportCountryId;
        $this->passportIsIdCard = $passportIsIdCard;
        $this->genderTypeId = $genderTypeId;
        $this->nationality = $nationality;
        $this->birthday = $birthday;
        $this->peselNumber = $peselNumber;
        $this->nipNumber = $nipNumber;
        $this->nifNumber = $nifNumber;
        $this->nieNumber = $nieNumber;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'id'                 => $this->getId(),
            'isSaved'            => $this->isSaved(),
            'firstName'          => $this->getFirstName(),
            'lastName'           => $this->getLastName(),
            'position'           => $this->getPosition(),
            'contacts'           => $this->getContacts()->toArray(),
            'birthCountryId'     => $this->getBirthCountryId(),
            'placeOfBirth'       => $this->getPlaceOfBirth(),
            'passportNumber'     => $this->getPassportNumber(),
            'passportValidFrom'  => $this->getPassportValidFrom(),
            'passportValidUntil' => $this->getPassportValidUntil(),
            'passportIssuedBy'   => $this->getPassportIssuedBy(),
            'passportCountryId'  => $this->getPassportCountryId(),
            'passportIsIdCard'   => $this->getPassportIsIdCard(),
            'genderTypeId'       => $this->getGenderTypeId(),
            'nationality'        => $this->getNationality(),
            'birthday'           => $this->getBirthday(),
            'peselNumber'        => $this->getPeselNumber(),
            'nipNumber'          => $this->getNipNumber(),
            'nifNumber'          => $this->getNifNumber(),
            'nieNumber'          => $this->getNieNumber(),
        ];
    }

    public function isSaved(): bool
    {
        return !is_null($this->getId());
    }

    /**
     * @return string
     */
    public function getFirstName(): string
    {
        return $this->firstName;
    }

    /**
     * @return string
     */
    public function getLastName(): string
    {
        return $this->lastName;
    }

    /**
     * @return Collection
     */
    public function getContacts(): Collection
    {
        return $this->contacts;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getPosition(): ?string
    {
        return $this->position;
    }

    /**
     * @return int|null
     */
    public function getBirthCountryId(): ?int
    {
        return $this->birthCountryId;
    }

    /**
     * @return string|null
     */
    public function getPlaceOfBirth(): ?string
    {
        return $this->placeOfBirth;
    }

    /**
     * @return string|null
     */
    public function getPassportNumber(): ?string
    {
        return $this->passportNumber;
    }

    /**
     * @return string|null
     */
    public function getPassportValidFrom(): ?string
    {
        return $this->passportValidFrom;
    }

    /**
     * @return string|null
     */
    public function getPassportValidUntil(): ?string
    {
        return $this->passportValidUntil;
    }

    /**
     * @return string|null
     */
    public function getPassportIssuedBy(): ?string
    {
        return $this->passportIssuedBy;
    }

    /**
     * @return int|null
     */
    public function getPassportCountryId(): ?int
    {
        return $this->passportCountryId;
    }

    /**
     * @return bool
     */
    public function getPassportIsIdCard(): bool
    {
        return $this->passportIsIdCard;
    }

    /**
     * @return int|null
     */
    public function getGenderTypeId(): ?int
    {
        return $this->genderTypeId;
    }

    /**
     * @return string|null
     */
    public function getNationality(): ?string
    {
        return $this->nationality;
    }

    /**
     * @return string|null
     */
    public function getBirthday(): ?string
    {
        return $this->birthday;
    }

    /**
     * @return string|null
     */
    public function getPeselNumber(): ?string
    {
        return $this->peselNumber;
    }

    /**
     * @return string|null
     */
    public function getNipNumber(): ?string
    {
        return $this->nipNumber;
    }

    /**
     * @return string|null
     */
    public function getNifNumber(): ?string
    {
        return $this->nifNumber;
    }

    /**
     * @return string|null
     */
    public function getNieNumber(): ?string
    {
        return $this->nieNumber;
    }
}
