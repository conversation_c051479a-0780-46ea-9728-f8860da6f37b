<?php

namespace App\Core\Brands;

use App\Core\Data\Models\Brand;
use App\Core\Brands\Contracts\BrandServiceContract;
use App\Core\Data\Repositories\Contracts\BrandRepositoryContract;

class BrandService implements BrandServiceContract
{
    private BrandRepositoryContract $brandRepo;

    public function __construct(BrandRepositoryContract $brandRepo)
    {
        $this->brandRepo = $brandRepo;
    }

    public function storeUpdateBrand(
        string $name,
        ?string $description = null,
        ?int $brandId = null
    ): ?Brand
    {
        $brand = is_null($brandId) ? $this->brandRepo->getEmptyBrandModel() : $this->brandRepo->getBrandById($brandId);

        if (is_null($brand)) {
            return null;
        }

        $brand->name = $name;
        $brand->description = $description;

        $brand->save();

        return $brand;
    }
}
