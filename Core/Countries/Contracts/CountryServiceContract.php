<?php

namespace App\Core\Countries\Contracts;

use App\Core\Data\Models\Country;

interface CountryServiceContract
{
    public function fullStoreUpdateCountryCurrency(
        int $countryId,
        string $code,
        string $name,
        bool $hasFbaWarehouse = false,
        bool $doingTax = false,
        ?array $countriesCurrency = [],
        ?string $inEuStartDate = null,
        ?string $inEuEndDate = null,
        ?string $domesticReverseChargeApplicableFrom = null
    ): ?Country;

}
