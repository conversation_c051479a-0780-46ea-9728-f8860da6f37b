<?php

namespace App\Core\Countries;

use App\Core\Countries\Contracts\CountryServiceContract;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\CountryCurrency;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;

class CountryService implements CountryServiceContract
{
    /**
     * @var CountryRepositoryContract
     */
    private $countryRepo;

    /**
     * CountryService constructor.
     *
     * @param CountryRepositoryContract $countryRepo
     */
    public function __construct(
        CountryRepositoryContract $countryRepo
    ) {
        $this->countryRepo = $countryRepo;
    }

    public function fullStoreUpdateCountryCurrency(
        int $countryId,
        string $code,
        string $name,
        bool $hasFbaWarehouse = false,
        bool $doingTax = false,
        ?array $countriesCurrency = [],
        ?string $inEuStartDate = null,
        ?string $inEuEndDate = null,
        ?string $domesticReverseChargeApplicableFrom = null
    ): ?Country {
        $activeCurrencyId = collect($countriesCurrency)->filter(function (array $currency) {
            return is_null($currency['endDate']);
        })->pluck('currency')->first()['id'] ?? null;

        $country = $this->updateCountry(
            $countryId,
            $code,
            $name,
            $hasFbaWarehouse,
            $doingTax,
            $inEuStartDate,
            $inEuEndDate,
            $domesticReverseChargeApplicableFrom,
            $activeCurrencyId
        );

        if (count($countriesCurrency) > 0) {
            foreach ($countriesCurrency as $countryCurrency) {
                if ($countryCurrency['delete']) {
                    $this->countryRepo->deleteCountryCurrencyById($countryCurrency['id']);
                } else {
                    $this->storeUpdateCountryCurrency(
                        $countryId,
                        $countryCurrency['currency']['id'],
                        $countryCurrency['id'],
                        $countryCurrency['startDate'],
                        $countryCurrency['endDate']
                    );
                }
            }
        }

        return $country;
    }

    private function updateCountry(
        int $countryId,
        string $code,
        string $name,
        bool $hasFbaWarehouse = false,
        bool $doingTax = false,
        ?string $inEuStartDate = null,
        ?string $inEuEndDate = null,
        ?string $domesticReverseChargeApplicableFrom = null,
        ?int $currencyId = null
    ): Country {
        $country = $this->countryRepo->getCountryById($countryId);
        $country->code = $code;
        $country->name = $name;
        $country->has_fba_warehouse = $hasFbaWarehouse;
        $country->doing_tax = $doingTax;
        $country->in_eu_start_date = $inEuStartDate;
        $country->in_eu_end_date = $inEuEndDate;
        $country->domestic_reverse_charge_applicable_from = $domesticReverseChargeApplicableFrom;
        $country->currency_id = $currencyId;

        $country->save();

        return $country;
    }

    private function storeUpdateCountryCurrency(
        int $countryId,
        int $currencyId,
        ?int $countryCurrencyId = null,
        ?string $startDate = null,
        ?string $endDate = null
    ): CountryCurrency {
        $countryCurrency = $this->countryRepo->getEmptyCountryCurrencyModel();
        if (!is_null($countryCurrencyId)) {
            $countryCurrency = $this->countryRepo->getCountryCurrencyById($countryCurrencyId);
        }
        $countryCurrency->country_id = $countryId;
        $countryCurrency->currency_id = $currencyId;
        $countryCurrency->start_date = $startDate;
        $countryCurrency->end_date = $endDate;

        $countryCurrency->save();

        return $countryCurrency;
    }
}
