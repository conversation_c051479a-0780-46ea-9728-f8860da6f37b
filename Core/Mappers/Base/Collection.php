<?php

namespace App\Core\Mappers\Base;

use ArrayIterator;
use Countable;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection as LaravelCollection;
use IteratorAggregate;
use Traversable;

class Collection implements Countable, IteratorAggregate, Arrayable, Jsonable
{
    protected array $items = [];

    public function getIterator(): Traversable
    {
        return new ArrayIterator($this->items);
    }

    public function count(): int
    {
        return count($this->items);
    }

    public function toArray(): array
    {
        $items = [];
        foreach ($this->items as $key => $item) {
            /**
             * @var DataValue $item
             */
            $items[$key] = $item->toArray();
        }

        return $items;
    }

    public function toBaseCollection(): LaravelCollection
    {
        return collect($this->toArray());
    }

    public function getFieldsFlatten(): LaravelCollection
    {
        $categoryOrData = $this->toBaseCollection()->pluck('data')->flatten(1);

        return $this->extractData($categoryOrData, collect())->keyBy('key');
    }

    private function extractData(LaravelCollection $categoryOrData, LaravelCollection $flatten): LaravelCollection
    {
        foreach ($categoryOrData as $value) {
            if ($value['isCategory']) {
                $data = collect($value['data'] ?? []);
                $this->extractData($data, $flatten);
            } else {
                $flatten->push($value);
            }
        }

        return $flatten;
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function put(DataValue $value): self
    {
        $this->items[$value->getKey()] = $value;

        return $this;
    }

    public function get(string $key): ?DataValue
    {
        return $this->items[$key] ?? null;
    }

    public function has(string $key): bool
    {
        return !is_null($this->get($key) ?? null);
    }

    public function values(): self
    {
        $this->items = array_values($this->items);

        return $this;
    }

    public function remap(): void
    {
        $original = collect($this->items);
        $withParent = $original->filter(function (DataValue $category) {
            return !is_null($category->getCategory());
        });
        $withParentKeys = $withParent->keys()->toArray();
        $withParent = $withParent->groupBy(function (DataValue $category) {
            return $category->getCategory();
        });

        $original = $original->map(function (DataValue $category) use ($withParent) {
            /**
             * @var DataValue $child
             */
            $children = $withParent->get($category->getKey());
            if (is_null($children)) {
                return $category;
            }

            foreach ($children as $child) {
                $category->putData($child);
            }

            return $category;
        })->filter(function (DataValue $category) use ($withParentKeys) {
            return !in_array($category->getKey(), $withParentKeys);
        });

        $items = [];
        foreach ($original as $key => $item) {
            $items[$key] = $item;
        }
        $this->items = $items;
    }
}
