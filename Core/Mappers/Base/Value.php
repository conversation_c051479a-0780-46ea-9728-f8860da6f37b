<?php

namespace App\Core\Mappers\Base;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

readonly class Value implements Arrayable, Jsonable
{
    public function __construct(
        private string $name,
        private string $description,
        private string $category,
        private mixed $value,
        private mixed $original,
    ) {
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @return string
     */
    public function getCategory(): string
    {
        return $this->category;
    }

    /**
     * @return mixed
     */
    public function getValue(): mixed
    {
        return $this->value;
    }

    public function getOriginal(): mixed
    {
        return $this->original;
    }

    public function toArray(): array
    {
        return [
            'name'        => $this->getName(),
            'description' => $this->getDescription(),
            'category'    => $this->getCategory(),
            'value'       => $this->getValue(),
            'original'    => $this->getOriginal(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
