<?php
declare(strict_types=1);

namespace App\Core\Mappers\Base;

use App\Core\Mappers\Base\Exceptions\CategoryNotFoundException;
use App\Core\Mappers\Base\Exceptions\MethodAlreadyExistsException;
use App\Core\Mappers\Base\Exceptions\MethodNotFoundException;
use App\Core\Mappers\Base\Exceptions\MethodReturnParameterException;
use App\Core\Mappers\Base\Exceptions\NotCorrectlyInitializedException;
use App\Core\Mappers\Base\Exceptions\ValueException;
use App\Core\Mappers\Contracts\MapperServiceContract;
use Closure;
use Illuminate\Support\Str;

abstract class Mapper
{
    protected ?Collection $data = null;
    private const CATEGORY_COMMON = 'common';
    private const CATEGORY_TEXT_MANIPULATION = 'text-manipulation';
    private const CATEGORY_DATE_TIME = 'date-time';

    public static bool $useNameAsValue = false;
    public static bool $useExceptionAsValue = true;

    abstract public function categories(): void;

    abstract public static function getName(): string;

    abstract public static function getDescription(): string;

    abstract public static function getClass(): string;

    abstract public static function getExample(): Mapper;

    private static ?MapperServiceContract $mapperService = null;

    protected static function getMapperService(): MapperServiceContract
    {
        if (is_null(self::$mapperService)) {
            /** @noinspection PhpUnhandledExceptionInspection */
            self::$mapperService = app()->make(MapperServiceContract::class);
        }

        return self::$mapperService;
    }

    /**
     * @throws NotCorrectlyInitializedException
     */
    public function getData(): Collection
    {
        if (is_null($this->data)) {
            throw new NotCorrectlyInitializedException();
        }

        return $this->data;
    }

    /**
     * @throws NotCorrectlyInitializedException
     */
    public function toArray(): array
    {
        return [
            'name'        => $this::getName(),
            'description' => $this::getDescription(),
            'data'        => $this->getData()->values()->toArray(),
        ];
    }

    /**
     * @throws NotCorrectlyInitializedException
     */
    public function toJson(): string
    {
        return evat_json_encode($this->toArray());
    }

    /**
     * @param string $key
     * @param string $name
     * @param string|null $parentCategory
     * @param int|null $sequence
     * @return $this
     * @throws NotCorrectlyInitializedException
     */
    protected function addCategory(string $key, string $name, ?string $parentCategory = null, ?int $sequence = null): self
    {
        if (is_null($this->data)) {
            throw new NotCorrectlyInitializedException();
        }

        $dataValue = new DataValue(
            name: $name,
            key: $key,
            description: null,
            value: null,
            isCategory: true,
            category: $parentCategory,
            sequence: $sequence,
            original: null
        );

        $this->data->put($dataValue);

        return $this;
    }

    /**
     * @throws NotCorrectlyInitializedException
     * @throws MethodAlreadyExistsException
     * @throws CategoryNotFoundException
     * @noinspection PhpRedundantCatchClauseInspection
     * @noinspection PhpReturnValueOfMethodIsNeverUsedInspection
     */
    private function addValue(
        string $name,
        string $description,
        string $method,
        string $categoryKey,
        mixed $value,
        mixed $original,
    ): self {
        if (is_null($this->data)) {
            throw new NotCorrectlyInitializedException();
        }

        if ($this->data->has($method)) {
            throw new MethodAlreadyExistsException();
        }

        if (!$this->data->has($categoryKey)) {
            throw new CategoryNotFoundException();
        }

        if ($value instanceof Closure) {
            try {
                $value = $value();
            } catch (ValueException $exception) {
                $value = null;
                if (self::$useExceptionAsValue) {
                    $value = $exception->getMessage();
                }
            }
        }

        if ($original instanceof Closure) {
            try {
                $original = $original();
            } catch (ValueException $exception) {
                $original = null;
                if (self::$useExceptionAsValue) {
                    $original = $exception->getMessage();
                }
            }
        }

        if (self::$useNameAsValue && is_null($value)) {
            $value = $name;
        }

        if (self::$useNameAsValue && is_null($original)) {
            $original = $name;
        }

        $key = $method;

        $dataValue = new DataValue(
            name: $name,
            key: $key,
            description: $description,
            value: $value,
            isCategory: false,
            category: $categoryKey,
            original: $original
        );

        $category = $this->data->get($categoryKey);
        $category->putData($dataValue);

        $this->data->put($category);

        return $this;
    }

    protected function createValue(
        string $name,
        string $description,
        string $categoryKey,
        mixed $value,
        mixed $original = null,
    ): Value {
        if (!is_null($value) && is_null($original)) {
            $original = $value;
        }

        return new Value(
            $name,
            $description,
            $categoryKey,
            $value,
            $original,
        );
    }

    /**
     * @throws NotCorrectlyInitializedException
     * @throws MethodNotFoundException
     * @throws MethodAlreadyExistsException
     * @throws MethodReturnParameterException
     * @throws CategoryNotFoundException
     */
    public function __invoke(): void
    {
        $this->data = new Collection();

        $this->addCategory(self::CATEGORY_TEXT_MANIPULATION, _l('mappers.Text manipulation'));
        $this->addCategory(self::CATEGORY_COMMON, _l('common.Common'));
        $this->addCategory(self::CATEGORY_DATE_TIME, _l('mappers.Time and Date'));

        $this->categories();
        $this->setup();
    }

    /**
     * @throws CategoryNotFoundException
     * @throws MethodAlreadyExistsException
     * @throws MethodNotFoundException
     * @throws MethodReturnParameterException
     * @throws NotCorrectlyInitializedException
     */
    private function setup(): void
    {
        $methods = array_filter(get_class_methods($this), function (string $method) {
            $method = Str::snake($method);
            $method = explode('_', $method);
            if (count($method) < 3) {
                return false;
            }

            if ($method[0] !== 'get') {
                return false;
            }

            if (end($method) !== 'value') {
                return false;
            }

            return true;
        });

        foreach ($methods as $method) {
            if (!method_exists($this, $method)) {
                throw new MethodNotFoundException('Method -> "' . $method . '" not found');
            }

            /**
             * @var Value $valueData
             */
            $valueData = $this->$method();
            if (!is_a($valueData, Value::class)) {
                throw new MethodReturnParameterException('Method "' . $method . '" must return object of class "' . Value::class . '"');
            }

            $this->addValue(
                $valueData->getName(),
                $valueData->getDescription(),
                $method,
                $valueData->getCategory(),
                $valueData->getValue(),
                $valueData->getOriginal()
            );
        }
        $this->data->remap();
    }

    /** @noinspection PhpUnused */
    public function getCommaValue(): Value
    {
        return $this->createValue(_l('mappers.Comma'), _l('mappers.Adds comma to given place'), self::CATEGORY_TEXT_MANIPULATION, ',', ',');
    }

    /** @noinspection PhpUnused */
    public function getDotValue(): Value
    {
        return $this->createValue(_l('mappers.Dot'), _l('mappers.Adds dot to given place'), self::CATEGORY_TEXT_MANIPULATION, '.', '.');
    }

    /** @noinspection PhpUnused */
    public function getSpaceValue(): Value
    {
        return $this->createValue(_l('mappers.Space'), _l('mappers.Adds space to given place'), self::CATEGORY_TEXT_MANIPULATION, ' ', ' ');
    }

    /** @noinspection PhpUnused */
    public function getDateNowValue(): Value
    {
        return $this->createValue(_l('mappers.Date'), _l('mappers.Adds Date now'), self::CATEGORY_DATE_TIME, now()->format('d.m.Y'), now()->format('d.m.Y'));
    }

    /** @noinspection PhpUnused */
    public function getTrueValue(): Value
    {
        return $this->createValue(_l('mappers.True'), _l('mappers.True'), self::CATEGORY_COMMON, true, true);
    }

    protected function formatNumber(?float $number = null, int $decimals = 0, string $decimalSeparator = ',', string $thousandSeparator = '.'): ?string
    {
        if (is_null($number)) {
            return $number;
        }

        $number = round($number, $decimals);

        return number_format($number, $decimals, $decimalSeparator, $thousandSeparator);
    }
}
