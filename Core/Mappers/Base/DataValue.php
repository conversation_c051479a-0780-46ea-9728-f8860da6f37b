<?php

namespace App\Core\Mappers\Base;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class DataValue implements Arrayable, Jsonable
{
    private string $name, $key;
    private bool $isCategory;
    private mixed $value;
    private ?string $category, $description, $method;
    private ?array $data = null;
    private ?int $sequence;
    private mixed $original;

    public function __construct(
        string $name,
        string $key,
        ?string $description = null,
        mixed $value = null,
        bool $isCategory = false,
        ?string $category = null,
        ?int $sequence = null,
        mixed $original = null,
    ) {
        $this->name = $name;
        $this->description = $description;
        $this->value = $value;
        $this->key = $key;
        $this->isCategory = $isCategory;
        $this->category = $category;
        if (!$isCategory) {
            $this->method = $key;
        }
        $this->sequence = $sequence;
        $this->original = $original;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getCategory(): ?string
    {
        return $this->category;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @return string
     */
    public function getKey(): string
    {
        return $this->key;
    }

    /**
     * @return string|null
     */
    public function getMethod(): ?string
    {
        return $this->method;
    }

    /**
     * @return bool
     */
    public function isCategory(): bool
    {
        return $this->isCategory;
    }

    /**
     * @return mixed
     */
    public function getValue(): mixed
    {
        return $this->value;
    }

    /**
     * @return array|null
     */
    public function getData(): ?array
    {
        return $this->data;
    }

    public function putData(DataValue $value): void
    {
        $this->data[] = $value;
    }

    /**
     * @return int|null
     */
    public function getSequence(): string
    {
        $sequence = $this->sequence;
        if (is_null($sequence)) {
            return 'ZZ';
        }

        return 'AA' . $sequence;
    }

    public function getOriginal(): mixed
    {
        return $this->original;
    }

    public function toArray(): array
    {
        if (!$this->isCategory()) {
            return [
                'key'         => $this->getKey(),
                'name'        => $this->getName(),
                'description' => $this->getDescription(),
                'method'      => $this->getMethod(),
                'isCategory'  => false,
                'category'    => $this->getCategory(),
                'value'       => $this->getValue(),
                'original'    => $this->getOriginal()
            ];
        }

        $data = [];
        $sorted = $this->sortData($this->getData());
        foreach ($sorted as $key => $value) {
            /**
             * @var DataValue $value
             */
            $data[$key] = $value->toArray();
        }

        return [
            'key'        => $this->getKey(),
            'name'       => $this->getName(),
            'isCategory' => true,
            'category'   => $this->getCategory(),
            'data'       => $data
        ];
    }

    private function sortData(?array $data = null): Collection
    {
        if (!is_array($data)) {
            return collect();
        }

        return collect($data)->sortBy(function (DataValue $dataValue) {
            $prefix = $dataValue->isCategory() ? '1' : '0';

            return $prefix . '_' . $dataValue->getSequence() . '_' . $dataValue->getName();
        }, SORT_NATURAL)->values();
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
