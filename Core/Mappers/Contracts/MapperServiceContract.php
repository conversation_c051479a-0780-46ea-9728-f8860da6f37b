<?php

namespace App\Core\Mappers\Contracts;

use App\Core\Mappers\Base\Mapper;
use App\Core\Mappers\Mappers\Company\CompanyMapper;
use App\Core\Mappers\Mappers\Invoices\EslReport\EslReportMapper;
use App\Core\Mappers\Mappers\Invoices\OssIossReport\OssIossReportMapper;
use App\Core\Mappers\Mappers\Invoices\VatReport\VatReportMapper;
use App\Core\Mappers\MappersData\CompanyMapperData\CompanyData;
use App\Core\Mappers\MappersData\InvoicesForEslReport\EslReportInvoicesData;
use App\Core\Mappers\MappersData\InvoicesForOssIossReport\OssIossReportInvoicesData;
use App\Core\Mappers\MappersData\InvoicesForVatReport\VatReportInvoicesData;

interface MapperServiceContract
{
    public function resolveMapper(string $mapper, MapperDataContract $data): Mapper;

    public function resolveCompanyMapper(CompanyData $data): CompanyMapper;

    public function resolveVatReportMapper(VatReportInvoicesData $data): VatReportMapper;

    public function resolveOssIossReportMapper(OssIossReportInvoicesData $data): OssIossReportMapper;

    public function resolveEslReportMapper(EslReportInvoicesData $data): EslReportMapper;
}
