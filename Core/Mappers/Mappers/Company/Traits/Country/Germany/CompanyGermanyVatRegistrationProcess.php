<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Germany;

use App\Core\Data\Models\CompanyCountrySale;
use App\Core\Data\Models\Country;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyGermanyVatRegistrationProcess
{
    public function getCompanyGermanyVatRegistrationProcess(): ?CompanyCountrySale
    {
        return $this->company?->countrySales
            ->where('country_id', Country::DE)
            ->first();
    }

    public function getCompanyGermanyVatRegistrationProcessDateFirstSaleValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company date of first sale'),
            _l('company-mapper.Prints Company date of first sale'),
            self::CATEGORY_COMPANY_GERMANY_VAT_REGISTRATION,
            function () {
                $dateFirstSale = $this->getCompanyGermanyVatRegistrationProcess();
                if (is_null($dateFirstSale)) {
                    return null;
                }

                if (is_null($dateFirstSale->first_sale_date)) {
                    return null;
                }

                return Carbon::parse($dateFirstSale->first_sale_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyGermanyVatRegistrationProcessEstimateSaleAmountCurrentYearValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Estimate sale amount in the current year'),
            _l('company-mapper.Prints Company Estimate sale amount in the current year'),
            self::CATEGORY_COMPANY_GERMANY_VAT_REGISTRATION,
            function () {
                return $this->getCompanyGermanyVatRegistrationProcess()?->current_year_sale_estimate;
            }
        );
    }

    public function getCompanyGermanyVatRegistrationProcessEstimateSaleAmountNextYearValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Estimate sale amount in the next year'),
            _l('company-mapper.Prints Company Estimate sale amount in the next year'),
            self::CATEGORY_COMPANY_GERMANY_VAT_REGISTRATION,
            function () {
                return $this->getCompanyGermanyVatRegistrationProcess()?->next_year_sale_estimate;
            }
        );
    }
}
