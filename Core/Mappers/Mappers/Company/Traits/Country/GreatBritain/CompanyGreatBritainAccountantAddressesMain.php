<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Address;
use App\Core\Mappers\Base\Value;
trait CompanyGreatBritainAccountantAddressesMain
{
    public function getCompanyGreatBritainAccountantAddressMain(): ?Address
    {
        return $this->getCompanyGreatBritainAccountant()?->address;
    }

    public function getCompanyGreatBritainAccountantDisplayAddressValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant display address'),
            _l('company-mapper.Prints Company accountant display address'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->display_address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address country'),
            _l('company-mapper.Prints Company accountant address country'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->country?->name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressCountryCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address country code'),
            _l('company-mapper.Prints Company accountant address country code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->country?->code;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address street'),
            _l('company-mapper.Prints Company accountant address street'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->street;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressStateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address state'),
            _l('company-mapper.Prints Company accountant address state'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->state;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address house number'),
            _l('company-mapper.Prints Company accountant address house number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->house_number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address postal code'),
            _l('company-mapper.Prints Company accountant address postal code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->postal_code;
            }
        );
    }

    public function getCompanyGreatBritainAccountantAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address city'),
            _l('company-mapper.Prints Company accountant address city'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyGreatBritainAccountantAddressMain()?->city?->name;
            }
        );
    }

}
