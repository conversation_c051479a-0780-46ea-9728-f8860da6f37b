<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Address;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\InstitutionBranch;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyGreatBritainAccountantBranches
{
    public function getCompanyGreatBritainAccountantBranches(): ?InstitutionBranch
    {
        return $this->company
            ->companyAccountants
            ->where('accountant.country_id', Country::GB)
            ->first()?->institutionBranch;
    }

    public function getCompanyGreatBritainAccountantBranchesAddress(): ?Address
    {
        if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
            return $this->getCompanyGreatBritainAccountantBranches()?->address;
        }

        return $this->getCompanyGreatBritainAccountant()?->address;
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPerson(): ?Address
    {
        if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
            return $this->getCompanyGreatBritainAccountantBranches()?->authorisedPerson?->address;
        }

        return $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->address;
    }

    public function getCompanyGreatBritainAccountantBranchesNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant name'),
            _l('company-mapper.Prints Company assigned accountant name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
                    return $this->getCompanyGreatBritainAccountantBranches()?->branch_name;
                }

                return $this->getCompanyGreatBritainAccountant()?->full_legal_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesShortNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant short name'),
            _l('company-mapper.Prints Company assigned accountant short name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
                    return $this->getCompanyGreatBritainAccountantBranches()?->branch_short_name;
                }

                return $this->getCompanyGreatBritainAccountant()?->short_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person first name'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person first name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {

                if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
                    return $this->getCompanyGreatBritainAccountantBranches()?->authorisedPerson?->first_name;
                }

                return $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->first_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonNameLastValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person last name'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person last name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
                    return $this->getCompanyGreatBritainAccountantBranches()?->authorisedPerson?->last_name;
                }

                return $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->last_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonPositionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person position'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person position'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
                    return $this->getCompanyGreatBritainAccountantBranches()?->authorisedPerson?->position;
                }

                return $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->position;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonDateBirthValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person Date of birth'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person Date of birth'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyGreatBritainAccountantBranches())) {
                    $birthDate = $this->getCompanyGreatBritainAccountantBranches()?->authorisedPerson?->birth_date;
                } else {
                    $birthDate = $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->birth_date;
                }

                if (is_null($birthDate)) {
                    return $birthDate;
                }

                return Carbon::parse($birthDate)->format('d.m.Y');
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesDisplayAddressValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant display address'),
            _l('company-mapper.Prints Company assigned accountant display address'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->display_address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address country'),
            _l('company-mapper.Prints Company assigned accountant address country'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->country?->name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressCountryCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address country code'),
            _l('company-mapper.Prints Company assigned accountant address country code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->country?->code;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address street'),
            _l('company-mapper.Prints Company assigned accountant address street'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->street;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressStateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address state'),
            _l('company-mapper.Prints Company assigned accountant address state'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->state;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address house number'),
            _l('company-mapper.Prints Company assigned accountant address house number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->house_number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address postal code'),
            _l('company-mapper.Prints Company assigned accountant address postal code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->postal_code;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address city'),
            _l('company-mapper.Prints Company assigned accountant address city'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAddress()?->city?->name;
            }
        );
    }


    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonDisplayAddressValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person display address'),
            _l('company-mapper.Prints Company assigned accountant authorised person display address'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->display_address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address country'),
            _l('company-mapper.Prints Company assigned accountant authorised person address country'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->country?->name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressCountryCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address country code'),
            _l('company-mapper.Prints Company assigned accountant authorised person address country code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->country?->code;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address street'),
            _l('company-mapper.Prints Company assigned accountant authorised person address street'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->street;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressStateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address state'),
            _l('company-mapper.Prints Company assigned accountant authorised person address state'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->state;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address house number'),
            _l('company-mapper.Prints Company assigned accountant authorised person address house number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->house_number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address postal code'),
            _l('company-mapper.Prints Company assigned accountant authorised person address postal code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->postal_code;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address city'),
            _l('company-mapper.Prints Company assigned accountant authorised person address city'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountantBranchesAuthorisedPerson()?->city?->name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonPeselNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person PESEL number'),
            _l('company-mapper.Prints Company assigned accountant authorised person PESEL number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->pesel_number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBranchesAuthorisedPersonNipNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person NIP number'),
            _l('company-mapper.Prints Company assigned accountant authorised person NIP number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyGreatBritainAccountant()?->authorisedPerson?->nip_number;
            }
        );
    }
}
