<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\TaxNumber;
use App\Core\Data\Models\VatNumber;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyTaxDataGreatBritain
{
    public function getCompanyVatNumberGreatBritain(): ?VatNumber
    {
        return $this->company?->vatNumbers
            ->where('country_id', Country::GB)
            ->first();
    }

    public function getCompanyTaxNumberGreatBritain(): ?TaxNumber
    {
        return $this->company?->taxNumbers
            ->where('country_id', Country::GB)
            ->whereNull('end_date')
            ->first();
    }

    public function getCompanyVatNumberGreatBritainValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number'),
            _l('company-mapper.Prints Company vat number'),
            self::CATEGORY_COMPANY_GB_TAX_DATA,
            function () {
                return $this->getCompanyVatNumberGreatBritain();
            }
        );
    }

    public function getCompanyVatNumberGreatBritainRegisterDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number register date'),
            _l('company-mapper.Prints Company  vat number register date'),
            self::CATEGORY_COMPANY_GB_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyVatNumberGreatBritain();
                if (is_null($vatNumber)) {
                    return null;
                }

                return Carbon::parse($vatNumber->register_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyVatNumberGreatBritainEndDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number end date'),
            _l('company-mapper.Prints Company vat number end date'),
            self::CATEGORY_COMPANY_GB_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyVatNumberGreatBritain();
                if (is_null($vatNumber)) {
                    return null;
                }

                if (is_null($vatNumber->end_date)) {
                    return null;
                }

                return Carbon::parse($vatNumber->end_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyTaxNumberGreatBritainValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number'),
            _l('company-mapper.Prints Company tax number'),
            self::CATEGORY_COMPANY_GB_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyTaxNumberGreatBritain();
                if (is_null($taxNumber)) {
                    return null;
                }

                return $taxNumber->number;
            }
        );
    }

    public function getCompanyTaxNumberGreatBritainRegisterDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number register date'),
            _l('company-mapper.Prints Company tax number register date'),
            self::CATEGORY_COMPANY_GB_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyTaxNumberGreatBritain();
                if (is_null($taxNumber)) {
                    return null;
                }

                return Carbon::parse($taxNumber->register_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyTaxNumberGreatBritainEndDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number end date'),
            _l('company-mapper.Prints Company tax number end date'),
            self::CATEGORY_COMPANY_GB_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyTaxNumberGreatBritain();
                if (is_null($taxNumber)) {
                    return null;
                }

                if (is_null($taxNumber->end_date)) {
                    return null;
                }

                return Carbon::parse($taxNumber->end_date)->format('d.m.Y');
            }
        );
    }
}
