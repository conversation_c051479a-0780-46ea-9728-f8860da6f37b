<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\CompanyCountrySale;
use App\Core\Data\Models\Country;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyGreatBritainVatRegistrationProcess
{
    public function getCompanyGreatBritainVatRegistrationProcess(): ?CompanyCountrySale
    {
        return $this->company?->countrySales
            ->where('country_id', Country::GB)
            ->first();
    }

    public function getCompanyGreatBritainVatRegistrationProcessDateFirstSaleValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company date of first sale'),
            _l('company-mapper.Prints Company date of first sale'),
            self::CATEGORY_COMPANY_GB_VAT_REGISTRATION_GENERAL,
            function () {
                $dateFirstSale = $this->getCompanyGreatBritainVatRegistrationProcess();
                if (is_null($dateFirstSale)) {
                    return null;
                }

                if (is_null($dateFirstSale->first_sale_date)) {
                    return null;
                }

                return Carbon::parse($dateFirstSale->first_sale_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyGreatBritainVatRegistrationProcessEstimateSaleAmountCurrentYearValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Estimate sale amount in the current year'),
            _l('company-mapper.Prints Company Estimate sale amount in the current year'),
            self::CATEGORY_COMPANY_GB_VAT_REGISTRATION_GENERAL,
            function () {
                return $this->getCompanyGreatBritainVatRegistrationProcess()?->current_year_sale_estimate;
            }
        );
    }

    public function getCompanyGreatBritainVatRegistrationProcessEstimateSaleAmountNextYearValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Estimate sale amount in the next year'),
            _l('company-mapper.Prints Company Estimate sale amount in the next year'),
            self::CATEGORY_COMPANY_GB_VAT_REGISTRATION_GENERAL,
            function () {
                return $this->getCompanyGreatBritainVatRegistrationProcess()?->next_year_sale_estimate;
            }
        );
    }
}
