<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Bank;
use App\Core\Mappers\Base\Value;

trait CompanyGreatBritainAccountantBank
{
    public function getCompanyGreatBritainAccountantBank(): ?Bank
    {
        return $this->getCompanyGreatBritainAccountant()?->bank;
    }

    public function getCompanyGreatBritainAccountantBankNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank name'),
            _l('company-mapper.Prints Company Accountant bank name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyGreatBritainAccountantBank()?->name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBankBranchNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank branch name'),
            _l('company-mapper.Prints Company Accountant bank branch name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyGreatBritainAccountantBank()?->branch_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBankSwiftValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank swift'),
            _l('company-mapper.Prints Company Accountant bank swift'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyGreatBritainAccountantBank()?->swift;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBankIbanValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank iban'),
            _l('company-mapper.Prints Company Accountant bank iban'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyGreatBritainAccountantBank()?->iban;
            }
        );
    }

    public function getCompanyGreatBritainAccountantBankBeneficiaryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank beneficiary'),
            _l('company-mapper.Prints Company Accountant bank beneficiary'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyGreatBritainAccountantBank()?->beneficiary;
            }
        );
    }
}
