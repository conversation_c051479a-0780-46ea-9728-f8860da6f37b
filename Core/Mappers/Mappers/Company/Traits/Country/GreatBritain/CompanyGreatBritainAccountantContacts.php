<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\ContactType;
use App\Core\Mappers\Base\Value;
trait CompanyGreatBritainAccountantContacts
{
    public function getCompanyGreatBritainAccountantContactPhoneValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts phone'),
            _l('company-mapper.Prints Company accountant contacts phone'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_CONTACTS,
            function () {
                $phone = $this->getCompanyGreatBritainAccountant()?->contacts->where('contact_type_id', ContactType::PHONE)->first();
                if (is_null($phone)) {
                    return null;
                }

                return $phone->value;
            }
        );
    }

    public function getCompanyGreatBritainAccountantContactEmailValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts email'),
            _l('company-mapper.Prints Company accountant contacts email'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_CONTACTS,
            function () {
                $email = $this->getCompanyGreatBritainAccountant()?->contacts->where('contact_type_id', ContactType::EMAIL)->first();
                if (is_null($email)) {
                    return null;
                }

                return $email->value;
            }
        );
    }

    public function getCompanyGreatBritainAccountantContactMobileValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts mobile'),
            _l('company-mapper.Prints Company accountant contacts mobile'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_CONTACTS,
            function () {
                $mobile = $this->getCompanyGreatBritainAccountant()?->contacts->where('contact_type_id', ContactType::MOBILE)->first();
                if (is_null($mobile)) {
                    return null;
                }

                return $mobile->value;
            }
        );
    }

    public function getCompanyGreatBritainAccountantContactPersonFirstNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts person first name'),
            _l('company-mapper.Prints Company accountant contacts person first name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_CONTACTS,
            function () {
                return $this->getCompanyGreatBritainAccountant()?->contactPerson?->first_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantContactPersonLastNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts person last name'),
            _l('company-mapper.Prints Company accountant contacts person last name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_CONTACTS,
            function () {
                return $this->getCompanyGreatBritainAccountant()?->contactPerson?->last_name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantContactPersonPositionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts person position'),
            _l('company-mapper.Prints Company accountant contacts person position'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_CONTACTS,
            function () {
                return $this->getCompanyGreatBritainAccountant()?->contactPerson?->position;
            }
        );
    }
}
