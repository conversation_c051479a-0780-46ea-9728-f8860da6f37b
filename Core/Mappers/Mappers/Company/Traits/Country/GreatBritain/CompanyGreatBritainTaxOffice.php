<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\TaxOffice;
use App\Core\Mappers\Base\Value;

trait CompanyGreatBritainTaxOffice
{
    private function getOfficeForGreatBritain(): ?TaxOffice
    {
        return $this->getTaxOffice(Country::IT);
    }

    public function getGreatBritainTaxOfficePostalCodeAndCityValue(): Value
    {
        $value = $this->getOfficeForGreatBritain()?->tax_office_postal_city;

        return $this->createValue(
            _l('company-mapper.Tax Office postal city'),
            _l('company-mapper.Prints tax office postal city'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE,
            $value,
            $value
        );
    }

    public function getGreatBritainTaxOfficeAddressValue(): Value
    {
        $taxOfficeAddress = $this->getOfficeForGreatBritain()?->tax_office_address;

        return $this->createValue(
            _l('company-mapper.Tax Office address'),
            _l('company-mapper.Prints tax office address'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE,
            $taxOfficeAddress,
            $taxOfficeAddress,
        );
    }

    public function getGreatBritainTaxOfficeNameValue(): Value
    {
        $taxOffice = $this->getOfficeForGreatBritain()?->tax_office;

        return $this->createValue(
            _l('company-mapper.Tax Office name'),
            _l('company-mapper.Prints tax office name'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE,
            $taxOffice,
            $taxOffice,
        );
    }

    public function getGreatBritainTaxOfficeBankSwiftValue(): Value
    {
        $swift = $this->getOfficeForGreatBritain()?->swift;

        return $this->createValue(
            _l('company-mapper.Tax office SWIFT'),
            _l('company-mapper.Prints bank SWIFT'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE_BANK,
            $swift,
            $swift,
        );
    }

    public function getGreatBritainTaxOfficeBankIbanValue(): Value
    {
        $iban = $this->getOfficeForGreatBritain()?->iban;

        return $this->createValue(
            _l('company-mapper.Tax office IBAN'),
            _l('company-mapper.Prints bank IBAN'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE_BANK,
            $iban,
            $iban,
        );
    }

    public function getGreatBritainTaxOfficeBankNameValue(): Value
    {
        $name = $this->getOfficeForGreatBritain()?->bank;

        return $this->createValue(
            _l('company-mapper.Tax office bank name'),
            _l('company-mapper.Prints bank name'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE_BANK,
            $name,
            $name,
        );
    }

    public function getGreatBritainTaxOfficeBeneficiaryValue(): Value
    {
        $beneficiary = $this->getOfficeForGreatBritain()?->tax_office;

        return $this->createValue(
            _l('company-mapper.Tax office beneficiary'),
            _l('company-mapper.Prints beneficiary name'),
            self::CATEGORY_COMPANY_GB_TAX_OFFICE_BANK,
            $beneficiary,
            $beneficiary,
        );
    }
}


