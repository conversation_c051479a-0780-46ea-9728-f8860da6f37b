<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Address;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\GenderType;
use App\Core\Data\Models\Person;
use App\Core\Mappers\Base\Exceptions\ValueException;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyGreatBritainAccountantLegalRepresentative
{
    public function getCompanyGreatBritainAccountantLegalRepresentative(): ?Person
    {
        return $this->getCompanyGreatBritainAccountant()?->authorisedPerson;
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddress(): ?Address
    {
        return $this->getCompanyGreatBritainAccountantLegalRepresentative()?->address;
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeFirstNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative first name'),
            _l('company-mapper.Prints germany accountant legal representative first name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $name = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->first_name;
                if (is_null($name)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative first name missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeLastNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative last name'),
            _l('company-mapper.Prints GreatBritain accountant legal representative last name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $name = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->last_name;
                if (is_null($name)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative last name missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePositionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative position'),
            _l('company-mapper.Prints GreatBritain accountant legal representative position'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyGreatBritainAccountantLegalRepresentative()?->position;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeNationalityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative nationality'),
            _l('company-mapper.Prints GreatBritain accountant legal representative nationality'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $name = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->nationality;
                if (is_null($name)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative nationality missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $name;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeBirthDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative birth date'),
            _l('company-mapper.Prints GreatBritain accountant legal representative birth date'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $birthdate = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->birth_date;
                if (is_null($birthdate)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative birth date missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return Carbon::parse($birthdate)->format('d.m.Y');
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePlaceOfBirthValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative place of birth'),
            _l('company-mapper.Prints GreatBritain accountant legal representative place of birth'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $placeOfBirth = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->place_of_birth;
                if (is_null($placeOfBirth)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative place of birth missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $placeOfBirth;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeCountryOfBirthValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative country birth'),
            _l('company-mapper.Prints GreatBritain accountant legal representative country birth'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $countryOfBirth = Country::all()
                    ->where('id', $this->getCompanyGreatBritainAccountantLegalRepresentative()?->birth_country_id)
                    ->pluck('name')
                    ->first();
                if (is_null($countryOfBirth)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative country birth missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $countryOfBirth;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeGenderTypeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative Gender Type'),
            _l('company-mapper.Prints GreatBritain accountant legal representative Gender Type'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $genderTypeId = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->gender_type_id;
                if (is_null($genderTypeId)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative country birth missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                $genderType = [
                    GenderType::MALE      => 'M',
                    GenderType::FEMALE    => 'F',
                    GenderType::NONBINARY => 'N'
                ];

                return $genderType[$genderTypeId];
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePassportNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal passport number'),
            _l('company-mapper.Prints GreatBritain accountant legal passport number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyGreatBritainAccountantLegalRepresentative()?->passport_number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePassportValidFromValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal passport valid from date'),
            _l('company-mapper.Prints GreatBritain accountant legal passport valid from date'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $passportValidFrom = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->passport_valid_from;
                if (is_null($passportValidFrom)) {
                    return $passportValidFrom;

                }

                return Carbon::parse($passportValidFrom)->format('d.m.Y');
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePassportValidUntilValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal passport valid until date'),
            _l('company-mapper.Prints GreatBritain accountant legal passport valid until date'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $passportValidUntil = $this->getCompanyGreatBritainAccountantLegalRepresentative()?->passport_valid_until;
                if (is_null($passportValidUntil)) {
                    return $passportValidUntil;

                }

                return Carbon::parse($passportValidUntil)->format('d.m.Y');
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePassportIssuedValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal passport issued'),
            _l('company-mapper.Prints GreatBritain accountant legal passport issued'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyGreatBritainAccountantLegalRepresentative()?->passport_issued_by;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePassportCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative country passport'),
            _l('company-mapper.Prints GreatBritain accountant legal representative country passport'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $country = Country::all()
                    ->where('id', $this->getCompanyGreatBritainAccountantLegalRepresentative()?->passport_country_id)
                    ->pluck('name')
                    ->first();
                if (is_null($country)) {
                    return null;
                }

                return $country;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative address street'),
            _l('company-mapper.Prints GreatBritain accountant legal representative address street'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyGreatBritainAccountantLegalRepresentativeAddress()?->street;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative address street missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative address house number'),
            _l('company-mapper.Prints GreatBritain accountant legal representative address house number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyGreatBritainAccountantLegalRepresentativeAddress()?->house_number;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative address house number missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative address city'),
            _l('company-mapper.Prints GreatBritain accountant legal representative address city'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyGreatBritainAccountantLegalRepresentativeAddress()?->city?->name;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative address city missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative address postal code'),
            _l('company-mapper.Prints GreatBritain accountant legal representative address postal code'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyGreatBritainAccountantLegalRepresentativeAddress()?->postal_code;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative address postal code missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative address country '),
            _l('company-mapper.Prints GreatBritain accountant legal representative address country '),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyGreatBritainAccountantLegalRepresentativeAddress()?->country?->name;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company GreatBritain accountant legal representative address country  missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeAddressAdditionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant legal representative address addition'),
            _l('company-mapper.Prints GreatBritain accountant legal representative address addition'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyGreatBritainAccountantLegalRepresentativeAddress()?->addition;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativePeselNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant legal representative PESEL number'),
            _l('company-mapper.Prints Company accountant legal representative PESEL number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyGreatBritainAccountantLegalRepresentative()?->pesel_number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantLegalRepresentativeNipNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant legal representative NIP number'),
            _l('company-mapper.Prints Company legal representative NIP number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyGreatBritainAccountantLegalRepresentative()?->nip_number;
            }
        );
    }
}
