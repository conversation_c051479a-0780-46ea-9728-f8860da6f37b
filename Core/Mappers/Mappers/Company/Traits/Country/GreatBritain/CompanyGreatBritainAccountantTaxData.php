<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Country;
use App\Core\Mappers\Base\Value;

trait CompanyGreatBritainAccountantTaxData
{
    public function getCompanyGreatBritainAccountantTaxNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant tax number'),
            _l('company-mapper.Prints GreatBritain accountant tax number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyGreatBritainAccountant()?->taxNumbers->where('country_id', Country::GB)->first();
                if (is_null($taxNumber)) {
                    return null;
                }

                return $taxNumber->number;
            }
        );
    }

    public function getCompanyGreatBritainAccountantVatNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company GreatBritain accountant vat number'),
            _l('company-mapper.Prints GreatBritain accountant vat number'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyGreatBritainAccountant()?->vatNumbers->where('country_id', Country::GB)->first();
                if (is_null($vatNumber)) {
                    return null;
                }

                return $vatNumber->vat_number;
            }
        );
    }
}
