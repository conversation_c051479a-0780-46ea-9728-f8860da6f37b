<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\GreatBritain;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\Institution;
use App\Core\Mappers\Base\Value;
use App\Core\Mappers\Base\Exceptions\ValueException;

trait CompanyGreatBritainAccountantGeneralData
{
    public function getCompanyGreatBritainAccountant(): ?Institution
    {
        $companyAccountant = $this->company
            ->companyAccountants
            ->where('accountant.country_id', Country::GB)
            ->first();

        return $companyAccountant
            ?->accountant
            ?->institutionInstitutionType
            ?->institution;
    }

    public function getCompanyGreatBritainAccountantNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant GreatBritain name'),
            _l('company-mapper.Prints Company accountant GreatBritain name'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_GENERAL_DATA,
            function () {
                $fullLegalName = $this->getCompanyGreatBritainAccountant()?->full_legal_name;
                if (is_null($fullLegalName)) {
                    throw new ValueException(
                        _l('documents.error.Company accountant GreatBritain name is missing',
                            ['url' => route('companies.general.edit-general')]
                        ));
                }

                return $fullLegalName;
            }
        );
    }

    public function getCompanyGreatBritainAccountantTypeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant GreatBritain legal type'),
            _l('company-mapper.Prints Company accountant GreatBritain legal type'),
            self::CATEGORY_COMPANY_GB_ACCOUNTANT_GENERAL_DATA,
            function () {
                $legalType = $this->getCompanyGreatBritainAccountant()?->legal_type;
                if (is_null($legalType)) {
                    return null;
                }

                return $legalType;
            }
        );
    }
}
