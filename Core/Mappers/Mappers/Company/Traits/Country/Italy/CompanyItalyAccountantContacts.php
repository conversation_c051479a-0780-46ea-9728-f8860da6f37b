<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\ContactType;
use App\Core\Mappers\Base\Value;

trait CompanyItalyAccountantContacts
{
    public function getCompanyItalyAccountantContactPhoneValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts phone'),
            _l('company-mapper.Prints Company accountant contacts phone'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_CONTACTS,
            function () {
                $phone = $this->getCompanyItalyAccountant()?->contacts->where('contact_type_id', ContactType::PHONE)->first();
                if (is_null($phone)) {
                    return null;
                }

                return $phone->value;
            }
        );
    }

    public function getCompanyItalyAccountantContactEmailValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts email'),
            _l('company-mapper.Prints Company accountant contacts email'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_CONTACTS,
            function () {
                $email = $this->getCompanyItalyAccountant()?->contacts->where('contact_type_id', ContactType::EMAIL)->first();
                if (is_null($email)) {
                    return null;
                }

                return $email->value;
            }
        );
    }

    public function getCompanyItalyAccountantContactMobileValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts mobile'),
            _l('company-mapper.Prints Company accountant contacts mobile'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_CONTACTS,
            function () {
                $mobile = $this->getCompanyItalyAccountant()?->contacts->where('contact_type_id', ContactType::MOBILE)->first();
                if (is_null($mobile)) {
                    return null;
                }

                return $mobile->value;
            }
        );
    }

    public function getCompanyItalyAccountantContactPersonFirstNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts person first name'),
            _l('company-mapper.Prints Company accountant contacts person first name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_CONTACTS,
            function () {
                return $this->getCompanyItalyAccountant()?->contactPerson?->first_name;
            }
        );
    }

    public function getCompanyItalyAccountantContactPersonLastNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts person last name'),
            _l('company-mapper.Prints Company accountant contacts person last name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_CONTACTS,
            function () {
                return $this->getCompanyItalyAccountant()?->contactPerson?->last_name;
            }
        );
    }

    public function getCompanyItalyAccountantContactPersonPositionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant contacts person position'),
            _l('company-mapper.Prints Company accountant contacts person position'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_CONTACTS,
            function () {
                return $this->getCompanyItalyAccountant()?->contactPerson?->position;
            }
        );
    }

}
