<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Address;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\GenderType;
use App\Core\Data\Models\Person;
use App\Core\Mappers\Base\Exceptions\ValueException;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyItalyAccountantLegalRepresentative
{
    public function getCompanyItalyAccountantLegalRepresentative(): ?Person
    {
        return $this->getCompanyItalyAccountant()?->authorisedPerson;
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddress(): ?Address
    {
        return $this->getCompanyItalyAccountantLegalRepresentative()?->address;
    }

    public function getCompanyItalyAccountantLegalRepresentativeFirstNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative first name'),
            _l('company-mapper.Prints germany accountant legal representative first name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $name = $this->getCompanyItalyAccountantLegalRepresentative()?->first_name;
                if (is_null($name)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative first name missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $name;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeLastNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative last name'),
            _l('company-mapper.Prints Italy accountant legal representative last name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $name = $this->getCompanyItalyAccountantLegalRepresentative()?->last_name;
                if (is_null($name)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative last name missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $name;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePositionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative position'),
            _l('company-mapper.Prints Italy accountant legal representative position'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyItalyAccountantLegalRepresentative()?->position;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeNationalityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative nationality'),
            _l('company-mapper.Prints Italy accountant legal representative nationality'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $name = $this->getCompanyItalyAccountantLegalRepresentative()?->nationality;
                if (is_null($name)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative nationality missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $name;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeBirthDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative birth date'),
            _l('company-mapper.Prints Italy accountant legal representative birth date'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $birthdate = $this->getCompanyItalyAccountantLegalRepresentative()?->birth_date;
                if (is_null($birthdate)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative birth date missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return Carbon::parse($birthdate)->format('d.m.Y');
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePlaceOfBirthValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative place of birth'),
            _l('company-mapper.Prints Italy accountant legal representative place of birth'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $placeOfBirth = $this->getCompanyItalyAccountantLegalRepresentative()?->place_of_birth;
                if (is_null($placeOfBirth)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative place of birth missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $placeOfBirth;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeCountryOfBirthValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative country birth'),
            _l('company-mapper.Prints Italy accountant legal representative country birth'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $countryOfBirth = Country::all()
                    ->where('id', $this->getCompanyItalyAccountantLegalRepresentative()?->birth_country_id)
                    ->pluck('name')
                    ->first();
                if (is_null($countryOfBirth)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative country birth missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $countryOfBirth;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeGenderTypeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative Gender Type'),
            _l('company-mapper.Prints Italy accountant legal representative Gender Type'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $genderTypeId = $this->getCompanyItalyAccountantLegalRepresentative()?->gender_type_id;
                if (is_null($genderTypeId)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative country birth missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                $genderType = [
                    GenderType::MALE      => 'M',
                    GenderType::FEMALE    => 'F',
                    GenderType::NONBINARY => 'N'
                ];

                return $genderType[$genderTypeId];
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePassportNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal passport number'),
            _l('company-mapper.Prints Italy accountant legal passport number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyItalyAccountantLegalRepresentative()?->passport_number;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePassportValidFromValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal passport valid from date'),
            _l('company-mapper.Prints Italy accountant legal passport valid from date'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $passportValidFrom = $this->getCompanyItalyAccountantLegalRepresentative()?->passport_valid_from;
                if (is_null($passportValidFrom)) {
                    return $passportValidFrom;

                }

                return Carbon::parse($passportValidFrom)->format('d.m.Y');
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePassportValidUntilValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal passport valid until date'),
            _l('company-mapper.Prints Italy accountant legal passport valid until date'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $passportValidUntil = $this->getCompanyItalyAccountantLegalRepresentative()?->passport_valid_until;
                if (is_null($passportValidUntil)) {
                    return $passportValidUntil;

                }

                return Carbon::parse($passportValidUntil)->format('d.m.Y');
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePassportIssuedValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal passport issued'),
            _l('company-mapper.Prints Italy accountant legal passport issued'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyItalyAccountantLegalRepresentative()?->passport_issued_by;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePassportCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative country passport'),
            _l('company-mapper.Prints Italy accountant legal representative country passport'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $country = Country::all()
                    ->where('id', $this->getCompanyItalyAccountantLegalRepresentative()?->passport_country_id)
                    ->pluck('name')
                    ->first();
                if (is_null($country)) {
                    return null;
                }

                return $country;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative address street'),
            _l('company-mapper.Prints Italy accountant legal representative address street'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyItalyAccountantLegalRepresentativeAddress()?->street;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative address street missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative address house number'),
            _l('company-mapper.Prints Italy accountant legal representative address house number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyItalyAccountantLegalRepresentativeAddress()?->house_number;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative address house number missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative address city'),
            _l('company-mapper.Prints Italy accountant legal representative address city'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyItalyAccountantLegalRepresentativeAddress()?->city?->name;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative address city missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative address postal code'),
            _l('company-mapper.Prints Italy accountant legal representative address postal code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyItalyAccountantLegalRepresentativeAddress()?->postal_code;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative address postal code missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative address country '),
            _l('company-mapper.Prints Italy accountant legal representative address country '),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                $address = $this->getCompanyItalyAccountantLegalRepresentativeAddress()?->country?->name;
                if (is_null($address)) {
                    throw new ValueException(
                        _l('documents.error.Company Italy accountant legal representative address country  missing',
                            ['url' => route('companies.general.legal-representatives.index')]
                        ));
                }

                return $address;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeAddressAdditionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant legal representative address addition'),
            _l('company-mapper.Prints Italy accountant legal representative address addition'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyItalyAccountantLegalRepresentativeAddress()?->addition;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativePeselNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant legal representative PESEL number'),
            _l('company-mapper.Prints Company accountant legal representative PESEL number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyItalyAccountantLegalRepresentative()?->pesel_number;
            }
        );
    }

    public function getCompanyItalyAccountantLegalRepresentativeNipNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant legal representative NIP number'),
            _l('company-mapper.Prints Company legal representative NIP number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_LEGAL_REPRESENTATIVE,
            function () {
                return $this->getCompanyItalyAccountantLegalRepresentative()?->nip_number;
            }
        );
    }
}
