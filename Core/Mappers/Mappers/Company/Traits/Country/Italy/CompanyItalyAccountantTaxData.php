<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Country;
use App\Core\Mappers\Base\Value;

trait CompanyItalyAccountantTaxData
{
    public function getCompanyItalyAccountantTaxNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant tax number'),
            _l('company-mapper.Prints Italy accountant tax number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyItalyAccountant()?->taxNumbers->where('country_id', Country::IT)->first();
                if (is_null($taxNumber)) {
                    return null;
                }

                return $taxNumber->number;
            }
        );
    }

    public function getCompanyItalyAccountantVatNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Italy accountant vat number'),
            _l('company-mapper.Prints Italy accountant vat number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyItalyAccountant()?->vatNumbers->where('country_id', Country::IT)->first();
                if (is_null($vatNumber)) {
                    return null;
                }

                return $vatNumber->vat_number;
            }
        );
    }

}
