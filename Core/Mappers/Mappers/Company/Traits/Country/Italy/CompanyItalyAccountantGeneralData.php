<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\Institution;
use App\Core\Mappers\Base\Value;
use App\Core\Mappers\Base\Exceptions\ValueException;

trait CompanyItalyAccountantGeneralData
{
    public function getCompanyItalyAccountant(): ?Institution
    {
        $companyAccountant = $this->company
            ->companyAccountants
            ->where('accountant.country_id', Country::IT)
            ->first();

        return $companyAccountant
            ?->accountant
            ?->institutionInstitutionType
            ?->institution;
    }

    public function getCompanyItalyAccountantNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant Italy name'),
            _l('company-mapper.Prints Company accountant Italy name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_GENERAL_DATA,
            function () {
                $fullLegalName = $this->getCompanyItalyAccountant()?->full_legal_name;
                if (is_null($fullLegalName)) {
                    throw new ValueException(
                        _l('documents.error.Company accountant Italy name is missing',
                            ['url' => route('companies.general.edit-general')]
                        ));
                }

                return $fullLegalName;
            }
        );
    }

    public function getCompanyItalyAccountantItalyTypeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant Italy legal type'),
            _l('company-mapper.Prints Company accountant Italy legal type'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_GENERAL_DATA,
            function () {
                $legalType = $this->getCompanyItalyAccountant()?->legal_type;
                if (is_null($legalType)) {
                    return null;
                }

                return $legalType;
            }
        );
    }
}
