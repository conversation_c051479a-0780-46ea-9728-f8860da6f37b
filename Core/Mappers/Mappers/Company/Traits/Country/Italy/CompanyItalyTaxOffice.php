<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\TaxOffice;
use App\Core\Mappers\Base\Value;

trait CompanyItalyTaxOffice
{
    private function getOfficeForItaly(): ?TaxOffice
    {
        return $this->getTaxOffice(Country::IT);
    }

    public function getItalyTaxOfficePostalCodeAndCityValue(): Value
    {
        $value = $this->getOfficeForItaly()?->tax_office_postal_city;

        return $this->createValue(
            _l('company-mapper.Tax Office postal city'),
            _l('company-mapper.Prints tax office postal city'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE,
            $value,
            $value
        );
    }

    public function getItalyTaxOfficeAddressValue(): Value
    {
        $taxOfficeAddress = $this->getOfficeForItaly()?->tax_office_address;

        return $this->createValue(
            _l('company-mapper.Tax Office address'),
            _l('company-mapper.Prints tax office address'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE,
            $taxOfficeAddress,
            $taxOfficeAddress,
        );
    }

    public function getItalyTaxOfficeNameValue(): Value
    {
        $taxOffice = $this->getOfficeForItaly()?->tax_office;

        return $this->createValue(
            _l('company-mapper.Tax Office name'),
            _l('company-mapper.Prints tax office name'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE,
            $taxOffice,
            $taxOffice,
        );
    }

    public function getItalyTaxOfficeBankSwiftValue(): Value
    {
        $swift = $this->getOfficeForItaly()?->swift;

        return $this->createValue(
            _l('company-mapper.Tax office SWIFT'),
            _l('company-mapper.Prints bank SWIFT'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE_BANK,
            $swift,
            $swift,
        );
    }

    public function getItalyTaxOfficeBankIbanValue(): Value
    {
        $iban = $this->getOfficeForItaly()?->iban;

        return $this->createValue(
            _l('company-mapper.Tax office IBAN'),
            _l('company-mapper.Prints bank IBAN'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE_BANK,
            $iban,
            $iban,
        );
    }

    public function getItalyTaxOfficeBankNameValue(): Value
    {
        $name = $this->getOfficeForItaly()?->bank;

        return $this->createValue(
            _l('company-mapper.Tax office bank name'),
            _l('company-mapper.Prints bank name'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE_BANK,
            $name,
            $name,
        );
    }

    public function getItalyTaxOfficeBeneficiaryValue(): Value
    {
        $beneficiary = $this->getOfficeForItaly()?->tax_office;

        return $this->createValue(
            _l('company-mapper.Tax office beneficiary'),
            _l('company-mapper.Prints beneficiary name'),
            self::CATEGORY_COMPANY_ITALY_TAX_OFFICE_BANK,
            $beneficiary,
            $beneficiary,
        );
    }
}


