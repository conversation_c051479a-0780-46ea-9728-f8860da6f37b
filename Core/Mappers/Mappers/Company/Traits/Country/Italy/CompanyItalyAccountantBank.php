<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Bank;
use App\Core\Mappers\Base\Value;
trait CompanyItalyAccountantBank
{
    public function getCompanyItalyAccountantBank(): ?Bank
    {
        return $this->getCompanyItalyAccountant()?->bank;
    }

    public function getCompanyItalyAccountantBankNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank name'),
            _l('company-mapper.Prints Company Accountant bank name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyItalyAccountantBank()?->name;
            }
        );
    }

    public function getCompanyItalyAccountantBankBranchNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank branch name'),
            _l('company-mapper.Prints Company Accountant bank branch name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyItalyAccountantBank()?->branch_name;
            }
        );
    }

    public function getCompanyItalyAccountantBankSwiftValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank swift'),
            _l('company-mapper.Prints Company Accountant bank swift'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyItalyAccountantBank()?->swift;
            }
        );
    }

    public function getCompanyItalyAccountantBankIbanValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank iban'),
            _l('company-mapper.Prints Company Accountant bank iban'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyItalyAccountantBank()?->iban;
            }
        );
    }

    public function getCompanyItalyAccountantBankBeneficiaryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Accountant bank beneficiary'),
            _l('company-mapper.Prints Company Accountant bank beneficiary'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BANK,
            function () {
                return $this->getCompanyItalyAccountantBank()?->beneficiary;
            }
        );
    }
}
