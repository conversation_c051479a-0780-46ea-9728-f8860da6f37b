<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Address;
use App\Core\Mappers\Base\Value;
trait CompanyItalyAccountantAddressesMain
{
    public function getCompanyItalyAccountantAddressMain(): ?Address
    {
        return $this->getCompanyItalyAccountant()?->address;
    }

    public function getCompanyItalyAccountantDisplayAddressValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant display address'),
            _l('company-mapper.Prints Company accountant display address'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->display_address;
            }
        );
    }

    public function getCompanyItalyAccountantAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address country'),
            _l('company-mapper.Prints Company accountant address country'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->country?->name;
            }
        );
    }

    public function getCompanyItalyAccountantAddressCountryCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address country code'),
            _l('company-mapper.Prints Company accountant address country code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->country?->code;
            }
        );
    }

    public function getCompanyItalyAccountantAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address street'),
            _l('company-mapper.Prints Company accountant address street'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->street;
            }
        );
    }

    public function getCompanyItalyAccountantAddressStateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address state'),
            _l('company-mapper.Prints Company accountant address state'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->state;
            }
        );
    }

    public function getCompanyItalyAccountantAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address house number'),
            _l('company-mapper.Prints Company accountant address house number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->house_number;
            }
        );
    }

    public function getCompanyItalyAccountantAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address postal code'),
            _l('company-mapper.Prints Company accountant address postal code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->postal_code;
            }
        );
    }

    public function getCompanyItalyAccountantAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant address city'),
            _l('company-mapper.Prints Company accountant address city'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_ADDRESS_MAIN,
            function () {
                return $this->getCompanyItalyAccountantAddressMain()?->city?->name;
            }
        );
    }

}
