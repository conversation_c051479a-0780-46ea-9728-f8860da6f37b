<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\TaxNumber;
use App\Core\Data\Models\VatNumber;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyTaxDataItaly
{
    public function getCompanyVatNumberItaly(): ?VatNumber
    {
        return $this->company?->vatNumbers
            ->where('country_id', Country::IT)
            ->first();
    }

    public function getCompanyTaxNumberItaly(): ?TaxNumber
    {
        return $this->company?->taxNumbers
            ->where('country_id', Country::IT)
            ->whereNull('end_date')
            ->first();
    }

    public function getCompanyVatNumberItalyValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number'),
            _l('company-mapper.Prints Company vat number'),
            self::CATEGORY_COMPANY_ITALY_TAX_DATA,
            function () {
                return $this->getCompanyVatNumberItaly();
            }
        );
    }

    public function getCompanyVatNumberItalyRegisterDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number register date'),
            _l('company-mapper.Prints Company  vat number register date'),
            self::CATEGORY_COMPANY_ITALY_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyVatNumberItaly();
                if (is_null($vatNumber)) {
                    return null;
                }

                return Carbon::parse($vatNumber->register_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyVatNumberItalyEndDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company accountant vat number end date'),
            _l('company-mapper.Prints Company accountant vat number end date'),
            self::CATEGORY_COMPANY_ITALY_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyVatNumberItaly();
                if (is_null($vatNumber)) {
                    return null;
                }

                if (is_null($vatNumber->end_date)) {
                    return null;
                }

                return Carbon::parse($vatNumber->end_date)->format('d.m.Y');
            }
        );
    }

    public function getCompanyTaxNumberItalyValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number'),
            _l('company-mapper.Prints Company tax number'),
            self::CATEGORY_COMPANY_ITALY_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyTaxNumberItaly();
                if (is_null($taxNumber)) {
                    return null;
                }

                return $taxNumber->number;
            }
        );
    }

    public function getCompanyTaxNumberItalyRegisterDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number register date'),
            _l('company-mapper.Prints Company tax number register date'),
            self::CATEGORY_COMPANY_ITALY_TAX_DATA,
            function () {
                $taxNumber = $this->getCompanyTaxNumberItaly();
                if (is_null($taxNumber)) {
                    return null;
                }

                return Carbon::parse($taxNumber->register_date)->format('d.m.Y');
            }
        );
    }
}
