<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Italy;

use App\Core\Data\Models\Address;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\InstitutionBranch;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyItalyAccountantBranches
{
    public function getCompanyItalyAccountantBranches(): ?InstitutionBranch
    {
        return $this->company
            ->companyAccountants
            ->where('accountant.country_id', Country::IT)
            ->first()?->institutionBranch;
    }

    public function getCompanyItalyAccountantBranchesAddress(): ?Address
    {
        if (!is_null($this->getCompanyItalyAccountantBranches())) {
            return $this->getCompanyItalyAccountantBranches()?->address;
        }

        return $this->getCompanyItalyAccountant()?->address;
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPerson(): ?Address
    {
        if (!is_null($this->getCompanyItalyAccountantBranches())) {
            return $this->getCompanyItalyAccountantBranches()?->authorisedPerson?->address;
        }

        return $this->getCompanyItalyAccountant()?->authorisedPerson?->address;
    }

    public function getCompanyItalyAccountantBranchesNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant name'),
            _l('company-mapper.Prints Company assigned accountant name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyItalyAccountantBranches())) {
                    return $this->getCompanyItalyAccountantBranches()?->branch_name;
                }

                return $this->getCompanyItalyAccountant()?->full_legal_name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesShortNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant short name'),
            _l('company-mapper.Prints Company assigned accountant short name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyItalyAccountantBranches())) {
                    return $this->getCompanyItalyAccountantBranches()?->branch_short_name;
                }

                return $this->getCompanyItalyAccountant()?->short_name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonNameValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person first name'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person first name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {

                if (!is_null($this->getCompanyItalyAccountantBranches())) {
                    return $this->getCompanyItalyAccountantBranches()?->authorisedPerson?->first_name;
                }

                return $this->getCompanyItalyAccountant()?->authorisedPerson?->first_name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonNameLastValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person last name'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person last name'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyItalyAccountantBranches())) {
                    return $this->getCompanyItalyAccountantBranches()?->authorisedPerson?->last_name;
                }

                return $this->getCompanyItalyAccountant()?->authorisedPerson?->last_name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonPositionValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person position'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person position'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyItalyAccountantBranches())) {
                    return $this->getCompanyItalyAccountantBranches()?->authorisedPerson?->position;
                }

                return $this->getCompanyItalyAccountant()?->authorisedPerson?->position;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonDateBirthValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant Authorised Person Date of birth'),
            _l('company-mapper.Prints Company assigned accountant Authorised Person Date of birth'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                if (!is_null($this->getCompanyItalyAccountantBranches())) {
                    $birthDate = $this->getCompanyItalyAccountantBranches()?->authorisedPerson?->birth_date;
                } else {
                    $birthDate = $this->getCompanyItalyAccountant()?->authorisedPerson?->birth_date;
                }

                if (is_null($birthDate)) {
                    return $birthDate;
                }

                return Carbon::parse($birthDate)->format('d.m.Y');
            }
        );
    }

    public function getCompanyItalyAccountantBranchesDisplayAddressValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant display address'),
            _l('company-mapper.Prints Company assigned accountant display address'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->display_address;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address country'),
            _l('company-mapper.Prints Company assigned accountant address country'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->country?->name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressCountryCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address country code'),
            _l('company-mapper.Prints Company assigned accountant address country code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->country?->code;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address street'),
            _l('company-mapper.Prints Company assigned accountant address street'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->street;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressStateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address state'),
            _l('company-mapper.Prints Company assigned accountant address state'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->state;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address house number'),
            _l('company-mapper.Prints Company assigned accountant address house number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->house_number;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address postal code'),
            _l('company-mapper.Prints Company assigned accountant address postal code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->postal_code;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant address city'),
            _l('company-mapper.Prints Company assigned accountant address city'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAddress()?->city?->name;
            }
        );
    }


    public function getCompanyItalyAccountantBranchesAuthorisedPersonDisplayAddressValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person display address'),
            _l('company-mapper.Prints Company assigned accountant authorised person display address'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->display_address;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressCountryValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address country'),
            _l('company-mapper.Prints Company assigned accountant authorised person address country'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->country?->name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressCountryCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address country code'),
            _l('company-mapper.Prints Company assigned accountant authorised person address country code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->country?->code;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressStreetValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address street'),
            _l('company-mapper.Prints Company assigned accountant authorised person address street'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->street;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressStateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address state'),
            _l('company-mapper.Prints Company assigned accountant authorised person address state'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->state;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressHouseNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address house number'),
            _l('company-mapper.Prints Company assigned accountant authorised person address house number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->house_number;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressPostalCodeValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address postal code'),
            _l('company-mapper.Prints Company assigned accountant authorised person address postal code'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->postal_code;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonAddressCityValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person address city'),
            _l('company-mapper.Prints Company assigned accountant authorised person address city'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountantBranchesAuthorisedPerson()?->city?->name;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonPeselNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person PESEL number'),
            _l('company-mapper.Prints Company assigned accountant authorised person PESEL number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountant()?->authorisedPerson?->pesel_number;
            }
        );
    }

    public function getCompanyItalyAccountantBranchesAuthorisedPersonNipNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company assigned accountant authorised person NIP number'),
            _l('company-mapper.Prints Company assigned accountant authorised person NIP number'),
            self::CATEGORY_COMPANY_ITALY_ACCOUNTANT_BRANCHES,
            function () {
                return $this->getCompanyItalyAccountant()?->authorisedPerson?->nip_number;
            }
        );
    }

}
