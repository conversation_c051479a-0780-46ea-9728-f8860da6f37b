<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Poland;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\TaxNumber;
use App\Core\Data\Models\VatNumber;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyTaxDataPoland
{
    public function getCompanyVatNumberPl(): ?VatNumber
    {
        return $this->company->vatNumbers
            ->where('country_id', Country::PL)
            ->first();
    }

    public function getCompanyTaxNumberPl(): ?TaxNumber
    {
        return $this->company->taxNumbers
            ->where('country_id', Country::PL)
            ->whereNull('end_date')
            ->first();
    }

    public function getCompanyVatNumberPlValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number'),
            _l('company-mapper.Prints Company vat number'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                return $this->getCompanyVatNumberPl()?->vat_number;
            },
            function () {
                return $this->getCompanyVatNumberPl()?->vat_number;
            },
        );
    }

    public function getCompanyVatNumberPlRegisterDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number register date'),
            _l('company-mapper.Prints Company  vat number register date'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                $vatNumber = $this->getCompanyVatNumberPl();
                if (is_null($vatNumber)) {
                    return null;
                }

                return Carbon::parse($vatNumber->register_date)->format('d.m.Y');
            },
            function () {
                $vatNumber = $this->getCompanyVatNumberPl();
                if (is_null($vatNumber)) {
                    return null;
                }

                return Carbon::parse($vatNumber->register_date)->format('d.m.Y');
            },
        );
    }

    public function getCompanyVatNumberPlEndDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company vat number end date'),
            _l('company-mapper.Prints Company vat number end date'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                $endDate = $this->getCompanyVatNumberPl()?->end_date;
                if (is_null($endDate)) {
                    return null;
                }

                return Carbon::parse($endDate)->format('d.m.Y');
            },
            function () {
                $endDate = $this->getCompanyVatNumberPl()?->end_date;
                if (is_null($endDate)) {
                    return null;
                }

                return Carbon::parse($endDate)->format('d.m.Y');
            },
        );
    }

    public function getCompanyTaxNumberPlValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number'),
            _l('company-mapper.Prints Company tax number'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                return $this->getCompanyTaxNumberPl()?->number;
            },
            function () {
                return $this->getCompanyTaxNumberPl()?->number;
            }
        );
    }

    public function getCompanyTaxNumberPlRegisterDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number register date'),
            _l('company-mapper.Prints Company tax number register date'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                $registerDate = $this->getCompanyTaxNumberPl()?->register_date;
                if (is_null($registerDate)) {
                    return null;
                }

                return Carbon::parse($registerDate)->format('d.m.Y');
            },
            function () {
                $registerDate = $this->getCompanyTaxNumberPl()?->register_date;
                if (is_null($registerDate)) {
                    return null;
                }

                return Carbon::parse($registerDate)->format('d.m.Y');
            },
        );
    }

    public function getCompanyTaxNumberPlEndDateValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company tax number end date'),
            _l('company-mapper.Prints Company tax number end date'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                $endDate = $this->getCompanyTaxNumberPl()?->end_date;
                if (is_null($endDate)) {
                    return null;
                }

                return Carbon::parse($endDate)->format('d.m.Y');
            },
            function () {
                $endDate = $this->getCompanyTaxNumberPl()?->end_date;
                if (is_null($endDate)) {
                    return null;
                }

                return Carbon::parse($endDate)->format('d.m.Y');
            },
        );
    }

    public function getCompanyKrsNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company krs number'),
            _l('company-mapper.Prints Company krs number'),
            self::CATEGORY_COMPANY_POLAND_TAX_DATA,
            function () {
                return $this->company->krs_number;

            },
            function () {
                return $this->company->krs_number;

            },
        );
    }
}
