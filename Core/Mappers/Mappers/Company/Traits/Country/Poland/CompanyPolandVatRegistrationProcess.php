<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Poland;

use App\Core\Data\Models\CompanyCountrySale;
use App\Core\Data\Models\Country;
use App\Core\Mappers\Base\Value;
use Carbon\Carbon;

trait CompanyPolandVatRegistrationProcess
{
    public function getCompanyPolandVatRegistrationProcess(): ?CompanyCountrySale
    {
        return $this->company->countrySales
            ->where('country_id', Country::PL)
            ->first();
    }

    public function getCompanyPolandVatRegistrationProcessDateFirstSaleValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company date of first sale'),
            _l('company-mapper.Prints Company date of first sale'),
            self::CATEGORY_COMPANY_POLAND_VAT_REGISTRATION_GENERAL,
            function () {
                $dateFirstSale = $this->getCompanyPolandVatRegistrationProcess()?->first_sale_date;
                if (is_null($dateFirstSale)) {
                    return null;
                }

                return Carbon::parse($dateFirstSale)->format('d.m.Y');
            },
            function () {
                $dateFirstSale = $this->getCompanyPolandVatRegistrationProcess()?->first_sale_date;
                if (is_null($dateFirstSale)) {
                    return null;
                }

                return Carbon::parse($dateFirstSale)->format('d.m.Y');
            },
        );
    }

    public function getCompanyPolandVatRegistrationProcessEstimateSaleAmountCurrentYearValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Estimate sale amount in the current year'),
            _l('company-mapper.Prints Company Estimate sale amount in the current year'),
            self::CATEGORY_COMPANY_POLAND_VAT_REGISTRATION_GENERAL,
            function () {
                return $this->getCompanyPolandVatRegistrationProcess()?->current_year_sale_estimate;
            },
            function () {
                return $this->getCompanyPolandVatRegistrationProcess()?->current_year_sale_estimate;
            },
        );
    }

    public function getCompanyPolandVatRegistrationProcessEstimateSaleAmountNextYearValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Estimate sale amount in the next year'),
            _l('company-mapper.Prints Company Estimate sale amount in the next year'),
            self::CATEGORY_COMPANY_POLAND_VAT_REGISTRATION_GENERAL,
            function () {
                return $this->getCompanyPolandVatRegistrationProcess()?->next_year_sale_estimate;
            },
            function () {
                return $this->getCompanyPolandVatRegistrationProcess()?->next_year_sale_estimate;
            },
        );
    }
}
