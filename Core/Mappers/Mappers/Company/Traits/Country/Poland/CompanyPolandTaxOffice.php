<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Poland;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\TaxOffice;
use App\Core\Mappers\Base\Value;

trait CompanyPolandTaxOffice
{
    private function getOfficeForPoland(): ?TaxOffice
    {
        return $this->getTaxOffice(Country::PL);
    }

    public function getPolandTaxOfficePostalCodeAndCityValue(): Value
    {
        $value = $this->getOfficeForPoland()?->tax_office_postal_city;

        return $this->createValue(
            _l('company-mapper.Tax Office postal city'),
            _l('company-mapper.Prints tax office postal city'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE,
            $value,
            $value
        );
    }

    public function getPolandTaxOfficeAddressValue(): Value
    {
        $taxOfficeAddress = $this->getOfficeForPoland()?->tax_office_address;

        return $this->createValue(
            _l('company-mapper.Tax Office address'),
            _l('company-mapper.Prints tax office address'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE,
            $taxOfficeAddress,
            $taxOfficeAddress,
        );
    }

    public function getPolandTaxOfficeNameValue(): Value
    {
        $taxOffice = $this->getOfficeForPoland()?->tax_office;

        return $this->createValue(
            _l('company-mapper.Tax Office name'),
            _l('company-mapper.Prints tax office name'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE,
            $taxOffice,
            $taxOffice,
        );
    }

    public function getPolandTaxOfficeBankSwiftValue(): Value
    {
        $swift = $this->getOfficeForPoland()?->swift;

        return $this->createValue(
            _l('company-mapper.Tax office SWIFT'),
            _l('company-mapper.Prints bank SWIFT'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE_BANK,
            $swift,
            $swift,
        );
    }

    public function getPolandTaxOfficeBankIbanValue(): Value
    {
        $iban = $this->getOfficeForPoland()?->iban;

        return $this->createValue(
            _l('company-mapper.Tax office IBAN'),
            _l('company-mapper.Prints bank IBAN'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE_BANK,
            $iban,
            $iban,
        );
    }

    public function getPolandTaxOfficeBankNameValue(): Value
    {
        $name = $this->getOfficeForPoland()?->bank;

        return $this->createValue(
            _l('company-mapper.Tax office bank name'),
            _l('company-mapper.Prints bank name'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE_BANK,
            $name,
            $name,
        );
    }

    public function getPolandTaxOfficeBeneficiaryValue(): Value
    {
        $beneficiary = $this->getOfficeForPoland()?->tax_office;

        return $this->createValue(
            _l('company-mapper.Tax office beneficiary'),
            _l('company-mapper.Prints beneficiary name'),
            self::CATEGORY_COMPANY_POLAND_TAX_OFFICE_BANK,
            $beneficiary,
            $beneficiary,
        );
    }
}

