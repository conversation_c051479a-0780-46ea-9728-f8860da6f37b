<?php

namespace App\Core\Mappers\Mappers\Company\Traits\Country\Poland;

use App\Core\Data\Models\Country;
use App\Core\Mappers\Base\Value;

trait CompanyPolandAccountantTaxData
{
    public function getCompanyPolandAccountantTaxNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Poland accountant tax number'),
            _l('company-mapper.Prints Poland accountant tax number'),
            self::CATEGORY_COMPANY_POLAND_ACCOUNTANT_TAX_DATA,
            function () {
                return $this->getCompanyPolandAccountant()
                    ?->taxNumbers
                    ->where('country_id', Country::PL)
                    ->first()
                    ?->number;
            },
            function () {
                return $this->getCompanyPolandAccountant()
                    ?->taxNumbers
                    ->where('country_id', Country::PL)
                    ->first()
                    ?->number;
            },
        );
    }

    public function getCompanyPolandAccountantVatNumberValue(): Value
    {
        return $this->createValue(
            _l('company-mapper.Company Poland accountant vat number'),
            _l('company-mapper.Prints Poland accountant vat number'),
            self::CATEGORY_COMPANY_POLAND_ACCOUNTANT_TAX_DATA,
            function () {
                return $this->getCompanyPolandAccountant()
                    ?->vatNumbers
                    ->where('country_id', Country::PL)
                    ->first()
                    ?->vat_number;
            },
            function () {
                return $this->getCompanyPolandAccountant()
                    ?->vatNumbers
                    ->where('country_id', Country::PL)
                    ->first()
                    ?->vat_number;
            },
        );
    }
}
