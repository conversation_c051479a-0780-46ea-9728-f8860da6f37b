<?php

namespace App\Core\Companies;

use App\Core\Addresses\Contracts\AddressServiceContract;
use App\Core\Banks\Contracts\BankServiceContract;
use App\Core\Common\History\History;
use App\Core\Common\Store\Store;
use App\Core\Companies\Contracts\CompanyServiceContract;
use App\Core\Companies\Contracts\RegistrationServiceContract;
use App\Core\Companies\Registration\DataTransfer\RegistrationTransferObject;
use App\Core\Companies\Registration\Registration;
use App\Core\CompanyCountrySales\Contracts\CompanyCountrySaleServiceContract;
use App\Core\Contacts\Contracts\ContactServiceContract;
use App\Core\Data\Models\Address;
use App\Core\Data\Models\BusinessType;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\CompanyStatus;
use App\Core\Data\Models\ContactType;
use App\Core\Data\Models\Document;
use App\Core\Data\Models\DocumentCategory;
use App\Core\Data\Models\File;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Models\Platform;
use App\Core\Data\Models\RegistrationProcess;
use App\Core\Data\Models\RegistrationProcessType;
use App\Core\Data\Models\RegistrationStatus;
use App\Core\Data\Models\RegistrationWarehouseUsage;
use App\Core\Data\Models\RegistrationWarehouseUsageType;
use App\Core\Data\Models\Role;
use App\Core\Data\Models\Store as StoreModel;
use App\Core\Data\Models\User;
use App\Core\Data\Models\Warehouse;
use App\Core\Data\Repositories\Contracts\BusinessTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentCategoryRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentRepositoryContract;
use App\Core\Data\Repositories\Contracts\LegalRepresentativeRepositoryContract;
use App\Core\Data\Repositories\Contracts\MarketplacesRepositoryContract;
use App\Core\Data\Repositories\Contracts\ProductTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use App\Core\Data\Repositories\GenderTypeRepository;
use App\Core\Data\Repositories\IossNumberRepository;
use App\Core\Data\Repositories\OssNumberRepository;
use App\Core\File\Contracts\FileServiceContract;
use App\Core\IossNumbers\Contracts\IossNumberServiceContract;
use App\Core\OssNumbers\Contracts\OssNumberServiceContract;
use App\Core\People\Contracts\PersonServiceContract;
use App\Core\RegistrationProcess\Contracts\RegistrationProcessServiceContract;
use App\Core\SalesChannels\Contracts\SalesChannelServiceContract;
use App\Core\Shareholders\Contracts\ShareholderServiceContract;
use App\Core\System\Notifications\Contracts\NotificationsServiceContract;
use App\Core\VatNumbers\Contracts\VatNumberServiceContract;
use App\Core\Warehouses\Contracts\WarehouseServiceContract;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Fluent;
use Throwable;

class RegistrationService implements RegistrationServiceContract
{
    const COMPANY_REGISTRATION_KEY = 'company-registration';
    const COMPANY_VAT_REGISTRATION_KEY = 'company-vat-registration';

    private CountryRepositoryContract $countryRepo;
    private BusinessTypeRepositoryContract $businessTypeRepo;
    private CurrencyRepositoryContract $currencyRepo;
    private MarketplacesRepositoryContract $marketplacesRepo;
    private ProductTypeRepositoryContract $productTypeRepo;
    private OssNumberRepository $ossNumberRepo;
    private IossNumberRepository $iossNumberRepo;
    private GenderTypeRepository $genderTypeRepo;
    private CompanyRepositoryContract $companyRepo;
    private CompanyServiceContract $companyService;
    private AddressServiceContract $addressService;
    private PersonServiceContract $personService;
    private ContactServiceContract $contactService;
    private RegistrationProcessServiceContract $registrationProcessService;
    private OssNumberServiceContract $ossNumberService;
    private IossNumberServiceContract $iossNumberService;
    private VatNumberServiceContract $vatNumberService;
    private ShareholderServiceContract $shareholderService;
    private BankServiceContract $bankService;
    private SalesChannelServiceContract $salesChannelService;
    private WarehouseServiceContract $warehouseService;
    private CompanyCountrySaleServiceContract $companyCountrySaleService;
    private FileServiceContract $fileService;
    private DocumentRepositoryContract $documentRepo;
    private LegalRepresentativeRepositoryContract $legalRepresentativeRepo;
    private UserRepositoryContract $userRepo;
    private NotificationsServiceContract $notificationsService;
    private DocumentCategoryRepositoryContract $documentCategoryRepo;

    public function __construct(
        CountryRepositoryContract $countryRepo,
        BusinessTypeRepositoryContract $businessTypeRepo,
        CurrencyRepositoryContract $currencyRepo,
        MarketplacesRepositoryContract $marketplacesRepo,
        ProductTypeRepositoryContract $productTypeRepo,
        OssNumberRepository $ossNumberRepo,
        IossNumberRepository $iossNumberRepo,
        GenderTypeRepository $genderTypeRepo,
        CompanyRepositoryContract $companyRepo,
        CompanyServiceContract $companyService,
        AddressServiceContract $addressService,
        PersonServiceContract $personService,
        ContactServiceContract $contactService,
        RegistrationProcessServiceContract $registrationProcessService,
        OssNumberServiceContract $ossNumberService,
        IossNumberServiceContract $iossNumberService,
        VatNumberServiceContract $vatNumberService,
        ShareholderServiceContract $shareholderService,
        BankServiceContract $bankService,
        SalesChannelServiceContract $salesChannelService,
        WarehouseServiceContract $warehouseService,
        CompanyCountrySaleServiceContract $companyCountrySaleService,
        FileServiceContract $fileService,
        DocumentRepositoryContract $documentRepo,
        LegalRepresentativeRepositoryContract $legalRepresentativeRepo,
        UserRepositoryContract $userRepo,
        NotificationsServiceContract $notificationsService,
        DocumentCategoryRepositoryContract $documentCategoryRepo
    ) {
        $this->countryRepo = $countryRepo;
        $this->businessTypeRepo = $businessTypeRepo;
        $this->currencyRepo = $currencyRepo;
        $this->marketplacesRepo = $marketplacesRepo;
        $this->productTypeRepo = $productTypeRepo;
        $this->ossNumberRepo = $ossNumberRepo;
        $this->iossNumberRepo = $iossNumberRepo;
        $this->genderTypeRepo = $genderTypeRepo;
        $this->companyRepo = $companyRepo;
        $this->companyService = $companyService;
        $this->addressService = $addressService;
        $this->personService = $personService;
        $this->contactService = $contactService;
        $this->registrationProcessService = $registrationProcessService;
        $this->ossNumberService = $ossNumberService;
        $this->iossNumberService = $iossNumberService;
        $this->vatNumberService = $vatNumberService;
        $this->shareholderService = $shareholderService;
        $this->bankService = $bankService;
        $this->salesChannelService = $salesChannelService;
        $this->warehouseService = $warehouseService;
        $this->companyCountrySaleService = $companyCountrySaleService;
        $this->fileService = $fileService;
        $this->documentRepo = $documentRepo;
        $this->legalRepresentativeRepo = $legalRepresentativeRepo;
        $this->userRepo = $userRepo;
        $this->notificationsService = $notificationsService;
        $this->documentCategoryRepo = $documentCategoryRepo;
    }

    public function getRegistrationDocuments(
        int $companyCountryId,
        array $vatRegistrationCountriesIds,
        array $platformsIds
    ): Collection {
        $vatRegistrationCountriesIds = array_map(fn(int $vatRegistrationCountryId) => $vatRegistrationCountryId, $vatRegistrationCountriesIds);
        $platformsIds = array_map(fn(int $platformId) => $platformId, $platformsIds);

        $platformsDocumentCategories = $this->documentCategoryRepo
            ->getAllDocumentCategoriesForPlatforms($platformsIds, DocumentCategory::CATEGORY_INCORPORATION_AND_LEGAL);
        $countriesDocumentCategories = $this->documentCategoryRepo
            ->getAllDocumentCategoriesForCompanyCountryByCountries(
                $companyCountryId,
                $vatRegistrationCountriesIds,
                DocumentCategory::CATEGORY_INCORPORATION_AND_LEGAL
            );

        return $countriesDocumentCategories->merge($platformsDocumentCategories)
            ->groupBy(function (DocumentCategory $documentCategory) {
                return $documentCategory->platform_id ?? 0;
            });
    }

    /**
     * @inheritdoc
     */
    public function collectDataForRegistration(User $user, ?Company $existingCompany = null): RegistrationTransferObject
    {
        $key = self::COMPANY_REGISTRATION_KEY;
        if (!is_null($existingCompany)) {
            $key = self::COMPANY_VAT_REGISTRATION_KEY;
            // This is hack because of #1308
            $key = $key . '-' . $existingCompany->id;
        }

        $initialRegistrationData
            = $registration
            = $this->resolveInitialRegistrationData($user, $existingCompany)
            ->toArray();

        $autoSave = Store::getFirstForUserByKey($user->id, $key);
        if (!is_null($autoSave)) {
            $registration = $autoSave->data;
        }

        $data = $this->collectCommonDataForRegistration($user, $existingCompany);
        $data->setRegistrationData($registration);

        $data->setInitialRegistrationData($initialRegistrationData);

        return $data;
    }

    /**
     * @param User $user
     * @param Company|null $existingCompany
     * @return RegistrationTransferObject
     * @noinspection PhpUnusedParameterInspection
     */
    private function collectCommonDataForRegistration(User $user, ?Company $existingCompany = null): RegistrationTransferObject
    {
        $countries = $this->countryRepo->getAllCountries()->load('currency');
        $businessTypes = $this->businessTypeRepo->getAllBusinessTypes();
        $currencies = $this->currencyRepo->getAllCurrencies();
        $platforms = $this->marketplacesRepo->getAllPlatforms()->filter(function (Platform $platform) {
            $skipPlatforms = [
                Platform::PLATFORM_ALL,
                Platform::PLATFORM_SHOPIFY,
            ];

            return !in_array($platform->id, $skipPlatforms);
        });
        $genderTypes = $this->genderTypeRepo->getAllGenderTypes();
        $productTypes = $this->productTypeRepo->getAllProductTypes();
        $allOssNumberTypes = $this->ossNumberRepo->getAllOssNumberTypes();
        $allIossNumberTypes = $this->iossNumberRepo->getAllIossNumberTypes();
        $emptyOssNumberModel = $this->ossNumberRepo->getEmptyOssNumberModel();
        $emptyIossNumberModel = $this->iossNumberRepo->getEmptyIossNumberModel();
        $registrationCountries = $this->getAvailableRegistrationCountriesIds($existingCompany);

        return (new RegistrationTransferObject())
            ->setCountries($countries)
            ->setBusinessTypes($businessTypes)
            ->setCurrencies($currencies)
            ->setPlatforms($platforms)
            ->setGenderTypes($genderTypes)
            ->setProductTypes($productTypes)
            ->setAllOssNumberTypes($allOssNumberTypes)
            ->setAllIossNumberTypes($allIossNumberTypes)
            ->setRegistrationCountries($registrationCountries)
            ->setEmptyOssNumberModel($emptyOssNumberModel)
            ->setEmptyIossNumberModel($emptyIossNumberModel);
    }

    /**
     * @inheritDoc
     */
    public function getAvailableRegistrationCountriesIds(?Company $company = null): Collection
    {
        $registrationCountriesIds = RegistrationProcess::REGISTRATION_COUNTRIES_IDS;
        if (!is_null($company)) {
            $registeredCountriesIds = $company
                ->load('registrationProcesses.country')
                ->registrationProcesses
                ->filter(function (RegistrationProcess $registrationProcess) {
                    return $registrationProcess->registration_type_id === RegistrationProcessType::VAT_REGISTRATION;
                })
                ->keyBy('country_id')
                ->keys()
                ->toArray();

            $registrationCountriesIds = array_filter($registrationCountriesIds, function (int $countryId) use ($registeredCountriesIds) {
                return !in_array($countryId, $registeredCountriesIds);
            });

        }

        return $this->countryRepo->getAllCountriesByIds($registrationCountriesIds);
    }

    private function resolveInitialRegistrationData(User $user, ?Company $existingCompany = null): Registration
    {
        return new Registration($user, $existingCompany);
    }

    /**
     * @throws Throwable
     */
    public function completeRegistration(User $user, string $key, ?int $partnerId = null): Company
    {
        if (is_null($partnerId)) {
            $partnerId = InstitutionInstitutionType::PULLUS_PARTNER_ID;
        }
//        DB::beginTransaction();
        $store = Store::getFirstForUserByKey($user->id, $key);
        $data = $this->fluentizeData($store->data);
        $company = $this->tryToGetCompany($data);
        $isNewCompany = is_null($company);

        try {
            // Store company data if new company
            if (is_null($company)) {
                $companyAddress = $this->storeCompanyAddress($data);

                $company = $this->storeCompany(
                    $data,
                    $partnerId,
                    $companyAddress->id
                );
                $this->storeCompanyContact($company->id, $data);
                $this->storeLegalRepresentative($company, $data);
                $this->resolveOssAndIossNumbers($company, $data);
                $this->resolveVatNumbers($company, $data);
                $this->resolveShareholders($company, $data);
                $this->resolveBankDetails($company, $data);
                $this->resolveSalesChannels($company, $data);
                $this->resolveProductTypes($company, $data);
                $this->resolveWarehousesAndWarehouseUsages($company, $data);
                $this->resolveBindingUserToCompany($company, $user);
            }

            $this->resolveRegistrationCountries($company, $data);
            $this->resolveSales($company, $data);
            $this->resolveDocuments($company, $user, $store, $data);
            $this->resolveHistoryAndActivity($company);

            $store->delete();
            $this->clearUserDataIfNeeded($user);

//            DB::commit();
        } catch (Throwable $exception) {
//            DB::rollBack();
            $companyId = $company?->id ?? null;
            if ($isNewCompany && !is_null($companyId)) {
                DB::statement('delete from companies where id = ' . $companyId . ';');
            }

            throw $exception;
        }

        return $company;
    }

    private function clearUserDataIfNeeded(User $user): void
    {
        $preferences = $user->preferences ?? [];
        $externallyAddedSalesChannel = $preferences['registrationState'] ?? null;
        if (!is_null($externallyAddedSalesChannel)) {
            unset($preferences['registrationState']);
        }

        $user->preferences = $preferences;

        $user->save();
    }

    /**
     * @param RegistrationProcess $registrationProcess
     * @return void
     */
    private function sendRegistrationNotifications(RegistrationProcess $registrationProcess): void
    {
        //send notification to all partner users
        $company = $registrationProcess->company->load('partner.institution');
        if (!is_null($company->partner_id)) {
            $institution = $company->partner->institution;
            $usersIds = $this->userRepo->getAllUsersForInstitution($company->partner_id)
                ->pluck('id')
                ->toArray();

            $msgParams = [
                'type'              => _l($registrationProcess->registrationProcessType->registration_type),
                'registration-link' => route('registration-process.show', ['id' => $registrationProcess->id, 'countryCode' => $registrationProcess->country->code]), //route registration proces/id
                'company'           => $registrationProcess->company->full_legal_name,
                'country'           => $registrationProcess->country->name,
                'institution'       => $institution->full_legal_name,
            ];
            $message = _l('registration-process.start-notification', $msgParams);

            $this->notificationsService->successNotificationForUsers($usersIds, $message);
        }
    }

    private function resolveHistoryAndActivity(Company $company): void
    {
        $company = $company->load(
            'bank',
            'address',
            'contacts',
            'onlineShops',
            'salesChannels',
            'vatNumbers',
            'shareholders.address',
            'productTypes',
            'warehouseUsages',
            'countrySales'
        );

        History::created('history.Company created', $company);
    }

    private function resolveBindingUserToCompany(Company $company, User $user): void
    {
        if ($user->isClientType()) {
            $role = [
                'company_id' => $company->id,
                'role_id'    => Role::ROLE_CLIENT_ADMIN,
                'user_id'    => $user->id
            ];
            $this->companyRepo->insertCompanyUserRoles($role);
        }
    }

    private function resolveDocuments(Company $company, User $user, StoreModel $store, Fluent $data): void
    {
        $documentCategories = $this->collectDocumentCategoriesWithFiles($store, $data);
        $date = Carbon::now();
        foreach ($documentCategories as $documentCategory) {
            $files = $documentCategory->files;
            foreach ($files as $file) {
                /**
                 * @var File $file
                 */
                $document = $this->createDocumentModel(
                    $company,
                    $user,
                    $documentCategory->id,
                    $date
                );
                $newFile = $this->fileService->storeFileToDatabase(
                    $file->name,
                    $file->mime,
                    $file->extension,
                    $file->size,
                    $document
                );
                $this->fileService->moveFileToFilesystem($file->file_path, $newFile->file_path);
            }
        }
    }

    private function createDocumentModel(Company $company, User $user, int $documentCategoryId, Carbon $date): Document
    {
        $document = $this->documentRepo->getEmptyDocumentModel();
        $document->document_category_id = $documentCategoryId;
        $document->company_id = $company->id;
        $document->update_user_id = $user->id;
        $document->updated_at = $date->toDateTimeString();
        $document->date = $date->toDateString();
        $document->save();

        return $document;
    }

    private function collectDocumentCategoriesWithFiles(StoreModel $store, Fluent $data): Collection
    {
        $files = $store->files->keyBy('id');

        return collect($data->requiredDocuments->toArray())
            ->pluck('documentCategories')
            ->map(function (Fluent $documentCategories) {
                return $documentCategories->toArray();
            })
            ->flatten()
            ->filter(function (Fluent $documentCategory) {
                return count($documentCategory->files->toArray()) > 0;
            })
            ->map(function (Fluent $documentCategory) use ($files) {
                $categoryFiles = $documentCategory->files->toArray();
                $newFiles = [];
                foreach ($categoryFiles as $categoryFile) {
                    $fileData = $files->get($categoryFile->id);
                    if (!is_null($fileData)) {
                        $newFiles[] = $fileData;
                    }
                }

                $newFiles = collect($newFiles);
                $documentCategory->files = $newFiles;

                return $documentCategory;
            });
    }

    private function resolveSales(Company $company, Fluent $data): void
    {
        $salesCountries = $data->main?->selectedCountries?->toArray() ?? [];
        foreach ($salesCountries as $salesCountry) {
            $this->companyCountrySaleService
                ->storeUpdateDeleteCompanyCountrySale(
                    $company->id,
                    $salesCountry->id,
                    $salesCountry->dateOfFirstSale,
                    $salesCountry->estimatedSalesInCurrentYear,
                    $salesCountry->estimatedSalesInNextYear,
                    null,
                );
        }
    }

    private function resolveWarehousesAndWarehouseUsages(Company $company, Fluent $data): void
    {
        $marketplacesAndShops = $data->platformsAndSales->marketplacesAndShops;

        if (!$marketplacesAndShops->sellGoods) {
            $warehouseTypeId = $this->getWarehouseTypeId($data);
            $ownerAmazon = RegistrationWarehouseUsage::OWNER_AMAZON;

            // If Europe Multi-Country Inventory OR European Fulfilment Network
            $euTypes = [
                RegistrationWarehouseUsageType::MCI,
                RegistrationWarehouseUsageType::EFN,
            ];
            if (in_array($warehouseTypeId, $euTypes)) {
                $selectedCountries = $marketplacesAndShops
                    ->selectedCountries
                    ->toArray();
                foreach ($selectedCountries as $country) {
                    $this->warehouseService
                        ->storeWarehouseUsage(
                            $company->id,
                            $ownerAmazon,
                            $warehouseTypeId,
                            $country->id
                        );
                }
            }

            // If NOT Europe there is NO countries
            if ($warehouseTypeId === RegistrationWarehouseUsageType::PAN) {
                $this->warehouseService
                    ->storeWarehouseUsage(
                        $company->id,
                        $ownerAmazon,
                        $warehouseTypeId
                    );
            }
        }

        // Custom warehouses
        if ($marketplacesAndShops->other) {
            $customWarehouses = $marketplacesAndShops->warehouses?->toArray() ?? [];
            foreach ($customWarehouses as $warehouse) {
                $this->warehouseService
                    ->storeUpdateCompanyWarehouse(
                        $company->id,
                        $warehouse->uid,
                        $warehouse->name,
                        $warehouse->otherWarehouseOperatorName,
                        $warehouse->firstTimeUsed,
                        $warehouse->country->id,
                        $warehouse->postalCode,
                        Warehouse::SOURCE_USER,
                        null,
                        $warehouse->street,
                        $warehouse->streetNo,
                        $warehouse->city,
                        $warehouse->county,
                        $warehouse->state,
                    );

                $warehouseAddress = $this->addressService
                    ->storeAddress(
                        $warehouse->street,
                        $warehouse->city,
                        $warehouse->postalCode,
                        $warehouse->country->id,
                        $warehouse->streetNo,
                        $warehouse->state
                    );
                $this->warehouseService
                    ->storeWarehouseUsage(
                        $company->id,
                        $warehouse->name,
                        RegistrationWarehouseUsageType::OTHER,
                        $warehouseAddress->country_id,
                        $warehouseAddress->id,
                    );
            }
        }
    }

    private function resolveProductTypes(Company $company, Fluent $data): void
    {
        $productTypes = $data->platformsAndSales
            ->marketplacesAndShops
            ->selectedServices
            ->toArray();
        $productTypesIds = collect($productTypes)
            ->pluck('id')
            ->toArray();

        $this->companyService
            ->storeDeleteCompanyProductTypes(
                $company->id,
                $productTypesIds
            );
    }

    /**
     * @throws Exception
     */
    private function resolveSalesChannels(Company $company, Fluent $data): void
    {
        $salesChannels = $data->platformsAndSales
            ->marketplacesAndShops
            ->salesChannels
            ->toArray();
        foreach ($salesChannels as $salesChannel) {
            $id = $salesChannel->id;
            if ($id === 'none') {
                $id = null;
            }
            $this->salesChannelService
                ->storeUpdateSalesChannel(
                    companyId: $company->id,
                    name: $salesChannel->accountName,
                    platformId: $salesChannel->platform->id,
                    uaid: $salesChannel->uaid,
                    url: $salesChannel->url,
                    id: $id,
                    currencyId: $salesChannel->currency?->id,
                    platformRegionId: $salesChannel->platformRegion?->id,
                    shopifyAccessToken: $salesChannel->shopifyAccessToken
                );
        }
    }

    private function resolveBankDetails(Company $company, Fluent $data): void
    {
        $bankData = $data->bankDetails;
        if ($bankData->bankDetailsSkipped) {
            return;
        }

        $this->bankService
            ->storeUpdateBank(
                $bankData->name,
                $bankData->beneficiary,
                $bankData->swiftOrBic,
                $bankData->ibanOrAccountNumber,
                null,
                null,
                null,
                $company->id,
                null,
                $bankData->ibanValid,
                $bankData->ibanValidated,
                $bankData->branch
            );
    }

    private function resolveShareholders(Company $company, Fluent $data): void
    {
        $shareholders = $data->shareholdersAuthorisedPersonData;
        $businessTypeId = $data->general->basic->businessType->id;
        $skipBusinessTypes = [
            BusinessType::SOLE_TRADER,
            BusinessType::FREELANCER,
            BusinessType::PARTNERSHIP,
            BusinessType::CHARITY,
        ];
        $skipped = $shareholders->shareholdersSkipped;
        if (in_array($businessTypeId, $skipBusinessTypes) || $skipped) {
            return;
        }

        $shareholders = $shareholders?->shareholders?->toArray() ?? [];
        foreach ($shareholders as $shareholder) {
            $isPerson = !$shareholder->isCompany;
            $shareholder = $isPerson ? $shareholder->person : $shareholder->company;

            $shareholderAddress = $this->storeShareholderAddress($shareholder);
            $this->storeShareholder(
                $shareholder,
                $company,
                $shareholderAddress,
                $isPerson
            );
        }
    }

    private function storeShareholder(Fluent $shareholder, Company $company, Address $shareholderAddress, bool $isPerson): void
    {
        $birthday = $shareholder->incorporationDate;
        $birthCountryId = $shareholder->country->id;
        $documentIsIdCard = null;
        $passportNumber = null;
        $passportValidFrom = null;
        $passportValidUntil = null;
        $passportCountryId = null;
        $passportIssuedBy = null;
        if ($isPerson) {
            $birthday = $shareholder->birthday;
            $birthCountryId = $shareholder->birthCountry->id;
            $documentIsIdCard = $shareholder->identificationDocument === 'idCard';
            $document = $documentIsIdCard ? $shareholder->idCard : $shareholder->passport;

            $passportNumber = $document->number;
            $passportValidFrom = $document->validFrom;
            $passportValidUntil = $document->validUntil;
            $passportCountryId = $document->country->id;
            $passportIssuedBy = $document->issuedBy;
        }

        $this->shareholderService
            ->storeUpdateDeleteShareholder(
                $isPerson,
                false,
                $shareholder->shareOfCapital,
                $shareholderAddress->id,
                $company->id,
                false,
                null,
                $shareholder->name,
                $shareholder->lastName,
                $shareholder->name,
                $birthday,
                $birthCountryId,
                $shareholder->placeOfBirth,
                $passportNumber,
                $passportValidFrom,
                $passportValidUntil,
                $passportCountryId,
                $passportIssuedBy,
                $shareholder->nationality,
                $shareholder->gender?->id,
                $documentIsIdCard
            );
    }

    private function storeShareholderAddress(Fluent $shareholder): Address
    {
        return $this->addressService
            ->storeAddress(
                $shareholder->address,
                $shareholder->city,
                $shareholder->zipPostalCode,
                $shareholder->country->id,
                $shareholder->houseNumber,
                $shareholder->stateProvince,
                $shareholder->additionToAddress,
            );
    }

    private function resolveRegistrationCountries(Company $company, Fluent $data): void
    {
        $countries = $data->main?->selectedCountries?->toArray() ?? [];
        foreach ($countries as $country) {
            $process = $this->registrationProcessService
                ->resolveRegistrationProcess(
                    $company->id,
                    $country->id
                );

            $this->sendRegistrationNotifications($process);
        }
    }

    private function resolveVatNumbers(Company $company, Fluent $data): void
    {
        $vatNumbers = $data->main?->vatNumbers?->toArray() ?? [];
        foreach ($vatNumbers as $vatNumber) {
            $number = $vatNumber->vatNumber ?? $vatNumber->number;
            $countryId = $vatNumber->country->id;
            $vatRegistrationDate = $vatNumber->vatRegistrationDate;
            $valid = $vatNumber->validated && $vatNumber->valid;
            $isFromApi = $vatNumber->api;
            if ($isFromApi) {
                $valid = $vatNumber->status === 'active';
            }
            $status = RegistrationStatus::NEW;
            if ($isFromApi && $valid) {
                $status = RegistrationStatus::REGISTERED;
            }

            $this->registrationProcessService
                ->resolveRegistrationProcess(
                    $company->id,
                    $countryId,
                    $status
                );

            $this->vatNumberService->storeUpdateDeleteVatNumber(
                $company->id,
                $countryId,
                $number,
                $vatRegistrationDate,
            );
        }
    }

    private function resolveOssAndIossNumbers(Company $company, Fluent $data): void
    {
        $main = $data->main;
        $ossNumber = $main->ossNumber;
        $ossRegistration = $main->ossRegistration;
        $ossRegistrationCountry = $main->ossRegistrationSelectedCountry;
        $this->resolveOssNumber(
            $company->id,
            $ossRegistration,
            $ossNumber,
            $ossRegistrationCountry
        );

        $iossNumber = $main->iossNumber;
        $iossRegistration = $main->iossRegistration;
        $iossRegistrationCountry = $main->iossRegistrationSelectedCountry;
        $this->resolveIossNumber(
            $company->id,
            $iossRegistration,
            $iossNumber,
            $iossRegistrationCountry
        );
    }

    private function resolveOssNumber(
        int $companyId,
        bool $wantsToRegister,
        ?Fluent $ossNumber = null,
        ?Fluent $ossRegistrationCountry = null
    ): void {
        if ($wantsToRegister && !is_null($ossRegistrationCountry)) {
            $process = $this->registrationProcessService
                ->resolveRegistrationProcess(
                    $companyId,
                    $ossRegistrationCountry->id,
                    RegistrationStatus::NEW,
                    RegistrationProcessType::OSS_NUMBER_REGISTRATION
                );
            $this->sendRegistrationNotifications($process);

            return;
        }

        if (!is_null($ossNumber)) {
            $number = $ossNumber->fullNumber;
            if (is_null($number)) {
                $number = ($ossNumber->type->code ?? '') . $ossNumber->numberWithoutCode;
            }

            $this->ossNumberService
                ->storeUpdateDeleteOssNumber(
                    $companyId,
                    $ossNumber->issueCountry->id,
                    $ossNumber->type->id,
                    $number,
                    $ossNumber->registerDate,
                    false,
                    $ossNumber->endDate,
                    $ossNumber->id
                );

            $this->registrationProcessService
                ->resolveRegistrationProcess(
                    $companyId,
                    $ossNumber->issueCountry->id,
                    RegistrationStatus::REGISTERED,
                    RegistrationProcessType::OSS_NUMBER_REGISTRATION,
                    $ossNumber->type->id,
                );
        }
    }

    private function resolveIossNumber(
        int $companyId,
        bool $wantsToRegister,
        ?Fluent $iossNumber = null,
        ?Fluent $iossRegistrationCountry = null
    ): void {
        if ($wantsToRegister && !is_null($iossRegistrationCountry)) {
            $process = $this->registrationProcessService
                ->resolveRegistrationProcess(
                    $companyId,
                    $iossRegistrationCountry->id,
                    RegistrationStatus::NEW,
                    RegistrationProcessType::IOSS_NUMBER_REGISTRATION
                );

            $this->sendRegistrationNotifications($process);

            return;
        }

        if (!is_null($iossNumber)) {
            $number = $iossNumber->fullNumber;
            if (is_null($number)) {
                $number = ($iossNumber->type->code ?? '') . $iossNumber->numberWithoutCode;
            }

            $this->iossNumberService
                ->storeUpdateDeleteIossNumber(
                    $companyId,
                    $iossNumber->issueCountry->id,
                    $iossNumber->type->id,
                    $number,
                    $iossNumber->registerDate,
                    false,
                    $iossNumber->endDate,
                    $iossNumber->id
                );

            $this->registrationProcessService
                ->resolveRegistrationProcess(
                    $companyId,
                    $iossNumber->issueCountry->id,
                    RegistrationStatus::REGISTERED,
                    RegistrationProcessType::IOSS_NUMBER_REGISTRATION,
                    $iossNumber->type->id,
                );
        }
    }

    private function storeCompanyContact(int $companyId, Fluent $data): void
    {
        $contact = $data->general->contact;

        $this->contactService
            ->storeContact(
                value: $contact->contactEmail,
                contactTypeId: ContactType::EMAIL,
                companyId: $companyId,
            );
    }

    private function storeLegalRepresentative(Company $company, Fluent $data): void
    {
        $shareholdersAuthorisedPersonData = $data->shareholdersAuthorisedPersonData;
        if ($shareholdersAuthorisedPersonData->authorisedPersonSkipped) {
            return;
        }

        $person = $shareholdersAuthorisedPersonData->authorisedPerson;
        $authorizedPersonAddress = $this->storeAuthorizedPersonAddress($data);
        $idDocument = $person->idCard;
        $isIdCard = true;
        if ($person->identificationDocument === 'passport') {
            $idDocument = $person->passport;
            $isIdCard = false;
        }

        $person = $this->personService->storeUpdatePerson(
            $person->name,
            $person->lastName,
            _l('common.Authorised person'),
            $person->birthday,
            $authorizedPersonAddress->id,
            null,
            null,
            $person->birthCountry->id,
            $person->placeOfBirth,
            $idDocument->number,
            $idDocument->validFrom,
            $idDocument->validUntil,
            $idDocument->country->id,
            $idDocument->issuedBy,
            $person->nationality,
            $person->gender->id,
            $isIdCard,
        );

        $legalRepresentative = $this->legalRepresentativeRepo->getEmptyLegalRepresentativeModel();

        $legalRepresentative->company_id = $company->id;
        $legalRepresentative->person_id = $person->id;

        $legalRepresentative->save();
    }

    private function storeAuthorizedPersonAddress(Fluent $data): Address
    {
        $person = $data->shareholdersAuthorisedPersonData->authorisedPerson;

        return $this->addressService
            ->storeAddress(
                $person->address,
                $person->city,
                $person->zipPostalCode,
                $person->birthCountry->id,
                $person->houseNumber,
                $person->stateProvince,
                $person->additionToAddress,
            );
    }

    private function storeCompanyAddress(Fluent $data): Address
    {
        $contact = $data->general->contact;
        $main = $data->main;

        return $this->addressService
            ->storeAddress(
                $contact->companyAddress,
                $contact->city,
                $contact->zipPostalCode,
                $main->country->id,
                $contact->houseNumber,
                $contact->stateProvince,
                $contact->additionToAddress,
            );
    }

    private function storeCompany(Fluent $data, int $partnerId, int $addressId): Company
    {
        $basic = $data->general->basic;
        $marketplacesAndShops = $data->platformsAndSales->marketplacesAndShops;
        $sales = $data->platformsAndSales->sales;

        $goodsImportedFrom = collect($sales->goodsImportedFrom->toArray())
            ->keyBy('id')
            ->keys()
            ->toArray();
        if (count($goodsImportedFrom) < 1) {
            $goodsImportedFrom = null;
        }

        $warehouseTypeId = $this->getWarehouseTypeId($data);
        $goodsImportDescription = '';
        if (!is_null($warehouseTypeId)) {
            $goodsImportDescription = $this->companyService->getGoodsImportDescription($warehouseTypeId);
        }

        return $this->companyService
            ->createCompany(
                $basic->fullLegalName,
                $basic->legalEntityType,
                $basic->businessActivity,
                $addressId,
                $partnerId,
                $basic->partnerCode,
                $basic->registrationNumber,
                $basic->incorporationDate,
                $basic->valueOfShareCapital,
                $basic->currency?->id,
                $marketplacesAndShops->provideServices,
                $marketplacesAndShops->sellGoods,
                CompanyStatus::ACTIVE,
                $basic->businessType->id,
                $marketplacesAndShops->goodsStoredIn !== 'noStoreEu',
                $sales->areManufacturers,
                $sales->haveGoodsSupplier,
                $goodsImportDescription,
                $goodsImportedFrom,
            );
    }

    private function getWarehouseTypeId(Fluent $data): ?int
    {
        $goodsStoredIn = $data->platformsAndSales->marketplacesAndShops->goodsStoredIn;
        $goodsStoredInMap = [
            'noStoreEu'   => null,
            'amazonEFN'   => RegistrationWarehouseUsageType::EFN,
            'amazonMCI'   => RegistrationWarehouseUsageType::MCI,
            'amazonPanEU' => RegistrationWarehouseUsageType::PAN,
        ];

        return $goodsStoredInMap[$goodsStoredIn] ?? null;
    }

    /**
     * @throws Exception
     */
    private function tryToGetCompany(Fluent $data): ?Company
    {
        $companyId = $data->helperData->companyId;
        if (!is_null($companyId)) {
            $company = $this->companyRepo->getCompanyById($companyId);
            if (is_null($company)) {
                throw new Exception('Company ID given but no company found');
            }

            return $company;
        }

        return null;
    }

    private function fluentizeData(array $data): Fluent
    {
        $chunk = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $part = $this->fluentizeData($value);
            } else {
                $part = $value;
            }
            $chunk[$key] = $part;
        }

        return new Fluent($chunk);
    }
}
