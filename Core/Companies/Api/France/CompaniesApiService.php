<?php

namespace App\Core\Companies\Api\France;

use App\Core\Companies\Api\France\Contracts\CompaniesApiServiceContract;
use App\Core\Companies\Api\France\Responses\CompanyResponse;
use GuzzleHttp\Client;
use Throwable;

class CompaniesApiService implements CompaniesApiServiceContract
{
    /**
     * @var bool
     */
    private $isTest = false;

    /**
     * @var string|null
     */
    private $token = null;

    /**
     * @inheritDoc
     */
    public function getCompanyDataBySiret(string $siret): ?CompanyResponse
    {
        $data = [
            'uniteLegale'   => null,
            'etablissement' => null,
        ];
        $siret = $this->cleanSiret($siret);
        $siren = $this->extractSiren($siret);

        $sirenData = $this->getUniteLegaleData($siren);
        $siretData = $this->getEtablissementData($siret);

        if (!is_null($sirenData)) {
            $data['uniteLegale'] = $sirenData['uniteLegale'] ?? null;
        }
        if (!is_null($siretData)) {
            $data['etablissement'] = $siretData['etablissement'] ?? null;
        }

        if (is_null($sirenData) || is_null($siretData)) {
            return null;
        }

        return new CompanyResponse($data);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyDataBySiren(string $siren): ?CompanyResponse
    {
        $data = [
            'uniteLegale' => null,
        ];

        $sirenData = $this->getUniteLegaleData($siren);

        if (!is_null($sirenData)) {
            $data['uniteLegale'] = $sirenData['uniteLegale'] ?? null;
        }

        if (is_null($sirenData)) {
            return null;
        }

        return new CompanyResponse($data);
    }

    /**
     * @inheritDoc
     */
    public function getUniteLegaleData(string $siren): ?array
    {
        return $this->makeInseeRequest('siren/' . $siren);
    }

    /**
     * @inheritDoc
     */
    public function getEtablissementData(string $siret): ?array
    {
        return $this->makeInseeRequest('siret/' . $siret);
    }

    /**
     * @param string $endpoint
     * @param bool $repeat - used only in recursive call
     * @return array|null
     */
    private function makeInseeRequest(string $endpoint, bool $repeat = false): ?array
    {
        $baseUrl = $this->getInseeApiBaseUrl();
        $options = [
            'base_uri' => $baseUrl,
            'headers'  => $this->getHeaders()
        ];
        $client = new Client($options);

        try {
            $response = $client->request('GET', $this->buildUrl($endpoint));
        } catch (Throwable $exception) {
            $code = $exception->getCode() ?? 0;
            if ($code !== 401 || $repeat) {
                return null;
            }

            $this->token = null;

            return $this->makeInseeRequest($endpoint, true);
        }

        try {
            $data = $response->getBody()->getContents();
            $data = json_decode($data, true);
        } catch (Throwable ) {
            $data = null;
        }

        if (is_null($data)) {
            return null;
        }

        return $data;
    }

    /**
     * @return string
     */
    private function getInseeApiBaseUrl(): string
    {
        return config('evat.france.insee.apiEndpointUrl');
    }

    /**
     * @param string $apiEndpoint
     * @return string
     */
    private function buildUrl(string $apiEndpoint): string
    {
        $apiEndpoint = trim($apiEndpoint, '/');
        $suffix = '';
        if (!$this->isTest) {
            $suffix = 'V3/';
        }

        return '/entreprises/sirene/' . $suffix . $apiEndpoint;
    }

    /**
     * @param string $siret
     * @return string
     */
    private function cleanSiret(string $siret): string
    {
        return preg_replace('/\s+/', '', $siret);
    }

    /**
     * @return array
     */
    private function getHeaders(): array
    {
        return [
            'Authorization' => $this->getToken()
        ];
    }

    private function getToken(): string
    {
        if ($this->isTest) {
            return 'Bearer ' . config('evat.france.insee.testToken');
        }
        if (!is_null($this->token)) {
            return $this->token;
        }

        $consumerKey = config('evat.france.insee.consumerKey');
        $consumerSecret = config('evat.france.insee.consumerSecret');
        $token = $consumerKey . ':' . $consumerSecret;
        $token = base64_encode($token);

        $options = [
            'base_uri' => $this->getInseeApiBaseUrl(),
            'headers'  => [
                'Authorization' => 'Basic ' . $token
            ]
        ];

        $client = new Client($options);
        $response = $client->request(
            'POST',
            'token',
            [
                'form_params' => [
                    'grant_type' => 'client_credentials'
                ]
            ]
        );

        $data = $response->getBody()->getContents();
        $data = json_decode($data, true);

        $token = $data['token_type'] . ' ' . $data['access_token'];
        $this->token = $token;

        return $token;
    }

    /**
     * @param string $siret
     * @return string
     */
    private function extractSiren(string $siret): string
    {
        return substr($siret, 0, 9);
    }
}
