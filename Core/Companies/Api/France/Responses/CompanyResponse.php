<?php

namespace App\Core\Companies\Api\France\Responses;

use App\Core\Data\Models\BusinessActivity;
use App\Core\Data\Models\City;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\InseeTerritoryCode;
use App\Core\Data\Models\LegalEntityType;
use App\Core\Data\Repositories\Contracts\CityRepositoryContract;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Database\Query\Builder;

class CompanyResponse implements Arrayable, Jsonable
{
    private array $rawData;
    private Builder|LegalEntityType $legalEntityTypeModel;
    private Builder|BusinessActivity $businessActivityModel;
    private Builder|Country $countryModel;
    private Builder|InseeTerritoryCode $inseeCountryModel;
    private CityRepositoryContract $cityRepo;

    public function __construct(array $rawData)
    {
        $this->rawData = $rawData;
        $this->legalEntityTypeModel = app()->make(LegalEntityType::class);
        $this->businessActivityModel = app()->make(BusinessActivity::class);
        $this->countryModel = app()->make(Country::class);
        $this->inseeCountryModel = app()->make(InseeTerritoryCode::class);
        $this->cityRepo = app()->make(CityRepositoryContract::class);
    }

    public function getNic(): ?string
    {
        return $this->getRawData()['etablissement']['nic'] ?? null;
    }

    public function getBusinessActivities(): array
    {
        $activities = $this->getRawData()['uniteLegale']['periodesUniteLegale'] ?? [];
        if (count($activities) < 1) {
            return [];
        }

        $rawActivities = [];
        $queryCodes = [];
        foreach ($activities as $activity) {
            $date = $activity['dateDebut'];
            $code = $activity['activitePrincipaleUniteLegale'];
            if (is_null($code) || in_array($code, $rawActivities)) {
                continue;
            }
            $qCodes = $this->resolveBusinessActivitiesCodes($code);
            foreach ($qCodes as $qCode) {
                $queryCodes[] = $qCode;
            }

            $rawActivities[$date] = [
                'baseCode' => $code,
                'altCodes' => $qCodes,
            ];
        }

        $businessActivitiesDb = $this->businessActivityModel
            ->whereIn('code', $queryCodes)
            ->get()
            ->load('country')
            ->keyBy('code');

        $data = [];
        foreach ($rawActivities as $date => $activity) {
            $dbActivity = $businessActivitiesDb->get($activity['baseCode']);
            if (is_null($dbActivity)) {
                $alts = $activity['altCodes'];
                foreach ($alts as $alt) {
                    $dbActivity = $businessActivitiesDb->get($alt);
                    if (!is_null($dbActivity)) {
                        break;
                    }
                }
            }
            if (is_null($dbActivity)) {
                continue;
            }
            $dbActivity = $dbActivity->toArray();
            $dbActivity['date_from_active'] = $date;
            unset($dbActivity['country_id']);
            $data[] = $dbActivity;
        }

        return $data;
    }

    private function resolveBusinessActivitiesCodes(string $code): array
    {
        $data = [];
        $code = explode('.', $code);
        $first = $code[0];
        $data[] = $first;
        if (count($code) < 2) {
            return $data;
        }
        $last = $code[1];
        $cnt = mb_strlen($last);
        for ($i = 1; $i < $cnt; $i++) {
            $data[] = $first . '.' . substr($last, 0, $i);
        }
        $data[] = $first . '.' . $last;

        $data = collect($data)
            ->sortByDesc(function ($item) {
                return $item;
            })
            ->toArray();

        return $data;
    }

    public function getLegalEntityTypes(): array
    {
        $types = $this->getRawData()['uniteLegale']['periodesUniteLegale'] ?? [];
        if (count($types) < 1) {
            return [];
        }

        $rawTypes = [];
        foreach ($types as $type) {
            $date = $type['dateDebut'];
            $code = $type['categorieJuridiqueUniteLegale'];
            if (is_null($code) || in_array($code, $rawTypes)) {
                continue;
            }
            $rawTypes[$date] = $code;
        }

        $legalTypesDb = $this->legalEntityTypeModel
            ->whereIn('code', $rawTypes)
            ->get()
            ->load('country')
            ->keyBy('code');

        $data = [];
        foreach ($rawTypes as $date => $type) {
            $dbType = $legalTypesDb->get($type);
            if (is_null($dbType)) {
                continue;
            }
            $dbType = $dbType->toArray();
            $dbType['date_from_active'] = $date;
            unset($dbType['country_id']);
            $data[] = $dbType;
        }

        return $data;
    }

    public function getIncorporationDate(): ?Carbon
    {
        $date = $this->getRawData()['uniteLegale']['dateCreationUniteLegale'] ?? null;
        if (is_null($date)) {
            return null;
        }

        return Carbon::parse($date);
    }

    public function getSiren(): ?string
    {
        return $this->getRawData()['etablissement']['siren'] ?? null;
    }

    public function getFullLegalName(): ?string
    {
        return $this->getRawData()['etablissement']['uniteLegale']['denominationUniteLegale'] ?? null;
    }

    public function getContactInformationAddress(): ?string
    {
        return $this->getRawData()['etablissement']['adresseEtablissement']['complementAdresseEtablissement'] ?? null;
    }

    public function getContactInformationHouse(): ?string
    {
        return $this->getRawData()['etablissement']['adresseEtablissement']['numeroVoieEtablissement'] ?? null;
    }

    public function getContactInformationStreet(): ?string
    {
        return $this->getRawData()['etablissement']['adresseEtablissement']['libelleVoieEtablissement'] ?? null;
    }

    public function getContactInformationPostalCode(): ?string
    {
        return $this->getRawData()['etablissement']['adresseEtablissement']['codePostalEtablissement'] ?? null;
    }

    public function getContactInformationCity(): ?City
    {
        $country = $this->getContactInformationCountry();
        if (is_null($country)) {
            return null;
        }

        $addressData = $this->getRawData()['etablissement']['adresseEtablissement'] ?? null;
        if (is_null($addressData)) {
            return null;
        }

        $cityString = $addressData['libelleCommuneEtablissement'] ?? null;
        if ($country->id !== $country::FR) {
            $cityString = $addressData['libelleCommuneEtablissement'] ?? null;
        }
        if (is_null($cityString)) {
            return null;
        }

        $cityString = preg_replace('/\d+/u', '', $cityString);
        $cityString = trim($cityString);
        $cityString = mb_strtoupper($cityString);

        return $this->cityRepo->createCityIfNotExists($cityString, $country->id);
    }

    /**
     * Country can be null if given insee code but not in db
     *
     * @return Country|null
     */
    public function getContactInformationCountry(): ?Country
    {
        $inseeCountry = $this->getRawData()['etablissement']['adresseEtablissement']['libellePaysEtrangerEtablissement'] ?? null;
        if (!is_null($inseeCountry)) {
            $inseeCountry = $this->inseeCountryModel
                ->where('code', $inseeCountry)
                ->first();

            if (is_null($inseeCountry)) {
                return null;
            }
            $inseeCountry = $inseeCountry->country;
        }
        if (is_null($inseeCountry)) {
            $inseeCountry = $this->countryModel->find($this->countryModel::FR);
        }

        return $inseeCountry;
    }

    public function getRawData()
    {
        return $this->rawData;
    }

    public function toArray()
    {
        $dateCreationUniteLegale = $this->getIncorporationDate();
        if (!is_null($dateCreationUniteLegale)) {
            $dateCreationUniteLegale = $dateCreationUniteLegale->toDateString();
        }

        $contactInformationCountry = $this->getContactInformationCountry();
        if (!is_null($contactInformationCountry)) {
            $contactInformationCountry = $contactInformationCountry->toArray();
        }

        $contactInformationCity = $this->getContactInformationCity();
        if (!is_null($contactInformationCity)) {
            $contactInformationCity = $contactInformationCity->toArray();
        }

        return [
            // unite legale
            'siren'                        => $this->getSiren(),
            'nic'                          => $this->getNic(),
            'incorporationDate'            => $dateCreationUniteLegale,
            'fullLegalName'                => $this->getFullLegalName(),
            'legalEntityTypes'             => $this->getLegalEntityTypes(),
            'businessActivity'             => $this->getBusinessActivities(),

            // etablissement
            'contactInformationAddress'    => $this->getContactInformationAddress(),
            'contactInformationHouse'      => $this->getContactInformationHouse(),
            'contactInformationStreet'     => $this->getContactInformationStreet(),
            'contactInformationPostalCode' => $this->getContactInformationPostalCode(),
            'contactInformationCity'       => $contactInformationCity,
            'contactInformationCountry'    => $contactInformationCountry,
        ];
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
}
