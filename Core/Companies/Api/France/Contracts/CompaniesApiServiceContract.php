<?php

namespace App\Core\Companies\Api\France\Contracts;

use App\Core\Companies\Api\France\Responses\CompanyResponse;

interface CompaniesApiServiceContract
{
    /**
     * @param string $siret
     * @return CompanyResponse|null
     */
    public function getCompanyDataBySiret(string $siret): ?CompanyResponse;

    /**
     * @param string $siren
     * @return CompanyResponse|null
     */
    public function getCompanyDataBySiren(string $siren): ?CompanyResponse;

    /**
     * @param string $siren
     * @return array|null
     */
    public function getUniteLegaleData(string $siren): ?array;

    /**
     * @param string $siret
     * @return array|null
     */
    public function getEtablissementData(string $siret): ?array;
}