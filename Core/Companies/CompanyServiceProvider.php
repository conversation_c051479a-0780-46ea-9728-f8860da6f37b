<?php

namespace App\Core\Companies;

use App\Core\Companies\Api\France\CompaniesApiService;
use App\Core\Companies\Api\France\Contracts\CompaniesApiServiceContract;
use App\Core\Companies\Contracts\CompanyServiceContract;
use App\Core\Companies\Contracts\RegistrationServiceContract;
use App\Core\System\Providers\CoreModuleServiceProvider;

class CompanyServiceProvider extends CoreModuleServiceProvider
{
    protected function resolve(): void
    {
        $this->bind(CompanyServiceContract::class, CompanyService::class);
        $this->bind(RegistrationServiceContract::class, RegistrationService::class);

        // Api
        $this->bind(CompaniesApiServiceContract::class, CompaniesApiService::class);
    }
}
