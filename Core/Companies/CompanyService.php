<?php

namespace App\Core\Companies;

use App\Core\Addresses\Contracts\AddressServiceContract;
use App\Core\Common\History\History;
use App\Core\Companies\Contracts\CompanyServiceContract;
use App\Core\Companies\DataTransfer\CreateCompanyApiRequest;
use App\Core\Contacts\Contracts\ContactServiceContract;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\CompanyAccountant;
use App\Core\Data\Models\CompanyStatus;
use App\Core\Data\Models\CompanyTaxPeriodType;
use App\Core\Data\Models\Contact;
use App\Core\Data\Models\ContactType;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\ImportGoodsReverseDocument;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Models\InstitutionType;
use App\Core\Data\Models\InstitutionTypeCountryPartner;
use App\Core\Data\Models\RegistrationWarehouseUsageType;
use App\Core\Data\Models\TaxPeriodType;
use App\Core\Data\Models\User;
use App\Core\Data\Repositories\Contracts\ApiRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyAccountantRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyContactPersonRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\FileRepositoryContract;
use App\Core\Data\Repositories\Contracts\LegalRepresentativeRepositoryContract;
use App\Core\Data\Repositories\Contracts\RegistrationSetupRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use App\Core\File\Contracts\FileServiceContract;
use App\Core\IossNumbers\Contracts\IossNumberServiceContract;
use App\Core\OssNumbers\Contracts\OssNumberServiceContract;
use App\Core\System\Exceptions\PartnerCompanyCountryDevException;
use App\Core\TaxNumbers\Contracts\TaxNumberServiceContract;
use App\Core\TaxOffices\Contracts\TaxOfficeServiceContract;
use App\Core\VatNumbers\Contracts\VatNumberServiceContract;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Throwable;

class CompanyService implements CompanyServiceContract
{
    private CompanyRepositoryContract $companyRepo;
    private RegistrationSetupRepositoryContract $registrationSetupRepo;
    private CompanyAccountantRepositoryContract $companyAccountantRepo;
    private CountryRepositoryContract $countryRepo;
    private UserRepositoryContract $userRepo;
    private FileServiceContract $fileService;
    private FileRepositoryContract $fileRepo;
    private TaxOfficeServiceContract $taxOfficeService;
    private AddressServiceContract $addressService;
    private CompanyContactPersonRepositoryContract $companyContactPersonRepo;
    private LegalRepresentativeRepositoryContract $legalRepresentativeRepo;
    private ContactServiceContract $contactService;
    private VatNumberServiceContract $vatNumberService;
    private ApiRepositoryContract $apiRepo;
    private TaxNumberServiceContract $taxNumberService;
    private OssNumberServiceContract $ossNumberService;
    private IossNumberServiceContract $iossNumberService;

    public function __construct(
        CompanyRepositoryContract $companyRepo,
        RegistrationSetupRepositoryContract $registrationSetupRepo,
        CompanyAccountantRepositoryContract $companyAccountantRepo,
        CountryRepositoryContract $countryRepo,
        UserRepositoryContract $userRepo,
        FileServiceContract $fileService,
        FileRepositoryContract $fileRepo,
        TaxOfficeServiceContract $taxOfficeService,
        AddressServiceContract $addressService,
        CompanyContactPersonRepositoryContract $companyContactPersonRepo,
        LegalRepresentativeRepositoryContract $legalRepresentativeRepo,
        ContactServiceContract $contactService,
        VatNumberServiceContract $vatNumberService,
        TaxNumberServiceContract $taxNumberService,
        ApiRepositoryContract $apiRepo,
        OssNumberServiceContract $ossNumberService,
        IossNumberServiceContract $iossNumberService
    ) {
        $this->companyRepo = $companyRepo;
        $this->registrationSetupRepo = $registrationSetupRepo;
        $this->companyAccountantRepo = $companyAccountantRepo;
        $this->countryRepo = $countryRepo;
        $this->userRepo = $userRepo;
        $this->fileService = $fileService;
        $this->fileRepo = $fileRepo;
        $this->taxOfficeService = $taxOfficeService;
        $this->addressService = $addressService;
        $this->companyContactPersonRepo = $companyContactPersonRepo;
        $this->legalRepresentativeRepo = $legalRepresentativeRepo;
        $this->contactService = $contactService;
        $this->vatNumberService = $vatNumberService;
        $this->apiRepo = $apiRepo;
        $this->taxNumberService = $taxNumberService;
        $this->ossNumberService = $ossNumberService;
        $this->iossNumberService = $iossNumberService;
    }

    public function updateGeneralData(
        int $companyId,
        string $fullLegalName,
        string $incorporationDate,
        string $businessActivity,
        int $businessTypeId,
        ?float $shareCapital,
        ?int $shareCapitalCurrencyId,
        ?string $partnerCode,
        ?string $legalType = null,
        ?string $registrationNumber = null,
        ?int $partnerId = null,
        ?string $tradeName = null,
        ?string $registrationNumberIssuer = null,
        ?string $krsNumber = null,
        ?string $companyDescription = null,
        ?int $lawGoverningCountryId = null,
        ?int $billingAddressId = null,
        ?int $auditingAddressId = null,
        ?string $web = null,
        ?string $placeOfCommercialRegistry = null,
        ?string $nationalEuIdentificationNumber = null,
        ?int $addressId = null,
        ?string $phoneNumber = null,
        ?string $email = null,
        ?string $faxNumber = null
    ): Company {
        if ($companyId < 0) {
            $company = $this->companyRepo->getEmptyCompanyModel();
            $historyMessage = 'history.Company created';
        } else {
            $company = $this->companyRepo->getCompanyById($companyId)->load('address');
            $historyMessage = 'history.General data edited';
        }

        $incorporationDate = Carbon::parse($incorporationDate)->toDateString();

        $oldCompanyBillingAddressId = $company->billing_address_id;
        $oldCompanyAuditingAddressId = $company->auditing_address_id;

        $company->full_legal_name = $fullLegalName;
        $company->legal_type = $legalType ?? null;
        $company->partner_code = $partnerCode;
        $company->trade_name = $tradeName;
        $company->registration_number = $registrationNumber;
        $company->registration_number_issuer = $registrationNumberIssuer;
        $company->krs_number = $krsNumber;
        $company->incorporation_date = $incorporationDate;
        $company->share_capital = $shareCapital;
        $company->share_capital_currency_id = $shareCapitalCurrencyId;
        $company->business_activity = $businessActivity;
        $company->business_type_id = $businessTypeId;
        $company->company_description = $companyDescription;
        $company->national_law_governing_country_id = $lawGoverningCountryId;
        $company->billing_address_id = $billingAddressId;
        $company->auditing_address_id = $auditingAddressId;
        $company->web = $web;
        $company->place_of_commercial_registry = $placeOfCommercialRegistry;
        $company->national_eu_identification_number = $nationalEuIdentificationNumber;
        $this->updateCompanyContact($company, ContactType::EMAIL, $email);
        $this->updateCompanyContact($company, ContactType::PHONE, $phoneNumber);
        $this->updateCompanyContact($company, ContactType::FAX, $faxNumber);

        if (!is_null($addressId)) {
            $company->address_id = $addressId;
        }
        if (is_null($company->legal_type)) {
            $company->legal_type = 'N/A';
        }

        if (is_null($company->partner_id) && is_null($partnerId)) {
            $company->partner_id = config('evat.register.partnerId');
        } elseif (!is_null($partnerId)) {
            $company->partner_id = $partnerId;
        }

        if (is_null($company->status_id)) {
            $company->status_id = CompanyStatus::ACTIVE;
        }

        $company->save();

        if (is_null($billingAddressId) && !is_null($oldCompanyBillingAddressId)) {
            $this->addressService->deleteAddressById($oldCompanyBillingAddressId);
        }
        if (is_null($auditingAddressId) && !is_null($oldCompanyAuditingAddressId)) {
            $this->addressService->deleteAddressById($oldCompanyAuditingAddressId);
        }

        History::updated($historyMessage, $company);

        return $company;
    }

    public function updateEoriNumber(int $companyId, ?string $eoriNumber): Company
    {
        $company = $this->companyRepo->getCompanyById($companyId);

        $company->eori_number = $eoriNumber;

        $company->save();

        History::updated('history.EORI number updated', $company);

        return $company;
    }

    public function updateUkEoriNumber(int $companyId, ?string $ukEoriNumber): Company
    {
        $company = $this->companyRepo->getCompanyById($companyId);
        $company->uk_eori_number = $ukEoriNumber;
        $company->save();

        History::updated('history.UK EORI number updated', $company);

        return $company;
    }

    public function createCompany(
        string $fullLegalName,
        string $legalType,
        string $businessActivity,
        int $addressId,
        int $partnerId,
        ?string $partnerCode = null,
        ?string $registrationNumber = null,
        ?string $incorporationDate = null,
        ?float $shareCapital = null,
        ?int $shareCapitalCurrencyId = null,
        ?bool $providingServices = null,
        ?bool $sellingGoods = null,
        ?int $statusId = null,
        ?int $businessTypeId = null,
        ?bool $usingEuWarehouses = null,
        ?bool $areManufacturers = null,
        ?bool $haveGoodsSupplier = null,
        ?string $goodsImportDescription = null,
        ?array $goodsImportCountryIds = null,
        ?string $tradeName = null,
    ): Company {
        if (!is_null($partnerCode)) {
            $partnerCode = preg_replace('/\s+/', '', $partnerCode);
        }
        $incorporationDate = Carbon::parse($incorporationDate)->toDateString();

        $company = $this->companyRepo->getEmptyCompanyModel();

        $company->full_legal_name = trim($fullLegalName);
        $company->legal_type = $legalType;
        $company->business_activity = $businessActivity;
        $company->address_id = $addressId;
        $company->partner_id = $partnerId;

        $company->registration_number = $registrationNumber;
        $company->partner_code = $partnerCode;
        $company->incorporation_date = $incorporationDate;
        $company->share_capital = $shareCapital;
        $company->share_capital_currency_id = $shareCapitalCurrencyId;
        $company->using_eu_warehouses = $usingEuWarehouses;
        $company->are_manufacturers = $areManufacturers;
        $company->have_goods_supplier = $haveGoodsSupplier;
        $company->providing_services = $providingServices;
        $company->selling_goods = $sellingGoods;
        $company->goods_import_description = $goodsImportDescription;
        $company->trade_name = $tradeName;
        $company->status_id = $statusId;
        $company->business_type_id = $businessTypeId;

        $company->save();

        if (!is_null($goodsImportCountryIds)) {
            $this->storeImportFromCountries($company->id, $goodsImportCountryIds);
        }

        return $company;
    }

    public function getGoodsImportDescription(int $warehouseTypeId): string
    {
        return match ($warehouseTypeId) {
            RegistrationWarehouseUsageType::MCI, RegistrationWarehouseUsageType::PAN => $this->registrationSetupRepo->getRegistrationSetupByKey('import_desc_pan_mci_de')->value,
            RegistrationWarehouseUsageType::EFN => $this->registrationSetupRepo->getRegistrationSetupByKey('import_desc_efn_de')->value,
            RegistrationWarehouseUsageType::OTHER => $this->registrationSetupRepo->getRegistrationSetupByKey('import_desc_other_de')->value,
            default => $this->registrationSetupRepo->getRegistrationSetupByKey('import_desc_no_de')->value,
        };
    }

    public function deactivate(int $companyId, string $date): void
    {
        $company = $this->companyRepo->getCompanyById($companyId);
        $company->deactivate_date = Carbon::parse($date)->toDateString();
        $company->status_id = CompanyStatus::CANCELLED;
        $company->save();

        History::created('history.Deactivated', $company);
    }

    public function storeUpdateDeleteCompanyAccountant(
        int $companyId,
        int $accountantId,
        bool $deleted,
        ?int $companyAccountantId = null,
        ?int $institutionBranchId = null
    ): void {
        $companyAccountant = $this->companyAccountantRepo->getEmptyCompanyAccountantModel();
        if (!is_null($companyAccountantId)) {
            $companyAccountant = $this->companyAccountantRepo
                ->getCompanyAccountantById($companyAccountantId);
            if (is_null($companyAccountant)) {
                return;
            }
            if ($deleted) {
                $langParams = ['country' => $companyAccountant->country_name];
                History::deleted('history.Accountant deleted for country', $companyAccountant, null, $langParams);

                $companyAccountant->delete();

                return;
            }
        }

        if (
            !is_null($companyAccountantId) &&
            $companyAccountant->company_id === $companyId &&
            $companyAccountant->accountant_id === $accountantId &&
            $companyAccountant->institution_branch_id === $institutionBranchId
        ) {
            return;
        }

        $companyAccountant->company_id = $companyId;
        $companyAccountant->accountant_id = $accountantId;
        $companyAccountant->institution_branch_id = $institutionBranchId;

        //store company tax office if company/accountant meets criteria
        $countryId = $companyAccountant->accountant->country_id;
        $this->taxOfficeService->storeCompanyTaxOfficeForCountry($companyId, $countryId);

        try {
            $companyAccountant->save();

            $langParams = ['country' => $companyAccountant->country_name];
            if (!is_null($companyAccountantId)) {
                History::updated('history.Accountant updated for country', $companyAccountant, null, $langParams);
            } else {
                History::created('history.Accountant created for country', $companyAccountant, null, $langParams);
            }
        } catch (Throwable) {
        }
    }

    public function storeDeleteCompanyProductTypes(int $companyId, array $productTypeIds): void
    {
        $company = $this->companyRepo->getCompanyById($companyId);
        $existingProductTypeIds = $company->productTypes->pluck('id')->toArray();
        $idsToStore = array_diff($productTypeIds, $existingProductTypeIds);
        $idsToDelete = array_diff($existingProductTypeIds, $productTypeIds);

        foreach ($idsToStore as $typeId) {
            $company->productTypes()->attach($typeId);
        }
        foreach ($idsToDelete as $typeId) {
            $company->productTypes()->detach($typeId);
        }
    }

    public function getCompaniesOnInstitutionForUser(
        int $userId,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $limit = 40
    ): Collection {
        $user = $this->userRepo->getUserById($userId);
        $companiesIds = null;
        if (!$user->isAdministrative()) {
            /** @noinspection PhpUnhandledExceptionInspection */
            $companiesIds = $this->resolveCompaniesIdsForUserOnInstitution($user, $institutionInstitutionTypeId);
        }

        return $this->companyRepo->getCompaniesByUserSearch($userId, $search, $companiesIds, $limit);
    }

    public function resolveCompaniesIdsForUserOnInstitution(User $user, ?int $institutionInstitutionTypeId = null): array
    {
        $companiesIds = [];
        if ($user->isDeveloper()) {
            return $companiesIds;
        }

        if (!$user->isClientType()) {
            if (is_null($institutionInstitutionTypeId)) {
                /** @noinspection PhpUnhandledExceptionInspection */
                throw new Exception('For non client users parameter $institutionInstitutionTypeId is mandatory');
            }
            $user->load(
                'institutionInstitutionTypeUserRoles',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionTypeAccountants.companyAccountants.company',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionTypePartners.companyAccountants.company',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.teams.usersTeams',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.teams'
            );

            /** @noinspection PhpUndefinedMethodInspection */
            $institutionInstitutionTypeUserRoles = $user->institutionInstitutionTypeUserRoles
                ->groupBy('institution_institution_type_id')
                ->get($institutionInstitutionTypeId)
                ->keyBy('role_id');

            /**
             * @var InstitutionInstitutionType $institutionInstitutionType
             */
            $institutionInstitutionType = $institutionInstitutionTypeUserRoles->first()->institutionInstitutionType;

            if ($institutionInstitutionType->institution_type_id === InstitutionType::TYPE_ACCOUNTANT) {
                $companiesIds = $institutionInstitutionType
                    ->institutionTypePartners
                    ->filter(function (InstitutionTypeCountryPartner $institutionTypePartner) {
                        return $institutionTypePartner->companyAccountants->count() > 0;
                    })
                    ->transform(function (InstitutionTypeCountryPartner $institutionTypePartner) {
                        $countryId = $institutionTypePartner->country_id;

                        return $institutionTypePartner->companyAccountants
                            ->filter(function (CompanyAccountant $companyAccountant) {
                                return !is_null($companyAccountant->company);
                            })->transform(function (CompanyAccountant $companyAccountant) use ($countryId) {
                                $companyAccountant->company->accountant_country_id = $countryId;

                                return $companyAccountant->company;
                            });
                    })->flatten(1)
                    ->keyBy('id')
                    ->keys()
                    ->toArray();

            } elseif ($institutionInstitutionType->institution_type_id === InstitutionType::TYPE_PARTNER) {
                $companiesIds = $this->companyRepo
                    ->getAllCompaniesByPartnerId($institutionInstitutionTypeId)
                    ->pluck('id')
                    ->toArray();

                $accountants = $institutionInstitutionType
                    ->institutionTypeAccountants
                    ->filter(function (InstitutionTypeCountryPartner $institutionTypePartner) {
                        return $institutionTypePartner->companyAccountants->count() > 0;
                    })
                    ->transform(function (InstitutionTypeCountryPartner $institutionTypePartner) {
                        $countryId = $institutionTypePartner->country_id;

                        return $institutionTypePartner->companyAccountants
                            ->transform(function (CompanyAccountant $companyAccountant) use ($countryId) {
                                $companyAccountant->company->accountant_country_id = $countryId;

                                return $companyAccountant->company;
                            });
                    })->flatten(1)
                    ->keyBy('id')
                    ->keys()
                    ->toArray();
                foreach ($accountants as $accountant) {
                    if (!in_array($accountant, $companiesIds)) {
                        $companiesIds[] = $accountant;
                    }
                }
            } else {
                $companiesIds = collect();

                $companiesIds = $companiesIds->toArray();
            }

        } else {
            $companiesIds = $user->load('companiesUserRoles')
                ->companiesUserRoles
                ->pluck('company_id')
                ->toArray();
        }

        if (!is_null($companiesIds)) {
            $companiesIds = collect($companiesIds)->toArray();
        }

        return $companiesIds;
    }

    public function getCompaniesOnInstitutionForUserPaginate(
        int $userId,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $perPage = 40,
        ?array $companyStatusIds = null,
        ?int $partnerId = null,
        ?int $countryId = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): LengthAwarePaginator {
        $user = $this->userRepo->getUserById($userId);
        $companiesIds = null;
        if (!$user->isDeveloper()) {
            /** @noinspection PhpUnhandledExceptionInspection */
            $companiesIds = $this->resolveCompaniesIdsForUserOnInstitution($user, $institutionInstitutionTypeId);
        }

        return $this->companyRepo->getCompaniesByUserSearchPaginate(
            $userId,
            $search,
            $companiesIds,
            $perPage,
            ['status', 'address.country', 'partner.institution', 'activeRegistrationProcesses'],
            $companyStatusIds,
            $partnerId,
            $countryId,
            $orderBy,
            $orderDirection
        );
    }

    public function getCompaniesForExportOnInstitutionForUser(
        int $userId,
        ?int $institutionInstitutionTypeId = null,
        ?array $companyStatusIds = null,
        ?int $partnerId = null
    ): Collection {
        $user = $this->userRepo->getUserById($userId);
        /** @noinspection PhpUnhandledExceptionInspection */
        $companiesIds = $this->resolveCompaniesIdsForUserOnInstitution($user, $institutionInstitutionTypeId);

        return $this->companyRepo->getCompaniesForExportByUserSearch(
            $userId,
            $companiesIds,
            ['status', 'address.country', 'partner.institution'],
            $companyStatusIds,
            $partnerId
        );
    }

    public function resolveCompaniesIdsWithCountryByUserIdOnInstitution(int $userId, ?int $institutionInstitutionTypeId = null): Collection
    {
        $user = $this->userRepo->getUserById($userId);

        /** @noinspection PhpUnhandledExceptionInspection */
        return $this->resolveCompaniesIdsByCountryForUserOnInstitution($user, $institutionInstitutionTypeId);
    }

    private function resolveCompaniesIdsByCountryForUserOnInstitution(User $user, ?int $institutionInstitutionTypeId = null): Collection
    {
        $companies = [];
        $countriesData = $this->countryRepo->getAllCountriesWhereDoingTax();
        if ($user->isDeveloper()) {
            return collect();
        }

        if (!$user->isClientType()) {
            if (is_null($institutionInstitutionTypeId)) {
                /** @noinspection PhpUnhandledExceptionInspection */
                throw new Exception('For non client users parameter $institutionInstitutionTypeId is mandatory');
            }
            $user->load(
                'institutionInstitutionTypeUserRoles',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionTypePartners.companyAccountants',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.institutionTypeAccountants.companyAccountants.company',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.teams.usersTeams',
                'institutionInstitutionTypeUserRoles.institutionInstitutionType.teams'
            );

            /** @noinspection PhpUndefinedMethodInspection */
            $institutionInstitutionTypeUserRoles = $user->institutionInstitutionTypeUserRoles
                ->groupBy('institution_institution_type_id')
                ->get($institutionInstitutionTypeId)
                ->keyBy('role_id');

            /**
             * @var InstitutionInstitutionType $institutionInstitutionType
             */
            $institutionInstitutionType = $institutionInstitutionTypeUserRoles->first()->institutionInstitutionType;

            if ($institutionInstitutionType->institution_type_id === InstitutionType::TYPE_ACCOUNTANT) {
                $companiesData = $institutionInstitutionType
                    ->institutionTypePartners
                    ->filter(function (InstitutionTypeCountryPartner $institutionTypePartner) {
                        return $institutionTypePartner->companyAccountants->count() > 0;
                    });

                foreach ($companiesData as $iip) {
                    $blank = [
                        'countryId'    => $iip->country_id,
                        'companiesIds' => []
                    ];
                    /**
                     * @var InstitutionTypeCountryPartner $iip
                     */
                    $countryData = $companies[$iip->country_id] ?? $blank;
                    foreach ($iip->companyAccountants as $companyAccountant) {
                        if (!in_array($companyAccountant->company_id, $countryData)) {
                            $countryData['companiesIds'][] = $companyAccountant->company_id;
                        }
                    }
                    $companies[$iip->country_id] = $countryData;
                }
            } elseif ($institutionInstitutionType->institution_type_id === InstitutionType::TYPE_PARTNER) {
                /** @noinspection PhpUnhandledExceptionInspection */
                throw new PartnerCompanyCountryDevException();
            }
        } else {
            // Client can access all of his companies on all countries
            $companiesIds = $user->load('companiesUserRoles')
                ->companiesUserRoles
                ->pluck('company_id')
                ->toArray();
            foreach ($countriesData as $country) {
                $companies[] = [
                    'countryId'    => $country->id,
                    'companiesIds' => $companiesIds
                ];
            }
        }

        if (!is_null($companies)) {
            $companies = collect($companies);
        }

        return $companies->filter(function (array $countryData) {
            return count($countryData['companiesIds']) > 0;
        })->keyBy('countryId');
    }

    public function isPartnerCodeUniqueForPartner(string $partnerCode, int $partnerInstitutionInstitutionTypeId): bool
    {
        $company = $this->companyRepo->getCompanyByPartnerCodeAndPartnerId($partnerCode, $partnerInstitutionInstitutionTypeId);
        if (is_null($company)) {
            return true;
        }

        return false;
    }

    public function activateCompany(int $companyId): void
    {
        $company = $this->companyRepo->getCompanyById($companyId);
        $company->deactivate_date = null;
        $company->status_id = CompanyStatus::ACTIVE;
        $company->save();

        History::created('history.Company activated', $company);
    }

    public function storeUpdateDeleteImportGoodsReverseDocuments(
        int $companyId,
        int $countryId,
        bool $delete,
        ?int $fileId = null,
        ?int $importGoodsReverseDocumentId = null,
        ?string $startDate = null,
        ?string $expirationDate = null
    ): void {
        $countryName = $this->countryRepo->getCountryById($countryId)->name;
        $langParams = ['country' => $countryName];
        if ($delete) {

            $importGoodsReverseDocument = $this->companyRepo->getImportGoodsReverseDocumentById($importGoodsReverseDocumentId);
            History::deleted('history.Import goods reverse documents deleted for country', $importGoodsReverseDocument, null, $langParams);

            $this->deleteImportGoodsReverseDocuments($importGoodsReverseDocumentId, $fileId);
        } else {
            $this->storeUpdateImportGoodsReverseDocuments(
                $companyId,
                $countryId,
                $importGoodsReverseDocumentId,
                $startDate,
                $expirationDate
            );
        }
    }

    private function deleteImportGoodsReverseDocuments(
        int $importGoodsReverseDocumentId,
        ?int $fileId
    ): void {
        $importGoodsReverseDocument = $this->companyRepo->getImportGoodsReverseDocumentById($importGoodsReverseDocumentId);

        if (!is_null($importGoodsReverseDocument)) {
            $this->companyRepo->deleteImportGoodsReverseDocument($importGoodsReverseDocumentId);
            $this->deleteGoodsReverseDocument($fileId);
        }
    }

    public function deleteGoodsReverseDocument(int $documentId): void
    {
        $file = $this->fileRepo->getFileById($documentId);

        if (!is_null($file)) {
            $this->fileRepo->deleteFileById($documentId);
        }

    }

    private function storeUpdateImportGoodsReverseDocuments(
        int $companyId,
        int $countryId,
        ?int $reverseChargeId = null,
        ?string $startDate = null,
        ?string $expirationDate = null
    ): ImportGoodsReverseDocument {
        $countryName = $this->countryRepo->getCountryById($countryId)->name;
        $langParams = ['country' => $countryName];

        $importGoodsReverseDocuments = $this->companyRepo->getEmptyImportGoodsReverseDocument();
        $actionUpdate = false;

        if (!is_null($reverseChargeId)) {
            $importGoodsReverseDocuments = $this->companyRepo->getImportGoodsReverseDocumentById($reverseChargeId);
            $actionUpdate = true;
        }

        if (
            $actionUpdate &&
            $importGoodsReverseDocuments->company_id === $companyId &&
            $importGoodsReverseDocuments->country_id === $countryId &&
            $importGoodsReverseDocuments->start_date === $startDate &&
            $importGoodsReverseDocuments->expiration_date === $expirationDate
        ) {
            return $importGoodsReverseDocuments;
        }

        $importGoodsReverseDocuments->company_id = $companyId;
        $importGoodsReverseDocuments->country_id = $countryId;
        $importGoodsReverseDocuments->start_date = $startDate;
        $importGoodsReverseDocuments->expiration_date = $expirationDate;

        $importGoodsReverseDocuments->save();

        if ($actionUpdate) {
            History::updated('history.Import goods reverse documents updated for country', $importGoodsReverseDocuments, null, $langParams);
        } else {
            History::created('history.Import goods reverse documents created for country', $importGoodsReverseDocuments, null, $langParams);
        }

        return $importGoodsReverseDocuments;
    }

    public function saveImportGoodsReverseDocumentFile(
        UploadedFile $file,
        int $companyId,
        int $countryId,
        ?int $importGoodsReverseDocumentId = null,
        ?string $startDate = null,
        ?string $expirationDate = null
    ): ImportGoodsReverseDocument {
        $importGoodsReverseDocument = $this->storeUpdateImportGoodsReverseDocuments(
            $companyId,
            $countryId,
            $importGoodsReverseDocumentId,
            $startDate,
            $expirationDate
        );

        /** @noinspection PhpUnhandledExceptionInspection */
        $this->fileService->storeUploadedFile($importGoodsReverseDocument, $file);

        return $importGoodsReverseDocument;
    }

    public function storeUpdateCompanyContactPerson(
        int $companyId,
        int $personId,
        int $companyContactPersonTypeId,
        ?int $companyContactPersonId,
        ?array $historyData = []
    ): void {
        $actionUpdate = true;

        $companyContactPerson = null;
        if (!is_null($companyContactPersonId)) {
            $companyContactPerson = $this->companyContactPersonRepo->getCompanyContactPersonById($companyContactPersonId);
        }
        if (is_null($companyContactPerson)) {
            $companyContactPerson = $this->companyContactPersonRepo->getEmptyCompanyContactPersonModel();
            $actionUpdate = false;
        }
        $companyContactPerson->company_id = $companyId;
        $companyContactPerson->person_id = $personId;
        $companyContactPerson->contact_person_type_id = $companyContactPersonTypeId;
        $companyContactPerson->save();

        $contactPersonType = $this->getCompanyContactPersonTypeTranslation($companyContactPersonTypeId);

        if ($actionUpdate && count($historyData) === 0) {
            return;
        }

        if ($actionUpdate) {
            History::updated('history.' . $contactPersonType . ' updated', $companyContactPerson, null, [], $historyData);
        } else {
            History::created('history.' . $contactPersonType . ' created', $companyContactPerson);
        }
    }

    public function updateSiret(
        int $companyId,
        ?string $siret = null
    ): Company {
        $company = $this->companyRepo->getCompanyById($companyId);
        $company->siret = $siret;

        $company->save();

        return $company;
    }

    public function deleteCompanyContactPersonById(int $companyContactPersonId): void
    {
        $contactPerson = $this->companyContactPersonRepo->getCompanyContactPersonById($companyContactPersonId);

        $contactPersonType = $this->getCompanyContactPersonTypeTranslation($contactPerson->contact_person_type_id);
        History::deleted('history.' . $contactPersonType . ' deleted', $contactPerson);

        $this->companyContactPersonRepo->deleteCompanyContactPersonById($companyContactPersonId);
    }

    private function getCompanyContactPersonTypeTranslation(int $companyContactPersonTypeId): string
    {
        $allContactPersonTypes = $this->companyContactPersonRepo->getAllContactPersonTypes();
        $contactPersonType = _l('company-profile.contact-person-type.General');

        foreach ($allContactPersonTypes as $type) {
            if ($type->id === $companyContactPersonTypeId) {
                $contactPersonType = _l($type->type_lang_key);
                break;
            }
        }

        return $contactPersonType;
    }

    public function createDeleteLegalRepresentatives(
        int $companyId,
        ?int $personId,
        ?bool $delete = null
    ): void {
        $company = $this->companyRepo->getCompanyById($companyId)->load('legalRepresentatives');

        if (!$delete) {
            $legalRepresentative = $this->legalRepresentativeRepo->getLegalRepresentativeByPersonAndCompanyIds($personId, $companyId);

            if ($legalRepresentative->count() > 0) {
                return;
            }

            $description = 'history.Legal representative created';
            $legalRepresentative = $this->companyRepo->createLegalRepresentative($companyId, $personId);

            $company->load('legalRepresentatives');

            History::created($description, $legalRepresentative);

        } else {
            $legalRepresentative = $this->companyRepo->getLegalRepresentative($companyId, $personId);
            $description = 'history.Legal representative deleted';
            $company->load('legalRepresentatives');

            History::deleted($description, $legalRepresentative);

            $this->companyRepo->deleteLegalRepresentative($companyId, $personId);
        }
    }

    private function storeImportFromCountries(int $companyId, array $countriesIds): void
    {
        $data = [];
        foreach ($countriesIds as $countryId) {
            $data[] = [
                'company_id' => $companyId,
                'country_id' => $countryId,
            ];
        }
        $this->companyRepo->insertImportFromCountries($data);
    }

    private function updateCompanyContact(Company $company, int $contactTypeConst, ?string $contact): void
    {
        if (!in_array($contactTypeConst, [ContactType::PHONE, ContactType::EMAIL, ContactType::FAX])) {
            return;
        }

        if (is_null($contact)) {
            Contact::query()
                ->where('contact_type_id', $contactTypeConst)
                ->where('company_id', $company->id)
                ->delete();

            return;
        }

        Contact::updateOrCreate(
            ['contact_type_id' => $contactTypeConst, 'company_id' => $company->id],
            ['value' => $contact]
        );
    }

    public function resolveTaxPeriodsDatesByOrderInYear(
        Company $company,
        Country $country,
        ?int $filterYear = null,
        ?Carbon $firstSaleDate = null
    ): Collection {
        $startDate = $this->resolveCompanyStartDate($company, $country->id, $firstSaleDate?->toDateString());
        $endDate = Carbon::now()->subMonth()->endOfMonth();
        if ($startDate->gte($endDate)) {
            return collect();
        }

        $periodTypes = $this->resolvePeriodsTypesStartEndDate($company, $country->id, $startDate);
        $quarters = $this->resolveQuarters($startDate, $country->id);

        $years = collect(new CarbonPeriod($startDate->clone()->startOfYear(), '1 month', $endDate->clone()->endOfYear()))
            ->map(function (Carbon $monthStartDate) use ($periodTypes, $startDate, $endDate, $quarters) {
                $monthEndDate = $monthStartDate->clone()->endOfMonth();
                $key = $this->buildVatReportDatesKey($monthStartDate->toDateString(), $monthEndDate->toDateString());

                $reportPeriod = $this->resolveIsPeriodPeriodType(
                    $periodTypes,
                    $monthStartDate,
                    $monthEndDate,
                    TaxPeriodType::M
                );

                $quarter = $quarters->filter(function (Collection $months) use ($monthStartDate) {
                    return $months->contains($monthStartDate->month);
                })->keys()->first();

                return [
                    'month'          => $monthStartDate->month,
                    'quarter'        => $quarter,
                    'year'           => $monthStartDate->year,
                    'nameShort'      => _l($monthStartDate->format('M')),
                    'start'          => $monthStartDate->toDateString(),
                    'end'            => $monthEndDate->toDateString(),
                    'startFormatted' => $monthStartDate->format(_l_date_format('short-year-date')),
                    'endFormatted'   => $monthEndDate->format(_l_date_format('short-year-date')),
                    'reportPeriod'   => $reportPeriod,
                    'key'            => $key
                ];
            })
            ->groupBy(function (array $month) {
                return $month['year'];
            })
            ->map(function (Collection $months) use ($periodTypes) {
                $changed = 1;
                $current = $months->first()['quarter'];

                return $months->groupBy(function (array $month) use (&$changed, &$current) {
                    $quarter = $month['quarter'];
                    if ($quarter !== $current) {
                        $current = $quarter;
                        $changed++;
                    }

                    return $changed . $quarter;
                })
                    ->values()
                    ->map(function (Collection $months) use ($periodTypes) {
                        // ORDER IS IMPORTANT BECAUSE QUARTER AND YEAR ARE IN CORRECT ORDER, DATES ARE CHANGING ONLY
                        $first = $months->first();
                        $last = $months->last();
                        $quarter = $first['quarter'];
                        $year = $first['year'];

                        $quarterStartDate = Carbon::parse($first['start']);
                        $quarterEndDate = Carbon::parse($last['end']);

                        // CHECK DATES IF GB PERIOD
                        $monthsCount = $months->count();
                        if ($monthsCount !== 3) {
                            if ($quarterStartDate->month < 3) {
                                $quarterStartDate = $quarterStartDate->subMonths(3 - $monthsCount);
                            } else {
                                $quarterEndDate = $quarterEndDate->startOfMonth()->addMonths(3 - $monthsCount)->endOfMonth();
                            }
                        }

                        $reportPeriod = $this->resolveIsPeriodPeriodType(
                            $periodTypes,
                            $quarterStartDate,
                            $quarterEndDate,
                            TaxPeriodType::Q
                        );

                        $key = $this->buildVatReportDatesKey($quarterStartDate->toDateString(), $quarterEndDate->toDateString());

                        return [
                            'quarter'        => $quarter,
                            'nameShort'      => _l('Q' . $quarter),
                            'months'         => $months->toArray(),
                            'year'           => $year,
                            'start'          => $quarterStartDate->toDateString(),
                            'end'            => $quarterEndDate->toDateString(),
                            'startFormatted' => $quarterStartDate->format(_l_date_format('short-year-date')),
                            'endFormatted'   => $quarterEndDate->format(_l_date_format('short-year-date')),
                            'reportPeriod'   => $reportPeriod,
                            'key'            => $key
                        ];
                    });
            })->map(function (Collection $quarters) {

                $first = $quarters->first();
                $year = $first['year'];
                $yearStart = Carbon::parse($year . '-01-01');
                $start = $yearStart->startOfYear();
                $end = $yearStart->clone()->endOfYear();

                $key = $this->buildVatReportDatesKey($start->toDateString(), $end->toDateString());

                return [
                    'year'           => $year,
                    'yearId'         => $year,
                    'quarters'       => $quarters->toArray(),
                    'start'          => $start->toDateString(),
                    'end'            => $end->toDateString(),
                    'startFormatted' => $start->format(_l_date_format('short-year-date')),
                    'endFormatted'   => $end->format(_l_date_format('short-year-date')),
                    'reportPeriod'   => true,
                    'key'            => $key
                ];
            });

        if (!is_null($filterYear)) {
            $years = $years->filter(function (array $year) use ($filterYear) {
                return $year['yearId'] === $filterYear;
            });
        }

        return $years->values();
    }

    private function resolveQuarters(Carbon $companyStartDate, $countryId): Collection
    {
        if ($countryId === Country::GB) {
            $start = $companyStartDate->startOfMonth();
        } else {
            $start = $companyStartDate->startOfYear();
        }

        return collect(new CarbonPeriod($start, '1 quarter', $start->clone()->addYear()->subDay()->endOfMonth()))
            ->map(function (Carbon $quarter) {
                $months = new CarbonPeriod($quarter, '1 month', $quarter->clone()->addMonths(2));

                return collect($months)->map(function (Carbon $month) {
                    return $month->month;
                });
            })->keyBy(function (Collection $months, $key) {
                return $key + 1;
            });
    }

    private function resolveIsPeriodPeriodType(
        Collection $periodTypes,
        Carbon $periodStartDate,
        Carbon $periodEndDate,
        int $type
    ): bool {
        $types = $periodTypes->filter(function (array $companyTaxPeriodType) use ($periodStartDate, $periodEndDate, $type) {
            if ($companyTaxPeriodType['tax_period_type_id'] !== $type) {
                return false;
            }

            $startDate = Carbon::parse($companyTaxPeriodType['start_date']);
            $endDate = $companyTaxPeriodType['end_date'];
            if (is_null($endDate)) {
                return $periodStartDate->gte($startDate) || $periodEndDate->gte($startDate);
            }

            $endDate = Carbon::parse($endDate);

            $carbonPeriodI = new CarbonPeriod($startDate, $endDate);
            $carbonPeriodII = new CarbonPeriod($periodStartDate, $periodEndDate);

            return $carbonPeriodI->overlaps($carbonPeriodII);
        });

        return $types->count() > 0;
    }

    private function resolvePeriodsTypesStartEndDate(Company $company, int $countryId, Carbon $startDate): Collection
    {
        $periodTypes = $company->periodTypes
            ->groupBy('country_id')
            ->get($countryId, collect());
        $firstPeriodTypeDate = $periodTypes->first()?->start_date;

        $firstPeriodTypeDateCheck = !is_null($firstPeriodTypeDate) && Carbon::parse($firstPeriodTypeDate)->gt($startDate);
        if ($firstPeriodTypeDateCheck || $periodTypes->count() < 1) {
            $firstPeriodType = new CompanyTaxPeriodType();
            $firstPeriodType->start_date = $startDate->toDateString();
            $firstPeriodType->tax_period_type_id = TaxPeriodType::M;
            $periodTypes->prepend($firstPeriodType)->values();
        }

        return $periodTypes
            ->map(function (CompanyTaxPeriodType $companyTaxPeriodType, int $index) use ($periodTypes) {
                $endDate = $periodTypes->get($index + 1)?->start_date;
                if (!is_null($endDate)) {
                    $endDate = Carbon::parse($endDate)->toDateString();
                }

                $type = $companyTaxPeriodType->toArray();
                $type['end_date'] = $endDate;
                unset($type['tax_period_type']);
                unset($type['company_id']);
                unset($type['country_id']);
                unset($type['id']);

                return $type;
            });
    }

    private function buildVatReportDatesKey(string $dateFrom, string $dateTo): string
    {
        return $dateFrom . '::' . $dateTo;
    }

    private function resolveCompanyStartDate(Company $company, int $countryId, ?string $firstSaleDate = null): Carbon
    {
        $vatNumberDate = $company->vatNumbers
            ->sortBy('calculated_register_date', SORT_NATURAL)
            ->groupBy('country_id')
            ->get($countryId)
            ?->first()
            ?->calculated_register_date;
        $taxNumberDate = $company->taxNumbers
            ->sortBy('register_date', SORT_NATURAL)
            ->groupBy('country_id')
            ->get($countryId)
            ?->first()
            ?->register_date;
        $periodTypesDate = $company->periodTypes
            ->sortBy('start_date', SORT_NATURAL)
            ->groupBy('country_id')
            ->get($countryId)
            ?->first()
            ?->start_date;

        if ($countryId === Country::GB && !is_null($periodTypesDate)) {
            return Carbon::parse($periodTypesDate);
        }

        $dates = collect([
            $firstSaleDate,
            $vatNumberDate,
            $taxNumberDate,
            $periodTypesDate
        ])->filter(function (?string $date) {
            return !is_null($date);
        })->map(function (string $date) {
            return Carbon::parse($date);
        })->sortBy(function (Carbon $date) {
            return $date->timestamp;
        }, SORT_NATURAL);

        $date = $dates->first();
        if (is_null($date)) {
            $date = Carbon::now();
        }

        return $date;
    }

    public function apiCompanyFactory(CreateCompanyApiRequest $request): Company
    {
        /**
         * Validates request and if fails throws ValidationApiException
         * which is handled in Core\System\Exceptions\Handler
         *
         * @noinspection PhpUnhandledExceptionInspection
         */
        $request->validate();

        $dbIdentificationNumbers = $request->getAllIdentificationNumbers();
        if ($dbIdentificationNumbers->count() > 0) {
            /**
             * If identification numbers are found, then the company
             * is already in the database, and we can use it.
             */
            $company = $dbIdentificationNumbers->first()->getCompany();
        } else {
            /**
             * If identification numbers are not found, then the company
             * is not in the database, and we need to create it from
             * data in the request.
             */
            $company = $this->createNewCompanyByApi($request);
        }

        $company->load('companyApiConsumers');

        $apiConsumer = $company->companyApiConsumers->keyBy('api_consumer_id');
        if (!$apiConsumer->has($request->getApiConsumerId())) {
            $apiConsumer = $this->apiRepo->getEmptyCompanyApiConsumerModel();
            $apiConsumer->company_id = $company->id;
            $apiConsumer->api_consumer_id = $request->getApiConsumerId();
            $apiConsumer->save();
        }

        return $company;
    }

    private function createNewCompanyByApi(CreateCompanyApiRequest $request): Company
    {
        $addressData = $request->getEstablishmentAddress();
        $address = $this->addressService
            ->storeAddress(
                street: $addressData['street'],
                cityName: $addressData['city'],
                postalCode: $addressData['postalCode'],
                countryId: $addressData['countryId'],
                houseNumber: $addressData['houseNumber'],
                state: $addressData['state'],
                addition: $addressData['addressAddition']
            );

        $data = $request->getCompany();

        $company = $this->createCompany(
            fullLegalName: $data['name'],
            legalType: $data['legalEntityType'],
            businessActivity: $data['businessActivity'],
            addressId: $address->id,
            partnerId: $request->getPartnerId(),
            registrationNumber: $data['registrationNumber'],
            incorporationDate: $data['incorporationDate'],
            shareCapital: $data['shareCapitalValue'],
            shareCapitalCurrencyId: $data['shareCapitalCurrencyId'],
            statusId: CompanyStatus::ACTIVE,
            businessTypeId: $data['businessTypeId'],
            tradeName: $data['tradeName']
        );

        $contactData = $request->getContact();
        $this->contactService
            ->storeContact(
                value: $contactData['email'],
                contactTypeId: ContactType::EMAIL,
                companyId: $company->id,
            );
        $this->contactService
            ->storeContact(
                value: $contactData['phoneNumber'],
                contactTypeId: ContactType::PHONE,
                companyId: $company->id,
            );

        $vatNumber = $request->getVatNumber();
        $taxNumber = $request->getTaxNumber();
        $ossNumber = $request->getOssNumber();
        $iossNumber = $request->getIossNumber();

        if (!is_null($vatNumber)) {
            $this->vatNumberService
                ->storeUpdateDeleteVatNumber(
                    companyId: $company->id,
                    countryId: $vatNumber['countryId'],
                    vatNumber: $vatNumber['number'],
                    registerDate: $vatNumber['registerDate'],
                    permanentEstablishment: $vatNumber['establishmentStatusId'],
                );
        }

        if (!is_null($taxNumber)) {
            $this->taxNumberService
                ->storeTaxNumber(
                    companyId: $company->id,
                    countryId: $taxNumber['countryId'],
                    number: $taxNumber['number'],
                    registerDate: $taxNumber['registerDate'],
                );
        }

        if (!is_null($ossNumber)) {
            $this->ossNumberService
                ->storeUpdateDeleteOssNumber(
                    companyId: $company->id,
                    issueCountryId: $ossNumber['countryId'],
                    typeId: $ossNumber['ossNumberTypeId'],
                    number: $ossNumber['number'],
                    registerDate: $ossNumber['registerDate'],
                    delete: false
                );
        }

        if (!is_null($iossNumber)) {
            $this->iossNumberService
                ->storeUpdateDeleteIossNumber(
                    companyId: $company->id,
                    issueCountryId: $iossNumber['countryId'],
                    typeId: $iossNumber['iossNumberTypeId'],
                    number: $iossNumber['number'],
                    registerDate: $iossNumber['registerDate'],
                    delete: false
                );
        }

        return $company;
    }
}
