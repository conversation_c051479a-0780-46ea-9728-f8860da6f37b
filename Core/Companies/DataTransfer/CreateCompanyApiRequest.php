<?php

namespace App\Core\Companies\DataTransfer;

use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Data\Models\ApiConsumer;
use App\Core\Data\Models\Contracts\HasCompanyContract;
use App\Core\Data\Models\EstablishmentStatus;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Models\IossNumber;
use App\Core\Data\Models\OssNumber;
use App\Core\Data\Models\TaxNumber;
use App\Core\Data\Models\VatNumber;
use App\Core\Data\Repositories\Contracts\IossNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\OssNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatNumberRepositoryContract;
use App\Core\VatNumbers\Api\VatNumberApi;
use Closure;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Contracts\Validation\Validator as ValidatorContract;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator as ValidatorFacade;
use Illuminate\Validation\Rule;
use Throwable;

class CreateCompanyApiRequest implements Arrayable, Jsonable
{
    private array $company;
    private array $contact;
    private array $establishmentAddress;
    private ?array $vatNumber;
    private ?array $taxNumber;
    private ?array $ossNumber;
    private ?array $iossNumber;

    private Collection $allIdentificationNumbers;

    private ValidatorContract $validator;

    private ApiConsumer $apiConsumer;

    public function __construct(array $companyRequestData, ApiConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;

        $this->contact = $companyRequestData['contact'] ?? [];
        $this->establishmentAddress = $companyRequestData['establishmentAddress'] ?? [];
        $this->vatNumber = $companyRequestData['vatNumber'] ?? null;
        $this->taxNumber = $companyRequestData['taxNumber'] ?? null;
        $this->ossNumber = $companyRequestData['ossNumber'] ?? null;
        $this->iossNumber = $companyRequestData['iossNumber'] ?? null;

        $this->resolveAllIdentificationNumbers();

        $dataKeys = [
            'contact',
            'establishmentAddress',
            'vatNumber',
            'taxNumber',
            'ossNumber',
            'iossNumber'
        ];
        foreach ($dataKeys as $key) {
            unset($companyRequestData[$key]);
        }

        $this->company = $companyRequestData;
        $this->run();
    }

    public function toArray(): array
    {
        return [
            'company'              => $this->getCompany(),
            'establishmentAddress' => $this->getEstablishmentAddress(),
            'contact'              => $this->getContact(),
            'vatNumber'            => $this->getVatNumber(),
            'taxNumber'            => $this->getTaxNumber(),
            'ossNumber'            => $this->getOssNumber(),
            'iossNumber'           => $this->getIossNumber(),
            'apiConsumer'          => $this->getApiConsumer()->toArray(),
        ];
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    public function validate(): void
    {
        if ($this->validator->passes()) {
            return;
        }

        $errors = $this->resolveErrors($this->validator->errors()->toArray());
        $codes = $errors->keys();
        $errors = $errors->values();

        /** @noinspection PhpUnhandledExceptionInspection */
        throw new ValidationApiException(
            errors: $errors->toArray(),
            statusCode: $codes->toArray(),
            meta: ['codes' => $this->getAllMessages()->toArray()]
        );
    }

    /** @noinspection PhpUnused */
    public function getPartnerId(): int
    {
        return $this->getApiConsumer()->crm_institution_id ?? InstitutionInstitutionType::PULLUS_PARTNER_ID;
    }

    /**
     * @return Collection<HasCompanyContract,VatNumber,TaxNumber,OssNumber,IossNumber>
     */
    public function getAllIdentificationNumbers(): Collection
    {
        return $this->allIdentificationNumbers;
    }

    public function getCompany(): array
    {
        return $this->company;
    }

    public function getContact(): array
    {
        return $this->contact;
    }

    public function getEstablishmentAddress(): array
    {
        return $this->establishmentAddress;
    }

    public function getVatNumber(): ?array
    {
        return $this->vatNumber;
    }

    public function getTaxNumber(): ?array
    {
        return $this->taxNumber;
    }

    public function getOssNumber(): ?array
    {
        return $this->ossNumber;
    }

    public function getIossNumber(): ?array
    {
        return $this->iossNumber;
    }

    public function getApiConsumerId(): int
    {
        return $this->getApiConsumer()->id;
    }

    private function getApiConsumer(): ApiConsumer
    {
        return $this->apiConsumer;
    }

    private function validateCompany(): Collection
    {
        $rules = [
            'name'                   => 'required|string',
            'businessTypeId'         => 'required|exists:business_types,id',
            'tradeName'              => 'string',
            'legalEntityType'        => 'required|string',
            'businessActivity'       => 'required|string',
            'registrationNumber'     => 'required|string',
            'incorporationDate'      => 'required|date',
            'shareCapitalValue'      => 'required|numeric',
            'shareCapitalCurrencyId' => 'required|exists:currencies,id',
        ];
        $codes = [
            'name.required'                   => 1011,
            'name.string'                     => 1012,
            'partnerCode.string'              => 1021,
            'partnerCode.unique'              => 1022,
            'businessTypeId.required'         => 1031,
            'businessTypeId.exists'           => 1032,
            'tradeName.string'                => 1041,
            'legalEntityType.required'        => 1051,
            'legalEntityType.string'          => 1052,
            'businessActivity.required'       => 1061,
            'businessActivity.string'         => 1062,
            'registrationNumber.required'     => 1071,
            'registrationNumber.string'       => 1072,
            'incorporationDate.required'      => 1081,
            'incorporationDate.date'          => 1082,
            'shareCapitalValue.required'      => 1091,
            'shareCapitalValue.numeric'       => 1092,
            'shareCapitalCurrencyId.required' => 1101,
            'shareCapitalCurrencyId.exists'   => 1102,
        ];

        return $this->validateItem($this->company, $rules, $codes);
    }

    private function validateContact(): Collection
    {
        $rules = [
            'email'       => 'required|email:rfc,dns',
            'phoneNumber' => 'required|string',
        ];
        $codes = [
            'email.required'       => 2001,
            'email.email'          => 2002,
            'phoneNumber.required' => 2011,
            'phoneNumber.string'   => 2012,
        ];

        return $this->validateItem($this->contact, $rules, $codes, 'contact');
    }

    private function validateEstablishmentAddress(): Collection
    {
        $rules = [
            'street'          => 'required|string',
            'houseNumber'     => 'required',
            'addressAddition' => 'string|nullable',
            'city'            => 'required|string',
            'postalCode'      => 'required',
            'state'           => 'string|nullable',
            'countryId'       => 'required|exists:countries,id',
        ];

        $codes = [
            'street.required'        => 3001,
            'street.string'          => 3002,
            'houseNumber.required'   => 3011,
            'addressAddition.string' => 3021,
            'city.required'          => 3031,
            'city.string'            => 3032,
            'postalCode.required'    => 3041,
            'state.string'           => 3043,
            'countryId.required'     => 3051,
            'countryId.exists'       => 3052,
        ];

        return $this->validateItem($this->establishmentAddress, $rules, $codes, 'establishmentAddress');
    }

    private function validateVatNumber(): Collection
    {
        $vatNumber = $this->vatNumber;
        if (is_null($vatNumber)) {
            return collect();
        }

        $rules = [
            'countryId'             => 'required|exists:countries,id',
            'number'                => [
                'required',
                function (string $attribute, mixed $value, Closure $fail) {
                    $numbers = $this->getAllIdentificationNumbers()->keyBy('company_id');
                    if ($numbers->count() > 1) {
                        $fail(101);
                    }

                    if (!$this->validateVatNumberApi($value)) {
                        $fail(4013);
                    }
                }
            ],
            'registerDate'          => 'required|date',
            'establishmentStatusId' => [
                'required',
                Rule::in([
                    EstablishmentStatus::PERMANENT_ESTABLISHMENT,
                    EstablishmentStatus::OTHER_FACILITIES,
                ]),
            ]
        ];
        $codes = [
            'countryId.required'             => 4001,
            'countryId.exists'               => 4002,
            'number.required'                => 4011,
            'number.string'                  => 4012,
            'registerDate.required'          => 4021,
            'registerDate.date'              => 4022,
            'establishmentStatusId.required' => 4031,
            'establishmentStatusId.in'       => 4032,
        ];

        return $this->validateItem($vatNumber, $rules, $codes, 'vatNumber');
    }

    private function validateTaxNumber(): Collection
    {
        $taxNumber = $this->taxNumber;
        if (is_null($taxNumber)) {
            return collect();
        }

        $rules = [
            'countryId'    => 'required|exists:countries,id',
            'number'       => 'required|string',
            'registerDate' => 'required|date',
        ];
        $codes = [
            'countryId.required'    => 5001,
            'countryId.exists'      => 5002,
            'number.required'       => 5011,
            'number.string'         => 5012,
            'registerDate.required' => 5021,
            'registerDate.date'     => 5022,
        ];

        return $this->validateItem($taxNumber, $rules, $codes, 'taxNumber');
    }

    private function validateOssNumber(): Collection
    {
        $ossNumber = $this->ossNumber;
        if (is_null($ossNumber)) {
            return collect();
        }
        $rules = [
            'countryId'       => 'required|exists:countries,id',
            'number'          => 'required|string',
            'registerDate'    => 'required|date',
            'ossNumberTypeId' => 'required|exists:oss_number_types,id',
        ];

        $codes = [
            'countryId.required'       => 6001,
            'countryId.exists'         => 6002,
            'number.required'          => 6011,
            'number.string'            => 6012,
            'registerDate.required'    => 6021,
            'registerDate.date'        => 6022,
            'ossNumberTypeId.required' => 6031,
            'ossNumberTypeId.exists'   => 6032,
        ];

        return $this->validateItem($ossNumber, $rules, $codes, 'ossNumber');
    }

    private function validateIossNumber(): Collection
    {
        $iossNumber = $this->iossNumber;
        if (is_null($iossNumber)) {
            return collect();
        }
        $rules = [
            'countryId'        => 'required|exists:countries,id',
            'number'           => 'required|string',
            'registerDate'     => 'required|date',
            'iossNumberTypeId' => 'required|exists:ioss_number_types,id',
        ];

        $codes = [
            'countryId.required'        => 7001,
            'countryId.exists'          => 7002,
            'number.required'           => 7011,
            'number.string'             => 7012,
            'registerDate.required'     => 7021,
            'registerDate.date'         => 7022,
            'iossNumberTypeId.required' => 7031,
            'iossNumberTypeId.exists'   => 7032,
        ];

        return $this->validateItem($iossNumber, $rules, $codes, 'iossNumber');
    }

    private function validateItem(array $data, array $rules, array $codes, ?string $prefix = null): Collection
    {
        $validator = $this->make($data, $rules, $codes);

        if ($validator->passes()) {
            return collect();
        }

        return collect($validator->getMessageBag()->getMessages())
            ->mapWithKeys(function ($error, string $key) use ($prefix) {
                $error = $error[0] ?? null;
                if (!is_null($error)) {
                    $error = (int)$error;
                }

                if (!is_null($prefix)) {
                    $key = $prefix . '.' . $key;
                }

                return [$key => $error];
            });
    }

    private function resolveAllIdentificationNumbers(): void
    {
        $companies = [];

        $vatNumber = $this->vatNumber;
        if (!is_null($vatNumber)) {
            /** @var VatNumberRepositoryContract $vatNumberRepo */
            $vatNumberRepo = resolve(VatNumberRepositoryContract::class);
            $number = $vatNumberRepo->getVatNumberByVatNumber($vatNumber['number'])
                ?->load('company');
            if (!is_null($number)) {
                $companies[] = $number;
            }
        }

        $taxNumber = $this->taxNumber;
        if (!is_null($taxNumber)) {
            /** @var TaxNumberRepositoryContract $taxNumberRepo */
            $taxNumberRepo = resolve(TaxNumberRepositoryContract::class);
            $number = $taxNumberRepo->getTaxNumberByTaxNumber($taxNumber['number'])
                ?->load('company');
            if (!is_null($number)) {
                $companies[] = $number;
            }
        }

        $ossNumber = $this->ossNumber;
        if (!is_null($ossNumber)) {
            /** @var OssNumberRepositoryContract $ossNumberRepo */
            $ossNumberRepo = resolve(OssNumberRepositoryContract::class);
            $number = $ossNumberRepo->getOssNumberByOssNumber($ossNumber['number'])
                ?->load('company');
            if (!is_null($number)) {
                $companies[] = $number;
            }
        }

        $iossNumber = $this->iossNumber;
        if (!is_null($iossNumber)) {
            /** @var IossNumberRepositoryContract $iossNumberRepo */
            $iossNumberRepo = resolve(IossNumberRepositoryContract::class);
            $number = $iossNumberRepo->getIossNumberByIossNumber($iossNumber['number'])
                ?->load('company');
            if (!is_null($number)) {
                $companies[] = $number;
            }
        }

        $this->allIdentificationNumbers = collect($companies);
    }

    private function resolveErrors(array $errors): Collection
    {
        $messages = $this->getAllMessages();

        return collect($errors)
            ->mapWithKeys(function (array $error) use ($messages) {
                $error = $error[0] ?? 0;
                $error = (int)$error;

                return [$error => $messages->get($error) ?? 'Unknown validation error'];
            });
    }

    private function getAllMessages(): Collection
    {
        return collect([
            101 => 'Company identification numbers error.',

            1011 => 'Name is required.',
            1012 => 'Name must be a string.',
            1031 => 'Business type is required.',
            1032 => 'Business type does not exist.',
            1041 => 'Trade name must be a string.',
            1051 => 'Legal entity type is required.',
            1052 => 'Legal entity type must be a string.',
            1061 => 'Business activity is required.',
            1062 => 'Business activity must be a string.',
            1071 => 'Registration number is required.',
            1072 => 'Registration number must be a string.',
            1081 => 'Incorporation date is required.',
            1082 => 'Incorporation date must be a date.',
            1091 => 'Share capital value is required.',
            1092 => 'Share capital value must be a numeric.',
            1101 => 'Share capital currency is required.',
            1102 => 'Share capital currency does not exist.',

            2001 => 'E-mail is required.',
            2002 => 'E-mail is not a valid e-mail address.',
            2011 => 'Phone number is required.',
            2012 => 'Phone number must be a string.',

            3001 => 'Establishment address street is required.',
            3002 => 'Establishment address street must be a string.',
            3011 => 'Establishment address house number is required.',
            3021 => 'Establishment address addition must be a string.',
            3031 => 'Establishment address city is required.',
            3032 => 'Establishment address city must be a string.',
            3041 => 'Establishment address postal code is required.',
            3042 => 'Establishment address postal code must be a string.',
            3043 => 'Establishment address state must be a string.',
            3051 => 'Establishment address country is required.',
            3052 => 'Establishment address country does not exist.',

            4001 => 'VAT number country is required.',
            4002 => 'VAT number country does not exist.',
            4011 => 'VAT number is required.',
            4012 => 'VAT number must be a string.',
            4013 => 'VAT number is invalid.',
            4021 => 'VAT number register date is required.',
            4022 => 'VAT number register date must be a date.',
            4031 => 'VAT number establishment status is required.',
            4032 => 'VAT number establishment status does not exist.',

            5001 => 'Tax number country is required.',
            5002 => 'Tax number country does not exist.',
            5011 => 'Tax number is required.',
            5012 => 'Tax number must be a string.',
            5021 => 'Tax number register date is required.',
            5022 => 'Tax number register date must be a date.',

            6001 => 'OSS number country is required.',
            6002 => 'OSS number country does not exist.',
            6011 => 'OSS number is required.',
            6012 => 'OSS number must be a string.',
            6021 => 'OSS number register date is required.',
            6022 => 'OSS number register date must be a date.',
            6031 => 'OSS number type is required.',
            6032 => 'OSS number type does not exist.',

            7001 => 'IOSS number country is required.',
            7002 => 'IOSS number country does not exist.',
            7011 => 'IOSS number is required.',
            7012 => 'IOSS number must be a string.',
            7021 => 'IOSS number register date is required.',
            7022 => 'IOSS number register date must be a date.',
            7031 => 'IOSS number type is required.',
            7032 => 'IOSS number type does not exist.',
        ]);
    }

    private function run(): void
    {
        $errors = $this->validateCompany()
            ->merge($this->validateContact())
            ->merge($this->validateEstablishmentAddress())
            ->merge($this->validateVatNumber())
            ->merge($this->validateTaxNumber())
            ->merge($this->validateOssNumber())
            ->merge($this->validateIossNumber());

        $this->validator = $this->make([], [], [])
            ->after(function (ValidatorContract $validator) use ($errors) {
                foreach ($errors as $key => $error) {
                    $validator->errors()->add($key, $error);
                }
            });

    }

    private function make(array $data, array $rules, array $messages): ValidatorContract
    {
        return ValidatorFacade::make($data, $rules, $messages);
    }

    private function validateVatNumberApi(?string $vatNumber = null): bool
    {
        $vatNumber = $vatNumber ?? '';

        try {
            $validation = VatNumberApi::validateVatNumber($vatNumber);
        } catch (Throwable) {
            return false;
        }

        return $validation->valid ?? false;
    }
}
