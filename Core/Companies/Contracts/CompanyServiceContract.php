<?php

namespace App\Core\Companies\Contracts;

use App\Core\Companies\DataTransfer\CreateCompanyApiRequest;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\Country;
use App\Core\Data\Models\ImportGoodsReverseDocument;
use App\Core\Data\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;

interface CompanyServiceContract
{
    public function apiCompanyFactory(CreateCompanyApiRequest $request): Company;

    public function updateGeneralData(
        int $companyId,
        string $fullLegalName,
        string $incorporationDate,
        string $businessActivity,
        int $businessTypeId,
        ?float $shareCapital,
        ?int $shareCapitalCurrencyId,
        ?string $partnerCode,
        ?string $legalType = null,
        ?string $registrationNumber = null,
        ?int $partnerId = null,
        ?string $tradeName = null,
        ?string $registrationNumberIssuer = null,
        ?string $krsNumber = null,
        ?string $companyDescription = null,
        ?int $lawGoverningCountryId = null,
        ?int $billingAddressId = null,
        ?int $auditingAddressId = null,
        ?string $web = null,
        ?string $placeOfCommercialRegistry = null,
        ?string $nationalEuIdentificationNumber = null,
        ?int $addressId = null,
        ?string $phoneNumber = null,
        ?string $email = null,
        ?string $faxNumber = null
    ): Company;

    public function updateEoriNumber(
        int $companyId,
        ?string $eoriNumber
    ): Company;

    public function updateUkEoriNumber(
        int $companyId,
        ?string $ukEoriNumber
    ): Company;

    public function createDeleteLegalRepresentatives(
        int $companyId,
        ?int $personId,
        ?bool $delete = null
    ): void;

    public function createCompany(
        string $fullLegalName,
        string $legalType,
        string $businessActivity,
        int $addressId,
        int $partnerId,
        ?string $partnerCode = null,
        ?string $registrationNumber = null,
        ?string $incorporationDate = null,
        ?float $shareCapital = null,
        ?int $shareCapitalCurrencyId = null,
        ?bool $providingServices = null,
        ?bool $sellingGoods = null,
        ?int $statusId = null,
        ?int $businessTypeId = null,
        ?bool $usingEuWarehouses = null,
        ?bool $areManufacturers = null,
        ?bool $haveGoodsSupplier = null,
        ?string $goodsImportDescription = null,
        ?array $goodsImportCountryIds = null,
        ?string $tradeName = null,
    ): Company;

    public function getGoodsImportDescription(int $warehouseTypeId): string;

    public function deactivate(int $companyId, string $date): void;

    public function storeUpdateDeleteCompanyAccountant(
        int $companyId,
        int $accountantId,
        bool $deleted,
        ?int $companyAccountantId = null,
        ?int $institutionBranchId = null
    ): void;

    public function storeDeleteCompanyProductTypes(
        int $companyId,
        array $productTypeIds
    ): void;

    public function getCompaniesOnInstitutionForUser(
        int $userId,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $limit = 40
    ): Collection;

    public function getCompaniesOnInstitutionForUserPaginate(
        int $userId,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $perPage = 40,
        ?array $companyStatusIds = null,
        ?int $partnerId = null,
        ?int $countryId = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): LengthAwarePaginator;

    public function getCompaniesForExportOnInstitutionForUser(
        int $userId,
        ?int $institutionInstitutionTypeId = null,
        ?array $companyStatusIds = null,
        ?int $partnerId = null
    ): Collection;

    public function resolveCompaniesIdsForUserOnInstitution(User $user, ?int $institutionInstitutionTypeId = null): array;

    public function resolveCompaniesIdsWithCountryByUserIdOnInstitution(int $userId, ?int $institutionInstitutionTypeId = null): Collection;

    public function isPartnerCodeUniqueForPartner(string $partnerCode, int $partnerInstitutionInstitutionTypeId): bool;

    public function activateCompany(int $companyId): void;

    public function storeUpdateDeleteImportGoodsReverseDocuments(
        int $companyId,
        int $countryId,
        bool $delete,
        ?int $fileId = null,
        ?int $importGoodsReverseDocumentId = null,
        ?string $startDate = null,
        ?string $expirationDate = null
    ): void;

    public function saveImportGoodsReverseDocumentFile(
        UploadedFile $file,
        int $companyId,
        int $countryId,
        ?int $importGoodsReverseDocumentId = null,
        ?string $startDate = null,
        ?string $expirationDate = null
    ): ImportGoodsReverseDocument;

    public function deleteGoodsReverseDocument(int $documentId): void;

    public function storeUpdateCompanyContactPerson(
        int $companyId,
        int $personId,
        int $companyContactPersonTypeId,
        ?int $companyContactPersonId,
        ?array $historyData = []
    ): void;

    public function updateSiret(
        int $companyId,
        ?string $siret = null
    ): Company;

    public function deleteCompanyContactPersonById(int $companyContactPersonId): void;

    public function resolveTaxPeriodsDatesByOrderInYear(
        Company $company,
        Country $country,
        ?int $filterYear = null,
        ?Carbon $firstSaleDate = null
    ): Collection;
}
