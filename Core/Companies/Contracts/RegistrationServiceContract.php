<?php

namespace App\Core\Companies\Contracts;

use App\Core\Companies\Registration\DataTransfer\RegistrationTransferObject;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\User;
use Illuminate\Support\Collection;

interface RegistrationServiceContract
{
    /**
     * @param User $user
     * @param Company|null $existingCompany
     * @return RegistrationTransferObject
     */
    public function collectDataForRegistration(User $user, ?Company $existingCompany = null): RegistrationTransferObject;

    public function getRegistrationDocuments(
        int $companyCountryId,
        array $vatRegistrationCountriesIds,
        array $platformsIds,
    ): Collection;

    public function completeRegistration(User $user, string $key, ?int $partnerId = null): Company;

    /**
     * @param Company|null $company
     * @return Collection
     */
    public function getAvailableRegistrationCountriesIds(?Company $company = null): Collection;
}
