<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\OssNumberType;
use League\Fractal\TransformerAbstract;

class OssNumberTypeTransformer extends TransformerAbstract
{
    public function transform(OssNumberType $type)
    {
        return [
            'id'    => $type->id,
            'value' => $type->id,
            'label' => _l($type->lang_key),
            'name'  => _l($type->lang_key),
            'code'  => $type->code
        ];
    }
}
