<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\Currency;
use League\Fractal\TransformerAbstract;

class CurrencyTransformer extends TransformerAbstract
{
    public function transform(Currency $currency)
    {
        return [
            'id'     => $currency->id,
            'value'  => $currency->id,
            'code'   => $currency->code,
            'label'  => $currency->code_with_name,
            'name'   => $currency->name,
            'symbol' => $currency->symbol
        ];
    }
}
