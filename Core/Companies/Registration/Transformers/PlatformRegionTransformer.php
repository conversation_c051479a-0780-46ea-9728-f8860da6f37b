<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\PlatformRegion;
use League\Fractal\TransformerAbstract;

class PlatformRegionTransformer extends TransformerAbstract
{
    public function transform(PlatformRegion $platformRegion)
    {
        return [
            'id'         => $platformRegion->id,
            'platformId' => $platformRegion->platform_id,
            'name'       => _l($platformRegion->key),
            'value'      => $platformRegion->id,
            'label'      => _l($platformRegion->key),
        ];
    }
}
