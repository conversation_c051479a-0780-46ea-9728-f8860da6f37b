<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\IossNumber;
use Carbon\Carbon;
use League\Fractal\TransformerAbstract;

class IossNumberTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'issueCountry',
        'type'
    ];

    public function transform(?IossNumber $iossNumber)
    {
        return [
            'id'                    => $iossNumber->id ?? null,
            'fullNumber'            => $iossNumber->number ?? null,
            'registerDate'          => $iossNumber->register_date ?? null,
            'endDate'               => $iossNumber->end_date ?? null,
            'delete'                => false,
            'issueCountry'          => null,
            'type'                  => null,
            'numberWithoutCode'     => $iossNumber->id ? substr($iossNumber->number, 2) : null,
            'numberCode'            => $iossNumber->id ? substr($iossNumber->number, 0, 2) : null,
            'registerDateFormatted' => $iossNumber->register_date ? Carbon::parse($iossNumber->register_date)->format('d.m.Y') : null
        ];
    }

    public function includeIssueCountry(?IossNumber $iossNumber)
    {
        $country = $iossNumber->issueCountry ?? null;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new TaxCountryTransformer());
    }

    public function includeType(?IossNumber $iossNumber)
    {
        $type = $iossNumber->type ?? null;
        if (is_null($type)) {
            return null;
        }

        return $this->item($type, new IossNumberTypeTransformer());
    }
}
