<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\GenderType;
use League\Fractal\TransformerAbstract;

class GenderTypeTransformer extends TransformerAbstract
{
    public function transform(GenderType $type)
    {
        return [
            'id'    => $type->id,
            'value' => $type->id,
            'label' => _l($type->lang_key),
            'name'  => _l($type->lang_key)
        ];
    }
}
