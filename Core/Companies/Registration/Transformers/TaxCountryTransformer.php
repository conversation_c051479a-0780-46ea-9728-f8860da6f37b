<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\Country;
use League\Fractal\TransformerAbstract;

class TaxCountryTransformer extends TransformerAbstract
{
    public function transform(?Country $country)
    {
        return [
            'id'      => $country->id ?? null,
            'value'   => $country->id ?? null,
            'code'    => $country->code ?? null,
            'vatCode' => $country->vat_code ?? null,
            'name'    => $country->name ?? null,
            'label'   => $country->name ?? null
        ];
    }
}
