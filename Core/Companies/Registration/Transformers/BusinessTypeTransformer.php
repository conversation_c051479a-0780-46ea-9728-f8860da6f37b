<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\BusinessType;
use League\Fractal\TransformerAbstract;

class BusinessTypeTransformer extends TransformerAbstract
{
    public function transform(BusinessType $type)
    {
        return [
            'id'    => $type->id,
            'value' => $type->id,
            'label' => _l($type->lang_key),
            'name'  => _l($type->lang_key)
        ];
    }
}
