<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\OssNumber;
use Carbon\Carbon;
use League\Fractal\TransformerAbstract;

class OssNumberTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'issueCountry',
        'type'
    ];

    public function transform(?OssNumber $ossNumber)
    {
        return [
            'id'                    => $ossNumber->id ?? null,
            'fullNumber'            => $ossNumber->number ?? null,
            'registerDate'          => $ossNumber->register_date ?? null,
            'endDate'               => $ossNumber->end_date ?? null,
            'delete'                => false,
            'issueCountry'          => null,
            'type'                  => null,
            'numberWithoutCode'     => $ossNumber->id ? substr($ossNumber->number, 2) : null,
            'numberCode'            => $ossNumber->id ? substr($ossNumber->number, 0, 2) : null,
            'registerDateFormatted' => $ossNumber->register_date ? Carbon::parse($ossNumber->register_date)->format('d.m.Y') : null
        ];
    }

    public function includeIssueCountry(?OssNumber $ossNumber)
    {
        $country = $ossNumber->issueCountry ?? null;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new TaxCountryTransformer());
    }

    public function includeType(?OssNumber $ossNumber)
    {
        $type = $ossNumber->type ?? null;
        if (is_null($type)) {
            return null;
        }

        return $this->item($type, new OssNumberTypeTransformer());
    }
}
