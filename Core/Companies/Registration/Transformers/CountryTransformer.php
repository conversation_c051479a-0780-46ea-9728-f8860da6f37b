<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\Country;
use League\Fractal\TransformerAbstract;

class CountryTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'currency'
    ];

    public function transform(Country $country)
    {
        $inEu = !is_null($country->in_eu_start_date) && is_null($country->in_eu_end_date);

        return [
            'id'                          => $country->id,
            'value'                       => $country->id,
            'code'                        => $country->code,
            'vatCode'                     => $country->vat_code,
            'name'                        => $country->name,
            'label'                       => $country->name,
            'inEu'                        => $inEu,
            'doingTax'                    => $country->doing_tax,
            'hasWarehouse'                => $country->has_fba_warehouse,
            // Default value
            'currency'                    => null,
            'hadSales'                    => false,
            'dateOfFirstSale'             => null,
            'estimatedSalesInCurrentYear' => null,
            'estimatedSalesInNextYear'    => null,
            'previouslyRegistered'        => false
        ];
    }

    public function includeCurrency(Country $country)
    {
        if (is_null($country->currency)) {
            return null;
        }

        return $this->item($country->currency, new CurrencyTransformer());
    }
}
