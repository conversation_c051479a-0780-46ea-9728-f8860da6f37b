<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\IossNumberType;
use League\Fractal\TransformerAbstract;

class IossNumberTypeTransformer extends TransformerAbstract
{
    public function transform(IossNumberType $type)
    {
        return [
            'id'    => $type->id,
            'value' => $type->id,
            'label' => _l($type->lang_key),
            'name'  => _l($type->lang_key),
            'code'  => $type->code
        ];
    }
}
