<?php

namespace App\Core\Companies\Registration\Transformers;

use App\Core\Data\Models\Platform;
use League\Fractal\TransformerAbstract;

class PlatformTransformer extends TransformerAbstract
{
    protected array $defaultIncludes = [
        'platformRegions'
    ];

    public function transform(Platform $platform)
    {
        return [
            'id'    => $platform->id,
            'name'  => _l($platform->name),
            'value' => $platform->id,
            'label' => _l($platform->name),
        ];
    }

    public function includePlatformRegions(Platform $platform)
    {
        return $this->collection($platform->platformRegions, new PlatformRegionTransformer());
    }
}
