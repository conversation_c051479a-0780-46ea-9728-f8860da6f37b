<?php

namespace App\Core\Companies\Registration;

use App\Core\Companies\Registration\RegistrationData\BankDetails;
use App\Core\Companies\Registration\RegistrationData\General;
use App\Core\Companies\Registration\RegistrationData\HelperData;
use App\Core\Companies\Registration\RegistrationData\Main;
use App\Core\Companies\Registration\RegistrationData\Payment;
use App\Core\Companies\Registration\RegistrationData\PlatformsAndSales;
use App\Core\Companies\Registration\RegistrationData\RequiredDocuments;
use App\Core\Companies\Registration\RegistrationData\ShareholdersAuthorisedPersonData;
use App\Core\Data\Models\Company;
use App\Core\Data\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Throwable;

class Registration implements Arrayable, Jsonable
{
    private User $user;
    private ?Company $existingCompany;
    private ?array $externallyAddedSalesChannel;

    public function __construct(User $user, ?Company $existingCompany = null)
    {
        $this->user = $user;
        $this->existingCompany = $existingCompany;
        $this->setExternallyAddedSalesChannel();
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'helperData'                       => $this->getHelperData()->toArray(),
            'main'                             => $this->getMain()->toArray(),
            'general'                          => $this->getGeneral()->toArray(),
            'shareholdersAuthorisedPersonData' => $this->getShareholdersAuthorisedPersonData()->toArray(),
            'bankDetails'                      => $this->getBankDetails()->toArray(),
            'platformsAndSales'                => $this->getPlatformsAndSales()->toArray(),
            'requiredDocuments'                => $this->getRequiredDocuments()->toArray(),
            'payment'                          => $this->getPayment()->toArray(),
        ];
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }

    private function getHelperData(): HelperData
    {
        return new HelperData($this->existingCompany, $this->externallyAddedSalesChannel);
    }

    private function getMain(): Main
    {
        return new Main($this->existingCompany);
    }

    private function getGeneral(): General
    {
        return new General();
    }

    private function getShareholdersAuthorisedPersonData(): ShareholdersAuthorisedPersonData
    {
        return new ShareholdersAuthorisedPersonData();
    }

    private function getBankDetails(): BankDetails
    {
        return new BankDetails();
    }

    private function getPlatformsAndSales(): PlatformsAndSales
    {
        return new PlatformsAndSales($this->externallyAddedSalesChannel);
    }

    private function getRequiredDocuments(): RequiredDocuments
    {
        return new RequiredDocuments();
    }

    private function getPayment(): Payment
    {
        return new Payment();
    }

    private function setExternallyAddedSalesChannel(): void
    {
        $preferences = $this->user->preferences ?? [];
        $externallyAddedSalesChannel = $preferences['registrationState'] ?? null;
        if (!is_null($externallyAddedSalesChannel)) {
            try {
                $externallyAddedSalesChannel = base64_decode($externallyAddedSalesChannel);
                $externallyAddedSalesChannel = decrypt($externallyAddedSalesChannel);
                $externallyAddedSalesChannel = json_decode($externallyAddedSalesChannel, true);
            } catch (Throwable) {
                $externallyAddedSalesChannel = null;
            }
        }

        $this->externallyAddedSalesChannel = $externallyAddedSalesChannel;
    }
}
