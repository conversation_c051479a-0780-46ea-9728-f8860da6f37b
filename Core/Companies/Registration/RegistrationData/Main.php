<?php

namespace App\Core\Companies\Registration\RegistrationData;

use App\Core\Companies\Registration\Transformers\CountryTransformer;
use App\Core\Data\Models\Company;
use Illuminate\Contracts\Support\Arrayable;

class Main implements Arrayable
{
    private ?Company $existingCompany;

    public function __construct(?Company $existingCompany = null)
    {
        $this->existingCompany = $existingCompany;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'country'                         => $this->getCountry(),
            'selectedCountries'               => [],
            'vatNumbers'                      => [],
            'selectedPlatforms'               => [],
            'ossRegistration'                 => false,
            'ossRegistrationSelectedCountry'  => null,
            'ossNumber'                       => null,
            'iossNumber'                      => null,
            'iossRegistration'                => false,
            'iossRegistrationSelectedCountry' => null,
            'emptyVatNumber'                  => $this->getEmptyVatNumber(),
        ];
    }

    private function getCountry(): ?array
    {
        if (is_null($this->existingCompany)) {
            return null;
        }

        return transform_data($this->existingCompany->address->country, new CountryTransformer())->toArray();
    }

    private function getEmptyVatNumber(): array
    {
        return [
            'id'                  => null,
            'number'              => null,
            'country'             => null,
            'vatRegistrationDate' => null,
            'validated'           => false,
            'valid'               => false,
            'api'                 => false,
            'existsInDb'          => false,
            'dbVatNumber'         => null,
        ];
    }
}
