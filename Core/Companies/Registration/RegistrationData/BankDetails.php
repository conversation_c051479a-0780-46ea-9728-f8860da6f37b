<?php

namespace App\Core\Companies\Registration\RegistrationData;

use Illuminate\Contracts\Support\Arrayable;

class BankDetails implements Arrayable
{
    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'name'                => null,
            'branch'              => null,
            'beneficiary'         => null,
            'ibanOrAccountNumber' => null,
            'swiftOrBic'          => null,
            'ibanValid'           => true,
            'ibanValidated'       => false,
            'bankDetailsSkipped'  => false,
        ];
    }
}

