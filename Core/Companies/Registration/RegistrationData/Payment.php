<?php

namespace App\Core\Companies\Registration\RegistrationData;

use Illuminate\Contracts\Support\Arrayable;

class Payment implements Arrayable
{
    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'selectedPlan'              => null,
            'vatRegistration'           => null,
            'vatRegistrationMultiplier' => 0,
            'ossRegistration'           => null,
            'iossRegistration'          => null,
            'total'                     => 0,
            'paymentDetails'            => $this->getPaymentDetails()
        ];
    }

    private function getPaymentDetails(): array
    {
        return [
            'companyName'     => null,
            'city'            => null,
            'state'           => null,
            'zip'             => null,
            'country'         => null,
            'street'          => null,
            'houseNo'         => null,
            'email'           => null,
            'selectedCountry' => null,
            'vatNumber'       => null,
            'promoCode'       => null,
            'paymentType'     => 'card'
        ];
    }
}
