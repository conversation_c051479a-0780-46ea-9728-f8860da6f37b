<?php

namespace App\Core\Companies\Registration\RegistrationData;

use App\Core\Data\Models\Platform;
use Illuminate\Contracts\Support\Arrayable;

class PlatformsAndSales implements Arrayable
{
    private ?array $externallyAddedSalesChannel;

    public function __construct(?array $externallyAddedSalesChannel = null)
    {
        $this->externallyAddedSalesChannel = $externallyAddedSalesChannel;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'mode'                 => 'marketplacesShops',
            'marketplacesValid'    => false,
            'salesValid'           => false,
            'marketplacesAndShops' => $this->getMarketplacesAndShops(),
            'sales'                => $this->getSales()
        ];
    }

    private function getMarketplacesAndShops(): array
    {
        $salesChannels = [];
        if (!is_null($this->externallyAddedSalesChannel)) {
            $salesChannels[] = $this->getSalesChannel($this->externallyAddedSalesChannel);
        }

        return [
            'salesChannels'     => $salesChannels,
            'provideServices'   => false,
            'sellDigitalGoods'  => false,
            'sellGoods'         => false,
            'selectedServices'  => [],
            'goodsStoredIn'     => null,
            'other'             => false,
            'selectedCountries' => [],
            'warehouses'        => [],
            'emptyWarehouse'    => $this->getEmptyWarehouse(),
            'emptySalesChannel' => $this->getEmptySalesChannel(),
        ];
    }

    private function getEmptyWarehouse(): array
    {
        return [
            'index'                      => null,
            'uid'                        => null,
            'uidValidated'               => false,
            'uidValid'                   => false,
            'name'                       => null,
            'otherWarehouseOperatorName' => null,
            'country'                    => null,
            'street'                     => null,
            'streetNo'                   => null,
            'city'                       => null,
            'postalCode'                 => null,
            'county'                     => null,
            'state'                      => null,
            'valid'                      => false,
        ];
    }

    private function getEmptySalesChannel(): array
    {
        return $this->getSalesChannel();
    }

    private function getSalesChannel(?array $externallyAddedSalesChannel = null): array
    {
        $index = null;
        $accountName = null;
        $amazonRefreshToken = null;
        $shopifyAccessToken = null;
        $platform = null;
        $uaid = null;
        $deletable = true;
        $editable = true;
        $valid = false;
        $validated = false;

        if (!is_null($externallyAddedSalesChannel)) {
            $platformId = $externallyAddedSalesChannel['platformId'] ?? null;
            if ($platformId === Platform::PLATFORM_SHOPIFY) {
                $index = 0;
                $accountName = $externallyAddedSalesChannel['accountName'];
                $shopifyAccessToken = $externallyAddedSalesChannel['shopifyToken'] ?? null;
                $platform = ['id' => $platformId];
                $uaid = $externallyAddedSalesChannel['uaid'];
                $valid = true;
                $validated = true;
                $deletable = false;
                $editable = false;
            }
        }

        return [
            'index'              => $index,
            'id'                 => null,
            'accountName'        => $accountName,
            'amazonRefreshToken' => $amazonRefreshToken,
            'shopifyAccessToken' => $shopifyAccessToken,
            'companyId'          => null,
            'currency'           => null,
            'platform'           => $platform,
            'platformRegion'     => null,
            'uaid'               => $uaid,
            'url'                => null,
            'valid'              => $valid,
            'validated'          => $validated,
            'deletable'          => $deletable,
            'editable'           => $editable,
            'additionalData'     => $externallyAddedSalesChannel
        ];
    }

    private function getSales(): array
    {
        return [
            'areManufacturers'       => false,
            'haveGoodsSupplier'      => false,
            'goodsImportedFrom'      => [],
            'selectedSalesCountries' => [],
        ];
    }
}
