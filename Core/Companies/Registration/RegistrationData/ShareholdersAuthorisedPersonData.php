<?php

namespace App\Core\Companies\Registration\RegistrationData;

use Illuminate\Contracts\Support\Arrayable;

class ShareholdersAuthorisedPersonData implements Arrayable
{
    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'mode'                              => 'shareholdersInfo',
            'shareholdersValid'                 => false,
            'shareholdersSkipped'               => false,
            'authorisedPersonValid'             => false,
            'authorisedPersonSameAsShareholder' => false,
            'authorisedPersonSkipped'           => false,
            'authorisedPerson'                  => $this->getAuthorizedPerson(),
            'emptyShareholder'                  => $this->getEmptyShareholder(),
            'shareholders'                      => []
        ];
    }

    private function getAuthorizedPerson(): array
    {
        return [

            'name'                   => null,
            'lastName'               => null,
            'shareOfCapital'         => null,
            'birthday'               => null,
            'address'                => null,
            'houseNumber'            => null,
            'additionToAddress'      => null,
            'city'                   => null,
            'stateProvince'          => null,
            'zipPostalCode'          => null,
            'country'                => null,
            'placeOfBirth'           => null,
            'birthCountry'           => null,
            'nationality'            => null,
            'gender'                 => null,
            'identificationDocument' => 'passport',
            'idCard'                 => $this->getEmptyDocumentData(),
            'passport'               => $this->getEmptyDocumentData(),
        ];
    }

    private function getEmptyShareholder(): array
    {
        return [
            'index'     => null,
            'isCompany' => true,
            'company'   => $this->getCompany(),
            'person'    => $this->getPerson(),
        ];
    }

    private function getCompany(): array
    {
        return [
            'name'              => null,
            'shareOfCapital'    => null,
            'incorporationDate' => null,
            'address'           => null,
            'houseNumber'       => null,
            'additionToAddress' => null,
            'city'              => null,
            'stateProvince'     => null,
            'zipPostalCode'     => null,
            'country'           => null,
        ];
    }

    private function getPerson(): array
    {
        return [
            'name'                   => null,
            'lastName'               => null,
            'shareOfCapital'         => null,
            'birthday'               => null,
            'address'                => null,
            'houseNumber'            => null,
            'additionToAddress'      => null,
            'city'                   => null,
            'stateProvince'          => null,
            'zipPostalCode'          => null,
            'country'                => null,
            'placeOfBirth'           => null,
            'birthCountry'           => null,
            'nationality'            => null,
            'gender'                 => null,
            'identificationDocument' => 'passport',
            'idCard'                 => $this->getEmptyDocumentData(),
            'passport'               => $this->getEmptyDocumentData(),
        ];
    }

    private function getEmptyDocumentData(): array
    {
        return [
            'number'     => null,
            'country'    => null,
            'issuedBy'   => null,
            'validFrom'  => null,
            'validUntil' => null,
        ];
    }
}
