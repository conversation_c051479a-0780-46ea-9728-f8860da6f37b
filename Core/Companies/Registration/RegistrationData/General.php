<?php

namespace App\Core\Companies\Registration\RegistrationData;

use Illuminate\Contracts\Support\Arrayable;

class General implements Arrayable
{
    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'mode'    => 'basic',
            'basic'   => $this->getBasic(),
            'contact' => $this->getContact(),
        ];
    }

    private function getBasic(): array
    {
        return [
            'partnerCode'          => null,
            'partnerCodeValidated' => false,
            'partnerCodeValid'     => false,
            'businessType'         => null,
            'fullLegalName'        => null,
            'legalEntityType'      => null,
            'businessActivity'     => null,
            'registrationNumber'   => null,
            'incorporationDate'    => null,
            'valueOfShareCapital'  => null,
            'valid'                => false,
            'currency'             => null,
        ];
    }

    private function getContact(): array
    {
        return [
            'companyAddress'    => null,
            'houseNumber'       => null,
            'additionToAddress' => null,
            'city'              => null,
            'stateProvince'     => null,
            'zipPostalCode'     => null,
            'valid'             => false,
            'contactEmail'      => null
        ];
    }
}
