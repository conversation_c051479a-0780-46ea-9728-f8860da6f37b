<?php

namespace App\Core\Companies\Registration\RegistrationData;

use App\Core\Data\Models\Company;
use Illuminate\Contracts\Support\Arrayable;

class HelperData implements Arrayable
{
    private ?Company $company;
    private ?array $externallyAddedSalesChannel;

    public function __construct(?Company $company = null, ?array $externallyAddedSalesChannel = null)
    {
        $this->company = $company;
        $this->externallyAddedSalesChannel = $externallyAddedSalesChannel;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        $mode = null;
        $additionalRegistration = false;
        $isCompanyPreloaded = false;
        if (!is_null($this->company)) {
            $mode = 'yesBut';
            $additionalRegistration = true;
            $isCompanyPreloaded = true;
        }

        return [
            'step'                        => $this->getInitialStep(),
            'mode'                        => $mode,
            'additionalRegistration'      => $additionalRegistration,
            'autoSaveId'                  => null,
            'companyId'                   => $this->company?->id,
            'isCompanyPreloaded'          => $isCompanyPreloaded,
            'autoSaveEnabled'             => false,
            'paymentEnabled'              => false,
            'externallyAddedSalesChannel' => $this->externallyAddedSalesChannel,
            'steps'                       => $this->getSteps(),
            'noShareholderIds'            => $this->getNoShareholderIds(),
            'main'                        => $this->getMain()
        ];
    }

    private function getInitialStep(): array
    {
        return $this->getSteps()[1];
    }

    private function getNoShareholderIds(): array
    {
        return [3, 4, 5, 6];
    }

    private function getMain(): array
    {
        return [
            'searchVatNumberKeyword' => null,
            'searchVatNumbers'       => [],
            'vatSearchCompleted'     => false
        ];
    }

    private function getSteps(): array
    {
        return [
            1 => [
                'sequence' => 1,
                'title'    => _l('register-company.Welcome'),
                'enabled'  => true,
                'done'     => false,
            ],
            2 => [
                'sequence' => 2,
                'title'    => _l('register-company.General'),
                'enabled'  => is_null($this->company),
                'done'     => false,
            ],
            3 => [
                'sequence' => 3,
                'title'    => _l('register-company.Business Owner'),
                'enabled'  => is_null($this->company),
                'done'     => false,
            ],
            4 => [
                'sequence' => 4,
                'title'    => _l('register-company.Banking details'),
                'enabled'  => is_null($this->company),
                'done'     => false,
            ],
            5 => [
                'sequence' => 5,
                'title'    => _l('register-company.E-Commerce platforms'),
                'enabled'  => true,
                'done'     => false,
            ],
            6 => [
                'sequence' => 6,
                'title'    => _l('register-company.Documents'),
                'enabled'  => true,
                'done'     => false,
            ],
            7 => [
                'sequence' => 7,
                'title'    => _l('register-company.Pricing'),
                'enabled'  => true,
                'done'     => false,
            ],
        ];
    }
}
