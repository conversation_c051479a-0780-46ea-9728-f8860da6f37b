<?php

namespace App\Core\Companies\Registration\DataTransfer;

use App\Core\Common\Common\DataTransfer\DataTransferObject;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Common\Common\Transformers\TransformedObject;
use App\Core\Companies\Registration\Transformers\BusinessTypeTransformer;
use App\Core\Companies\Registration\Transformers\CountryTransformer;
use App\Core\Companies\Registration\Transformers\CurrencyTransformer;
use App\Core\Companies\Registration\Transformers\GenderTypeTransformer;
use App\Core\Companies\Registration\Transformers\IossNumberTransformer;
use App\Core\Companies\Registration\Transformers\IossNumberTypeTransformer;
use App\Core\Companies\Registration\Transformers\OssNumberTransformer;
use App\Core\Companies\Registration\Transformers\OssNumberTypeTransformer;
use App\Core\Companies\Registration\Transformers\PlatformTransformer;
use App\Core\Companies\Registration\Transformers\ProductTypeTransformer;
use App\Core\Data\Models\IossNumber;
use App\Core\Data\Models\OssNumber;
use Illuminate\Support\Collection;

/**
 * @property TransformedObjectContract $countries
 * @property TransformedObjectContract $businessTypes
 * @property TransformedObjectContract $currencies
 * @property TransformedObjectContract $platforms
 * @property TransformedObjectContract $productTypes
 * @property TransformedObjectContract $allOssNumberTypes
 * @property TransformedObjectContract $genderTypes
 * @property TransformedObjectContract $allIossNumberTypes
 * @property TransformedObjectContract $registrationCountries
 * @property TransformedObjectContract $emptyOssNumberModel
 * @property TransformedObjectContract $emptyIossNumberModel
 * @property array $routes
 * @property array $registration
 * @property array|null $autoSave
 */
class RegistrationTransferObject extends DataTransferObject
{
    public function getCountries(): TransformedObject
    {
        return $this->countries;
    }

    public function setCountries(Collection $countries): RegistrationTransferObject
    {
        $this->countries = transform_data($countries, new CountryTransformer());

        return $this;
    }

    public function getBusinessTypes(): TransformedObject
    {
        return $this->businessTypes;
    }

    public function setBusinessTypes(Collection $businessTypes): RegistrationTransferObject
    {
        $this->businessTypes = transform_data($businessTypes, new BusinessTypeTransformer());

        return $this;
    }

    public function getCurrencies(): TransformedObject
    {
        return $this->currencies;
    }

    public function setCurrencies(Collection $currencies): RegistrationTransferObject
    {
        $this->currencies = transform_data($currencies, new CurrencyTransformer());

        return $this;
    }

    public function getPlatforms(): TransformedObject
    {
        return $this->platforms;
    }

    public function setPlatforms(Collection $platforms): RegistrationTransferObject
    {
        $this->platforms = transform_data($platforms, new PlatformTransformer());

        return $this;
    }

    public function getGenderTypes(): TransformedObject
    {
        return $this->genderTypes;
    }

    public function setGenderTypes(Collection $genderTypes): RegistrationTransferObject
    {
        $this->genderTypes = transform_data($genderTypes, new GenderTypeTransformer());

        return $this;
    }

    public function getProductTypes(): TransformedObject
    {
        return $this->productTypes;
    }

    public function setProductTypes(Collection $productTypes): RegistrationTransferObject
    {
        $this->productTypes = transform_data($productTypes, new ProductTypeTransformer());

        return $this;
    }

    public function getAllOssNumberTypes(): TransformedObject
    {
        return $this->allOssNumberTypes;
    }

    public function setAllOssNumberTypes(Collection $allOssNumberTypes): RegistrationTransferObject
    {
        $this->allOssNumberTypes = transform_data($allOssNumberTypes, new OssNumberTypeTransformer());

        return $this;
    }

    public function getAllIossNumberTypes(): TransformedObject
    {
        return $this->allIossNumberTypes;
    }

    public function setAllIossNumberTypes(Collection $allIossNumberTypes): RegistrationTransferObject
    {
        $this->allIossNumberTypes = transform_data($allIossNumberTypes, new IossNumberTypeTransformer());

        return $this;
    }

    public function getRegistrationCountries(): TransformedObject
    {
        return $this->registrationCountries;
    }

    public function setRegistrationCountries(Collection $registrationCountries): RegistrationTransferObject
    {
        $this->registrationCountries = transform_data($registrationCountries, new CountryTransformer());

        return $this;
    }

    public function getEmptyOssNumberModel(): TransformedObject
    {
        return $this->emptyOssNumberModel;
    }

    public function setEmptyOssNumberModel(OssNumber $emptyOssNumberModel): RegistrationTransferObject
    {
        $this->emptyOssNumberModel = transform_data($emptyOssNumberModel, new OssNumberTransformer());

        return $this;
    }

    public function getEmptyIossNumberModel(): TransformedObject
    {
        return $this->emptyIossNumberModel;
    }

    public function setEmptyIossNumberModel(IossNumber $emptyIossNumberModel): RegistrationTransferObject
    {
        $this->emptyIossNumberModel = transform_data($emptyIossNumberModel, new IossNumberTransformer());

        return $this;
    }

    public function getRoutes(): array
    {
        return $this->routes;
    }

    public function getRoutesJson(): string
    {
        return json_encode($this->getRoutes());
    }

    public function setRoutes(array $routes): RegistrationTransferObject
    {
        $this->routes = $routes;

        return $this;
    }

    public function setInitialRegistrationData(array $registration): RegistrationTransferObject
    {
        $this->registration = $registration;

        return $this;
    }

    public function getInitialRegistrationData(): array
    {
        return $this->registration;
    }

    public function getInitialRegistrationDataJson(): string
    {
        return evat_json_encode($this->getInitialRegistrationData());
    }

    public function setRegistrationData(?array $autoSave): RegistrationTransferObject
    {
        $this->autoSave = $autoSave;

        return $this;
    }

    public function getRegistrationData(): ?array
    {
        return $this->autoSave;
    }

    public function getRegistrationJson(): string
    {
        return evat_json_encode($this->getRegistrationData());
    }
}
