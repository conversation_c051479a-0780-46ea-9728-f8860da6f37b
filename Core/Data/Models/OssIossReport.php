<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * App\Core\Data\Models\OssIossReport
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property int $status_id
 * @property string $date_from
 * @property string $date_to
 * @property array|null $data
 * @property string $created_at
 * @property string $type
 * @property string $payment_reference_number
 * @property-read \App\Core\Data\Models\Company|null $company
 * @property-read \App\Core\Data\Models\Country|null $country
 * @property-read \App\Core\Data\Models\Invoice|null $firstIossInvoice
 * @property-read \App\Core\Data\Models\Invoice|null $firstOssInvoice
 * @property-read \App\Core\Data\Models\Invoice|null $firstVoesInvoice
 * @property-read \App\Core\Data\Models\ReportStatus $status
 * @method static Builder|OssIossReport newModelQuery()
 * @method static Builder|OssIossReport newQuery()
 * @method static Builder|OssIossReport query()
 * @method static Builder|OssIossReport whereCompanyId($value)
 * @method static Builder|OssIossReport whereCountryId($value)
 * @method static Builder|OssIossReport whereCreatedAt($value)
 * @method static Builder|OssIossReport whereData($value)
 * @method static Builder|OssIossReport whereDateFrom($value)
 * @method static Builder|OssIossReport whereDateTo($value)
 * @method static Builder|OssIossReport whereId($value)
 * @method static Builder|OssIossReport wherePaymentReferenceNumber($value)
 * @method static Builder|OssIossReport whereStatusId($value)
 * @method static Builder|OssIossReport whereType($value)
 * @mixin \Eloquent
 */
class OssIossReport extends Model
{
    protected $table = 'oss_ioss_reports';

    public const TYPE_OSS = 'oss';
    public const TYPE_IOSS = 'ioss';
    public const TYPE_VOES = 'voes';

    public const OSS_IOSS_TYPES_DATA = [
        self::TYPE_OSS  => [
            'schemeType'              => 'MOSS',
            'periodKey'               => 'Quarter',
            'identificationNumberKey' => 'VATNumber',
            'types'                   => [
                InvoiceSubtype::EU_B2C_SALES_INVOICE,
                InvoiceSubtype::CN_EU_B2C_SALES_INVOICE
            ],
            'itemTypes'               => [
                ItemType::GOODS,
                ItemType::DIGITAL_GOODS,
                ItemType::SERVICES,
                ItemType::SHIPPING,
                ItemType::PACKAGING,
            ]
        ],
        self::TYPE_IOSS => [
            'schemeType'              => 'IMPORT',
            'periodKey'               => 'Month',
            'identificationNumberKey' => 'IOSSNumber',
            'types'                   => [
                InvoiceSubtype::IOSS_EXPORT_INVOICE,
                InvoiceSubtype::CN_IOSS_EXPORT_INVOICE
            ],
            'itemTypes'               => [
                ItemType::GOODS,
                ItemType::SHIPPING,
                ItemType::PACKAGING,
            ]
        ],
        self::TYPE_VOES => [
            'schemeType'              => 'VOES',
            'periodKey'               => 'Quarter',
            'identificationNumberKey' => 'VATNumber',
            'types'                   => [
                InvoiceSubtype::EU_B2C_SALES_INVOICE,
                InvoiceSubtype::CN_EU_B2C_SALES_INVOICE
            ],
            'itemTypes'               => [
                ItemType::DIGITAL_GOODS,
                ItemType::SERVICES,
            ]
        ],
    ];

    public function getChangedPaymentDue(): ?float
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $vatDue = $data['payment_due'] ?? null;
        if (is_null($vatDue)) {
            return null;
        }

        return (float)$vatDue;
    }

    public function setChangedPaymentDue(float $vatDue): void
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $data['payment_due'] = $vatDue;
        $data = evat_json_encode($data);
        $this->data = $data;
    }

    public static function resolveSchemeData(string $scheme): array
    {
        $scheme = strtolower($scheme);
        $schemeData = self::OSS_IOSS_TYPES_DATA;
        if (!array_key_exists($scheme, $schemeData)) {
            $scheme = self::TYPE_OSS;
        }

        return $schemeData[$scheme];
    }

    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    /** @noinspection PhpUnused */
    public function status(): BelongsTo
    {
        return $this->belongsTo(ReportStatus::class, 'status_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getDataAttribute(): ?array
    {
        $data = $this->getRawOriginal('data');
        if (is_null($data)) {
            return null;
        }

        return json_decode($data, true);
    }

    public function setVatDue(string|float $vatDue): void
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $data['calculation']['vatDue'] = $vatDue;
        $this->data = evat_json_encode($data);
    }

    public function vatDueUpdateNeeded(string|float $vatDue): bool
    {
        $oldVatDue = $this->getLatestVatDue();
        if (is_null($oldVatDue)) {
            return true;
        }

        return $oldVatDue !== (float)$vatDue;
    }

    public function getLatestVatDue(): ?float
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);

        $vatDue = $data['calculation']['vatDue'] ?? null;
        if (is_null($vatDue)) {
            return null;
        }

        return (float)$vatDue;
    }

    /** @noinspection PhpUnused */
    public function firstOssInvoice(): HasOneThrough
    {
        $type = self::TYPE_OSS;
        $subtypes = self::OSS_IOSS_TYPES_DATA[$type]['types'];
        $itemTypes = self::OSS_IOSS_TYPES_DATA[$type]['itemTypes'];

        return $this->hasOneThrough(Invoice::class, self::class, 'id', 'company_id', 'id', 'company_id')
            ->select('invoices.*')
            ->join('invoice_items', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->whereColumn('invoices.invoice_date', '>=', 'oss_ioss_reports.date_from')
            ->whereColumn('invoices.invoice_date', '<=', 'oss_ioss_reports.date_to')
            ->where('oss_ioss_reports.type', $type)
            ->whereIn('invoices.invoice_subtype_id', $subtypes)
            ->whereIn('invoice_items.invoice_item_type_id', $itemTypes)
            ->orderBy('invoices.invoice_date')
            ->groupBy(
                'invoices.id',
                'invoices.invoice_date',
                'oss_ioss_reports.id'
            );
    }

    /** @noinspection PhpUnused */
    public function firstVoesInvoice(): HasOneThrough
    {
        $type = self::TYPE_VOES;
        $subtypes = self::OSS_IOSS_TYPES_DATA[$type]['types'];
        $itemTypes = self::OSS_IOSS_TYPES_DATA[$type]['itemTypes'];

        return $this->hasOneThrough(Invoice::class, self::class, 'id', 'company_id', 'id', 'company_id')
            ->select('invoices.*')
            ->join('invoice_items', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->whereColumn('invoices.invoice_date', '>=', 'oss_ioss_reports.date_from')
            ->whereColumn('invoices.invoice_date', '<=', 'oss_ioss_reports.date_to')
            ->where('oss_ioss_reports.type', $type)
            ->whereIn('invoices.invoice_subtype_id', $subtypes)
            ->whereIn('invoice_items.invoice_item_type_id', $itemTypes)
            ->orderBy('invoices.invoice_date')
            ->groupBy(
                'invoices.id',
                'invoices.invoice_date',
                'oss_ioss_reports.id'
            );
    }

    /** @noinspection PhpUnused */
    public function firstIossInvoice(): HasOneThrough
    {
        $type = self::TYPE_IOSS;
        $subtypes = self::OSS_IOSS_TYPES_DATA[$type]['types'];
        $itemTypes = self::OSS_IOSS_TYPES_DATA[$type]['itemTypes'];

        return $this->hasOneThrough(Invoice::class, self::class, 'id', 'company_id', 'id', 'company_id')
            ->select('invoices.*')
            ->join('invoice_items', 'invoice_items.invoice_id', '=', 'invoices.id')
            ->whereColumn('invoices.invoice_date', '>=', 'oss_ioss_reports.date_from')
            ->whereColumn('invoices.invoice_date', '<=', 'oss_ioss_reports.date_to')
            ->where('oss_ioss_reports.type', $type)
            ->whereIn('invoices.invoice_subtype_id', $subtypes)
            ->whereIn('invoice_items.invoice_item_type_id', $itemTypes)
            ->orderBy('invoices.invoice_date')
            ->groupBy(
                'invoices.id',
                'invoices.invoice_date',
                'oss_ioss_reports.id'
            );
    }
}
