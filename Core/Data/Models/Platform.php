<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Platform
 *
 * @property int $id
 * @property string $name
 * @property int $platform_type_id
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Marketplace> $marketplaces
 * @property int|null $marketplaces_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\PlatformRegion> $platformRegions
 * @property int|null $platform_regions_count
 * @property \App\Core\Data\Models\PlatformType|null $platformType
 * @method static Builder|Platform newModelQuery()
 * @method static Builder|Platform newQuery()
 * @method static Builder|Platform query()
 * @method static Builder|Platform whereId($value)
 * @method static Builder|Platform whereName($value)
 * @method static Builder|Platform wherePlatformTypeId($value)
 * @mixin \Eloquent
 */
class Platform extends Model
{
    protected $table = 'platforms';

    const PLATFORM_ALL = 0;
    const PLATFORM_AMAZON = 1;
    const PLATFORM_EBAY = 2;
    const PLATFORM_SHOPIFY = 3;
    const PLATFORM_WEBSHOP = 4;
    const PLATFORM_ALIEXPRESS = 5;
    const PLATFORM_ALIBABA = 6;
    const PLATFORM_WISH = 7;
    const PLATFORM_ETSY = 8;
    const PLATFORM_ZALANDO = 9;
    const PLATFORM_OFFLINE = 10;
    const PLATFORM_BRICK_AND_MORTAR = 11;
    const PLATFORM_GALILEO_PROTOCOL = 12;

    public function marketplaces(): HasMany
    {
        return $this->hasMany(Marketplace::class, 'platform_id', 'id')
            ->orderBy('name');
    }

    public function platformRegions(): HasMany
    {
        return $this->hasMany(PlatformRegion::class, 'platform_id', 'id');
    }

    public function platformType(): HasOne
    {
        return $this->hasOne(PlatformType::class, 'id', 'platform_type_id');
    }
}
