<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\ItemCodeCategory
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ItemTaxCode> $itemTaxCodes
 * @property int|null $item_tax_codes_count
 * @method static Builder|ItemCodeCategory newModelQuery()
 * @method static Builder|ItemCodeCategory newQuery()
 * @method static Builder|ItemCodeCategory query()
 * @method static Builder|ItemCodeCategory whereId($value)
 * @method static Builder|ItemCodeCategory whereName($value)
 * @mixin \Eloquent
 */
class ItemCodeCategory extends Model
{
    protected $table = 'item_code_categories';

    const GENERAL = 1;
    const BOOKS_PRINTED_MATERIALS = 2;
    const CLOTHING = 3;
    const FOOD = 4;
    const HEALTH_BEAUTY_AND_PERSONAL_CARE = 5;
    const OUTDOOR = 6;
    const INFANT_AND_BABY_SUPPLIES = 7;
    const OUTSIDE_THE_SCOPE_OF_VAT = 8;
    const TELECOMMUNICATION_SERVICES = 9;
    const BROADCASTING_SERVICES = 10;
    const E_SERVICES = 11;
    const COLLECTIBLES = 12;
    const COMPUTER = 13;
    const HOUSEHOLD = 14;
    const NEWSPAPER = 15;
    const PUBLICATIONS = 16;
    const SUBSCRIPTIONS = 17;
    const WARRANTIES = 18;
    const SCHOOL = 19;
    const INSTALLATION = 20;
    const SPORT = 21;
    const PASSENGER_TRANSPORT = 22;
    const IMMOVABLE_PROPERTY = 23;
    const GENERAL_SERVICES = 24;
    const PHOTOVOLTAIC_SYSTEM = 25;

    public function itemTaxCodes(): HasMany
    {
        return $this->hasMany(ItemTaxCode::class, 'item_code_category_id', 'id')
            ->orderBy('description');
    }
}
