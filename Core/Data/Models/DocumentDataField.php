<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionClassConstant;

/**
 * App\Core\Data\Models\DocumentDataField
 *
 * @property int $id
 * @property string $name
 * @property bool $user_input
 * @property string $data_type
 * @property int $sequence
 * @property string $key
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField whereDataType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField whereSequence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentDataField whereUserInput($value)
 * @mixin \Eloquent
 */
class DocumentDataField extends Model
{
    protected $table = 'document_data_fields';

    public const FIELD_NUMBER = 1;
    public const FIELD_DESCRIPTION = 2;
    public const FIELD_PAYMENT_DATE = 3;
    public const FIELD_PAYMENT_REFERENCE_NUMBER = 4;
    public const FIELD_AMOUNT = 5;
    public const FIELD_COUNTRY = 6;
    public const FIELD_PAYMENT_DUE_DATE = 8;
    public const FIELD_RESPONSE_DEADLINE_DATE = 9;
    public const FIELD_EXPIRATION_DATE = 10;
    public const FIELD_ISSUE_DATE = 11;

    // DATA TYPES
    public const DATA_TYPE_TEXT = 'TEXT';
    public const DATA_TYPE_INT = 'INT';
    public const DATA_TYPE_FLOAT = 'FLOAT';
    public const DATA_TYPE_ARRAY = 'ARRAY';
    public const DATA_TYPE_OBJECT = 'OBJECT';
    public const DATA_TYPE_BOOL = 'BOOL';
    public const DATA_TYPE_DATE = 'DATE';

    public static function getDataTypes(): array
    {
        $self = new ReflectionClass(new static());

        $constants = $self->getConstants(ReflectionClassConstant::IS_PUBLIC);

        return array_filter($constants, function (mixed $constant, mixed $key) {
            return Str::startsWith($key, 'DATA_TYPE_');
        }, ARRAY_FILTER_USE_BOTH);
    }
}
