<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\DocumentCategoryTypeCompanyCountries
 *
 * @property int $id
 * @property int $document_category_type_id
 * @property int $company_origin_country_id
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryTypeCompanyCountries newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryTypeCompanyCountries newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryTypeCompanyCountries query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryTypeCompanyCountries whereCompanyOriginCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryTypeCompanyCountries whereDocumentCategoryTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryTypeCompanyCountries whereId($value)
 * @mixin \Eloquent
 */
class DocumentCategoryTypeCompanyCountries extends Model
{
    protected $table = 'document_category_type_company_countries';

    public function country()
    {
        return $this->belongsTo(Country::class, 'company_origin_country_id', 'id');
    }
}
