<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyProductRawData
 *
 * @property int $id
 * @property int $shopify_bulk_file_id
 * @property string $shopify_product_id
 * @property array $raw_data
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData whereRawData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData whereShopifyBulkFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductRawData whereShopifyProductId($value)
 * @mixin \Eloquent
 */
class ShopifyProductRawData extends Model
{
    protected $table = 'shopify_products_raw_data';

    protected $casts = [
        'raw_data' => 'array'
    ];
}
