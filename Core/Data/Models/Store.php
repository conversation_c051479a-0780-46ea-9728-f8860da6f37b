<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFilesTrait;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Core\Data\Models\Store
 *
 * @property int $id
 * @property string $key
 * @property int $user_id
 * @property array $data
 * @property string $last_modification_time
 * @property int|null $delete_after
 * @property int|null $ttl
 * @property string|null $channel
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\File> $files
 * @property int|null $files_count
 * @property string $data_as_json
 * @method static \Illuminate\Database\Eloquent\Builder|Store newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Store newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Store query()
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereChannel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDeleteAfter($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereLastModificationTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTtl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereUserId($value)
 * @mixin \Eloquent
 */
class Store extends Model implements UploadableContract
{
    use HasFilesTrait;

    protected $table = 'store';
    public $timestamps = false;
    public bool $ifFileAppendId = true;

    protected $casts = [
        'data' => 'array',
    ];

    protected $appends = [
        'dataAsJson'
    ];

    /**
     * @inheritDoc
     */
    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('store/' . $dir . '/' . $this->getUploadResourceId()) . '/';
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function getDataAsJsonAttribute(): string
    {
        return evat_json_encode($this->data);
    }
}
