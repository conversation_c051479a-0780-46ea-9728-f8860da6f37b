<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyOrderItemRawData
 *
 * @property int $id
 * @property int $shopify_bulk_files_id
 * @property string $shopify_order_id
 * @property int|null $warehouse_country_id // ship_from_country_id
 * @property array $raw_data
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData whereRawData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData whereShopifyBulkFilesId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderItemRawData whereShopifyOrderId($value)
 * @mixin \Eloquent
 */
class ShopifyOrderItemRawData extends Model
{
    protected $table = 'shopify_order_items_raw_data';

    protected $casts = [
        'raw_data' => 'array'
    ];
}
