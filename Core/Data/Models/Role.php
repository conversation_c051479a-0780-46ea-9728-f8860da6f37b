<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Core\Data\Models\Role
 *
 * @property int $id
 * @property string $name
 * @property int|null $institution_type_id
 * @property mixed $original_name
 * @property \App\Core\Data\Models\InstitutionType|null $institutionType
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\User> $users
 * @property int|null $users_count
 * @method static Builder|Role newModelQuery()
 * @method static Builder|Role newQuery()
 * @method static Builder|Role query()
 * @method static Builder|Role whereId($value)
 * @method static Builder|Role whereInstitutionTypeId($value)
 * @method static Builder|Role whereName($value)
 * @mixin \Eloquent
 */
class Role extends Model
{
    protected $table = 'roles';

    const ROLE_ACCOUNTANT_ADMIN = 1;
    const ROLE_ACCOUNTANT_REGISTRATION_TEAM_LEADER = 2;
    const ROLE_ACCOUNTANT_REGISTRATION_TEAM_MEMBER = 3;
    const ROLE_ACCOUNTANT_COMPLIANCE_TEAM_LEADER = 4;
    const ROLE_ACCOUNTANT_COMPLIANCE_TEAM_MEMBER = 5;
    const ROLE_ACCOUNTANT_CUSTOMER_SUPPORT_TEAM_LEADER = 6;
    const ROLE_ACCOUNTANT_CUSTOMER_SUPPORT_TEAM_MEMBER = 7;

    const ROLE_PARTNER_ADMIN = 8;
    const ROLE_PARTNER_REGISTRATION_TEAM_LEADER = 9;
    const ROLE_PARTNER_REGISTRATION_TEAM_MEMBER = 10;
    const ROLE_PARTNER_COMPLIANCE_TEAM_LEADER = 11;
    const ROLE_PARTNER_COMPLIANCE_TEAM_MEMBER = 12;
    const ROLE_PARTNER_CUSTOMER_SUPPORT_TEAM_LEADER = 13;
    const ROLE_PARTNER_CUSTOMER_SUPPORT_TEAM_MEMBER = 14;

    const ROLE_CLIENT_ADMIN = 15;
    const ROLE_CLIENT_USER = 16;

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function getNameAttribute(): string
    {
        return _l($this->getRawOriginal('name'));
    }

    /** @noinspection PhpUnused */
    public function getOriginalNameAttribute()
    {
        return $this->getRawOriginal('name');
    }

    public function institutionType(): BelongsTo
    {
        return $this->belongsTo(InstitutionType::class, 'institution_type_id', 'id');
    }
}
