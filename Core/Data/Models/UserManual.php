<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\UserManual
 *
 * @property int $id
 * @property string $route_name
 * @property string $locale
 * @property string|null $content
 * @property string $updated_at
 * @property int $update_user_id
 * @property string $url_param_key
 * @property string $url_param_value
 * @property bool $has_content
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\UserManualMedia> $media
 * @property int|null $media_count
 * @property \App\Core\Data\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereRouteName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereUpdateUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereUrlParamKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManual whereUrlParamValue($value)
 * @mixin \Eloquent
 */
class UserManual extends Model
{
    protected $table = 'user_manuals';

    public const LOCALES = [
        'en'  => 'locales.english',
        'cn'  => 'locales.chinese',
        'fr'  => 'locales.french',
        'dev' => 'locales.dev'
    ];

    public const PARAM_NONE_KEY = 'paramNone';
    public const PARAM_NONE_VALUE = '0';

    protected $appends = [
        'has_content',
    ];

    public static function getLocales(): Collection
    {
        return collect(self::LOCALES)->map(function ($locale) {
            return _l($locale);
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'update_user_id', 'id');
    }

    public function media()
    {
        return $this->hasMany(UserManualMedia::class);
    }

    public function getHasContentAttribute(): bool
    {
        $content = $this->content ?? '';
        $content = trim($content);
        if ($content === '' || $content === '<p></p>') {
            return false;
        }

        return true;
    }
}
