<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;
use App\Core\Data\Models\Traits\HasHistoryTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\AmazonReport
 *
 * @property int $id
 * @property string|null $description
 * @property string|null $start_date
 * @property string|null $end_date
 * @property int $upload_user_id
 * @property string|null $parsing_started_at
 * @property string|null $parsing_ended_at
 * @property int $report_status_id
 * @property string $batch_id
 * @property string|null $error_reason
 * @property int|null $upload_company_id
 * @property int|null $upload_institution_institution_type_id
 * @property int|null $sales_channel_id
 * @property int|null $debug
 * @property string $uploaded_at
 * @property array $user_messages
 * @property int $original_report_id
 * @property string|null $chunk_name
 * @property-read EloquentCollection|AmazonReport[] $allChunkReports
 * @property-read int|null $all_chunk_reports_count
 * @property-read EloquentCollection|\App\Core\Data\Models\AmazonReportRawData[] $cycleOneTransactions
 * @property-read int|null $cycle_one_transactions_count
 * @property-read EloquentCollection|\App\Core\Data\Models\AmazonReportRawData[] $cycleTwoTransactions
 * @property-read int|null $cycle_two_transactions_count
 * @property-read EloquentCollection|AmazonReport[] $duplicateAmazonReports
 * @property-read int|null $duplicate_amazon_reports_count
 * @property-read EloquentCollection|\App\Core\Data\Models\AmazonReportRawData[] $errorTransactions
 * @property-read int|null $error_transactions_count
 * @property-read \App\Core\Data\Models\File|null $file
 * @property-read \App\Core\Data\Models\AmazonReportRawData|null $firstTransaction
 * @property-read mixed $create_history
 * @property-read EloquentCollection $duplicate_amazon_reports
 * @property-read bool $has_error_transactions
 * @property-read array|null $months
 * @property-read string $status_class
 * @property-read int|null $year
 * @property-read array|null $year_months
 * @property-read EloquentCollection|\App\Core\Data\Models\History[] $history
 * @property-read int|null $history_count
 * @property-read EloquentCollection|\App\Core\Data\Models\Invoice[] $invoices
 * @property-read int|null $invoices_count
 * @property-read \App\Core\Data\Models\AmazonReportRawData|null $lastTransaction
 * @property-read \App\Core\Data\Models\SalesChannel|null $salesChannel
 * @property-read \App\Core\Data\Models\AmazonReportStatus|null $status
 * @property-read EloquentCollection|\App\Core\Data\Models\AmazonReportRawData[] $successTransactions
 * @property-read int|null $success_transactions_count
 * @property-read EloquentCollection|\App\Core\Data\Models\AmazonReportRawData[] $transactions
 * @property-read int|null $transactions_count
 * @property-read \App\Core\Data\Models\Company|null $uploadCompany
 * @property-read \App\Core\Data\Models\User $uploadUser
 * @method static Builder|AmazonReport newModelQuery()
 * @method static Builder|AmazonReport newQuery()
 * @method static Builder|AmazonReport query()
 * @method static Builder|AmazonReport whereBatchId($value)
 * @method static Builder|AmazonReport whereChunkName($value)
 * @method static Builder|AmazonReport whereDebug($value)
 * @method static Builder|AmazonReport whereDescription($value)
 * @method static Builder|AmazonReport whereEndDate($value)
 * @method static Builder|AmazonReport whereErrorReason($value)
 * @method static Builder|AmazonReport whereId($value)
 * @method static Builder|AmazonReport whereOriginalReportId($value)
 * @method static Builder|AmazonReport whereParsingEndedAt($value)
 * @method static Builder|AmazonReport whereParsingStartedAt($value)
 * @method static Builder|AmazonReport whereReportStatusId($value)
 * @method static Builder|AmazonReport whereSalesChannelId($value)
 * @method static Builder|AmazonReport whereStartDate($value)
 * @method static Builder|AmazonReport whereUploadCompanyId($value)
 * @method static Builder|AmazonReport whereUploadInstitutionInstitutionTypeId($value)
 * @method static Builder|AmazonReport whereUploadUserId($value)
 * @method static Builder|AmazonReport whereUploadedAt($value)
 * @method static Builder|AmazonReport whereUserMessages($value)
 * @mixin \Eloquent
 */
class AmazonReport extends Model implements UploadableContract
{
    protected $table = 'amazon_reports';
    protected $appends = [
        'duplicate_amazon_reports',
    ];

    protected $casts = [
        'user_messages' => 'array'
    ];

    use HasFileTrait;
    use HasHistoryTrait;

    public function getModalData(): string
    {
        return json_encode([
            'id'               => $this->id,
            'chunkName'        => $this->chunk_name,
            'originalReportId' => $this->original_report_id
        ]);
    }

    public function status(): HasOne
    {
        return $this->hasOne(AmazonReportStatus::class, 'id', 'report_status_id');
    }

    /** @noinspection PhpUnused */
    public function isInQueue(): bool
    {
        return $this->status->id === AmazonReportStatus::STATUS_QUEUED;
    }

    /**
     * @inheritDoc
     */
    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('reports/' . $dir . '/' . $this->getUploadResourceId()) . '/report/';
    }

    public function uploadUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'upload_user_id', 'id');
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    /** @noinspection PhpUnused */
    public function getYearAttribute(): ?int
    {
        if (!is_null($this->start_date)) {
            return Carbon::parse($this->start_date)->year;
        }

        return null;
    }

    /** @noinspection PhpUnused */
    public function getMonthsAttribute(): ?array
    {
        $start = null;
        $end = null;
        if (!is_null($this->start_date)) {
            $start = Carbon::parse($this->start_date)->month;
        }
        if (!is_null($this->end_date)) {
            $end = Carbon::parse($this->end_date)->month;
        }

        if (!is_null($start) && !is_null($end)) {
            return range($start, $end);
        }

        return null;
    }

    /** @noinspection PhpUnused */
    public function getYearMonthsAttribute(): ?array
    {
        $months = [];
        $year = $this->year;
        if (!is_null($year) && !is_null($this->months)) {
            /** @noinspection PhpConditionAlreadyCheckedInspection */
            foreach ($this->months ?? [] as $month) {
                $months[] = $year . '-' . $month;
            }
        }

        if (count($months) > 0) {
            return $months;
        }

        return null;
    }

    /** @noinspection PhpUnused */
    public function getStatusClassAttribute(): string
    {
        $statuses = [
            AmazonReportStatus::STATUS_SUCCESS => 'fa-check',
            AmazonReportStatus::STATUS_DOUBLE  => 'fa-copy',
        ];

        return $statuses[$this->report_status_id] ?? 'fa-exclamation-triangle';
    }

    /**
     * Nullable. Only if report is double(current usage)
     * UPDATE COMMENT IF MORE USAGE ADDED
     *
     * @noinspection PhpUnused
     */
    public function uploadCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'upload_company_id', 'id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AmazonReportRawData::class, 'amazon_report_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function cycleOneTransactions(): HasMany
    {
        return $this->hasMany(AmazonReportRawData::class, 'amazon_report_id', 'id')
            ->select(['amazon_reports_raw_data.*'])
            ->join('amazon_reports', 'amazon_reports.id', '=', 'amazon_reports_raw_data.amazon_report_id')
            ->where('amazon_reports.report_status_id', AmazonReportStatus::STATUS_READ)
            ->where('amazon_reports_raw_data.cycle', AmazonReportRawData::CYCLE_READ);
    }

    /** @noinspection PhpUnused */
    public function cycleTwoTransactions(): HasMany
    {
        return $this->hasMany(AmazonReportRawData::class, 'amazon_report_id', 'id')
            ->select(['amazon_reports_raw_data.*'])
            ->join('amazon_reports', 'amazon_reports.id', '=', 'amazon_reports_raw_data.amazon_report_id')
            ->where('amazon_reports.report_status_id', AmazonReportStatus::STATUS_RESOLVED)
            ->where('amazon_reports_raw_data.cycle', AmazonReportRawData::CYCLE_PARTIALLY_DB_RESOLVED);
    }

    /** @noinspection PhpUnused */
    public function successTransactions(): HasMany
    {
        return $this->hasMany(AmazonReportRawData::class, 'amazon_report_id', 'id')
            ->where('amazon_reports_raw_data.success', true)
            ->whereNull('error_reasons');
    }

    /** @noinspection PhpUnused */
    public function errorTransactions(): HasMany
    {
        return $this->hasMany(AmazonReportRawData::class, 'amazon_report_id', 'id')
            ->where('amazon_reports_raw_data.success', false)
            ->whereNotNull('error_reasons');
    }

    /** @noinspection PhpUnused */
    public function firstTransaction(): HasOne
    {
        return $this->hasOne(AmazonReportRawData::class, 'amazon_report_id', 'id')
            ->whereNotNull('transaction_date')
            ->orderBy('transaction_date', 'ASC')
            ->limit(1);
    }

    /** @noinspection PhpMissingReturnTypeInspection
     * @noinspection PhpUnused
     */
    public function lastTransaction()
    {
        return $this->hasOne(AmazonReportRawData::class, 'amazon_report_id', 'id')
            ->whereNotNull('transaction_date')
            ->orderBy('transaction_date', 'DESC')
            ->limit(1);
    }

    /** @noinspection PhpUnused */
    public function getCompanyId()
    {
        $id = $this->id ?? null;
        if (is_null($id)) {
            return null;
        }

        $data = $this->select('companies.id as company_id')
            ->join('amazon_reports_transactions', 'amazon_reports.id', '=', 'amazon_reports_transactions.amazon_report_id')
            ->join('ecommerce_accounts', 'amazon_reports_transactions.ecommerce_account_id', '=', 'ecommerce_accounts.id')
            ->join('companies', 'ecommerce_accounts.company_id', '=', 'companies.id')
            ->where('amazon_reports.id', '=', $id)
            ->groupBy('companies.id')
            ->first();

        return $data->company_id ?? null;
    }

    /** @noinspection PhpUnused */
    public function getSalesChannelId()
    {
        if (!isset($this->id)) {
            return null;
        }

        $data = $this->select('amazon_reports_transactions.ecommerce_account_id as sales_channel_id')
            ->join('amazon_reports_transactions', 'amazon_reports.id', '=', 'amazon_reports_transactions.amazon_report_id')
            ->where('amazon_reports.id', '=', $this->id)
            ->limit(1)
            ->first();

        return $data['sales_channel_id'];
    }

    /** @noinspection PhpUnused */
    public function getCreateHistoryAttribute()
    {
        return $this->history->keyBy('action')->get('CREATE');
    }

    /** @noinspection PhpUnused */
    public function duplicateAmazonReports(): HasMany
    {
        return $this->hasMany(AmazonReport::class, 'sales_channel_id', 'sales_channel_id')
            ->select('amazon_reports.*')
            ->join('amazon_reports AS ar', function (JoinClause $join) {
                $join->on('ar.sales_channel_id', '=', 'amazon_reports.sales_channel_id')
                    ->whereNotNull('ar.sales_channel_id')
                    ->whereNotNull('amazon_reports.sales_channel_id')
                    ->whereColumn('ar.id', '!=', 'amazon_reports.id')
                    ->whereRaw('(("ar"."start_date"::date, "ar"."end_date"::date) OVERLAPS ("amazon_reports"."start_date"::date, "amazon_reports"."end_date"::date))');
            });
    }

    /** @noinspection PhpUnused */
    public function getDuplicateAmazonReportsAttribute(): EloquentCollection
    {
        $key = 'duplicateAmazonReports';
        if (!$this->relationLoaded($key) || is_null($this->id)) {
            return new EloquentCollection([]);
        }
        /**
         * @var EloquentCollection $duplicates
         */
        $duplicates = $this->getRelation('duplicateAmazonReports')
            ->filter(function (AmazonReport $report) {
                return $report->id !== $this->id;
            })->values();
        $this->setRelation('duplicateAmazonReports', $duplicates);

        return $duplicates;
    }

    /** @noinspection PhpUnused */
    public function getHasErrorTransactionsAttribute(): bool
    {
        return $this->errorTransactions->count() > 0;
    }

    public function salesChannel(): BelongsTo
    {
        return $this->belongsTo(SalesChannel::class, 'sales_channel_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function allChunkReports(): HasMany
    {
        return $this->hasMany(AmazonReport::class, 'original_report_id', 'original_report_id');
    }

    public function getBestChunk(): AmazonReport
    {
        $first = $this->allChunkReports->filter(function (AmazonReport $report) {
            return !is_null($report->salesChannel) && (!is_null($report->start_date) && !is_null($report->end_date));
        })->first();

        if (!is_null($first)) {
            return $first;
        }

        $first = $this->allChunkReports->filter(function (AmazonReport $report) {
            return !is_null($report->salesChannel) || (!is_null($report->start_date) && !is_null($report->end_date));
        })->first();

        if (!is_null($first)) {
            return $first;
        }

        return $this->allChunkReports->first();
    }

    public function getChunkErrorReasons(): Collection
    {
        return $this->allChunkReports
            ->filter(function (AmazonReport $amazonReport) {
                return !is_null($amazonReport->error_reason);
            })
            ->map(function (AmazonReport $report) {
                return $report->error_reason;
            });
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'resource_id', 'id')->where('resource', '=', $this::class);
    }
}
