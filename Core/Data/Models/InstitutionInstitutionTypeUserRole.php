<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Scopes\UserNotDeletedScope;

/**
 * App\Core\Data\Models\InstitutionInstitutionTypeUserRole
 *
 * @property int $id
 * @property int $institution_institution_type_id
 * @property int $role_id
 * @property int $user_id
 * @property \App\Core\Data\Models\InstitutionInstitutionType $institutionInstitutionType
 * @property \App\Core\Data\Models\Role|null $role
 * @property \App\Core\Data\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole whereInstitutionInstitutionTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole whereRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionInstitutionTypeUserRole whereUserId($value)
 * @mixin \Eloquent
 */
class InstitutionInstitutionTypeUserRole extends Model
{
    protected $table = 'institution_institution_type_user_role';

    public function institutionInstitutionType()
    {
        return $this->belongsTo(InstitutionInstitutionType::class, 'institution_institution_type_id', 'id');
    }

    public function role()
    {
        return $this->hasOne(Role::class, 'id', 'role_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withoutGlobalScope(UserNotDeletedScope::class);
    }
}
