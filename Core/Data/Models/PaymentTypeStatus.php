<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\PaymentTypeStatus
 *
 * @property int $id
 * @property string $name
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\PaymentType[] $paymentTypes
 * @property-read int|null $payment_types_count
 * @method static Builder|PaymentTypeStatus newModelQuery()
 * @method static Builder|PaymentTypeStatus newQuery()
 * @method static Builder|PaymentTypeStatus query()
 * @method static Builder|PaymentTypeStatus whereId($value)
 * @method static Builder|PaymentTypeStatus whereName($value)
 * @mixin \Eloquent
 */
class PaymentTypeStatus extends Model
{
    protected $table = 'payment_type_statuses';

    public const STATUS_NOT_PAID = 1;
    public const STATUS_PAID = 2;

    /** @noinspection PhpUnused */
    public function paymentTypes(): HasMany
    {
        return $this->hasMany(PaymentType::class, 'payment_type_id', 'id');
    }
}
