<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;
use App\Core\Data\Models\Traits\HasHistoryTrait;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\OtherReport
 *
 * @property int $id
 * @property int $company_id
 * @property int|null $ecommerce_account_id
 * @property int|null $company_warehouse_id
 * @property string|null $description
 * @property string|null $comment
 * @property string $start_date
 * @property string|null $end_date
 * @property int|null $sales_channel_id
 * @property int|null $upload_user_id
 * @property string $uploaded_at
 * @property int $status_id
 * @property \App\Core\Data\Models\SalesChannel|null $salesChannel
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\CompanyWarehouse|null $companyWarehouse
 * @property \App\Core\Data\Models\User|null $uploadUser
 * @method static Builder|AmazonReport newModelQuery()
 * @method static Builder|AmazonReport newQuery()
 * @method static Builder|AmazonReport query()
 * @method static Builder|AmazonReport whereBatchId($value)
 * @method static Builder|AmazonReport whereDebug($value)
 * @method static Builder|AmazonReport whereDescription($value)
 * @method static Builder|AmazonReport whereEndDate($value)
 * @method static Builder|AmazonReport whereErrorReason($value)
 * @method static Builder|AmazonReport whereId($value)
 * @method static Builder|AmazonReport whereParsingEndedAt($value)
 * @method static Builder|AmazonReport whereParsingStartedAt($value)
 * @method static Builder|AmazonReport whereReportStatusId($value)
 * @method static Builder|AmazonReport whereSalesChannelId($value)
 * @method static Builder|AmazonReport whereStartDate($value)
 * @method static Builder|AmazonReport whereUploadCompanyId($value)
 * @method static Builder|AmazonReport whereUploadInstitutionInstitutionTypeId($value)
 * @method static Builder|AmazonReport whereUploadUserId($value)
 * @method static Builder|AmazonReport whereUploadedAt($value)
 * @method static Builder|AmazonReport whereUserMessages($value)
 * @mixin \Eloquent
 */
class OtherReport extends Model implements UploadableContract
{
    protected $table = 'other_reports';

    use HasFileTrait;
    use HasHistoryTrait;

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /**
     * @inheritDoc
     */
    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('other-reports/' . $dir . '/' . $this->getUploadResourceId()) . '/';
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function salesChannel()
    {
        return $this->belongsTo(SalesChannel::class, 'ecommerce_account_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function companyWarehouse()
    {
        return $this->belongsTo(CompanyWarehouse::class, 'company_warehouse_id', 'id');
    }

    public function uploadUser()
    {
        return $this->belongsTo(User::class, 'upload_user_id', 'id');
    }

    public function status()
    {
        return $this->belongsTo(OtherReportStatus::class, 'status_id', 'id');
    }
}
