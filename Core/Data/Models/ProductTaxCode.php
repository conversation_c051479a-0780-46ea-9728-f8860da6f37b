<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ProductTaxCode
 *
 * @property int $id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTaxCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTaxCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTaxCode query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTaxCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductTaxCode whereName($value)
 * @mixin \Eloquent
 */
class ProductTaxCode extends Model
{
    protected $table = 'product_tax_codes';
}
