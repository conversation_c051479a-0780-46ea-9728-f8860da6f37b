<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\AmazonReportStatus
 *
 * @property int $id
 * @property string $name
 * @property string $css_text_class
 * @method static Builder|AmazonReportStatus newModelQuery()
 * @method static Builder|AmazonReportStatus newQuery()
 * @method static Builder|AmazonReportStatus query()
 * @method static Builder|AmazonReportStatus whereId($value)
 * @method static Builder|AmazonReportStatus whereName($value)
 * @mixin \Eloquent
 */
class AmazonReportStatus extends Model
{
    protected $table = 'amazon_reports_statuses';

    protected $appends = [
        'cssTextClass'
    ];

    public const STATUS_DOUBLE = 3;
    public const STATUS_QUEUED = 4;
    public const STATUS_PARSING_STARTED = 15;
    public const STATUS_READ = 16;
    public const STATUS_RESOLVED = 17;
    public const STATUS_SYNCED_DB = 18;
    public const STATUS_SUCCESS = 200;
    public const STATUS_FAILED = 500;
    public const STATUS_IN_PROCESS = 102;

    public static function getProcessingStatusIds(): array
    {
        return [
            self::STATUS_PARSING_STARTED,
            self::STATUS_READ,
            self::STATUS_RESOLVED,
            self::STATUS_SYNCED_DB,
        ];
    }

    /** @noinspection PhpUnused */
    public function getCssTextClassAttribute(): string
    {
        $id = $this->id;
        $colorClass = 'text-dark-gray';
        if (is_null($id)) {
            return $colorClass;
        }

        if ($this->id === self::STATUS_SUCCESS) {
            $colorClass = 'text-primary';
        }

        if ($this->id === self::STATUS_FAILED) {
            $colorClass = 'text-danger';
        }

        if ($this->id === self::STATUS_IN_PROCESS) {
            $colorClass = 'text-pink';
        }

        if (in_array($this->id, self::getProcessingStatusIds())) {
            $colorClass = 'text-pink';
        }

        if ($this->id === self::STATUS_DOUBLE) {
            $colorClass = 'text-orange';
        }

        if ($this->id === self::STATUS_QUEUED) {
            $colorClass = 'text-green';
        }

        return $colorClass;
    }
}
