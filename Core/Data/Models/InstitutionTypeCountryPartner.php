<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\InstitutionTypeCountryPartner
 *
 * @property int $id
 * @property int $institution_institution_type_id
 * @property int $partner_id
 * @property int $country_id
 * @property \App\Core\Data\Models\InstitutionInstitutionType|null $accountant
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CompanyAccountant> $companyAccountants
 * @property int|null $company_accountants_count
 * @property \App\Core\Data\Models\Country $country
 * @property \App\Core\Data\Models\InstitutionInstitutionType|null $institutionInstitutionType
 * @property \App\Core\Data\Models\InstitutionInstitutionType|null $partner
 * @method static Builder|InstitutionTypeCountryPartner newModelQuery()
 * @method static Builder|InstitutionTypeCountryPartner newQuery()
 * @method static Builder|InstitutionTypeCountryPartner query()
 * @method static Builder|InstitutionTypeCountryPartner whereCountryId($value)
 * @method static Builder|InstitutionTypeCountryPartner whereId($value)
 * @method static Builder|InstitutionTypeCountryPartner whereInstitutionInstitutionTypeId($value)
 * @method static Builder|InstitutionTypeCountryPartner wherePartnerId($value)
 * @mixin \Eloquent
 */
class InstitutionTypeCountryPartner extends Model
{
    protected $table = 'institution_type_country_partner';

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function institutionInstitutionType(): HasOne
    {
        return $this->hasOne(InstitutionInstitutionType::class, 'id', 'institution_institution_type_id');
    }

    public function partner(): HasOne
    {
        return $this->hasOne(InstitutionInstitutionType::class, 'id', 'partner_id');
    }

    public function companyAccountants(): HasMany
    {
        return $this->hasMany(CompanyAccountant::class, 'accountant_id', 'id');
    }

    public function accountant(): HasOne
    {
        return $this->hasOne(InstitutionInstitutionType::class, 'id', 'institution_institution_type_id')->where('institution_type_id', '=', InstitutionType::TYPE_ACCOUNTANT);
    }
}
