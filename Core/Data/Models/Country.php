<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Country
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property int|null $currency_id Country local currency
 * @property bool $has_fba_warehouse
 * @property string|null $in_eu_start_date
 * @property string|null $in_eu_end_date
 * @property bool $doing_tax
 * @property bool $is_community
 * @property string|null $domestic_reverse_charge_applicable_from
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CountryCurrency> $countryCurrency
 * @property int|null $country_currency_count
 * @property \App\Core\Data\Models\Currency|null $currency
 * @property mixed $code_with_name
 * @property mixed $flag_icon
 * @property mixed $in_eu
 * @property mixed $vat_code
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\TaxOffice> $taxOffices
 * @property int|null $tax_offices_count
 * @property \App\Core\Data\Models\CountryThreshold|null $threshold
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CountryVatRate> $vatRates
 * @property int|null $vat_rates_count
 * @method static Builder|Country newModelQuery()
 * @method static Builder|Country newQuery()
 * @method static Builder|Country query()
 * @method static Builder|Country whereCode($value)
 * @method static Builder|Country whereCurrencyId($value)
 * @method static Builder|Country whereDoingTax($value)
 * @method static Builder|Country whereDomesticReverseChargeApplicableFrom($value)
 * @method static Builder|Country whereHasFbaWarehouse($value)
 * @method static Builder|Country whereId($value)
 * @method static Builder|Country whereInEuEndDate($value)
 * @method static Builder|Country whereInEuStartDate($value)
 * @method static Builder|Country whereIsCommunity($value)
 * @method static Builder|Country whereName($value)
 * @mixin \Eloquent
 */
class Country extends Model
{
    protected $table = 'countries';

    public const US = 1;
    public const CA = 2;
    public const AF = 3;
    public const AL = 4;
    public const DZ = 5;
    public const AS = 6;
    public const AD = 7;
    public const AO = 8;
    public const AI = 9;
    public const AQ = 10;
    public const AG = 11;
    public const AR = 12;
    public const AM = 13;
    public const AW = 14;
    public const AU = 15;
    public const AT = 16;
    public const AZ = 17;
    public const BS = 18;
    public const BH = 19;
    public const BD = 20;
    public const BB = 21;
    public const BY = 22;
    public const BE = 23;
    public const BZ = 24;
    public const BJ = 25;
    public const BM = 26;
    public const BT = 27;
    public const BO = 28;
    public const BA = 29;
    public const BW = 30;
    public const BV = 31;
    public const BR = 32;
    public const IO = 33;
    public const BN = 34;
    public const BG = 35;
    public const BF = 36;
    public const BI = 37;
    public const KH = 38;
    public const CM = 39;
    public const CV = 40;
    public const KY = 41;
    public const CF = 42;
    public const TD = 43;
    public const CL = 44;
    public const CN = 45;
    public const CX = 46;
    public const CC = 47;
    public const CO = 48;
    public const KM = 49;
    public const CG = 50;
    public const CK = 51;
    public const CR = 52;
    public const HR = 53;
    public const CU = 54;
    public const CY = 55;
    public const CZ = 56;
    public const CD = 57;
    public const DK = 58;
    public const DJ = 59;
    public const DM = 60;
    public const DO = 61;
    public const TL = 62;
    public const EC = 63;
    public const EG = 64;
    public const SV = 65;
    public const GQ = 66;
    public const ER = 67;
    public const EE = 68;
    public const ET = 69;
    public const FK = 70;
    public const FO = 71;
    public const FJ = 72;
    public const FI = 73;
    public const FR = 74;
    public const FX = 75;
    public const GF = 76;
    public const PF = 77;
    public const TF = 78;
    public const GA = 79;
    public const GM = 80;
    public const GE = 81;
    public const DE = 82;
    public const GH = 83;
    public const GI = 84;
    public const GR = 85;
    public const GL = 86;
    public const GD = 87;
    public const GP = 88;
    public const GU = 89;
    public const GT = 90;
    public const GN = 91;
    public const GW = 92;
    public const GY = 93;
    public const HT = 94;
    public const HM = 95;
    public const HN = 96;
    public const HK = 97;
    public const HU = 98;
    public const IS = 99;
    public const IN = 100;
    public const ID = 101;
    public const IR = 102;
    public const IQ = 103;
    public const IE = 104;
    public const IL = 105;
    public const IT = 106;
    public const CI = 107;
    public const JM = 108;
    public const JP = 109;
    public const JO = 110;
    public const KZ = 111;
    public const KE = 112;
    public const KI = 113;
    public const KP = 114;
    public const KR = 115;
    public const KW = 116;
    public const KG = 117;
    public const LA = 118;
    public const LV = 119;
    public const LB = 120;
    public const LS = 121;
    public const LR = 122;
    public const LY = 123;
    public const LI = 124;
    public const LT = 125;
    public const LU = 126;
    public const MO = 127;
    public const MK = 128;
    public const MG = 129;
    public const MW = 130;
    public const MY = 131;
    public const MV = 132;
    public const ML = 133;
    public const MT = 134;
    public const MH = 135;
    public const MQ = 136;
    public const MR = 137;
    public const MU = 138;
    public const YT = 139;
    public const MX = 140;
    public const FM = 141;
    public const MD = 142;
    public const MC = 143;
    public const MN = 144;
    public const MS = 145;
    public const MA = 146;
    public const MZ = 147;
    public const MM = 148;
    public const NA = 149;
    public const NR = 150;
    public const NP = 151;
    public const NL = 152;
    public const AN = 153;
    public const NC = 154;
    public const NZ = 155;
    public const NI = 156;
    public const NE = 157;
    public const NG = 158;
    public const NU = 159;
    public const NF = 160;
    public const MP = 161;
    public const NO = 162;
    public const OM = 163;
    public const PK = 164;
    public const PW = 165;
    public const PA = 166;
    public const PG = 167;
    public const PY = 168;
    public const PE = 169;
    public const PH = 170;
    public const PN = 171;
    public const PL = 172;
    public const PT = 173;
    public const PR = 174;
    public const QA = 175;
    public const SS = 176;
    public const RE = 177;
    public const RO = 178;
    public const RU = 179;
    public const RW = 180;
    public const KN = 181;
    public const LC = 182;
    public const VC = 183;
    public const WS = 184;
    public const SM = 185;
    public const ST = 186;
    public const SA = 187;
    public const SN = 188;
    public const RS = 189;
    public const SC = 190;
    public const SL = 191;
    public const SG = 192;
    public const SK = 193;
    public const SI = 194;
    public const SB = 195;
    public const SO = 196;
    public const ZA = 197;
    public const GS = 198;
    public const ES = 199;
    public const LK = 200;
    public const SH = 201;
    public const PM = 202;
    public const SD = 203;
    public const SR = 204;
    public const SJ = 205;
    public const SZ = 206;
    public const SE = 207;
    public const CH = 208;
    public const SY = 209;
    public const TW = 210;
    public const TJ = 211;
    public const TZ = 212;
    public const TH = 213;
    public const TG = 214;
    public const TK = 215;
    public const TO = 216;
    public const TT = 217;
    public const TN = 218;
    public const TR = 219;
    public const TM = 220;
    public const TC = 221;
    public const TV = 222;
    public const UG = 223;
    public const UA = 224;
    public const AE = 225;
    public const GB = 226;
    public const UM = 227;
    public const UY = 228;
    public const UZ = 229;
    public const VU = 230;
    public const VA = 231;
    public const VE = 232;
    public const VN = 233;
    public const VG = 234;
    public const VI = 235;
    public const WF = 236;
    public const EH = 237;
    public const YE = 238;
    public const ZR = 239;
    public const ZM = 240;
    public const ZW = 241;
    public const JE = 242;
    public const GG = 243;
    public const IM = 244;
    public const AX = 245;
    public const BL = 246;
    public const BQ = 247;
    public const CW = 248;
    public const ME = 249;
    public const MF = 250;
    public const PS = 251;
    public const SX = 252;
    public const XK = 253;
    public const XI = 254;
    public const XU = 255;
    public const IC = 256;
    public const IT_NOVAT2 = 257;
    public const ES_CE = 258;
    public const IT_NOVAT1 = 259;
    public const ES_ML = 260;
    public const GR_69 = 261;
    public const ES_GC = 262;
    public const ES_TF = 263;
    public const DE_HE = 265;
    public const PT_20 = 266;
    public const PT_30 = 267;

    public const EU = -1;
    public const NON_EU = -2;
    public const ALL = 0;

    public const GROUPED_IDS = [
        self::EU,
        self::NON_EU,
        self::ALL,
    ];

    protected $appends = [
        'vat_code',
        'code_with_name',
        'in_eu',
        'flag_icon'
    ];

    public function currency(): HasOne
    {
        return $this->hasOne(Currency::class, 'id', 'currency_id');
    }

    public function threshold(): HasOne
    {
        return $this->hasOne(CountryThreshold::class, 'country_id', 'id')->orderByDesc('effective_from');
    }

    public function vatRates(): HasMany
    {
        return $this->hasMany(CountryVatRate::class, 'country_id', 'id')->orderByDesc('effective_from');
    }

    public function taxOffices(): HasMany
    {
        return $this->hasMany(TaxOffice::class, 'country_id', 'id');
    }

    public function getVatCodeAttribute(): string
    {
        $exceptions = [
            self::GR => 'EL',
            self::FX => 'FR'
        ];

        if (array_key_exists($this->id, $exceptions)) {
            return $exceptions[$this->id];
        }

        return $this->code;
    }

    public function getFlagIconAttribute(): string
    {
        $code = strtolower($this->code);
        if ($this->id === self::XI) {
            $code = 'gb-nir';
        }

        return 'flag-icon flag-icon-' . $code;
    }

    public function countryCurrency(): HasMany
    {
        return $this->hasMany(CountryCurrency::class, 'country_id', 'id');
    }

    public function getCodeWithNameAttribute(): string
    {
        return $this->code . ' (' . $this->name . ')';
    }

    public function getInEuAttribute(): bool
    {
        return !is_null($this->in_eu_start_date) && is_null($this->in_eu_end_date);
    }

    public function isInEuForYear(string $year): bool
    {
        $yearDate = Carbon::createFromDate($year, 01, 01);
        $startEuDate = !empty($this->in_eu_start_date) ? Carbon::parse($this->in_eu_start_date) : null;
        $endEuDate = !empty($this->in_eu_end_date) ? Carbon::parse($this->in_eu_end_date) : null;

        if (empty($startEuDate)) {
            return false;
        } elseif ($yearDate->greaterThanOrEqualTo($startEuDate)) {
            return empty($endEuDate) || $endEuDate->greaterThan($yearDate);
        } else {
            return false;
        }
    }

    public function isInEuForDate(string $date): bool
    {
        $date = Carbon::parse($date);
        if (is_null($this->in_eu_start_date)) {
            return false;
        }
        $startEuDate = Carbon::parse($this->in_eu_start_date);

        if ($startEuDate->gt($date)) {
            return false;
        }

        $endEuDate = !is_null($this->in_eu_end_date) ? Carbon::parse($this->in_eu_end_date) : null;
        if (is_null($endEuDate)) {
            return true;
        }

        return $date->lte($endEuDate);
    }

    public function isDomesticReverseChargeApplicableFromOnDate(?string $date = null): bool
    {
        $domesticReverseChargeApplicableFrom = $this->domestic_reverse_charge_applicable_from;
        if (is_null($domesticReverseChargeApplicableFrom)) {
            return false;
        }
        if (is_null($date)) {
            $date = Carbon::now();
        }

        $date = Carbon::parse($date);
        $domesticReverseChargeApplicableFrom = Carbon::parse($domesticReverseChargeApplicableFrom);

        return $date->gte($domesticReverseChargeApplicableFrom);
    }
}
