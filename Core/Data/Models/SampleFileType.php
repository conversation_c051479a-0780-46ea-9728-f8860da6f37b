<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\SampleFileType
 *
 * @property int $id
 * @property string $type
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\SampleFile> $sampleFiles
 * @property int|null $files_count
 * @method static Builder|SampleFileType newModelQuery()
 * @method static Builder|SampleFileType newQuery()
 * @method static Builder|SampleFileType query()
 * @method static Builder|SampleFileType whereId($value)
 * @method static Builder|SampleFileType whereType($value)
 * @mixin \Eloquent
 */
class SampleFileType extends Model
{
    protected $table = 'sample_file_types';

    public const REQUEST_VAT_SETTLEMENT_FILETYPE = 7;

    /** @noinspection PhpUnused */
    public function sampleFiles(): HasMany
    {
        return $this->hasMany(SampleFile::class, 'sample_file_type_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
