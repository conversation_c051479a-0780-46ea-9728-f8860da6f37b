<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * App\Core\Data\Models\IdentificationNumber
 *
 * @property int $id
 * @property string $value
 * @property int $resource_id
 * @property string $resource
 * @property string|null $note
 * @property int|null $country_id
 * @property int $identification_number_type_id
 * @property string|null $start_date
 * @property-read \App\Core\Data\Models\Country|null $country
 * @property-read \App\Core\Data\Models\IdentificationNumberType $identificationNumberType
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $resourceModel
 * @method static Builder|IdentificationNumber newModelQuery()
 * @method static Builder|IdentificationNumber newQuery()
 * @method static Builder|IdentificationNumber query()
 * @method static Builder|IdentificationNumber whereCountryId($value)
 * @method static Builder|IdentificationNumber whereId($value)
 * @method static Builder|IdentificationNumber whereIdentificationNumberTypeId($value)
 * @method static Builder|IdentificationNumber whereResource($value)
 * @method static Builder|IdentificationNumber whereResourceId($value)
 * @method static Builder|IdentificationNumber whereStartDate($value)
 * @method static Builder|IdentificationNumber whereValue($value)
 * @mixin \Eloquent
 */
class IdentificationNumber extends Model
{
    protected $table = 'identification_numbers';

    /** @noinspection PhpUnused */
    public function identificationNumberType(): BelongsTo
    {
        return $this->belongsTo(IdentificationNumberType::class, 'identification_number_type_id', 'id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function resourceModel(): MorphTo
    {
        return $this->morphTo('resourceModel', 'resource', 'resource_id', 'id');
    }

    protected static function booted(): void
    {
        parent::booted(); // TODO: Change the autogenerated stub

        static::saving(function (IdentificationNumber $identificationNumber) {
            if ($identificationNumber->identification_number_type_id === IdentificationNumberType::OSS_NUMBER) {
                $identificationNumber->value = \Str::of($identificationNumber->value)->start('EU');
            }
        });
    }
}
