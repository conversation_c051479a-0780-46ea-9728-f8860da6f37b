<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CountryDocumentCategoryType
 *
 * @property int $id
 * @property int $country_id
 * @property int $document_category_type_id
 * @method static \Illuminate\Database\Eloquent\Builder|CountryDocumentCategoryType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryDocumentCategoryType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryDocumentCategoryType query()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryDocumentCategoryType whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryDocumentCategoryType whereDocumentCategoryTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryDocumentCategoryType whereId($value)
 * @mixin \Eloquent
 */
class CountryDocumentCategoryType extends Model
{
    protected $table = 'country_document_category_type';

}
