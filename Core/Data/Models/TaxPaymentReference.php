<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Eloquent;

/**
 * App\Core\Data\Models\ItemType
 *
 * @property int $id
 * @property string $name
 * @method static Builder|ItemType newModelQuery()
 * @method static Builder|ItemType newQuery()
 * @method static Builder|ItemType query()
 * @method static Builder|ItemType whereId($value)
 * @method static Builder|ItemType whereName($value)
 * @mixin Eloquent
 */
class TaxPaymentReference extends Model
{
    protected $table = 'tax_payment_references';

    public const TAX_OFFICE = 1;
    public const ACCOUNTANT = 2;
    public const MANUAL = 3;
}
