<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\UserTeam
 *
 * @property int $id
 * @property int $user_id
 * @property int $team_id
 * @property \App\Core\Data\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserTeam newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTeam newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTeam query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTeam whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTeam whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTeam whereUserId($value)
 * @mixin \Eloquent
 */
class UserTeam extends Model
{
    protected $table = 'user_teams';

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withoutGlobalScope(\App\Core\Data\Models\Scopes\UserNotDeletedScope::class);
    }
}
