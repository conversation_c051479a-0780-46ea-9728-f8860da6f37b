<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\ReportApiContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\VatReport
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property int $vat_report_status_id
 * @property string $date_from
 * @property string $date_to
 * @property TaxNumber|null $valid_tax_number
 * @property VatNumber|null $valid_vat_number
 * @property string|null $payment_reference_number
 * @property array|null $data
 * @property Collection $changed_mapper_fields
 * @property Collection $calculation_history
 * @property string $created_at
 * @property bool $vat_settlement
 * @property bool $vat_refund_requested
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\Country|null $country
 * @property \App\Core\Data\Models\VatReport[]|Collection $documents
 * @property \App\Core\Data\Models\ReportStatus $vatReportStatus
 * @method static Builder|VatReport newModelQuery()
 * @method static Builder|VatReport newQuery()
 * @method static Builder|VatReport query()
 * @method static Builder|VatReport whereCompanyId($value)
 * @method static Builder|VatReport whereCountryId($value)
 * @method static Builder|VatReport whereCreatedAt($value)
 * @method static Builder|VatReport whereData($value)
 * @method static Builder|VatReport whereDateFrom($value)
 * @method static Builder|VatReport whereDateTo($value)
 * @method static Builder|VatReport whereId($value)
 * @method static Builder|VatReport wherePaymentReferenceNumber($value)
 * @method static Builder|VatReport whereReportStatusId($value)
 * @mixin \Eloquent
 */
class VatReport extends Model implements ReportApiContract
{
    protected $table = 'vat_reports';

    protected $appends = [
        'changed_mapper_fields',
        'calculation_history',
        'valid_tax_number',
        'valid_vat_number',
    ];

    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function getChangedVatDue(): ?float
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $vatDue = $data['vat_due'] ?? null;
        if (is_null($vatDue)) {
            return null;
        }

        return (float)$vatDue;
    }

    public function setChangedVatDue(float $vatDue): void
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $data['vat_due'] = $vatDue;
        $data = evat_json_encode($data);
        $this->data = $data;
    }

    /** @noinspection PhpUnused */
    public function vatReportStatus(): BelongsTo
    {
        return $this->belongsTo(ReportStatus::class, 'vat_report_status_id', 'id');
    }

    public function documents(): HasMany
    {
        return $this->hasMany(Document::class, 'resource_id', 'id')->where('resource', $this::class);
    }

    /** @noinspection PhpUnused */
    public function getDataAttribute(): ?array
    {
        $data = $this->getRawOriginal('data');
        if (is_null($data)) {
            return null;
        }

        return json_decode($data, true);
    }

    /** @noinspection PhpUnused */
    public function getCalculationHistoryAttribute(): Collection
    {
        return collect($this->data['calculation_history'] ?? [])->sortBy('calculatedAt', SORT_NATURAL, true);
    }

    public function getLatestCalculationHistory(): ?array
    {
        $history = $this->calculation_history->first();
        if (is_null($history)) {
            return null;
        }

        $history['calculation'] = collect($history['calculation'] ?? [])
            ->sortKeys()
            ->toArray();

        return $history;
    }

    public function getLatestCalculatedVat(): ?float
    {
        $history = $this->getLatestCalculationHistory();
        $hasCalculation = count($history['calculation'] ?? []) > 0;
        if (!$hasCalculation) {
            return null;
        }

        return $history['vat'] ?? null;
    }

    public function getLatestCalculatedVatWithCurrency(): ?string
    {
        $vat = $this->getLatestCalculatedVat();
        if (is_null($vat)) {
            return null;
        }

        return evat_money($vat, $this->country?->currency?->code ?? 'EUR');
    }

    public function resolveCalculationHistoryArray(array $calculation): array
    {
        $calculation['calculatedAt'] = Carbon::now()->toDateTimeString();
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $calculationData = $data['calculation_history'] ?? [];
        $calculationData[] = $calculation;
        $data['calculation_history'] = $calculationData;

        return $data;
    }

    public function resolveCalculationHistoryJson(array $calculation): string
    {
        return evat_json_encode($this->resolveCalculationHistoryArray($calculation));
    }

    public function setCalculationHistory(array $calculation): void
    {
        $this->data = $this->resolveCalculationHistoryJson($calculation);
    }

    public function historyUpdateNeeded(array $newCalculation): bool
    {
        $oldCalculation = $this->getLatestCalculationHistory();
        if (is_null($oldCalculation)) {
            return true;
        }

        $oldCalculation = collect($oldCalculation['calculation'])
            ->map(function ($subtype) {
                $vat = (float)($subtype['vat'] ?? 0.0);
                $net = (float)($subtype['net'] ?? 0.0);

                return round($vat + $net);
            });
        $newCalculation = collect($newCalculation['calculation'])
            ->map(function ($subtype) {
                $vat = (float)($subtype['vat'] ?? 0.0);
                $net = (float)($subtype['net'] ?? 0.0);

                return round($vat + $net);
            });

        if ($oldCalculation->count() !== $newCalculation->count()) {
            return true;
        }

        $difference = $newCalculation->filter(function (float $new, string $key) use ($oldCalculation) {
            $old = $oldCalculation->get($key);
            if (is_null($old)) {
                return true;
            }

            return round($new) !== round($old);
        });

        return $difference->count() > 0;
    }

    /** @noinspection PhpUnused */
    public function getChangedMapperFieldsAttribute(): Collection
    {
        return collect($this->data['changed_mapper_fields'] ?? []);
    }

    /** @noinspection PhpUnused */
    public function setChangedMapperFieldsAttribute(array $changedOrSubmittedFields): void
    {
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $data['changed_mapper_fields'] = $changedOrSubmittedFields;
        $data = evat_json_encode($data);
        $this->data = $data;
    }

    /** @noinspection PhpUnused */
    public function salesInvoices(): HasManyThrough
    {
        return $this->hasManyThrough(
            Invoice::class,
            VatReport::class,
            'id',
            'company_id',
            'id',
            'company_id'
        )
            ->join('invoice_subtypes', 'invoices.invoice_subtype_id', '=', 'invoice_subtypes.id')
            ->where('invoice_subtypes.invoice_type_id', InvoiceType::SALES_INVOICE)
            ->whereColumn('invoices.invoice_date', '>=', 'vat_reports.date_from')
            ->whereColumn('invoices.invoice_date', '<=', 'vat_reports.date_to');
    }

    /** @noinspection PhpUnused */
    public function getValidTaxNumberAttribute(): ?TaxNumber
    {
        if (is_null($this->id) || is_null($this->date_from) || is_null($this->date_to) || is_null($this->country_id)) {
            return null;
        }

        $vatReportDateFrom = Carbon::parse($this->date_from);
        $vatReportDateTo = Carbon::parse($this->date_to);

        return $this->company
            ->taxNumbers
            ->groupBy('country_id')
            ->get($this->country_id, collect())
            ->filter(function (TaxNumber $taxNumber) use ($vatReportDateFrom, $vatReportDateTo) {
                $taxNumberDateFrom = Carbon::parse($taxNumber->register_date);
                $taxNumberDateTo = Carbon::parse(
                    $taxNumber->end_date ?? Carbon::now()->addMonths(2)->endOfMonth()->toDateString()
                );

                if ($taxNumberDateFrom->gt($vatReportDateTo)) {
                    return false;
                }

                if ($taxNumberDateTo->lt($vatReportDateFrom)) {
                    return false;
                }

                return true;
            })->first();
    }

    /** @noinspection PhpUnused */
    public function getValidVatNumberAttribute(): ?VatNumber
    {
        if (is_null($this->id) || is_null($this->date_from) || is_null($this->date_to) || is_null($this->country_id)) {
            return null;
        }

        $vatReportDateFrom = Carbon::parse($this->date_from);
        $vatReportDateTo = Carbon::parse($this->date_to);

        return $this->company
            ->vatNumbers
            ->groupBy('country_id')
            ->get($this->country_id, collect())
            ->filter(function (VatNumber $taxNumber) use ($vatReportDateFrom, $vatReportDateTo) {
                $taxNumberDateFrom = Carbon::parse($taxNumber->register_date);
                $taxNumberDateTo = Carbon::parse(
                    $taxNumber->end_date ?? Carbon::now()->addMonths(2)->endOfMonth()->toDateString()
                );

                if ($taxNumberDateFrom->gt($vatReportDateTo)) {
                    return false;
                }

                if ($taxNumberDateTo->lt($vatReportDateFrom)) {
                    return false;
                }

                return true;
            })->first();
    }

    public function getDocumentsInCategory(int $categoryId): Collection
    {
        return $this->documents
            ->filter(function (Document $document) use ($categoryId) {
                return $document->document_category_id === $categoryId;
            });
    }

    public function getDocumentsInCategoryCount(int $categoryId): int
    {
        return $this->getDocumentsInCategory($categoryId)->count();
    }

    public function hasDocumentsInCategory(int $categoryId): bool
    {
        return $this->getDocumentsInCategoryCount($categoryId) > 0;
    }

    public function getCompany(): Company
    {
        return $this->company;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function setStatus(int $statusId): ReportApiContract
    {
        $this->vat_report_status_id = $statusId;

        return $this;
    }

    public function getResource(): string
    {
        return __CLASS__;
    }

    public function getResourceId(): int
    {
        return $this->id;
    }
}
