<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\ReportApiContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\EslReport
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property int $status_id
 * @property string $date_from
 * @property string $date_to
 * @property array|null $data
 * @property string $created_at
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\Country|null $country
 * @property Collection $calculation_history
 * @property \App\Core\Data\Models\ReportStatus $status
 * @property \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Document[] $documents
 * @property int|null $documents_count
 * @method static Builder|EslReport newModelQuery()
 * @method static Builder|EslReport newQuery()
 * @method static Builder|EslReport query()
 * @method static Builder|EslReport whereCompanyId($value)
 * @method static Builder|EslReport whereCountryId($value)
 * @method static Builder|EslReport whereCreatedAt($value)
 * @method static Builder|EslReport whereData($value)
 * @method static Builder|EslReport whereDateFrom($value)
 * @method static Builder|EslReport whereDateTo($value)
 * @method static Builder|EslReport whereId($value)
 * @method static Builder|EslReport whereStatusId($value)
 * @mixin \Eloquent
 */
class EslReport extends Model implements ReportApiContract
{
    protected $table = 'esl_reports';

    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function documents(): HasMany
    {
        return $this->hasMany(Document::class, 'resource_id', 'id')->where('resource', $this::class);
    }

    /** @noinspection PhpUnused */
    public function status(): BelongsTo
    {
        return $this->belongsTo(ReportStatus::class, 'status_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getDataAttribute(): ?array
    {
        $data = $this->getRawOriginal('data');
        if (is_null($data)) {
            return null;
        }

        return json_decode($data, true);
    }

    /** @noinspection PhpUnused */
    public function getCalculationHistoryAttribute(): Collection
    {
        return collect($this->data['calculation_history'] ?? [])->sortBy('calculatedAt', SORT_NATURAL, true);
    }

    public function getLatestCalculationHistory(): ?array
    {
        return $this->calculation_history->first();
    }

    public function historyUpdateNeeded(array $newCalculation): bool
    {
        $oldCalculation = $this->getLatestCalculationHistory();
        if (!is_array($oldCalculation)) {
            return true;
        }

        $oldCalculation = $this->sortCalculationData($oldCalculation['calculation']);
        $newCalculation = $this->sortCalculationData($newCalculation['calculation']);
        if ($oldCalculation->count() !== $newCalculation->count()) {
            return true;
        }

        return $oldCalculation->toJson() !== $newCalculation->toJson();
    }

    private function sortCalculationData(array $calculation): Collection
    {
        return collect($calculation)
            ->sortKeys(SORT_NATURAL)
            ->map(function (array $vatNumbers) {
                return collect($vatNumbers)
                    ->map(function (array $values) {
                        return collect($values)->sortKeys(SORT_NATURAL)->toArray();
                    })
                    ->sortKeys(SORT_NATURAL)->toArray();
            });
    }

    public function resolveCalculationHistoryArray(array $calculation): array
    {
        $calculation['calculatedAt'] = Carbon::now()->toDateTimeString();
        $data = $this->getRawOriginal('data', '{}');
        $data = json_decode($data, true);
        $calculationData = $data['calculation_history'] ?? [];
        $calculationData[] = $calculation;
        $data['calculation_history'] = $calculationData;

        return $data;
    }

    public function resolveCalculationHistoryJson(array $calculation): string
    {
        return evat_json_encode($this->resolveCalculationHistoryArray($calculation));
    }

    public static function getReportSubtypes(): array
    {
        return [
            InvoiceSubtype::EU_B2B_SALES_INVOICE,
            InvoiceSubtype::EU_B2B_PRO_FORMA_INVOICE,
            InvoiceSubtype::CN_EU_B2B_SALES_INVOICE,
        ];
    }

    public function getDocumentsInCategory(int $categoryId): Collection
    {
        return $this->documents
            ->filter(function (Document $document) use ($categoryId) {
                return $document->document_category_id === $categoryId;
            });
    }

    public function getDocumentsInCategoryCount(int $categoryId): int
    {
        return $this->getDocumentsInCategory($categoryId)->count();
    }

    public function hasDocumentsInCategory(int $categoryId): bool
    {
        return $this->getDocumentsInCategory($categoryId)->count();
    }

    public function getCompany(): Company
    {
        return $this->company;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function setStatus(int $statusId): ReportApiContract
    {
        $this->status_id = $statusId;

        return $this;
    }

    public function getResource(): string
    {
        return __CLASS__;
    }

    public function getResourceId(): int
    {
        return $this->id;
    }
}
