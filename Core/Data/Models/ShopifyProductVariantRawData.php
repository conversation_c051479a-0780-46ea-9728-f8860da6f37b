<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyProductVariantRawData
 *
 * @property int $id
 * @property int $shopify_bulk_file_id
 * @property string $shopify_product_id
 * @property array $raw_data
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData whereRawData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData whereShopifyBulkFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyProductVariantRawData whereShopifyProductId($value)
 * @mixin \Eloquent
 */
class ShopifyProductVariantRawData extends Model
{
    protected $table = 'shopify_product_variants_raw_data';

    protected $casts = [
        'raw_data' => 'array'
    ];
}
