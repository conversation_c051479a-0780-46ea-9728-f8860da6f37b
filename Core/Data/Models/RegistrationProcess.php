<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Traits\HasHistoryTrait;
use App\Core\System\Exceptions\InvalidTransitionException;

/**
 * App\Core\Data\Models\RegistrationProcess
 *
 * @property int $id
 * @property int $company_id
 * @property int $registration_status_id
 * @property int $country_id
 * @property int|null $promotion_id
 * @property int|null $partner_team_assigned_user_team_id
 * @property int|null $accountant_team_assigned_user_team_id
 * @property int|null $partner_team_id
 * @property int|null $accountant_team_id
 * @property string|null $termination_date
 * @property int $registration_type_id
 * @property int|null $number_type
 * @property \App\Core\Data\Models\Team|null $accountantTeam
 * @property \App\Core\Data\Models\UserTeam|null $accountantTeamAssignedUserTeam
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country|null $country
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\History> $createHistoryRecord
 * @property int|null $create_history_record_count
 * @property mixed $transitions
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\History> $history
 * @property int|null $history_count
 * @property \App\Core\Data\Models\Team|null $partnerTeam
 * @property \App\Core\Data\Models\UserTeam|null $partnerTeamAssignedUserTeam
 * @property \App\Core\Data\Models\RegistrationProcessType|null $registrationProcessType
 * @property \App\Core\Data\Models\RegistrationStatus $registrationStatus
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\History> $updateHistoryRecord
 * @property int|null $update_history_record_count
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereAccountantTeamAssignedUserTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereAccountantTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereNumberType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess wherePartnerTeamAssignedUserTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess wherePartnerTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess wherePromotionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereRegistrationStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereRegistrationTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcess whereTerminationDate($value)
 * @mixin \Eloquent
 */
class RegistrationProcess extends Model
{
    protected $table = 'registration_processes';

    public const REGISTRATION_COUNTRIES_IDS = [
        Country::CZ,
        Country::FR,
        Country::DE,
        Country::IT,
        Country::CZ,
        Country::PL,
        Country::ES,
        Country::GB,
    ];

    use HasHistoryTrait;

    public function registrationStatus()
    {
        return $this->belongsTo(RegistrationStatus::class, 'registration_status_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function registrationProcessType()
    {
        return $this->hasOne(RegistrationProcessType::class, 'id', 'registration_type_id');
    }

    public function getTransitionsAttribute()
    {
        $transitions = [
            RegistrationStatus::NEW                  => [
                'fromId' => RegistrationStatus::NEW,
                'toIds'  => [RegistrationStatus::REVIEW]
            ],
            RegistrationStatus::REVIEW               => [
                'fromId' => RegistrationStatus::REVIEW,
                'toIds'  => [
                    RegistrationStatus::DOCUMENTS_MISSING,
                    RegistrationStatus::READY_FOR_SIGNATURE,
                    RegistrationStatus::CANCELED
                ]
            ],
            RegistrationStatus::DOCUMENTS_MISSING    => [
                'fromId' => RegistrationStatus::DOCUMENTS_MISSING,
                'toIds'  => [
                    RegistrationStatus::REVIEW,
                    RegistrationStatus::READY_FOR_SIGNATURE,
                    RegistrationStatus::CANCELED
                ]
            ],
            RegistrationStatus::READY_FOR_SIGNATURE  => [
                'fromId' => RegistrationStatus::READY_FOR_SIGNATURE,
                'toIds'  => [
                    RegistrationStatus::REVIEW,
                    RegistrationStatus::DOCUMENTS_MISSING,
                    RegistrationStatus::SIGNED,
                    RegistrationStatus::CANCELED
                ]
            ],
            RegistrationStatus::SIGNED               => [
                'fromId' => RegistrationStatus::SIGNED,
                'toIds'  => [
                    RegistrationStatus::REVIEW,
                    RegistrationStatus::DOCUMENTS_MISSING,
                    RegistrationStatus::READY_FOR_SUBMISSION,
                    RegistrationStatus::CANCELED
                ]
            ],
            RegistrationStatus::READY_FOR_SUBMISSION => [
                'fromId' => RegistrationStatus::READY_FOR_SUBMISSION,
                'toIds'  => [
                    RegistrationStatus::REVIEW,
                    RegistrationStatus::DOCUMENTS_MISSING,
                    RegistrationStatus::SUBMITTED,
                    RegistrationStatus::CANCELED
                ]
            ],
            RegistrationStatus::SUBMITTED            => [
                'fromId' => RegistrationStatus::SUBMITTED,
                'toIds'  => [
                    RegistrationStatus::REVIEW,
                    RegistrationStatus::DOCUMENTS_MISSING,
                    RegistrationStatus::REGISTERED,
                    RegistrationStatus::CANCELED
                ]
            ],
            RegistrationStatus::CANCELED             => [
                'fromId' => RegistrationStatus::CANCELED,
                'toIds'  => [RegistrationStatus::NEW]
            ],
            RegistrationStatus::REGISTERED           => [
                'fromId' => RegistrationStatus::REGISTERED,
                'toIds'  => [
                    RegistrationStatus::REGISTERED
                ]
            ],
        ];

        return collect($transitions);
    }

    public function getPossibleTransitionIds()
    {
        return $this->transitions->get($this->registration_status_id)['toIds'];
    }

    public function isValidTransitionToId(int $statusId)
    {
        if ($this->registration_status_id === null) {
            return true;
        }

        $transitionIds = $this->getPossibleTransitionIds();

        return in_array($statusId, $transitionIds);
    }

    public function setRegistrationStatusIdAttribute(int $value)
    {
        if (!$this->isValidTransitionToId($value)) {
            throw new InvalidTransitionException('Not possible to set registration_status_id to ' . $value);
        }
        $this->attributes['registration_status_id'] = $value;
    }

    public function createHistoryRecord()
    {
        return $this->morphMany(History::class, 'history', 'resource_class', 'resource_id', 'id')
            ->where('action', '=', 'CREATE')
            ->orderBy('changed_at', 'ASC');
    }

    public function updateHistoryRecord()
    {
        return $this->morphMany(History::class, 'history', 'resource_class', 'resource_id', 'id')
            ->where('action', '=', 'UPDATE')
            ->orderBy('changed_at', 'DESC');
    }

    public function getCreateUserName()
    {
        $history = $this->createHistoryRecord->first();
        $user = 'N/A';
        if (!is_null($history)) {
            $user = $history->getChangeUser()->name;
        }

        return $user;
    }

    public function getCreateTime()
    {
        $history = $this->createHistoryRecord->first();
        $time = 'N/A';
        if (!is_null($history)) {
            $time = $history->getChangedAt()->format(_l_date_format('date'));
        }

        return $time;
    }

    public function getLastUpdateUserName()
    {
        $history = $this->updateHistoryRecord->first();
        $user = 'N/A';
        if (!is_null($history)) {
            $user = $history->getChangeUser()->name;
        }

        return $user;
    }

    public function getLastUpdateTime()
    {
        $history = $this->updateHistoryRecord->first();
        $time = 'N/A';
        if (!is_null($history)) {
            $time = $history->getChangedAt()->format(_l_date_format('date'));
        }

        return $time;
    }

    public function partnerTeam()
    {
        return $this->belongsTo(Team::class, 'partner_team_id', 'id');
    }

    public function partnerTeamAssignedUserTeam()
    {
        return $this->hasOne(UserTeam::class, 'id', 'partner_team_assigned_user_team_id');
    }

    public function accountantTeam()
    {
        return $this->belongsTo(Team::class, 'accountant_team_id', 'id');
    }

    public function accountantTeamAssignedUserTeam()
    {
        return $this->hasOne(UserTeam::class, 'id', 'accountant_team_assigned_user_team_id');
    }
}
