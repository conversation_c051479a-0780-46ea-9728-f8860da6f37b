<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyOrderRawData
 *
 * @property int $id
 * @property int $shopify_bulk_files_id
 * @property string $shopify_order_id
 * @property int|null $warehouse_country_id // ship_from_country_id
 * @property string $shipping_price_net
 * @property array $raw_data
 * @property string $invoice_items_net_amount
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData whereInvoiceItemsNetAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData whereRawData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData whereShippingPriceNet($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData whereShopifyBulkFilesId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyOrderRawData whereShopifyOrderId($value)
 * @mixin \Eloquent
 */
class ShopifyOrderRawData extends Model
{
    protected $table = 'shopify_orders_raw_data';

    protected $casts = [
        'raw_data' => 'array'
    ];
}
