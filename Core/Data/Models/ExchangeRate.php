<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ExchangeRate
 *
 * @property int $id
 * @property string $date
 * @property int $currency_id
 * @property float $value Euro based currency rate
 * @property \App\Core\Data\Models\Currency $currency
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate whereCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ExchangeRate whereValue($value)
 * @mixin \Eloquent
 */
class ExchangeRate extends Model
{
    protected $table = 'exchange_rates';

    protected $casts = [
        'value' => 'float'
    ];

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }
}
