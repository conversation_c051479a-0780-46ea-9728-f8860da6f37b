<?php

namespace App\Core\Data\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Core\Data\Models\BusinessModel
 *
 * @property int $id
 * @property string $name
 * @property string $short_name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Item> $items
 * @property int|null $items_count
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessModel query()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessModel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessModel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessModel whereShortName($value)
 * @mixin \Eloquent
 */
class BusinessModel extends Model
{
    const B2B = 1;
    const B2C = 2;
    const B2B_AND_B2C = 3;

    protected $table = 'business_models';

    public function items()
    {
        return $this->hasMany(Item::class, 'business_model_id', 'id');
    }
}
