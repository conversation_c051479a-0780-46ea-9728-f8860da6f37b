<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\BusinessActivity
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property string $name_en
 * @property int $country_id
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity query()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessActivity whereNameEn($value)
 * @mixin \Eloquent
 */
class BusinessActivity extends Model
{
    protected $table = 'business_activities';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }
}
