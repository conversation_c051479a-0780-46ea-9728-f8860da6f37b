<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\ItemType
 *
 * @property int $id
 * @property string $name
 * @method static Builder|ItemType newModelQuery()
 * @method static Builder|ItemType newQuery()
 * @method static Builder|ItemType query()
 * @method static Builder|ItemType whereId($value)
 * @method static Builder|ItemType whereName($value)
 * @mixin Eloquent
 */
class TaxSchemeName extends Model
{
    protected $table = 'tax_scheme_names';

    public const VALUE_ADDED_TAX = 1;
    public const ONE_STOP_SHOP = 2;
    public const IMPORT_ONE_STOP_SHOP = 3;
}
