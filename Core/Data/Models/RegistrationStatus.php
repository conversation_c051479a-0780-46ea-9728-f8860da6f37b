<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\RegistrationStatus
 *
 * @property int $id
 * @property string $status
 * @property string $name
 * @property string|null $description
 * @property string $lang_key
 * @property int|null $sequence
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\RegistrationProcess> $registrationProcesses
 * @property int|null $registration_processes_count
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus whereLangKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus whereSequence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationStatus whereStatus($value)
 * @mixin \Eloquent
 */
class RegistrationStatus extends Model
{
    protected $table = 'registration_statuses';

    const NEW = 1;
    const REVIEW = 2;
    const DOCUMENTS_MISSING = 3;
    const READY_FOR_SIGNATURE = 4;
    const SIGNED = 5;
    const READY_FOR_SUBMISSION = 6;
    const SUBMITTED = 7;
    const REGISTERED = 8;
    const CANCELED = 9;

    public function registrationProcesses()
    {
        return $this->hasMany(RegistrationProcess::class, 'registration_status_id', 'id');
    }
}
