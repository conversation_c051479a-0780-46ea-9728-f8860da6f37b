<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\SupplierAddress
 *
 * @property int $id
 * @property int $supplier_id
 * @property int $address_id
 * @property int $address_type_id
 * @property \App\Core\Data\Models\Address|null $address
 * @property \App\Core\Data\Models\AddressType|null $addressType
 * @property \App\Core\Data\Models\Supplier $supplier
 * @method static Builder|SupplierAddress newModelQuery()
 * @method static Builder|SupplierAddress newQuery()
 * @method static Builder|SupplierAddress query()
 * @method static Builder|SupplierAddress whereAddressId($value)
 * @method static Builder|SupplierAddress whereAddressTypeId($value)
 * @method static Builder|SupplierAddress whereId($value)
 * @method static Builder|SupplierAddress whereSupplierId($value)
 * @mixin \Eloquent
 */
class SupplierAddress extends Model
{
    protected $table = 'suppliers_addresses';

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id', 'id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class, 'id', 'address_id');
    }

    public function addressType(): HasOne
    {
        return $this->hasOne(AddressType::class, 'id', 'address_type_id');
    }
}
