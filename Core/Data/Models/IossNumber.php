<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\HasCompanyContract;
use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\IossNumber
 *
 * @property int $id
 * @property int $company_id
 * @property int $issue_country_id
 * @property int $type_id
 * @property string $number
 * @property string $register_date
 * @property string|null $end_date
 * @property-read \App\Core\Data\Models\Company $company
 * @property-read \App\Core\Data\Models\Country $issueCountry
 * @property-read \App\Core\Data\Models\IossNumberType $type
 * @method static Builder|IossNumber newModelQuery()
 * @method static Builder|IossNumber newQuery()
 * @method static Builder|IossNumber query()
 * @method static Builder|IossNumber whereCompanyId($value)
 * @method static Builder|IossNumber whereEndDate($value)
 * @method static Builder|IossNumber whereId($value)
 * @method static Builder|IossNumber whereIssueCountryId($value)
 * @method static Builder|IossNumber whereNumber($value)
 * @method static Builder|IossNumber whereRegisterDate($value)
 * @method static Builder|IossNumber whereTypeId($value)
 * @mixin \Eloquent
 */
class IossNumber extends Model implements HasCompanyContract
{
    protected $table = 'ioss_numbers';

    public function issueCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'issue_country_id', 'id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(IossNumberType::class, 'type_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function getCompany(): Company
    {
        return $this->company;
    }
}
