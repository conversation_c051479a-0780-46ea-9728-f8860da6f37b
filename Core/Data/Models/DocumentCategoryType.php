<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class DocumentCategoryType extends Model
{
    public function countries(): BelongsToMany
    {
        return $this->belongsToMany(Country::class, 'country_document_category_type', 'document_category_type_id', 'country_id');
    }

    public function companyCountries(): BelongsToMany
    {
        return $this->belongsToMany(Country::class, 'document_category_type_company_countries', 'document_category_type_id', 'company_origin_country_id');
    }

    public function platforms(): BelongsToMany
    {
        return $this->belongsToMany(Platform::class, 'document_category_type_ecommerce_platforms', 'document_category_type_id', 'platform_id');
    }
}
