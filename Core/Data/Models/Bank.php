<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\Bank
 *
 * @property int $id
 * @property string $name
 * @property string|null $branch_name
 * @property string $beneficiary
 * @property string|null $iban
 * @property string $swift
 * @property int|null $company_id
 * @property bool $iban_valid
 * @property bool $iban_validated
 * @property int|null $institution_id
 * @property string|null $account_number
 * @property string|null $routing_number
 * @property string|null $account_owner_name
 * @property \App\Core\Data\Models\Company|null $company
 * @method static \Illuminate\Database\Eloquent\Builder|Bank newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Bank newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Bank query()
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereAccountNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereAccountOwnerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereBeneficiary($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereBranchName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereIban($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereIbanValid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereIbanValidated($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereInstitutionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereRoutingNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bank whereSwift($value)
 * @mixin \Eloquent
 */
class Bank extends Model
{
    protected $table = 'banks';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
