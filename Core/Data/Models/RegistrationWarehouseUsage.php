<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\RegistrationWarehouseUsage
 *
 * @property int $id
 * @property int $company_id
 * @property string $owner
 * @property int $warehouse_type_id
 * @property int|null $country_id
 * @property int|null $address_id
 * @property \App\Core\Data\Models\Address|null $address
 * @property \App\Core\Data\Models\Country|null $country
 * @property \App\Core\Data\Models\RegistrationWarehouseUsageType $warehouseType
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage whereAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage whereOwner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsage whereWarehouseTypeId($value)
 * @mixin \Eloquent
 */
class RegistrationWarehouseUsage extends Model
{
    const OWNER_AMAZON = 'Amazon';
    const OWNER_EBAY = 'eBay';

    protected $table = 'registration_warehouse_usages';

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class);
    }

    public function warehouseType()
    {
        return $this->belongsTo(RegistrationWarehouseUsageType::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
