<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\GenderType
 *
 * @property int $id
 * @property string $lang_key
 * @method static \Illuminate\Database\Eloquent\Builder|GenderType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GenderType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GenderType query()
 * @method static \Illuminate\Database\Eloquent\Builder|GenderType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GenderType whereLangKey($value)
 * @mixin \Eloquent
 */
class GenderType extends Model
{
    const MALE = 1;
    const FEMALE = 2;
    const NONBINARY = 3;

    protected $table = 'gender_types';

    public static function getGenderTypeLabel(int $genderTypeId): string
    {
        $genderTypeLabel = '';

        switch ($genderTypeId) {
            case GenderType::MALE:
                $genderTypeLabel = _l('gender-type.Male');
                break;
            case GenderType::FEMALE:
                $genderTypeLabel = _l('gender-type.Female');
                break;
            case GenderType::NONBINARY:
                $genderTypeLabel = _l('gender-type.Non-binary');
                break;
        }

        return $genderTypeLabel;
    }
}
