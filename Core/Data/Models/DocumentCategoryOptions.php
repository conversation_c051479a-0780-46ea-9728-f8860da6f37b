<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\DocumentCategoryOptions
 *
 * @property int $id
 * @property int $document_category_id
 * @property bool $required_on_registration
 * @property bool|null $amount_in_balance_sheet_as_credit NULL - do not include, TRUE - include as credit, FALSE -  include as debit
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions whereAmountInBalanceSheetAsCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions whereDocumentCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryOptions whereRequiredOnRegistration($value)
 * @mixin \Eloquent
 */
class DocumentCategoryOptions extends Model
{
    protected $table = 'document_category_options';
}
