<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyEuThreshold
 *
 * @property int $id
 * @property int $company_id
 * @property string $date
 * @property \App\Core\Data\Models\Company $company
 * @property mixed $limit
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyEuThreshold newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyEuThreshold newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyEuThreshold query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyEuThreshold whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyEuThreshold whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyEuThreshold whereId($value)
 * @mixin \Eloquent
 */
class CompanyEuThreshold extends Model
{
    protected $table = 'company_eu_thresholds';

    protected $appends = [
        'limit'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getLimitAttribute()
    {
        return config('evat.euThreshold');
    }

}
