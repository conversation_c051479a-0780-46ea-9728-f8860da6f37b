<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\System\Helpers\Arr;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

/**
 * App\Core\Data\Models\AmazonReportRawData
 *
 * @property int $id
 * @property int $cycle
 * @property int $amazon_report_id
 * @property bool $success
 * @property array $error_reasons
 * @property int $column_count
 * @property int|null $event_type_id transaction_type_id
 * @property string|null $transaction_event_id
 * @property string|null $activity_transaction_id
 * @property string|null $transaction_date
 * @property string|null $data
 * @property string|null $parsed_data
 * @property string|null $unique_invoice_key
 * @property mixed $error_reasons_string
 * @method static Builder|AmazonReportRawData newModelQuery()
 * @method static Builder|AmazonReportRawData newQuery()
 * @method static Builder|AmazonReportRawData query()
 * @method static Builder|AmazonReportRawData whereActivityTransactionId($value)
 * @method static Builder|AmazonReportRawData whereAmazonReportId($value)
 * @method static Builder|AmazonReportRawData whereColumnCount($value)
 * @method static Builder|AmazonReportRawData whereCycle($value)
 * @method static Builder|AmazonReportRawData whereData($value)
 * @method static Builder|AmazonReportRawData whereErrorReasons($value)
 * @method static Builder|AmazonReportRawData whereEventTypeId($value)
 * @method static Builder|AmazonReportRawData whereId($value)
 * @method static Builder|AmazonReportRawData whereParsedData($value)
 * @method static Builder|AmazonReportRawData whereSuccess($value)
 * @method static Builder|AmazonReportRawData whereTransactionDate($value)
 * @method static Builder|AmazonReportRawData whereTransactionEventId($value)
 * @method static Builder|AmazonReportRawData whereUniqueInvoiceKey($value)
 * @mixin \Eloquent
 */
class AmazonReportRawData extends Model
{
    const DELIMITER = '{{|||}}';

    const CYCLE_READ = 1;
    const CYCLE_PARTIALLY_DB_RESOLVED = 2;
    const CYCLE_DB_RESOLVED = 3;

    protected $table = 'amazon_reports_raw_data';

    protected $appends = [
        'error_reasons_string'
    ];

    public static function encodeErrorsOrParsedData(array $data): string
    {
        return evat_json_encode($data);
    }

    public static function decodeErrorsOrParsedData(string $data)
    {
        $data = str_replace('\\', '\\\\', $data);
        $data = str_replace("\n", '', $data);
        $data = str_replace("\r", '', $data);

        return json_decode($data, true);
    }

    /** @noinspection PhpUnused */
    public function getParsedDataAttribute(): ?array
    {
        $parsed = $this->getRawOriginal('parsed_data');
        if (is_null($parsed)) {
            return null;
        }

        try {
            $parsed = self::decodeErrorsOrParsedData($parsed);
        } catch (Throwable) {
            return null;
        }

        return $parsed;
    }

    /** @noinspection PhpUnused */
    public function getErrorReasonsAttribute(): ?array
    {
        $errors = $this->getRawOriginal('error_reasons');
        if (is_null($errors)) {
            return null;
        }

        try {
            $errors = self::decodeErrorsOrParsedData($errors);
        } catch (Throwable) {
            return null;
        }

        return $errors;
    }

    /** @noinspection PhpUnused */
    public function getErrorReasonsStringAttribute(): ?string
    {
        if (count($this->error_reasons ?? []) < 1) {
            return null;
        }

        $errors = array_map(function ($error) {
            $message = $error['message'];
            if (user()->isDeveloper() || !is_null(sd()->getSwitchedFromDevUser())) {
                $message = $message . ' (CODE: ' . $error['code'] . ', THROWN: "' . $error['thrown'] . '", CAUGHT: "' . $error['caught'] . '")';
            }

            return $message;
        }, $this->error_reasons);

        return implode(', ', $errors);
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
