<?php

namespace App\Core\Data\Models;

/**
 *  App\Core\Data\Models\EstablishmentStatus
 *
 * todo: extend Model
 * doesnt have database table
 *
 * @property int $id
 * @property string $name
 * @property string $description
 */
class EstablishmentStatus
{
    public const PERMANENT_ESTABLISHMENT = 1;
    public const OTHER_FACILITIES = 2;

    private array $data = [];

    public function __construct(int $id)
    {
        $this->id = $id;

        $names = [
            self::PERMANENT_ESTABLISHMENT => [
                'name'        => 'vatNumbers.Permanent establishment',
                'description' => 'vatNumbers.Permanent establishment tooltip'
            ],
            self::OTHER_FACILITIES        => [
                'name'        => 'vatNumbers.Other facilities',
                'description' => 'vatNumbers.Other facilities tooltip'
            ],
        ];

        $this->name = $names[$id]['name'] ?? '';
        $this->description = $names[$id]['description'] ?? '';
    }

    public function __set($name, $value)
    {
        $this->data[$name] = $value;
    }

    public function __get($name): mixed
    {
        return $this->data[$name] ?? null;
    }
}
