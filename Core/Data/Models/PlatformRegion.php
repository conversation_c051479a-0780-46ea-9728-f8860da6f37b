<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\PlatformRegion
 *
 * @property int $id
 * @property string $key
 * @property string $description
 * @property int $platform_id
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion query()
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformRegion wherePlatformId($value)
 * @mixin \Eloquent
 */
class PlatformRegion extends Model
{
    protected $table = 'platform_regions';

    const US_EAST_1 = 1;
    const EU_WEST_1 = 2;
    const US_WEST_2 = 3;
}
