<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\ItemSalePrice
 *
 * @property int $id
 * @property int $currency_id
 * @property int $item_marketplace_id
 * @property string $start_date
 * @property float $net_price
 * @property float $gross_price
 * @property \App\Core\Data\Models\Currency|null $currency
 * @property \App\Core\Data\Models\ItemMarketplace $itemMarketplace
 * @method static Builder|ItemSalePrice newModelQuery()
 * @method static Builder|ItemSalePrice newQuery()
 * @method static Builder|ItemSalePrice query()
 * @method static Builder|ItemSalePrice whereCurrencyId($value)
 * @method static Builder|ItemSalePrice whereGrossPrice($value)
 * @method static Builder|ItemSalePrice whereId($value)
 * @method static Builder|ItemSalePrice whereItemMarketplaceId($value)
 * @method static Builder|ItemSalePrice whereNetPrice($value)
 * @method static Builder|ItemSalePrice whereStartDate($value)
 * @mixin \Eloquent
 */
class ItemSalePrice extends Model
{
    protected $table = 'item_sale_prices';
    protected $casts = [
        'net_price'   => 'double',
        'gross_price' => 'double',
    ];

    public function currency(): HasOne
    {
        return $this->hasOne(Currency::class, 'id', 'currency_id');
    }

    /** @noinspection PhpUnused */
    public function itemMarketplace(): BelongsTo
    {
        return $this->belongsTo(ItemMarketplace::class, 'item_marketplace_id', 'id');
    }
}
