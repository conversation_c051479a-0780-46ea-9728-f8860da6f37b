<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\InvoiceStatusInvoiceType
 *
 * @property int $id
 * @property int $invoice_status_id
 * @property int $invoice_type_id
 * @property \App\Core\Data\Models\InvoiceType $invoiceType
 * @method static Builder|InvoiceStatusInvoiceType newModelQuery()
 * @method static Builder|InvoiceStatusInvoiceType newQuery()
 * @method static Builder|InvoiceStatusInvoiceType query()
 * @method static Builder|InvoiceStatusInvoiceType whereId($value)
 * @method static Builder|InvoiceStatusInvoiceType whereInvoiceStatusId($value)
 * @method static Builder|InvoiceStatusInvoiceType whereInvoiceTypeId($value)
 * @mixin \Eloquent
 */
class InvoiceStatusInvoiceType extends Model
{
    protected $table = 'invoice_status_invoice_types';

    public function invoiceType(): BelongsTo
    {
        return $this->belongsTo(InvoiceType::class, 'invoice_type_id', 'id');
    }
}
