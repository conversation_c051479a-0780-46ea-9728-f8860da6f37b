<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Filesystem\Filesystem;

/**
 * App\Core\Data\Models\File
 *
 * @property int $id
 * @property string $name
 * @property string $mime
 * @property string $extension
 * @property int $resource_id
 * @property string $resource
 * @property int $size
 * @property string $created_at
 * @property string|null $content
 * @property mixed $created_formatted_date
 * @property string $fa_icon_class
 * @property mixed $file_path
 * @property mixed $file_size
 * @property mixed $full_name
 * @property mixed $human_size
 * @property \Illuminate\Database\Eloquent\Model|\Eloquent $resourceModel
 * @method static Builder|File newModelQuery()
 * @method static Builder|File newQuery()
 * @method static Builder|File query()
 * @method static Builder|File whereCreatedAt($value)
 * @method static Builder|File whereExtension($value)
 * @method static Builder|File whereId($value)
 * @method static Builder|File whereMime($value)
 * @method static Builder|File whereName($value)
 * @method static Builder|File whereResource($value)
 * @method static Builder|File whereResourceId($value)
 * @method static Builder|File whereSize($value)
 * @mixin \Eloquent
 */
class File extends Model
{
    protected $table = 'files';

    protected $appends = [
        'full_name',
        'file_path',
        'human_size',
        'created_formatted_date',
        'fa_icon_class',
    ];

    // ATTRIBUTES

    /** @noinspection PhpUnused */
    public function getFullNameAttribute(): ?string
    {
        if (is_null($this->id)) {
            return null;
        }

        return $this->name . '.' . $this->extension;
    }

    /** @noinspection PhpUnused */
    public function getFilePathAttribute(): ?string
    {
        if (is_null($this->id)) {
            return null;
        }
        /**
         * @var UploadableContract $uploadable
         */
        $uploadable = $this->resourceModel;

        $path = $uploadable->getUploadPath();
        if ($uploadable->ifFileAppendId) {
            $path = $path . $this->id;
        }

        return $path;
    }

    /** @noinspection PhpUnused */
    public function getFileSizeAttribute(): ?int
    {
        /**
         * @var Filesystem $fileSystem
         */
        $fileSystem = app()->make(Filesystem::class);
        if (!$fileSystem->exists($this->file_path)) {
            return null;
        }

        return $fileSystem->size($this->file_path);
    }

    // RELATIONSHIPS

    /** @noinspection PhpUnused */
    public function resourceModel(): MorphTo
    {
        return $this->morphTo('resourceModel', 'resource', 'resource_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getHumanSizeAttribute(): string
    {
        $size = $this->size;

        return bytes_to_human($size);
    }

    /** @noinspection PhpUnused */
    public function getCreatedFormattedDateAttribute(): string
    {
        return Carbon::parse($this->created_at)->format(_l_date_format('datetime'));
    }

    /** @noinspection PhpUnused */
    public function getFaIconClassAttribute(): string
    {
        $icons = [
            'doc'  => 'fa-file-word text-info',
            'docx' => 'fa-file-word text-info',
            'xls'  => 'fa-file-excel text-success',
            'xlsx' => 'fa-file-excel text-success',
            'jpg'  => 'fa-file-image text-info',
            'jpeg' => 'fa-file-image text-info',
            'png'  => 'fa-file-image text-info',
            'gif'  => 'fa-file-image text-info',
            'svg'  => 'fa-file-image text-info',
            'tiff' => 'fa-file-image text-info',
            'pdf'  => 'fa-file-pdf text-danger',
            'ppt'  => 'fa-file-powerpoint text-danger',
            'pptx' => 'fa-file-powerpoint text-danger',
        ];

        return $icons[$this->extension ?? ''] ?? 'fa-file';
    }

    /** @noinspection PhpUnused */
    public function getContentAttribute(): ?string
    {
        $path = $this->file_path;
        if (is_null($path)) {
            return null;
        }

        if (!is_file($path)) {
            return null;
        }

        $data = file_get_contents($path);
        if (!$data) {
            return null;
        }

        return $data;
    }
}
