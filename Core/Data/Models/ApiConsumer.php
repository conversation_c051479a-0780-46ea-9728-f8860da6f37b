<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\ApiConsumer
 *
 * @property int $id
 * @property string $name
 * @property string $key
 * @property string $secret
 * @property int|null $crm_institution_id
 * @property int|null $marketplace_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyApiConsumers[] $companyApiConsumers
 * @property-read int|null $company_api_consumers_count
 * @property-read \App\Core\Data\Models\Institution|null $institution
 * @property-read \App\Core\Data\Models\Marketplace|null $marketplace
 * @method static Builder|ApiConsumer newModelQuery()
 * @method static Builder|ApiConsumer newQuery()
 * @method static Builder|ApiConsumer query()
 * @method static Builder|ApiConsumer whereCrmInstitutionId($value)
 * @method static Builder|ApiConsumer whereId($value)
 * @method static Builder|ApiConsumer whereKey($value)
 * @method static Builder|ApiConsumer whereMarketplaceId($value)
 * @method static Builder|ApiConsumer whereName($value)
 * @method static Builder|ApiConsumer whereSecret($value)
 * @mixin \Eloquent
 */
class ApiConsumer extends Model
{
    protected $table = 'api_consumers';

    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class, 'crm_institution_id', 'id');
    }

    public function marketplace(): HasOne
    {
        return $this->hasOne(Marketplace::class, 'id', 'marketplace_id');
    }

    /** @noinspection PhpUnused */
    public function companyApiConsumers(): HasMany
    {
        return $this->hasMany(CompanyApiConsumers::class, 'api_consumer_id', 'id');
    }
}
