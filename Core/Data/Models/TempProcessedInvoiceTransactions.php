<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\TempProcessedInvoiceTransactions
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string $start_date
 * @property string $end_date
 * @property string|null $process_started
 * @property string|null $process_ended
 * @property string $status
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions query()
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereProcessEnded($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereProcessStarted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempProcessedInvoiceTransactions whereStatus($value)
 * @mixin \Eloquent
 */
class TempProcessedInvoiceTransactions extends Model
{
    protected $table = 'temp_processed_invoice_transactions';

}
