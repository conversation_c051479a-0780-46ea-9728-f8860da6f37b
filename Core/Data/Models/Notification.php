<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Notification
 *
 * @property int $id
 * @property int $user_id
 * @property string $message
 * @property string $type
 * @property string $created_at
 * @property string|null $read_at
 * @property int|null $sent_from_user_id
 * @property array|null $data
 * @property \App\Core\Data\Models\User|null $sentFromUser
 * @property \App\Core\Data\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Notification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Notification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Notification query()
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereSentFromUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Notification whereUserId($value)
 * @mixin \Eloquent
 */
class Notification extends Model
{
    protected $table = 'notifications';

    const SUCCESS = 'success';
    const INFO = 'info';
    const ERROR = 'error';
    const WARNING = 'warning';

    protected $casts = [
        'data' => 'array'
    ];

    public function sentFromUser()
    {
        return $this->belongsTo(User::class, 'sent_from_user_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
