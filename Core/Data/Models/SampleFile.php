<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;

/**
 * App\Core\Data\Models\SampleFile
 *
 * @property int $id
 * @property int $sample_file_type_id
 * @property string|null $description
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Country> $countries
 * @property int|null $countries_count
 * @property \App\Core\Data\Models\File|null $file
 * @property \App\Core\Data\Models\SampleFileType $sampleFileType
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFile query()
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFile whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFile whereSampleFileTypeId($value)
 * @mixin \Eloquent
 */
class SampleFile extends Model implements UploadableContract
{
    use HasFileTrait;

    protected $table = 'sample_files';

    public bool $ifFileAppendId = false;

    /**
     * @return string
     */
    public function getUploadPath(): string
    {
        return upload_path('sample_files') . '/' . $this->getUploadResourceId();
    }

    /**
     * @return int
     */
    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function countries()
    {
        return $this->belongsToMany(Country::class, 'sample_file_country', 'sample_file_id', 'country_id');
    }

    public function sampleFileType()
    {
        return $this->belongsTo(SampleFileType::class, 'sample_file_type_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

}
