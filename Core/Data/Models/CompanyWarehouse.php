<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\CompanyWarehouse
 *
 * @property int $id
 * @property int $company_id
 * @property int $warehouse_id
 * @property string $first_time_used
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Warehouse $warehouse
 * @method static Builder|CompanyWarehouse newModelQuery()
 * @method static Builder|CompanyWarehouse newQuery()
 * @method static Builder|CompanyWarehouse query()
 * @method static Builder|CompanyWarehouse whereCompanyId($value)
 * @method static Builder|CompanyWarehouse whereFirstTimeUsed($value)
 * @method static Builder|CompanyWarehouse whereId($value)
 * @method static Builder|CompanyWarehouse whereWarehouseId($value)
 * @mixin \Eloquent
 */
class CompanyWarehouse extends Model
{
    protected $table = 'company_warehouses';

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
