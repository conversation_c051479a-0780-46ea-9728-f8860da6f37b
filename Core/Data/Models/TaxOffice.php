<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\TaxOffice
 *
 * @property int $id
 * @property int $country_id
 * @property string $tax_office
 * @property int $address_id
 * @property string|null $payment_collection_institution
 * @property int|null $payment_collection_institution_address_id
 * @property string|null $bank
 * @property string|null $iban
 * @property string|null $swift
 * @property string|null $phone
 * @property string|null $email
 * @property string|null $web
 * @property string|null $recipient_id
 * @property string|null $code
 * @property string|null $de_special_email
 * @property string|null $epuap
 * @property string|null $jpk_vat
 * @property string|null $pec
 * @property \App\Core\Data\Models\Address|null $address
 * @property \App\Core\Data\Models\Country|null $country
 * @property mixed $tax_office_address
 * @property mixed $tax_office_postal_city
 * @property \App\Core\Data\Models\Address|null $paymentCollectionInstitutionAddress
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereBank($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereDeSpecialEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereEpuap($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereIban($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereJpkVat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice wherePaymentCollectionInstitution($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice wherePaymentCollectionInstitutionAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice wherePec($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereRecipientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereSwift($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereTaxOffice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaxOffice whereWeb($value)
 * @mixin \Eloquent
 */
class TaxOffice extends Model
{
    const DE_HAMELN = 1;
    const DE_BONN = 2;
    const DE_MAGDEBURG = 3;
    const DE_DORTMUND = 4;
    const DE_NURNBERG = 5;
    const DE_TRIER = 6;
    const DE_BERLIN = 7;
    const DE_ORIANENBURG = 8;
    const DE_NEUWIED = 9;
    const DE_MUHLHAUSEN = 10;
    const DE_KLEVE = 11;
    const DE_HANNOVER = 12;
    const DE_ROSTOCK = 13;
    const DE_SAARBRUCKEN = 14;
    const DE_KONSTANZ = 15;
    const DE_CHEMNITZ = 16;
    const DE_COTTBUS = 17;
    const DE_FLENSBURG = 18;
    const DE_BREMEN = 19;
    const DE_MUNCHEN = 20;
    const DE_HAMBURG = 21;
    const DE_OFFENBURG = 22;
    const DE_KASSEL = 23;

    const FR_EU_COMPANIES_OFFICE = 590;

    const CZ_NON_CZECH_COMPANIES_OFFICE = 786;

    protected $table = 'tax_offices';

    public function address()
    {
        return $this->hasOne(Address::class, 'id', 'address_id');
    }

    public function paymentCollectionInstitutionAddress()
    {
        return $this->hasOne(Address::class, 'id', 'payment_collection_institution_address_id');
    }

    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function getTaxOfficeAddressAttribute()
    {
        return $this->address->street . ' ' . $this->address->house_number;
    }

    public function getTaxOfficePostalCityAttribute()
    {
        return $this->address->postal_code . ' ' . $this->address->city->name;
    }
}
