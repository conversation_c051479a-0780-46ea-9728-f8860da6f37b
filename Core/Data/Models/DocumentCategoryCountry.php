<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\DocumentCategoryCountry
 *
 * @property int $id
 * @property int $country_id
 * @property int $document_category_id
 * @property bool $is_company_country
 * @property \App\Core\Data\Models\Country|null $country
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry whereDocumentCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryCountry whereIsCompanyCountry($value)
 * @mixin \Eloquent
 */
class DocumentCategoryCountry extends Model
{
    protected $table = 'document_category_countries';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }
}
