<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Team
 *
 * @property int $id
 * @property string $name
 * @property int|null $country_id
 * @property int $institution_institution_type_id
 * @property \App\Core\Data\Models\Country|null $country
 * @property \App\Core\Data\Models\InstitutionInstitutionType $institutionInstitutionType
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\UserTeam> $usersTeams
 * @property int|null $users_teams_count
 * @method static \Illuminate\Database\Eloquent\Builder|Team newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Team newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Team query()
 * @method static \Illuminate\Database\Eloquent\Builder|Team whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Team whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Team whereInstitutionInstitutionTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Team whereName($value)
 * @mixin \Eloquent
 */
class Team extends Model
{
    protected $table = 'teams';

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function institutionInstitutionType()
    {
        return $this->belongsTo(InstitutionInstitutionType::class, 'institution_institution_type_id', 'id');
    }

    public function usersTeams()
    {
        return $this->hasMany(UserTeam::class, 'team_id', 'id');
    }
}
