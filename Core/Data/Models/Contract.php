<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Core\Data\Models\Contract
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string $contract_number
 * @property string $start_date
 * @property string|null $end_date
 * @property int $resource_id
 * @property string $resource_class Ovo je owner tog contracta
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Company> $companies
 * @property int|null $companies_count
 * @method static \Illuminate\Database\Eloquent\Builder|Contract newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract query()
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereContractNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereResourceClass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereResourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Contract whereStartDate($value)
 * @mixin \Eloquent
 */
class Contract extends Model
{
    protected $table = 'contracts';

    protected $with = ['companies'];

    public function getResourceId(): int
    {
        return $this->resource_id;
    }

    public function getResourceClass(): ?string
    {
        return $this->resource_class;
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function companies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_contracts', 'contract_id', 'company_id');
    }

    public function resource(): ?BelongsTo
    {
        $class = $this->getResourceClass();
        if (is_null($class)) {
            return null;
        }

        return $this->belongsTo($class, 'resource_id', 'id');
    }
}
