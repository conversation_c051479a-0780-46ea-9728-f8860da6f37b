<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CountryThreshold
 *
 * @property int $id
 * @property int $country_id
 * @property int $value In country local currency
 * @property string $effective_from
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\Country|null $country
 * @property mixed $is_passed
 * @property mixed $percent
 * @property-write mixed $company_id
 * @property-write mixed $current
 * @property-write mixed $passed_date
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold query()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold whereEffectiveFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryThreshold whereValue($value)
 * @mixin \Eloquent
 */
class CountryThreshold extends Model
{
    protected $table = 'country_thresholds';

    protected $appends = [
        'is_passed',
        'percent',
    ];

    public function setCurrentAttribute($value)
    {
        $this->attributes['current'] = $value;
    }

    public function getIsPassedAttribute()
    {
        return isset($this->current) ? $this->current > $this->value : false;
    }

    public function getPercentAttribute()
    {
        return isset($this->current) ? round($this->current / $this->value * 100, 2) : 0;
    }

    public function setPassedDateAttribute($value)
    {
        $this->attributes['passed_date'] = $value;
    }

    public function setCompanyIdAttribute($value)
    {
        $this->attributes['company_id'] = $value;
    }


    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function company()
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
