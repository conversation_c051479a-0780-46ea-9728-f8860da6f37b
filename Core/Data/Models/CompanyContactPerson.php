<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyContactPerson
 *
 * @property int $id
 * @property int $company_id
 * @property int $contact_person_type_id
 * @property int $person_id
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Person $person
 * @property \App\Core\Data\Models\CompanyContactPersonType $type
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson whereContactPersonTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPerson wherePersonId($value)
 * @mixin \Eloquent
 */
class CompanyContactPerson extends Model
{
    protected $table = 'company_contact_people';

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function type()
    {
        return $this->belongsTo(CompanyContactPersonType::class, 'contact_person_type_id', 'id');
    }

    public function person()
    {
        return $this->belongsTo(Person::class, 'person_id', 'id');
    }
}
