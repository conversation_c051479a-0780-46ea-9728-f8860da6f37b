<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ItemTaxCode
 *
 * @property int $id
 * @property int $item_type_id
 * @property int $taxation_method_id
 * @property string|null $description
 * @property string $code
 * @property int $item_code_category_id
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CountryItemTaxCode> $countryItemTaxCodes
 * @property int|null $country_item_tax_codes_count
 * @property string|null $full_name
 * @property \App\Core\Data\Models\ItemCodeCategory $itemCodeCategory
 * @property \App\Core\Data\Models\ItemType $itemType
 * @property \App\Core\Data\Models\TaxationMethod $taxationMethod
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode query()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode whereItemCodeCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode whereItemTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemTaxCode whereTaxationMethodId($value)
 * @mixin \Eloquent
 */
class ItemTaxCode extends Model
{
    protected $table = 'item_tax_codes';

    protected $appends = [
        'full_name'
    ];

    const A_GEN_STANDARD_CODE = 'A_GEN_STANDARD';

    const A_GEN_STANDARD = 1;
    const A_BOOKS_GEN = 2;
    const A_BOOK_ATLAS = 3;
    const A_BOOK_AUDIOBOOK = 4;
    const A_BOOK_GLOBE = 5;
    const A_BOOK_MAGAZINE = 6;
    const A_BOOK_MAP = 7;
    const A_BOOK_ADULT = 8;
    const A_CLTH_BABY = 9;
    const A_CLTH_CHILD = 10;
    const A_CLTH_PROTECTIVE = 11;
    const A_FOOD_GEN = 12;
    const A_FOOD_CAKEDECOR = 13;
    const A_FOOD_CANFRUIT = 14;
    const A_FOOD_CEREALBARS = 15;
    const A_FOOD_CEREALCHOCBARS = 16;
    const A_FOOD_CHOCEREAL = 17;
    const A_FOOD_CNDY = 18;
    const A_FOOD_COFFEE = 19;
    const A_FOOD_DAIRY = 20;
    const A_FOOD_DESSERT = 21;
    const A_FOOD_DRIEDFRUIT = 22;
    const A_FOOD_FLOUR = 23;
    const A_FOOD_MEATCHICKEN = 24;
    const A_FOOD_MISCBEVERAGE = 25;
    const A_FOOD_NAAN = 26;
    const A_FOOD_NCARBWTR = 27;
    const A_FOOD_OIL = 28;
    const A_FOOD_PASTANOODLE = 29;
    const A_FOOD_PASTRYCASE = 30;
    const A_FOOD_PLAINBISCUIT = 31;
    const A_FOOD_PLAINCRACKER = 32;
    const A_FOOD_PLAINNUT = 33;
    const A_FOOD_RICE = 34;
    const A_FOOD_SEASONINGS = 35;
    const A_FOOD_SNACK = 36;
    const A_FOOD_SODAJUICE = 37;
    const A_FOOD_SPREAD = 38;
    const A_FOOD_SWEETNER = 39;
    const A_FOOD_TEA = 40;
    const A_FOOD_VEGETABLE = 41;
    const A_FOOD_PETFOOD = 42;
    const A_FOOD_ANIMALFOOD = 43;
    const A_FOOD_ANIMALMED = 44;
    const A_FOOD_ANIMALVITAMINS = 45;
    const A_HLTH_NUTRITIONBAR = 46;
    const A_HLTH_NUTRITIONDRINK = 47;
    const A_HLTH_PILLCAPSULETABLET = 48;
    const A_HLTH_SMOKINGCESSATION = 49;
    const A_HLTH_SMOKINGGUM = 50;
    const A_HLTH_VITAMINS = 51;
    const A_HPC_CONTRACEPTIVE = 52;
    const A_HPC_DIETARYSUPPL = 53;
    const A_HPC_INCONTINENCE = 54;
    const A_HPC_MEDICINE = 55;
    const A_HPC_PPECLOTHING = 56;
    const A_HPC_PPEMASKS = 57;
    const A_HPC_PPESANITISER = 58;
    const A_HPC_SANITARYPRODUCTS = 59;
    const A_HPC_THERMOMETER = 60;
    const A_HPC_WALKINGSTICK = 61;
    const A_HPC_WHEELCHAIR = 62;
    const A_OUTDOOR_FUEL = 63;
    const A_OUTDOOR_FERTILIZER = 64;
    const A_OUTDOOR_LAWNCONTROL = 65;
    const A_OUTDOOR_PLANTFOOD = 66;
    const A_OUTDOOR_PLANTS = 67;
    const A_OUTDOOR_SEEDS = 68;
    const A_BABY_BIBCLOTH = 69;
    const A_BABY_CARSEAT = 70;
    const A_BABY_NAPPIES = 71;
    const A_GEN_NOTAX = 72;
    const E_TS_STANDARD = 73;
    const E_TS_INTERNET_ACCESS = 74;
    const E_BS_STANDARD = 75;
    const E_BS_ADULTS = 76;
    const E_BS_TRIPLE_PLAY = 77;
    const E_BS_TV_ONLY = 78;
    const E_BS_ON_DEMAND = 79;
    const E_ES_STANDARD = 80;
    const E_ES_PUBLICATIONS = 81;
    const E_ES_BOOKS = 82;
    const E_ES_NEWSPAPERS = 83;
    const E_ES_EDUCATION = 84;
    const E_ES_LIBRARIES = 85;

    public function countryItemTaxCodes()
    {
        return $this->hasMany(CountryItemTaxCode::class, 'item_tax_code_id', 'id');
    }

    public function itemType()
    {
        return $this->belongsTo(ItemType::class, 'item_type_id', 'id');
    }

    public function itemCodeCategory()
    {
        return $this->belongsTo(ItemCodeCategory::class, 'item_code_category_id', 'id');
    }

    public function taxationMethod()
    {
        return $this->belongsTo(TaxationMethod::class, 'taxation_method_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function getFullNameAttribute(): ?string
    {
        return !empty($this->description) ? $this->description . ' (' . $this->code . ')' : null;
    }
}
