<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\ItemItemIdentificationType
 *
 * @property int $id
 * @property int $item_marketplace_id
 * @property int $item_identification_type_id
 * @property string $value
 * @property \App\Core\Data\Models\ItemIdentificationType $itemIdentificationType
 * @method static Builder|ItemItemIdentificationType newModelQuery()
 * @method static Builder|ItemItemIdentificationType newQuery()
 * @method static Builder|ItemItemIdentificationType query()
 * @method static Builder|ItemItemIdentificationType whereId($value)
 * @method static Builder|ItemItemIdentificationType whereItemIdentificationTypeId($value)
 * @method static Builder|ItemItemIdentificationType whereItemMarketplaceId($value)
 * @method static Builder|ItemItemIdentificationType whereValue($value)
 * @mixin \Eloquent
 */
class ItemItemIdentificationType extends Model
{
    protected $table = 'item_item_identification_types';

    public function itemIdentificationType(): BelongsTo
    {
        return $this->belongsTo(ItemIdentificationType::class, 'item_identification_type_id', 'id');
    }
}
