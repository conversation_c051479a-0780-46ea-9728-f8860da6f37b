<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\InvoiceSubtype
 *
 * @property int $id
 * @property string $type
 * @property int $invoice_type_id
 * @property bool $convert_to_eur
 * @property bool $convert_to_gbp
 * @property string|null $note_goods
 * @property string|null $note_services
 * @property string $name
 * @property \App\Core\Data\Models\InvoiceType $invoiceType
 * @method static Builder|InvoiceSubtype newModelQuery()
 * @method static Builder|InvoiceSubtype newQuery()
 * @method static Builder|InvoiceSubtype query()
 * @method static Builder|InvoiceSubtype whereConvertToEur($value)
 * @method static Builder|InvoiceSubtype whereConvertToGbp($value)
 * @method static Builder|InvoiceSubtype whereId($value)
 * @method static Builder|InvoiceSubtype whereInvoiceTypeId($value)
 * @method static Builder|InvoiceSubtype whereNoteGoods($value)
 * @method static Builder|InvoiceSubtype whereNoteServices($value)
 * @method static Builder|InvoiceSubtype whereType($value)
 * @mixin \Eloquent
 */
class InvoiceSubtype extends Model
{
    protected $table = 'invoice_subtypes';

    protected $appends = ['name'];

    public const DOMESTIC_SALES_INVOICE = 1;
    public const EU_B2B_SALES_INVOICE = 2;
    public const DOMESTIC_B2B_SALES_INVOICE = 3;
    public const EXPORT_INVOICE = 4;
    public const EU_B2C_SALES_INVOICE = 5;
    public const DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE = 6;
    public const IOSS_EXPORT_INVOICE = 7;
    public const UK_VOEC_DEEMED_EXPORT_INVOICE = 8;
    public const PURCHASE_INVOICE = 9;
    public const EU_CUSTOMS_DECLARATION_IMPORT_VAT = 10;
    public const EU_B2B_PURCHASE_INVOICE = 11;
    public const DOMESTIC_B2B_PURCHASE_INVOICE = 12;
    public const DOMESTIC_B2B_REVERSE_CHARGE_PURCHASE_INVOICE = 13;
    public const NON_EU_B2B_PURCHASE_INVOICE = 14;
    public const DOMESTIC_PRO_FORMA_INVOICE = 15;
    public const EU_B2B_PRO_FORMA_INVOICE = 16;
    public const DOMESTIC_B2B_PRO_FORMA_INVOICE = 17;
    public const EXPORT_PRO_FORMA_INVOICE = 18;
    public const EU_B2C_PRO_FORMA_INVOICE = 19;
    public const DOMESTIC_B2B_REVERSE_CHARGE_PRO_FORMA_INVOICE = 20;
    public const IOSS_EXPORT_PRO_FORMA_INVOICE = 21;
    public const UK_VOEC_EXPORT_PRO_FORMA_INVOICE = 22;
    public const CN_DOMESTIC_SALES_INVOICE = 23;
    public const CN_EU_B2B_SALES_INVOICE = 24;
    public const CN_DOMESTIC_B2B_SALES_INVOICE = 25;
    public const CN_EXPORT_INVOICE = 26;
    public const CN_EU_B2C_SALES_INVOICE = 27;
    public const CN_DOMESTIC_B2B_REVERSE_CHARGE_SALES_INVOICE = 28;
    public const CN_IOSS_EXPORT_INVOICE = 29;
    public const CN_UK_VOEC_DEEMED_EXPORT_INVOICE = 30;
    public const DN_PURCHASE_INVOICE = 31;
    public const DN_EU_B2B_PURCHASE_INVOICE = 32;
    public const DN_DOMESTIC_B2B_PURCHASE_INVOICE = 33;
    public const DN_DOMESTIC_B2B_REVERSE_CHARGE_PURCHASE_INVOICE = 34;
    public const DN_NON_EU_B2B_PURCHASE_INVOICE = 35;
    public const UK_IMPORT_VAT_CERTIFICATE = 36;
    public const EU_CUSTOMS_PROCEDURE_42 = 37;
    public const BE_POSTPONED_IMPORT_VAT_STATEMENT = 38;
    public const FR_POSTPONED_IMPORT_VAT_STATEMENT = 39;
    public const IE_POSTPONED_IMPORT_VAT_STATEMENT = 40;
    public const LU_POSTPONED_IMPORT_VAT_STATEMENT = 41;
    public const NL_POSTPONED_IMPORT_VAT_STATEMENT = 42;
    public const PL_POSTPONED_IMPORT_VAT_STATEMENT = 43;
    public const ES_POSTPONED_IMPORT_VAT_STATEMENT = 44;
    public const SE_POSTPONED_IMPORT_VAT_STATEMENT = 45;
    public const UK_CUSTOMS_DECLARATION_C88 = 46;
    public const UK_POSTPONED_IMPORT_VAT_STATEMENT = 47;
    public const UK_IMPORT_VAT_AND_DUTY_ADJUSTMENT_STATEMENT = 48;
    public const EU_DEEMED_B2B_INVOICE = 49;
    public const CN_EU_DEEMED_B2B_INVOICE = 50;
    public const UK_DEEMED_B2B_INVOICE = 51;
    public const CN_UK_DEEMED_B2B_INVOICE = 52;
    public const PURCHASE_INVOICE_CROSS_BORDER = 53;
    public const DN_PURCHASE_INVOICE_CROSS_BORDER = 54;
    public const DEEMED_DOMESTIC_SALES_INVOICE = 55;
    public const DEEMED_EU_B2C_SALES_INVOICE = 56;
    public const DEEMED_EXPORT_INVOICE = 57;
    public const DEEMED_IOSS_EXPORT_INVOICE = 58;
    public const DEEMED_CN_DOMESTIC_SALES_INVOICE = 59;
    public const DEEMED_CN_EU_B2C_SALES_INVOICE = 60;
    public const DEEMED_CN_EXPORT_INVOICE = 61;
    public const DEEMED_CN_IOSS_EXPORT_INVOICE = 62;
    public const UK_VOEC_EXPORT_INVOICE = 63;
    public const CN_UK_VOEC_EXPORT_INVOICE = 64;

    public const CROSS_BORDER_B2C_SALES_INVOICE = 65;
    public const CN_CROSS_BORDER_B2C_SALES_INVOICE = 66;
    public const DEEMED_CROSS_BORDER_B2C_SALES_INVOICE = 67;
    public const DEEMED_CN_CROSS_BORDER_B2C_SALES_INVOICE = 68;

    public function getNameAttribute(): string
    {
        $key = 'invoice-subtypes.';
        $name = ltrim($this->type, $key);

        return _l($name);
    }

    public function invoiceType(): BelongsTo
    {
        return $this->belongsTo(InvoiceType::class, 'invoice_type_id', 'id');
    }
}
