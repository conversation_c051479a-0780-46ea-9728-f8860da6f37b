<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CountryCurrency
 *
 * @property int $id
 * @property int $country_id
 * @property int $currency_id
 * @property string|null $start_date
 * @property string|null $end_date
 * @property \App\Core\Data\Models\Country $country
 * @property \App\Core\Data\Models\Currency $currency
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency query()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency whereCurrencyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryCurrency whereStartDate($value)
 * @mixin \Eloquent
 */
class CountryCurrency extends Model
{
    protected $table = 'country_currencies';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id', 'id');
    }
}
