<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\OssReport
 *
 * @property int $id
 * @property int $country_id
 * @property int $tax_scheme_group_id
 * @property int $tax_scheme_name_id
 * @property string $start_date
 * @property string|null $end_date
 * @property string|null $local_scheme_name
 * @property string|null $local_scheme_short_name
 * @property-read Country $country
 * @property-read TaxSchemeGroup $taxSchemeGroup
 * @property-read TaxSchemeName $taxSchemeName
 * @method static Builder|OssIossReport newModelQuery()
 * @method static Builder|OssIossReport newQuery()
 * @method static Builder|OssIossReport query()
 * @method static Builder|OssIossReport whereCompanyId($value)
 * @method static Builder|OssIossReport whereCountryId($value)
 * @method static Builder|OssIossReport whereCreatedAt($value)
 * @method static Builder|OssIossReport whereData($value)
 * @method static Builder|OssIossReport whereDateFrom($value)
 * @method static Builder|OssIossReport whereDateTo($value)
 * @method static Builder|OssIossReport whereId($value)
 * @method static Builder|OssIossReport whereStatusId($value)
 * @mixin Eloquent
 */
class TaxScheme extends Model
{
    protected $table = 'tax_schemes';

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    /** @noinspection PhpUnused */
    public function taxSchemeGroup(): HasOne
    {
        return $this->hasOne(TaxSchemeGroup::class, 'id', 'tax_scheme_group_id');
    }

    /** @noinspection PhpUnused */
    public function taxSchemeName(): HasOne
    {
        return $this->hasOne(TaxSchemeName::class, 'id', 'tax_scheme_name_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
