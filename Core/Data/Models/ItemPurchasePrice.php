<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\ItemPurchasePrice
 *
 * @property int $id
 * @property int $currency_id
 * @property int $item_marketplace_id
 * @property string $start_date
 * @property float $net_price
 * @property float $cost
 * @property float $price
 * @property \App\Core\Data\Models\Currency|null $currency
 * @method static Builder|ItemPurchasePrice newModelQuery()
 * @method static Builder|ItemPurchasePrice newQuery()
 * @method static Builder|ItemPurchasePrice query()
 * @method static Builder|ItemPurchasePrice whereCost($value)
 * @method static Builder|ItemPurchasePrice whereCurrencyId($value)
 * @method static Builder|ItemPurchasePrice whereId($value)
 * @method static Builder|ItemPurchasePrice whereItemMarketplaceId($value)
 * @method static Builder|ItemPurchasePrice whereNetPrice($value)
 * @method static Builder|ItemPurchasePrice wherePrice($value)
 * @method static Builder|ItemPurchasePrice whereStartDate($value)
 * @mixin \Eloquent
 */
class ItemPurchasePrice extends Model
{
    public $table = 'item_purchase_prices';
    protected $casts = [
        'net_price' => 'double',
        'cost'      => 'double',
        'price'     => 'double',
    ];

    public function currency(): HasOne
    {
        return $this->hasOne(Currency::class, 'id', 'currency_id');
    }
}
