<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\IdentificationNumberType
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $identificationNumbers
 * @property int|null $identification_numbers_count
 * @method static Builder|IdentificationNumberType newModelQuery()
 * @method static Builder|IdentificationNumberType newQuery()
 * @method static Builder|IdentificationNumberType query()
 * @method static Builder|IdentificationNumberType whereId($value)
 * @method static Builder|IdentificationNumberType whereName($value)
 * @mixin \Eloquent
 */
class IdentificationNumberType extends Model
{
    protected $table = 'identification_number_types';

    public const IOSS_NUMBER = 1;
    public const OSS_NUMBER = 2;
    public const VAT_NUMBER = 3;
    public const TAX_NUMBER = 4;
    public const GERMANY_ECONOMIC_ID = 5;

    public function identificationNumbers(): HasMany
    {
        return $this->hasMany(IdentificationNumber::class, 'identification_number_type_id', 'id');
    }
}
