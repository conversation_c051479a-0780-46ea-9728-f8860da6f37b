<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\ExpenseType
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int|null $parent_expense_type_id
 * @property-read \Illuminate\Database\Eloquent\Collection|ExpenseType[] $childrenExpenseTypes
 * @property-read int|null $children_expense_types_count
 * @property-read ExpenseType|null $parentExpenseType
 * @method static Builder|ExpenseType newModelQuery()
 * @method static Builder|ExpenseType newQuery()
 * @method static Builder|ExpenseType query()
 * @method static Builder|ExpenseType whereDescription($value)
 * @method static Builder|ExpenseType whereId($value)
 * @method static Builder|ExpenseType whereName($value)
 * @method static Builder|ExpenseType whereParentExpenseTypeId($value)
 * @mixin \Eloquent
 */
class ExpenseType extends Model
{
    protected $table = 'expenses_types';

    /** @noinspection PhpUnused */
    public function parentExpenseType(): BelongsTo
    {
        return $this->belongsTo(ExpenseType::class, 'id', 'parent_expense_type_id');
    }

    /** @noinspection PhpUnused */
    public function childrenExpenseTypes(): HasMany
    {
        return $this->hasMany(ExpenseType::class, 'parent_expense_type_id', 'id')
            ->with('childrenExpenseTypes');
    }
}
