<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Log
 *
 * @property int $id
 * @property int|null $user_id
 * @property string $level
 * @property mixed|null $message
 * @property mixed|null $content
 * @property string $created_at
 * @property mixed $content_code
 * @property \App\Core\Data\Models\User|null $user
 * @method static Builder|Log newModelQuery()
 * @method static Builder|Log newQuery()
 * @method static Builder|Log query()
 * @method static Builder|Log whereContent($value)
 * @method static Builder|Log whereCreatedAt($value)
 * @method static Builder|Log whereId($value)
 * @method static Builder|Log whereLevel($value)
 * @method static Builder|Log whereMessage($value)
 * @method static Builder|Log whereUserId($value)
 * @mixin \Eloquent
 */
class Log extends Model
{
    protected $table = 'log';
    protected $appends = ['content_code'];

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /** @noinspection PhpUnused */
    public function setContentAttribute(array $content): void
    {
        $this->attributes['content'] = json_encode($content);
    }

    /** @noinspection PhpUnused */
    public function getContentAttribute(): mixed
    {
        $data = json_decode($this->attributes['content'], true);
        if (is_array($data) && count($data) < 1) {
            $data = null;
        }

        return $data;
    }

    /** @noinspection PhpUnused */
    public function getMessageAttribute(): ?string
    {
        return $this->replaceQuotes($this->attributes['message']);
    }

    /** @noinspection PhpUnused */
    public function getContentCodeAttribute(): ?string
    {
        $content = $this->content;
        if (count($content) < 1) {
            return 'Array()';
        }
        $content = print_r($content, true);

        return $this->replaceQuotes($content);
    }

    private function replaceQuotes(?string $message = null): ?string
    {
        if (is_null($message)) {
            return null;
        }

        return trim(str_replace('"', '”', $message));
    }
}
