<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyInstallRequest
 *
 * @property int $id
 * @property string $shop_name
 * @property string $created_at
 * @property string|null $shopify_access_token
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest whereShopName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyInstallRequest whereShopifyAccessToken($value)
 * @mixin \Eloquent
 */
class ShopifyInstallRequest extends Model
{
    protected $table = 'shopify_install_requests';
}
