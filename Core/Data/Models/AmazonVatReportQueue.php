<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\AmazonVatReportQueue
 *
 * @property array|null $errors
 * @property bool $has_errors
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonVatReportQueue newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonVatReportQueue newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonVatReportQueue query()
 * @mixin \Eloquent
 */
class AmazonVatReportQueue extends Model
{
    protected $table = 'amazon_vat_report_queue';

    protected $appends = [
        'errors',
        'hasErrors',
    ];

    public function getHasErrorsAttribute(): bool
    {
        return !is_null($this->errors);
    }

    public function getErrorsAttribute(): ?array
    {
        $errors = $this->getRawOriginal('errors');
        if (is_null($errors)) {
            return null;
        }

        $errors = json_decode($errors, true);
        if (count($errors) < 1) {
            $errors = null;
        }

        return $errors;
    }
}
