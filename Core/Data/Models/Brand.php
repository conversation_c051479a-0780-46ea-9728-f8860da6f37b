<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFilesTrait;

/**
 * App\Core\Data\Models\Brand
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\File> $files
 * @property int|null $files_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Item> $items
 * @property int|null $items_count
 * @method static \Illuminate\Database\Eloquent\Builder|Brand newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Brand newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Brand query()
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Brand whereName($value)
 * @mixin \Eloquent
 */
class Brand extends Model implements UploadableContract
{
    use HasFilesTrait;

    protected $table = 'brands';

    public function items()
    {
        return $this->hasMany(Item::class, 'id', 'brand_id');
    }

    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('brands/' . $dir . '/' . $this->getUploadResourceId()) . '/';
    }

    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }
}
