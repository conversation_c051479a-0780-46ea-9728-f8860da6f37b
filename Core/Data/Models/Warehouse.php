<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\Warehouse
 *
 * @property int $id
 * @property string $uid
 * @property string $name
 * @property int $country_id
 * @property int $warehouse_operator_id
 * @property string|null $street
 * @property string|null $street_no
 * @property int|null $city_id
 * @property int|null $county_id
 * @property int|null $state_id
 * @property bool $global Global is FALSE if the warehouse was created by a company, otherwise warehouse belongs to some platform/marketplace
 * @property string|null $postal_code
 * @property int|null $platform_id
 * @property int|null $propose_company_id
 * @property string $source U for user(manual propose, S for system (from csv or other API))
 * @property bool $confirmed
 * @property string $created_at
 * @property int $create_user_id
 * @property string|null $updated_at
 * @property int|null $update_user_id
 * @property string|null $search_postal_code_uppercase
 * @property string|null $search_postal_code_uppercase_no_blanks
 * @property \App\Core\Data\Models\City|null $city
 * @property \App\Core\Data\Models\Country $country
 * @property \App\Core\Data\Models\County|null $county
 * @property \App\Core\Data\Models\Platform|null $platform
 * @property \App\Core\Data\Models\Company|null $proposeCompany
 * @property \App\Core\Data\Models\State|null $state
 * @property \App\Core\Data\Models\WarehouseOperator $warehouseOperator
 * @method static Builder|Warehouse newModelQuery()
 * @method static Builder|Warehouse newQuery()
 * @method static Builder|Warehouse query()
 * @method static Builder|Warehouse whereCityId($value)
 * @method static Builder|Warehouse whereConfirmed($value)
 * @method static Builder|Warehouse whereCountryId($value)
 * @method static Builder|Warehouse whereCountyId($value)
 * @method static Builder|Warehouse whereCreateUserId($value)
 * @method static Builder|Warehouse whereCreatedAt($value)
 * @method static Builder|Warehouse whereGlobal($value)
 * @method static Builder|Warehouse whereId($value)
 * @method static Builder|Warehouse whereName($value)
 * @method static Builder|Warehouse wherePlatformId($value)
 * @method static Builder|Warehouse wherePostalCode($value)
 * @method static Builder|Warehouse whereProposeCompanyId($value)
 * @method static Builder|Warehouse whereSearchPostalCodeUppercase($value)
 * @method static Builder|Warehouse whereSearchPostalCodeUppercaseNoBlanks($value)
 * @method static Builder|Warehouse whereSource($value)
 * @method static Builder|Warehouse whereStateId($value)
 * @method static Builder|Warehouse whereStreet($value)
 * @method static Builder|Warehouse whereStreetNo($value)
 * @method static Builder|Warehouse whereUid($value)
 * @method static Builder|Warehouse whereUpdateUserId($value)
 * @method static Builder|Warehouse whereUpdatedAt($value)
 * @method static Builder|Warehouse whereWarehouseOperatorId($value)
 * @mixin \Eloquent
 */
class Warehouse extends Model
{
    protected $table = 'warehouses';

    public const ALL_VIEWS_PROPOSED_CACHE_KEY = 'proposedWarehousesMenuNotificationCount';

    public const SOURCE_USER = 'U';
    public const SOURCE_SYSTEM = 'S';

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function warehouseOperator(): BelongsTo
    {
        return $this->belongsTo(WarehouseOperator::class, 'warehouse_operator_id');
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function county(): BelongsTo
    {
        return $this->belongsTo(County::class, 'county_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(Platform::class, 'platform_id');
    }

    public function proposeCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'propose_company_id');
    }
}
