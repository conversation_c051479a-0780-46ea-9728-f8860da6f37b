<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\CommodityCodeType
 *
 * @property int $id
 * @property string $name make it uppercase ALWAYS!
 * @property int|null $sequence
 * @method static Builder|CommodityCodeType newModelQuery()
 * @method static Builder|CommodityCodeType newQuery()
 * @method static Builder|CommodityCodeType query()
 * @method static Builder|CommodityCodeType whereId($value)
 * @method static Builder|CommodityCodeType whereName($value)
 * @method static Builder|CommodityCodeType whereSequence($value)
 * @mixin \Eloquent
 */
class CommodityCodeType extends Model
{
    protected $table = 'commodity_code_types';

    public const HS_CODE = 1;
    public const CN_CODE = 2;
    public const TARIC = 3;
    public const EZT = 4;

    /** @noinspection PhpUnused */
    public function setNameAttribute($value): void
    {
        $this->attributes['name'] = mb_strtoupper($value);
    }
}
