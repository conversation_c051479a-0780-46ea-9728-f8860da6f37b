<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\Translation
 *
 * @property int $id
 * @property string $key
 * @property string $locale
 * @property string|null $value
 * @property string $changed_at
 * @property int $changed_by
 * @property \App\Core\Data\Models\User $changeUser
 * @property Collection $connected
 * @property \Illuminate\Database\Eloquent\Collection<int, Translation> $rawConnectedTranslations
 * @property int|null $raw_connected_translations_count
 * @method static \Illuminate\Database\Eloquent\Builder|Translation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Translation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Translation query()
 * @method static \Illuminate\Database\Eloquent\Builder|Translation whereChangedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Translation whereChangedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Translation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Translation whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Translation whereLocale($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Translation whereValue($value)
 * @mixin \Eloquent
 */
class Translation extends Model
{
    protected $table = 'translations';

    public const LOCALES = [
        'en',
        'cn',
        'fr',
    ];

    public function rawConnectedTranslations(): HasMany
    {
        return $this->hasMany(Translation::class, 'key', 'key')->where('locale', '!=', 'en');
    }

    public function getConnectedAttribute(): Collection
    {
        $rawTranslations = $this->rawConnectedTranslations->keyBy('locale');
        $translations = collect();
        foreach (self::LOCALES as $locale) {
            if ($locale === 'en') {
                continue;
            }

            $raw = $rawTranslations->get($locale);
            if (is_null($raw)) {
                $raw = $this->newModelInstance();
                $raw->locale = $locale;
                $raw->key = $this->key;
            }

            $translations->put($locale, $raw);
        }

        return $translations;
    }

    public function changeUser()
    {
        return $this->belongsTo(User::class, 'changed_by', 'id')->withoutGlobalScopes();
    }
}
