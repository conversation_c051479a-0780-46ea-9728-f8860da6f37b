<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;

/**
 * App\Core\Data\Models\UserManualMedia
 *
 * @property int $id
 * @property string $type
 * @property int $user_manual_id
 * @property \App\Core\Data\Models\File|null $file
 * @property \App\Core\Data\Models\UserManual $userManual
 * @method static \Illuminate\Database\Eloquent\Builder|UserManualMedia newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserManualMedia newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserManualMedia query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserManualMedia whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManualMedia whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserManualMedia whereUserManualId($value)
 * @mixin \Eloquent
 */
class UserManualMedia extends Model implements UploadableContract
{
    use HasFileTrait;

    protected $table = 'user_manual_media';

    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('user-manual-media/' . $dir . '/' . $this->getUploadResourceId()) . '/';
    }

    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function userManual()
    {
        return $this->belongsTo(UserManual::class, 'user_manual_id', 'id');
    }
}
