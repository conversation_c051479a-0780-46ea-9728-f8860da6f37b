<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Unit
 *
 * @property int $id
 * @property string $name
 * @property string|null $short_name
 * @property int $unit_type_id
 * @property mixed $shor_name
 * @property \App\Core\Data\Models\UnitType $unitType
 * @method static \Illuminate\Database\Eloquent\Builder|Unit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Unit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Unit query()
 * @method static \Illuminate\Database\Eloquent\Builder|Unit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Unit whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Unit whereShortName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Unit whereUnitTypeId($value)
 * @mixin \Eloquent
 */
class Unit extends Model
{
    protected $table = 'units';

    const KG = 1;
    const CM = 2;
    const PIECE = 3;

    public function getShorNameAttribute()
    {
        $shortName = $this->short_name;
        if (is_null($shortName)) {
            $shortName = $this->name;
        }

        return $shortName;
    }

    public function unitType()
    {
        return $this->belongsTo(UnitType::class, 'unit_type_id', 'id');
    }
}
