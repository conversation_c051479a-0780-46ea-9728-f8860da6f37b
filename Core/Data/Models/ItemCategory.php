<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ItemCategory
 *
 * @property int $id
 * @property string $name
 * @property int|null $parent_category_id
 * @property ItemCategory|null $parentCategory
 * @property \Illuminate\Database\Eloquent\Collection<int, ItemCategory> $subCategories
 * @property int|null $sub_categories_count
 * @method static \Illuminate\Database\Eloquent\Builder|ItemCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemCategory whereParentCategoryId($value)
 * @mixin \Eloquent
 */
class ItemCategory extends Model
{
    protected $table = 'item_categories';

    public function parentCategory()
    {
        return $this->belongsTo(ItemCategory::class, 'parent_category_id', 'id');
    }

    public function subCategories()
    {
        return $this->hasMany(ItemCategory::class, 'parent_category_id', 'id');
    }

    public function getAllParents(): array
    {
        $category = $this;
        $parents[] = $category;
        while ($category->parentCategory) {
            array_unshift($parents, $category->parentCategory()->withCount('subCategories')->first());
            $category = $category->parentCategory;
        }

        return $parents;
    }

    public function getCategoryPathFromParents(array $parents): string
    {
        $parentNames = collect($parents)->pluck('name');
        return implode('/', $parentNames->toArray());
    }
}
