<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CountryAccountant
 *
 * @method static \Illuminate\Database\Eloquent\Builder|CountryAccountant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryAccountant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryAccountant query()
 * @mixin \Eloquent
 */
class CountryAccountant extends Model
{
    protected $table = 'country_accountants';

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
