<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyMandatoryWebhookRequestType
 *
 * @property int $id
 * @property string $type
 * @property string $description
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequestType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequestType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequestType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequestType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequestType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequestType whereType($value)
 * @mixin \Eloquent
 */
class ShopifyMandatoryWebhookRequestType extends Model
{
    const CUSTOMERS_DATA_REQUEST = 1;
    const CUSTOMERS_REDACT = 2;
    const SHOP_REDACT = 3;

    protected $table = 'shopify_mandatory_webhook_request_types';
}
