<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\ItemCommodityCodeType
 *
 * @property int $id
 * @property int $item_id
 * @property int $commodity_code_type_id
 * @property string $value
 * @property \App\Core\Data\Models\CommodityCodeType $commodityCodeType
 * @method static Builder|ItemCommodityCodeType newModelQuery()
 * @method static Builder|ItemCommodityCodeType newQuery()
 * @method static Builder|ItemCommodityCodeType query()
 * @method static Builder|ItemCommodityCodeType whereCommodityCodeTypeId($value)
 * @method static Builder|ItemCommodityCodeType whereId($value)
 * @method static Builder|ItemCommodityCodeType whereItemId($value)
 * @method static Builder|ItemCommodityCodeType whereValue($value)
 * @mixin \Eloquent
 */
class ItemCommodityCodeType extends Model
{
    protected $table = 'item_commodity_code_types';

    /** @noinspection PhpUnused */
    public function commodityCodeType(): BelongsTo
    {
        return $this->belongsTo(CommodityCodeType::class, 'commodity_code_type_id', 'id');
    }
}
