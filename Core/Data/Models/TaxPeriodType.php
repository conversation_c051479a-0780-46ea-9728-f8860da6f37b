<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\TaxPeriodType
 *
 * @property int $id
 * @property string $value
 * @property string $name
 * @method static Builder|TaxPeriodType newModelQuery()
 * @method static Builder|TaxPeriodType newQuery()
 * @method static Builder|TaxPeriodType query()
 * @method static Builder|TaxPeriodType whereId($value)
 * @method static Builder|TaxPeriodType whereName($value)
 * @method static Builder|TaxPeriodType whereValue($value)
 * @mixin \Eloquent
 */
class TaxPeriodType extends Model
{
    protected $table = 'tax_period_types';

    public const M = 1;
    public const Q = 2;
    public const A = 3;
}
