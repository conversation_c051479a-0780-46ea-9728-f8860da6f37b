<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\IossNumberType
 *
 * @property int $id
 * @property string $lang_key
 * @property string $code
 * @method static \Illuminate\Database\Eloquent\Builder|IossNumberType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IossNumberType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|IossNumberType query()
 * @method static \Illuminate\Database\Eloquent\Builder|IossNumberType whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IossNumberType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|IossNumberType whereLangKey($value)
 * @mixin \Eloquent
 */
class IossNumberType extends Model
{
    const IMPORT_SCHEME = 1;
    const INTERMEDIARY = 2;
    const TAXABLE_PERSON = 3;

    protected $table = 'ioss_number_types';
}
