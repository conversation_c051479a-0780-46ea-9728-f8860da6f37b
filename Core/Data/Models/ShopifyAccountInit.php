<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyAccountInit
 *
 * @property int $id
 * @property int $ecommerce_account_id
 * @property string|null $warehouses_imported_at
 * @property string|null $items_imported_at
 * @property string|null $invoices_imported_at
 * @property bool $products_create_webhook_subscribed
 * @property bool $products_update_webhook_subscribed
 * @property bool $locations_create_webhook_subscribed
 * @property bool $locations_update_webhook_subscribed
 * @property mixed $account_initialized
 * @property \App\Core\Data\Models\SalesChannel $salesChannel
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereEcommerceAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereInvoicesImportedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereItemsImportedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereLocationsCreateWebhookSubscribed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereLocationsUpdateWebhookSubscribed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereProductsCreateWebhookSubscribed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereProductsUpdateWebhookSubscribed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyAccountInit whereWarehousesImportedAt($value)
 * @mixin \Eloquent
 */
class ShopifyAccountInit extends Model
{
    protected $table = 'shopify_accounts_init';

    public function salesChannel()
    {
        return $this->belongsTo(SalesChannel::class, 'ecommerce_account_id', 'id');
    }

    public function getAccountInitializedAttribute()
    {
        return !is_null($this->invoices_imported_at)
            && !is_null($this->warehouses_imported_at)
            && $this->locations_create_webhook_subscribed
            && $this->locations_update_webhook_subscribed;
    }
}
