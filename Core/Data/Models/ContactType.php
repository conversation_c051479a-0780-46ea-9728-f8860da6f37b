<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ContactType
 *
 * @property int $id
 * @property string $type
 * @property string $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Contact> $contacts
 * @property int|null $contacts_count
 * @property bool $is_for_institution
 * @property bool $is_for_person
 * @method static \Illuminate\Database\Eloquent\Builder|ContactType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContactType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContactType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ContactType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactType whereType($value)
 * @mixin \Eloquent
 */
class ContactType extends Model
{
    const PHONE = 1;
    const MOBILE = 2;
    const EMAIL = 3;
    const LINKEDIN = 4;
    const WECHAT = 5;
    const WHATSAPP = 6;
    const MESSENGER = 7;
    const QQ = 8;
    const ASANA = 9;
    const FAX = 10;
    const WEBSITE = 11;

    protected $table = 'contact_types';
    protected $appends = [
        'is_for_person',
        'is_for_institution',
    ];

    public function contacts()
    {
        return $this->hasMany(Contact::class, 'contact_type_id', 'id');
    }

    public static function getTypeKey(int $id): string|null
    {
        switch ($id) {
            case self::PHONE:
                return 'phone';
            case self::MOBILE:
                return 'mobile';
            case self::EMAIL:
                return 'email';
            case self::LINKEDIN:
                return 'linkedin';
            case self::WECHAT:
                return 'wechat';
            case self::WHATSAPP:
                return 'whatsapp';
            case self::MESSENGER:
                return 'messenger';
            case self::QQ:
                return 'qq';
            case self::ASANA:
                return 'asana';
            case self::FAX:
                return 'fax';
            default:
                return null;
        }
    }

    public function getIsForPersonAttribute(): bool
    {
        return in_array($this->id, [
            self::EMAIL,
            self::PHONE,
            self::MOBILE,
        ]);
    }

    public function getIsForInstitutionAttribute(): bool
    {
        return in_array($this->id, [
            self::WECHAT,
            self::QQ,
            self::ASANA,
            self::WHATSAPP,
            self::MESSENGER
        ]);
    }
}
