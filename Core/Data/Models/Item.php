<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Traits\HasSourceTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Item
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property int $company_id
 * @property int|null $manufacture_country_id
 * @property int $item_tax_code_id
 * @property int $item_type_id
 * @property int|null $unit_id
 * @property int|null $business_model_id
 * @property int|null $item_category_id
 * @property int|null $brand_id
 * @property string|null $unit_value
 * @property string|null $manufacturer
 * @property array|null $identifiers Only used for users visual help. contains all marketplaces item identification types
 * @property string|null $identifiers_text
 * @property int|null $purchase_price_currency_id
 * @property float|null $purchase_price_cost
 * @property float|null $purchase_price_net
 * @property float|null $purchase_price
 * @property string $created_at
 * @property bool $is_purchase_price_guessed
 * @property int|null $resource_id
 * @property string|null $resource
 * @property \App\Core\Data\Models\Brand|null $brand
 * @property \App\Core\Data\Models\BusinessModel|null $businessModel
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\ItemCategory|null $itemCategory
 * @property \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemCommodityCodeType[] $itemCommodityCodeTypes
 * @property int|null $item_commodity_code_types_count
 * @property \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemItemIdentificationType[] $itemItemIdentificationTypes
 * @property int|null $item_item_identification_types_count
 * @property \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemPurchasePrice[] $propertyPurchasePrices
 * @property int|null $item_marketplace_purchase_prices_count
 * @property \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemSalePrice[] $propertySalePrices
 * @property int|null $item_marketplace_sale_prices_count
 * @property \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemMarketplace[] $itemMarketplaces
 * @property int|null $item_marketplaces_count
 * @property \App\Core\Data\Models\Currency|null $itemPurchasePriceCurrency
 * @property \App\Core\Data\Models\ItemTaxCode|null $itemTaxCode
 * @property \App\Core\Data\Models\ItemType $itemType
 * @property \App\Core\Data\Models\Country|null $manufactureCountry
 * @property \Illuminate\Database\Eloquent\Model|\Eloquent $source
 * @property \App\Core\Data\Models\Unit|null $unit
 * @method static Builder|Item newModelQuery()
 * @method static Builder|Item newQuery()
 * @method static Builder|Item query()
 * @method static Builder|Item whereBrandId($value)
 * @method static Builder|Item whereBusinessModelId($value)
 * @method static Builder|Item whereCompanyId($value)
 * @method static Builder|Item whereCreatedAt($value)
 * @method static Builder|Item whereDescription($value)
 * @method static Builder|Item whereId($value)
 * @method static Builder|Item whereIdentifiers($value)
 * @method static Builder|Item whereIdentifiersText($value)
 * @method static Builder|Item whereIsPurchasePriceGuessed($value)
 * @method static Builder|Item whereItemCategoryId($value)
 * @method static Builder|Item whereItemTaxCodeId($value)
 * @method static Builder|Item whereItemTypeId($value)
 * @method static Builder|Item whereManufactureCountryId($value)
 * @method static Builder|Item whereManufacturer($value)
 * @method static Builder|Item whereName($value)
 * @method static Builder|Item wherePurchasePrice($value)
 * @method static Builder|Item wherePurchasePriceCost($value)
 * @method static Builder|Item wherePurchasePriceCurrencyId($value)
 * @method static Builder|Item wherePurchasePriceNet($value)
 * @method static Builder|Item whereResource($value)
 * @method static Builder|Item whereResourceId($value)
 * @method static Builder|Item whereUnitId($value)
 * @method static Builder|Item whereUnitValue($value)
 * @mixin \Eloquent
 */
class Item extends Model
{
    use HasSourceTrait;

    protected $table = 'items';

    protected $casts = [
        'identifiers'         => 'array',
        'purchase_price_net'  => 'float',
        'purchase_price_cost' => 'float',
        'purchase_price'      => 'float',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function manufactureCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'manufacture_country_id', 'id');
    }

    public function itemType(): BelongsTo
    {
        return $this->belongsTo(ItemType::class, 'item_type_id', 'id');
    }

    public function itemCategory(): BelongsTo
    {
        return $this->belongsTo(ItemCategory::class, 'item_category_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function businessModel(): BelongsTo
    {
        return $this->belongsTo(BusinessModel::class, 'business_model_id', 'id');
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'brand_id', 'id');
    }

    public function itemTaxCode(): HasOne
    {
        return $this->hasOne(ItemTaxCode::class, 'id', 'item_tax_code_id');
    }

    /** @noinspection PhpUnused */
    public function itemCommodityCodeTypes(): HasMany
    {
        return $this->hasMany(ItemCommodityCodeType::class, 'item_id', 'id');
    }

    public function itemMarketplaces(): HasMany
    {
        return $this->hasMany(ItemMarketplace::class, 'item_id', 'id');
    }

    public function unit(): HasOne
    {
        return $this->hasOne(Unit::class, 'id', 'unit_id');
    }

    /** @noinspection PhpUnused */
    public function itemPurchasePriceCurrency(): HasOne
    {
        return $this->hasOne(Currency::class, 'id', 'purchase_price_currency_id');
    }

    public function itemItemIdentificationTypes(): HasManyThrough
    {
        return $this->hasManyThrough(ItemItemIdentificationType::class, ItemMarketplace::class, 'item_id', 'item_marketplace_id', 'id', 'id');
    }

    /** @noinspection PhpUnused */
    public function itemMarketplaceSalePrices(): HasManyThrough
    {
        return $this->hasManyThrough(
            ItemSalePrice::class,
            ItemMarketplace::class,
            'item_id',
            'item_marketplace_id',
            'id',
            'id'
        )
            ->orderBy(ItemSalePrice::getTableName() . '.start_date', 'DESC');
    }

    /** @noinspection PhpUnused */
    public function itemMarketplacePurchasePrices(): HasManyThrough
    {
        return $this->hasManyThrough(
            ItemPurchasePrice::class,
            ItemMarketplace::class,
            'item_id',
            'item_marketplace_id',
            'id',
            'id'
        )->orderBy(ItemPurchasePrice::getTableName() . '.start_date', 'DESC');
    }
}
