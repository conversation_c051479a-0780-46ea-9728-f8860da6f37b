<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Traits\HasFileTrait;
use App\Core\Data\Models\Contracts\UploadableContract;

/**
 * App\Core\Data\Models\ImportGoodsReverseDocument
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string|null $start_date
 * @property string|null $expiration_date
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country $country
 * @property \App\Core\Data\Models\File|null $file
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument query()
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument whereExpirationDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImportGoodsReverseDocument whereStartDate($value)
 * @mixin \Eloquent
 */
class ImportGoodsReverseDocument extends Model implements UploadableContract
{
    use HasFileTrait;

    protected $table = 'import_goods_reverse_documents';

    public bool $ifFileAppendId = false;

    /**
     * @return string
     */
    public function getUploadPath(): string
    {
        return upload_path('import_goods_reverse') . '/' . $this->getUploadResourceId();
    }

    /**
     * @return int
     */
    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
