<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\TaxCollectionResponsibility
 *
 * @property int $id
 * @property string $name
 * @property string|null $amazon_code
 * @property string|null $note
 * @method static Builder|TaxCollectionResponsibility newModelQuery()
 * @method static Builder|TaxCollectionResponsibility newQuery()
 * @method static Builder|TaxCollectionResponsibility query()
 * @method static Builder|TaxCollectionResponsibility whereAmazonCode($value)
 * @method static Builder|TaxCollectionResponsibility whereId($value)
 * @method static Builder|TaxCollectionResponsibility whereName($value)
 * @method static Builder|TaxCollectionResponsibility whereNote($value)
 * @mixin \Eloquent
 */
class TaxCollectionResponsibility extends Model
{
    protected $table = 'tax_collection_responsibilities';

    public const SELLER = 1;
    public const MARKETPLACE = 2;
    public const BUYER = 3;
}
