<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\ReportType
 *
 * @property int $id
 * @property string $name
 * @method static Builder|ReportType newModelQuery()
 * @method static Builder|ReportType newQuery()
 * @method static Builder|ReportType query()
 * @method static Builder|ReportType whereId($value)
 * @method static Builder|ReportType whereName($value)
 * @mixin \Eloquent
 */
class ReportType extends Model
{
    protected $table = 'report_types';

    public const TYPE_VAT = 1;
    public const TYPE_ESL = 2;
}
