<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Traits\HasHistoryTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\Company
 *
 * @property int $id
 * @property string|null $partner_code
 * @property string $full_legal_name
 * @property string|null $short_name
 * @property string|null $web
 * @property string|null $registration_number
 * @property string $legal_type
 * @property string|null $incorporation_date
 * @property string $business_activity
 * @property float|null $share_capital
 * @property int|null $share_capital_currency_id
 * @property bool|null $using_eu_warehouses
 * @property bool|null $are_manufacturers
 * @property bool|null $have_goods_supplier
 * @property bool|null $providing_services
 * @property bool|null $selling_goods
 * @property string|null $goods_supplier_company_name
 * @property int|null $goods_supplier_company_address_id
 * @property string|null $goods_shipment_company_name
 * @property int|null $goods_shipment_company_address_id
 * @property string|null $import_document_company_name
 * @property int|null $import_document_company_address_id
 * @property string|null $goods_import_description
 * @property string|null $eori_number
 * @property int $address_id
 * @property int|null $status_id
 * @property int $partner_id
 * @property string|null $deactivate_date
 * @property string|null $uk_eori_number
 * @property int|null $business_type_id
 * @property string|null $trade_name
 * @property string|null $registration_number_issuer
 * @property string|null $krs_number This is the Polish national court register number (KRS)
 * @property int|null $national_law_governing_country_id
 * @property string|null $company_description
 * @property string|null $place_of_commercial_registry
 * @property string|null $siret
 * @property string|null $national_eu_identification_number
 * @property int|null $auditing_address_id
 * @property int|null $billing_address_id
 * @property string $created_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\InstitutionTypeCountryPartner[] $accountants
 * @property-read int|null $accountants_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\RegistrationProcess[] $activeRegistrationProcesses
 * @property-read int|null $active_registration_processes_count
 * @property-read \App\Core\Data\Models\Address|null $address
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\SalesChannel[] $amazonAccounts
 * @property-read int|null $amazon_accounts_count
 * @property-read \App\Core\Data\Models\Address|null $auditingAddress
 * @property-read \App\Core\Data\Models\Bank|null $bank
 * @property-read \App\Core\Data\Models\Address|null $billingAddress
 * @property-read \App\Core\Data\Models\BusinessType|null $businessType
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Invitation[] $clientToAccountantInvitations
 * @property-read int|null $client_to_accountant_invitations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Invitation[] $clientToClientInvitations
 * @property-read int|null $client_to_client_invitations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\User[] $clientUsers
 * @property-read int|null $client_users_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyAccountant[] $companyAccountants
 * @property-read int|null $company_accountants_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyApiConsumers[] $companyApiConsumers
 * @property-read int|null $company_api_consumers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyTaxPaymentData[] $companyTaxPaymentData
 * @property-read int|null $company_tax_payment_data_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyUserRole[] $companyUsersRoles
 * @property-read int|null $company_users_roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyWarehouse[] $companyWarehouses
 * @property-read int|null $company_warehouses_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyContactPerson[] $contactPeople
 * @property-read int|null $contact_people_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ContactType[] $contactTypes
 * @property-read int|null $contact_types_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Contact[] $contacts
 * @property-read int|null $contacts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyCountrySale[] $countrySales
 * @property-read int|null $country_sales_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\SalesChannel[] $ebayAccounts
 * @property-read int|null $ebay_accounts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyEuThreshold[] $euThresholds
 * @property-read int|null $eu_thresholds_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\FlatVatRate[] $flatRates
 * @property-read int|null $flat_rates_count
 * @property-read \App\Core\Data\Models\IossNumber|null $active_ioss_number
 * @property-read \App\Core\Data\Models\OssNumber|null $active_oss_number
 * @property-read \App\Core\Data\Models\OssNumber|null $active_voes_number
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\IdentificationNumber[] $identificationNumbers
 * @property-read string $country_name
 * @property-read string|null $email
 * @property-read string|null $fax
 * @property-read string|null $name
 * @property-read string|null $phone
 * @property-read Collection $registration_process_countries
 * @property-read string $share_capital_currency_code
 * @property-read string|null $status_name
 * @property-read \App\Core\Data\Models\Country|null $goodsImportCountry
 * @property-read \App\Core\Data\Models\Address|null $goodsShipmentCompanyAddress
 * @property-read \App\Core\Data\Models\Address|null $goodsSupplierCompanyAddress
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\History[] $history
 * @property-read int|null $history_count
 * @property-read \App\Core\Data\Models\Address|null $importDocumentCompanyAddress
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ImportFromCountry[] $importFromCountries
 * @property-read int|null $import_from_countries_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ImportGoodsReverseDocument[] $importGoodsReverseDocuments
 * @property-read int|null $import_goods_reverse_documents_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\IossNumber[] $iossNumbers
 * @property-read int|null $ioss_numbers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyLegalRepresentative[] $legalRepresentatives
 * @property-read int|null $legal_representatives_count
 * @property-read \App\Core\Data\Models\Country|null $nationalLawGoverningCountry
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\OnlineShop[] $onlineShops
 * @property-read int|null $online_shops_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\OssNumber[] $ossNumbers
 * @property-read int|null $oss_numbers_count
 * @property-read \App\Core\Data\Models\InstitutionInstitutionType $partner
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Invitation[] $partnerToClientInvitations
 * @property-read int|null $partner_to_client_invitations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\PaymentRecipient[] $paymentRecipients
 * @property-read int|null $payment_recipients_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyTaxPeriodType[] $periodTypes
 * @property-read int|null $period_types_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ProductType[] $productTypes
 * @property-read int|null $product_types_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\RegistrationProcess[] $registrationProcesses
 * @property-read int|null $registration_processes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\SalesChannel[] $salesChannels
 * @property-read int|null $sales_channels_count
 * @property-read \App\Core\Data\Models\Currency|null $shareCapitalCurrency
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Shareholder[] $shareholders
 * @property-read int|null $shareholders_count
 * @property-read \App\Core\Data\Models\CompanyStatus|null $status
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\TaxNumber[] $taxNumbers
 * @property-read int|null $tax_numbers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\TaxOffice[] $taxOffices
 * @property-read int|null $tax_offices_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\CompanyCountryThreshold[] $thresholds
 * @property-read int|null $thresholds_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\VatNumber[] $vatNumbers
 * @property-read int|null $vat_numbers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\Waiver[] $waivers
 * @property-read int|null $waivers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\RegistrationWarehouseUsage[] $warehouseUsages
 * @property-read int|null $warehouse_usages_count
 * @method static Builder|Company newModelQuery()
 * @method static Builder|Company newQuery()
 * @method static Builder|Company query()
 * @method static Builder|Company whereAddressId($value)
 * @method static Builder|Company whereAreManufacturers($value)
 * @method static Builder|Company whereAuditingAddressId($value)
 * @method static Builder|Company whereBillingAddressId($value)
 * @method static Builder|Company whereBusinessActivity($value)
 * @method static Builder|Company whereBusinessTypeId($value)
 * @method static Builder|Company whereCompanyDescription($value)
 * @method static Builder|Company whereCreatedAt($value)
 * @method static Builder|Company whereDeactivateDate($value)
 * @method static Builder|Company whereEoriNumber($value)
 * @method static Builder|Company whereFullLegalName($value)
 * @method static Builder|Company whereGoodsImportDescription($value)
 * @method static Builder|Company whereGoodsShipmentCompanyAddressId($value)
 * @method static Builder|Company whereGoodsShipmentCompanyName($value)
 * @method static Builder|Company whereGoodsSupplierCompanyAddressId($value)
 * @method static Builder|Company whereGoodsSupplierCompanyName($value)
 * @method static Builder|Company whereHaveGoodsSupplier($value)
 * @method static Builder|Company whereId($value)
 * @method static Builder|Company whereImportDocumentCompanyAddressId($value)
 * @method static Builder|Company whereImportDocumentCompanyName($value)
 * @method static Builder|Company whereIncorporationDate($value)
 * @method static Builder|Company whereKrsNumber($value)
 * @method static Builder|Company whereLegalType($value)
 * @method static Builder|Company whereNationalEuIdentificationNumber($value)
 * @method static Builder|Company whereNationalLawGoverningCountryId($value)
 * @method static Builder|Company wherePartnerCode($value)
 * @method static Builder|Company wherePartnerId($value)
 * @method static Builder|Company wherePlaceOfCommercialRegistry($value)
 * @method static Builder|Company whereProvidingServices($value)
 * @method static Builder|Company whereRegistrationNumber($value)
 * @method static Builder|Company whereRegistrationNumberIssuer($value)
 * @method static Builder|Company whereSellingGoods($value)
 * @method static Builder|Company whereShareCapital($value)
 * @method static Builder|Company whereShareCapitalCurrencyId($value)
 * @method static Builder|Company whereShortName($value)
 * @method static Builder|Company whereSiret($value)
 * @method static Builder|Company whereStatusId($value)
 * @method static Builder|Company whereTradeName($value)
 * @method static Builder|Company whereUkEoriNumber($value)
 * @method static Builder|Company whereUsingEuWarehouses($value)
 * @method static Builder|Company whereWeb($value)
 * @property-read int|null $identification_numbers_count
 * @mixin \Eloquent
 */
class Company extends Model
{
    use HasHistoryTrait;

    protected $table = 'companies';

    /** @noinspection PhpUnused */
    const COMMON_COMPANY_ID = 1;

    protected $casts = [
        'share_capital' => 'float'
    ];

    protected $appends = ['name'];

    protected static function boot(): void
    {
        parent::boot();
    }

    /** @noinspection PhpUnused */
    public function shareCapitalCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'share_capital_currency_id', 'id');
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function contactTypes(): HasManyThrough
    {
        return $this->hasManyThrough(ContactType::class, Contact::class, 'company_id', 'id');
    }

    public function bank(): HasOne
    {
        return $this->hasOne(Bank::class, 'company_id', 'id');
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(CompanyStatus::class, 'status_id', 'id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class, 'id', 'address_id');
    }

    /** @noinspection PhpUnused */
    public function auditingAddress(): HasOne
    {
        return $this->hasOne(Address::class, 'id', 'auditing_address_id');
    }

    /** @noinspection PhpUnused */
    public function billingAddress(): HasOne
    {
        return $this->hasOne(Address::class, 'id', 'billing_address_id');
    }

    /** @noinspection PhpUnused */
    public function nationalLawGoverningCountry(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'national_law_governing_country_id');
    }

    /** @noinspection PhpMissingReturnTypeInspection */
    public function salesChannels()
    {
        return $this->hasMany(SalesChannel::class, 'company_id', 'id')->orderBy('id');
    }

    /** @noinspection PhpUnused */
    public function flatRates(): HasMany
    {
        return $this->hasMany(FlatVatRate::class, 'company_id', 'id');
    }

    public function vatNumbers(): HasMany
    {
        return $this->hasMany(VatNumber::class)->orderBy('end_date', 'DESC');
    }

    public function companyAccountants(): HasMany
    {
        return $this->hasMany(CompanyAccountant::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function companyWarehouses(): HasMany
    {
        return $this->hasMany(CompanyWarehouse::class)->orderBy('first_time_used');
    }

    /** @noinspection PhpUnused */
    public function companyUsersRoles(): HasMany
    {
        return $this->hasMany(CompanyUserRole::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function clientUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'companies_users_roles', 'company_id', 'user_id');
    }

    public function waivers(): HasMany
    {
        return $this->hasMany(Waiver::class);
    }

    public function thresholds(): HasMany
    {
        return $this->hasMany(CompanyCountryThreshold::class)->orderBy('year_id');
    }

    /** @noinspection PhpUnused */
    public function euThresholds(): HasMany
    {
        return $this->hasMany(CompanyEuThreshold::class, 'company_id', 'id');
    }

    public function taxNumbers(): HasMany
    {
        return $this->hasMany(TaxNumber::class)->orderBy('end_date', 'DESC');
    }

    /**
     * Direction is IMPORTANT
     *
     * @noinspection PhpUnused
     */

    public function periodTypes(): HasMany
    {
        return $this->hasMany(CompanyTaxPeriodType::class, 'company_id', 'id')->orderBy('start_date');
    }

    /** @noinspection PhpUnused */
    public function amazonAccounts(): HasMany
    {
        return $this->hasMany(SalesChannel::class)->where('platform_id', '=', Platform::PLATFORM_AMAZON);
    }

    /** @noinspection PhpUnused */
    public function ebayAccounts(): HasMany
    {
        return $this->hasMany(SalesChannel::class)->where('platform_id', '=', Platform::PLATFORM_EBAY);
    }

    /** @noinspection PhpUnused */
    public function onlineShops(): HasMany
    {
        return $this->hasMany(OnlineShop::class);
    }

    /** @noinspection PhpUnused */
    public function contactPeople(): HasMany
    {
        return $this->hasMany(CompanyContactPerson::class)
            ->select('company_contact_people.*')
            ->leftJoin('people', 'people.id', '=', 'company_contact_people.person_id')
            ->orderBy('people.last_name');
    }

    /** @noinspection PhpUnused */
    public function goodsSupplierCompanyAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'goods_supplier_company_address_id');
    }

    /** @noinspection PhpUnused */
    public function goodsShipmentCompanyAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'goods_shipment_company_address_id');
    }

    /** @noinspection PhpUnused */
    public function importDocumentCompanyAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'import_document_company_address_id');
    }

    public function shareholders(): HasMany
    {
        return $this->hasMany(Shareholder::class);
    }

    public function warehouseUsages(): HasMany
    {
        return $this->hasMany(RegistrationWarehouseUsage::class);
    }

    public function countrySales(): HasMany
    {
        return $this->hasMany(CompanyCountrySale::class);
    }

    public function countrySale(int $countryId): CompanyCountrySale|null
    {
        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return $this->countrySales()
            ->where('country_id', $countryId)
            ->first();
    }

    /** @noinspection PhpUnused */
    public function importFromCountries(): HasMany
    {
        return $this->hasMany(ImportFromCountry::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function goodsImportCountry(): HasOneThrough
    {
        return $this->hasOneThrough(
            Country::class,
            ImportFromCountry::class,
            'company_id',
            'id',
            'id',
            'country_id'
        )->orderBy('import_from_countries.id');
    }

    public function taxOffices(): BelongsToMany
    {
        return $this->belongsToMany(TaxOffice::class, 'company_tax_offices', 'company_id', 'tax_office_id');
    }

    public function taxOfficeInCountry($countryId): ?TaxOffice
    {
        return $this->taxOffices
            ->where('country_id', $countryId)
            ->first();
    }

    public function getNameAttribute(): ?string
    {
        $name = $this->full_legal_name;
        $partnerCode = $this->partner_code;
        if (is_null($name)) {
            return $partnerCode;
        }

        if (is_null($partnerCode)) {
            return $name;
        }

        return '[' . $partnerCode . '] - ' . $name;
    }

    /** @noinspection PhpUnused */
    public function getShareCapitalCurrencyCodeAttribute(): string
    {
        return $this->shareCapitalCurrency->code;
    }

    public function getEmailAttribute(): ?string
    {
        return $this->contacts->where('contact_type_id', ContactType::EMAIL)->pluck('value')->first();
    }

    public function getPhoneAttribute(): ?string
    {
        return $this->contacts->where('contact_type_id', ContactType::PHONE)->pluck('value')->first();
    }

    /** @noinspection PhpUnused */
    public function getFaxAttribute(): ?string
    {
        return $this->contacts->where('contact_type_id', ContactType::FAX)->pluck('value')->first();
    }

    /** @noinspection PhpUnused */
    public function getStatusNameAttribute(): ?string
    {
        return $this->status->name;
    }

    /** @noinspection PhpUnused */
    public function getCountryNameAttribute(): string
    {
        return $this->address->country->name;
    }

    public function accountants(): BelongsToMany
    {
        return $this->belongsToMany(InstitutionTypeCountryPartner::class, 'company_accountants', 'company_id', 'accountant_id');
    }

    /** @noinspection PhpUnused */
    public function identificationNumbers(): HasMany
    {
        return $this->hasMany(IdentificationNumber::class, 'resource_id', 'id')
            ->where('resource', $this::class)
            ->orderBy('start_date')
            ->orderBy('id', 'desc');
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(InstitutionInstitutionType::class, 'partner_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function registrationProcesses(): HasMany
    {
        return $this->hasMany(RegistrationProcess::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function activeRegistrationProcesses()
    {
        $allowedCountryIds = null;
        if (sd()->isServiceProvider()) {
            $allowedCountryIds = $this
                ->accountants
                ->where('institution_institution_type_id', sd()->getCurrentInstitutionInstitutionTypeId())
                ->pluck('country_id')
                ->toArray();
        }
        if (is_null($allowedCountryIds)) {
            return $this->hasMany(RegistrationProcess::class)
                ->whereNotIn('registration_status_id', [RegistrationStatus::REGISTERED, RegistrationStatus::CANCELED]);
        } else {
            return $this->hasMany(RegistrationProcess::class)
                ->whereIn('country_id', $allowedCountryIds)
                ->whereNotIn('registration_status_id', [RegistrationStatus::REGISTERED, RegistrationStatus::CANCELED]);
        }

    }

    /** @noinspection PhpUnused */
    public function getRegistrationProcessCountriesAttribute(): Collection
    {
        $registrationProcesses = $this->registrationProcesses->load('country');

        return $registrationProcesses->map(function ($registrationProcess) {
            return $registrationProcess->country;
        });
    }

    /** @noinspection PhpUnused */
    public function importGoodsReverseDocuments(): HasMany
    {
        return $this->hasMany(ImportGoodsReverseDocument::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function businessType(): BelongsTo
    {
        return $this->belongsTo(BusinessType::class, 'business_type_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function partnerToClientInvitations(): HasMany
    {
        return $this->hasMany(Invitation::class, 'company_id', 'id')
            ->where('invitations.invitation_type_id', InvitationType::PARTNER_TO_CLIENT);
    }

    /** @noinspection PhpUnused */
    public function clientToClientInvitations(): HasMany
    {
        return $this->hasMany(Invitation::class, 'company_id', 'id')
            ->where('invitations.invitation_type_id', InvitationType::CLIENT_TO_CLIENT);
    }

    /** @noinspection PhpUnused */
    public function clientToAccountantInvitations(): HasMany
    {
        return $this->hasMany(Invitation::class, 'inviting_company_id', 'id')
            ->where('invitations.invitation_type_id', InvitationType::CLIENT_TO_ACCOUNTANT);
    }

    /** @noinspection PhpUnused */
    public function ossNumbers(): HasMany
    {
        return $this->hasMany(OssNumber::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function iossNumbers(): HasMany
    {
        return $this->hasMany(IossNumber::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getActiveOssNumberAttribute(): ?OssNumber
    {
        return $this->ossNumbers
            ->whereNull('end_date')
            ->where('type_id', OssNumberType::UNION_SCHEME)
            ->sortByDesc('register_date')
            ->first();
    }

    public function getActiveOssNumberOnDate(string $date): ?OssNumber
    {
        $date = Carbon::parse($date);

        return $this->ossNumbers
            ->sortByDesc('register_date')
            ->filter(function (OssNumber $number) use ($date) {
                if (!is_null($number->end_date)) {
                    return false;
                }

                if ($number->type_id !== OssNumberType::UNION_SCHEME) {
                    return false;
                }

                $numberDate = Carbon::parse($number->register_date);

                return $numberDate->lte($date);
            })->first();
    }

    public function getActiveVoesNumberOnDate(string $date): ?OssNumber
    {
        $date = Carbon::parse($date);

        return $this->ossNumbers
            ->sortByDesc('register_date')
            ->filter(function (OssNumber $number) use ($date) {
                if (!is_null($number->end_date)) {
                    return false;
                }

                if ($number->type_id !== OssNumberType::NON_UNION_SCHEME) {
                    return false;
                }

                $numberDate = Carbon::parse($number->register_date);

                return $numberDate->lte($date);
            })->first();
    }

    /** @noinspection PhpUnused */
    public function getActiveVoesNumberAttribute(): ?OssNumber
    {
        return $this->ossNumbers
            ->whereNull('end_dates')
            ->where('type_id', OssNumberType::NON_UNION_SCHEME)
            ->sortByDesc('register_date')
            ->first();
    }

    public function getActiveIossNumberOnDate(string $date): ?IossNumber
    {
        $date = Carbon::parse($date);

        return $this->iossNumbers
            ->sortByDesc('register_date')
            ->filter(function (IossNumber $number) use ($date) {
                if (!is_null($number->end_date)) {
                    return false;
                }

                $numberDate = Carbon::parse($number->register_date);

                return $numberDate->lte($date);
            })->first();
    }

    /**
     * @param string $date
     * @param int|null $countryId
     * @return \Illuminate\Support\Collection<\App\Core\Data\Models\VatNumber>
     */
    public function getActiveVatNumbersOnDate(string $date, ?int $countryId = null): Collection
    {
        $date = Carbon::parse($date);

        return $this->vatNumbers->filter(function (VatNumber $vatNumber) use ($date, $countryId) {
            if (!is_null($countryId)) {
                if ($vatNumber->country_id !== $countryId) {
                    return false;
                }
            }

            $registerDate = Carbon::parse($vatNumber->register_date);
            $check = $date->gte($registerDate);
            $endDate = $vatNumber->end_date;
            if (!is_null($endDate)) {
                $endDate = Carbon::parse($endDate);
                $check = $check && $date->lt($endDate);
            }

            return $check;
        })->values();
    }

    /** @noinspection PhpUnused */
    public function getActiveIossNumberAttribute(): ?IossNumber
    {
        return $this->iossNumbers
            ->whereNull('end_date')
            ->sortByDesc('register_date')
            ->first();
    }

    /** @noinspection PhpUnused */
    public function legalRepresentatives(): HasMany
    {
        return $this->hasMany(CompanyLegalRepresentative::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function paymentRecipients(): HasMany
    {
        return $this->hasMany(PaymentRecipient::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function companyTaxPaymentData(): HasMany
    {
        return $this->hasMany(CompanyTaxPaymentData::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function companyApiConsumers(): HasMany
    {
        return $this->hasMany(CompanyApiConsumers::class, 'company_id', 'id');
    }

    public function hasWavierInCountryForDate(int $countryId, string $date): bool
    {
        $date = strtotime($date);

        return $this->waivers
                ->filter(function (Waiver $wavier) use ($countryId, $date) {
                    if ($wavier->country_id !== $countryId) {
                        return false;
                    }

                    $start = strtotime($wavier->start_date);
                    $end = $wavier->end_date;

                    $check = $date >= $start;
                    if (!is_null($end)) {
                        $end = strtotime($end);
                        $check = $check && ($date < $end);
                    }

                    return $check;
                })->count() > 0;
    }

    public function hasVatNumberInCountryForDate(int $countryId, string $date): bool
    {
        $date = Carbon::parse($date);
        /**
         * @var \App\Core\Data\Models\VatNumber|null $vatNumber
         */
        $vatNumber = $this->vatNumbers
            ->groupBy('country_id')
            ->get($countryId, collect())
            ->filter(function (VatNumber $vatNumber) use ($date) {
                $startDate = Carbon::parse($vatNumber->calculated_register_date);
                $endDate = $vatNumber->end_date;
                if (!is_null($endDate)) {
                    $endDate = Carbon::parse($endDate);
                }

                if ($date->lt($startDate)) {
                    return false;
                }

                if (!is_null($endDate) && $date->gte($endDate)) {
                    return false;
                }

                return true;
            })->first();

        return !is_null($vatNumber);
    }

    public function hasWarehouseInCountryForDate(int $countryId, string $date): bool
    {
        $date = strtotime($date);

        return $this->companyWarehouses
                ->loadMissing('warehouse')
                ->filter(function (CompanyWarehouse $companyWarehouse) use ($countryId, $date) {
                    $warehouse = $companyWarehouse->warehouse;
                    if ($warehouse->country_id !== $countryId) {
                        return false;
                    }

                    $start = strtotime($companyWarehouse->first_time_used);

                    return $date >= $start;
                })->count() > 0;
    }

    public function isOnlyVatNumberAndWarehouseInCompanyCountry(string $date): bool
    {
        $date = strtotime($date);

        $vatNumbers = $this->vatNumbers
            ->filter(function (VatNumber $vatNumber) use ($date) {
                $startDate = $vatNumber->register_date;
                if (is_null($startDate)) {
                    $startDate = $vatNumber->first_time_in_report;
                }

                if (is_null($startDate)) {
                    return false;
                }

                $endDate = $vatNumber->end_date;
                $startDate = strtotime($startDate);
                $check = $date >= $startDate;
                if (!is_null($endDate)) {
                    $endDate = strtotime($endDate);
                    $check = $check && ($date < $endDate);
                }

                return $check;
            });

        if ($vatNumbers->count() !== 1) {
            return false;
        }

        $companyCountryId = $this->address->country_id;
        /**
         * @var VatNumber $vatNumber
         */
        $vatNumber = $vatNumbers->first();
        $vatNumberCountryId = $vatNumber->country_id;
        if ($vatNumberCountryId !== $companyCountryId) {
            return false;
        }

        $companyWarehouses = $this->companyWarehouses
            ->filter(function (CompanyWarehouse $companyWarehouse) use ($date) {
                $firstTimeUsed = $companyWarehouse->first_time_used;
                if (is_null($firstTimeUsed)) {
                    return false;
                }

                $firstTimeUsed = strtotime($firstTimeUsed);

                return $date >= $firstTimeUsed;
            });

        if ($companyWarehouses->count() !== 1) {
            return false;
        }

        /**
         * @var \App\Core\Data\Models\CompanyWarehouse $companyWarehouse
         */
        $companyWarehouse = $companyWarehouses->first();
        if ($companyWarehouse->warehouse->country_id !== $companyCountryId) {
            return false;
        }

        return true;
    }

    public function hasOnlyVatNumberAndItIsInCompanyCountry(string $date): bool
    {
        $date = Carbon::parse($date);
        $companyCountryId = $this?->address?->country_id ?? null;
        if (is_null($companyCountryId)) {
            return false;
        }

        $vatNumbers = $this->vatNumbers
            ->filter(function (VatNumber $vatNumber) use ($date) {
                $startDate = $vatNumber->register_date ?? $vatNumber->first_time_in_report;
                if (is_null($startDate)) {
                    return false;
                }

                $startDate = Carbon::parse($startDate);
                $endDate = $vatNumber->end_date;
                $check = $date->gte($startDate);
                if (!is_null($endDate)) {
                    $endDate = Carbon::parse($endDate);
                    $check = $check && ($date->lt($endDate));
                }

                return $check;
            })->keyBy('country_id');

        if ($vatNumbers->count() !== 1) {
            return false;
        }

        $companyCountryVatNumber = $vatNumbers->get($companyCountryId);

        return !is_null($companyCountryVatNumber);
    }

    public function whetherTheCompanyHasPassedThresholdInCountryOnDate(string $date, Country $country): bool
    {
        $date = strtotime($date);
        $breakpoint = strtotime('2021-07-01');

        $thresholds = $this->thresholds;
        if ($date >= $breakpoint) {
            $thresholds = $this->euThresholds;
        }

        if ($thresholds->count() < 1) {
            return false;
        }

        if ($date >= $breakpoint) {
            $inEuStartDate = $country->in_eu_start_date;
            $inEuEndDate = $country->in_eu_end_date;
            if (is_null($inEuStartDate)) {
                return true;
            }

            $inEuStartDate = strtotime($inEuStartDate);
            $checkCountryInEu = $date >= $inEuStartDate;
            if (!is_null($inEuEndDate)) {
                $inEuEndDate = strtotime($inEuEndDate);
                $checkCountryInEu = $checkCountryInEu && ($date < $inEuEndDate);
            }
            if (!$checkCountryInEu) {
                return true;
            }

            return $thresholds->filter(function (CompanyCountryThreshold $threshold) use ($date) {
                    $thresholdDate = strtotime($threshold->date);

                    return $date >= $thresholdDate;
                })->count() > 0;
        } else {
            $thresholds = $thresholds->filter(function (CompanyCountryThreshold $threshold) use ($country, $date) {
                if ($threshold->country_id !== $country->id) {
                    return false;
                }

                $thresholdDate = strtotime($threshold->date);

                return $date >= $thresholdDate;
            });

            return $thresholds->count() > 0;
        }
    }

    public function productTypes(): BelongsToMany
    {
        return $this->belongsToMany(ProductType::class, 'company_product_types', 'company_id', 'product_type_id');
    }

    public function isCompanyCountryVatNumberPermanentEstablishmentForDate(string $date): bool
    {
        $companyCountryId = $this->address->country_id;

        return $this->isVatNumberInCountryOnDatePermanentEstablishment($date, $companyCountryId, true);
    }

    public function isVatNumberInCountryOnDatePermanentEstablishment(string $date, int $countryId, bool $default = false): bool
    {
        $date = Carbon::parse($date);
        /**
         * @var \App\Core\Data\Models\VatNumber|null $vatNumber
         */
        $vatNumber = $this->vatNumbers
            ->groupBy('country_id')
            ->get($countryId, collect())
            ->filter(function (VatNumber $vatNumber) use ($date) {
                $startDate = Carbon::parse($vatNumber->calculated_register_date);
                $endDate = $vatNumber->end_date;
                if (!is_null($endDate)) {
                    $endDate = Carbon::parse($endDate);
                }

                if ($date->lt($startDate)) {
                    return false;
                }

                if (!is_null($endDate) && $date->gte($endDate)) {
                    return false;
                }

                return true;
            })->first();

        if (is_null($vatNumber)) {
            return $default;
        }

        return $vatNumber->permanent_establishment;
    }
}
