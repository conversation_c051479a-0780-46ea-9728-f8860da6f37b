<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * App\Core\Data\Models\Address
 *
 * @property int $id
 * @property string $street
 * @property string|null $house_number
 * @property string|null $addition
 * @property int $city_id
 * @property string $postal_code
 * @property string|null $state
 * @property int $country_id
 * @property \App\Core\Data\Models\City $city
 * @property \App\Core\Data\Models\Country $country
 * @property string $display_address
 * @property string $elster_address
 * @property string|null $full_address_only
 * @property string|null $full_city_only
 * @property string|null $full_country_only
 * @method static Builder|Address newModelQuery()
 * @method static Builder|Address newQuery()
 * @method static Builder|Address query()
 * @method static Builder|Address whereAddition($value)
 * @method static Builder|Address whereCityId($value)
 * @method static Builder|Address whereCountryId($value)
 * @method static Builder|Address whereHouseNumber($value)
 * @method static Builder|Address whereId($value)
 * @method static Builder|Address wherePostalCode($value)
 * @method static Builder|Address whereState($value)
 * @method static Builder|Address whereStreet($value)
 * @mixin \Eloquent
 */
class Address extends Model
{
    protected $table = 'addresses';

    protected $appends = [
        'display_address',
        'elster_address',
    ];

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id', 'id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function getDisplayAddressAttribute(): string
    {
        $address = [
            $this->full_address_only ?? 'N/A',
            $this->full_city_only ?? 'N/A',
            $this->full_country_only ?? 'N/A'
        ];

        return implode(', ', $address);
    }

    /** @noinspection PhpUnused */
    public function getElsterAddressAttribute(): string
    {
        $address = $this->street ?? 'N/A';
        if (!is_null($this->house_number)) {
            $address .= ' ' . $this->house_number;
        }

        $address = trim($address);
        $address = Str::ascii($address);
        $removeChars = [',', '.', '/', '\\', '-', '"', '\'', '(', ')', '[', ']', '{', '}', ';', ':', '?', '!', '@', '#', '$', '%', '^', '&', '*', '`', '~', '<', '>', '|', '+'];
        foreach ($removeChars as $char) {
            $address = str_replace($char, '', $address);
        }

        return Str::limit($address, 30, '');
    }

    /** @noinspection PhpUnused */
    public function getFullAddressOnlyAttribute(): ?string
    {
        $address = null;
        if (!is_null($this->street)) {
            $address = $this->street;

            if (!is_null($this->house_number)) {
                $address .= ' ' . $this->house_number;
            }

            if (!is_null($this->addition)) {
                $address .= ' ' . $this->addition;
            }
        }

        $address = trim($address);
        if (mb_strlen($address) < 1) {
            $address = null;
        }

        return $address;
    }

    /** @noinspection PhpUnused */
    public function getFullCityOnlyAttribute(): ?string
    {
        $city = null;
        if (!is_null($this->postal_code)) {
            $city = $this->postal_code;
        }

        if (!is_null($this->city->name ?? null)) {
            $city .= ' ' . $this->city->name;
        }

        $city = trim($city);
        if (mb_strlen($city) < 1) {
            $city = null;
        }

        return $city;
    }

    /** @noinspection PhpUnused */
    public function getFullCountryOnlyAttribute(): ?string
    {
        $country = null;
        if (!is_null($this->country->name ?? null)) {
            $country = $this->country->name;
        }

        $country = trim($country);
        if (mb_strlen($country) < 1) {
            $country = null;
        }

        return $country;
    }
}
