<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Shareholder
 *
 * @property int $id
 * @property bool $is_person
 * @property bool $is_dominant
 * @property string $name
 * @property float $capital_value_percent
 * @property string|null $birth_date
 * @property int $address_id
 * @property int|null $company_id
 * @property int|null $birth_country_id
 * @property string|null $place_of_birth
 * @property string|null $passport_number
 * @property string|null $passport_valid_from
 * @property string|null $passport_valid_until
 * @property int|null $gender_type_id
 * @property int|null $passport_country_id
 * @property string|null $passport_issued_by
 * @property string|null $nationality
 * @property bool $passport_is_id_card If this field is "true" passport columns are ID card columns
 * @property int|null $institution_id
 * @property \App\Core\Data\Models\Address $address
 * @property \App\Core\Data\Models\Country|null $birthCountry
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\GenderType|null $genderType
 * @property mixed $display_address
 * @property mixed $display_name
 * @property mixed $first_name
 * @property mixed $last_name
 * @property \App\Core\Data\Models\Country|null $passportCountry
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder query()
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereBirthCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereBirthDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereCapitalValuePercent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereGenderTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereInstitutionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereIsDominant($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereIsPerson($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder whereNationality($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePassportCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePassportIsIdCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePassportIssuedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePassportNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePassportValidFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePassportValidUntil($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Shareholder wherePlaceOfBirth($value)
 * @mixin \Eloquent
 */
class Shareholder extends Model
{
    protected $table = 'shareholders';

    protected $appends = [
        'display_name',
        'display_address'
    ];

    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function getFirstNameAttribute()
    {
        $nameArray = explode("|", $this->name, 2);

        return $nameArray[0] ?? null;
    }

    public function getLastNameAttribute()
    {
        $nameArray = explode("|", $this->name, 2);

        return $nameArray[1] ?? null;
    }

    public function getDisplayNameAttribute()
    {
        return $this->is_person ? ($this->firstName . ' ' . $this->lastName) : $this->name;
    }

    public function getDisplayAddressAttribute()
    {
        return $this->address->displayAddress;
    }

    public function birthCountry()
    {
        return $this->hasOne(Country::class, 'id', 'birth_country_id');
    }

    public function passportCountry()
    {
        return $this->hasOne(Country::class, 'id', 'passport_country_id');
    }

    public function genderType()
    {
        return $this->hasOne(GenderType::class, 'id', 'gender_type_id');
    }
}
