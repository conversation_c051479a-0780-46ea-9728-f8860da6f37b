<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\VatRateType
 *
 * @property int $id
 * @property string $value
 * @property string $column_name
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType query()
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType whereColumnName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VatRateType whereValue($value)
 * @mixin \Eloquent
 */
class VatRateType extends Model
{
    protected $table = 'vat_rate_types';

    const S = 1;
    const R1 = 2;
    const R2 = 3;
    const R3 = 4;
    const PR = 5;
    const  Z = 6;
    const EXEMPT = 7;

    public function getNameAttribute()
    {
        return _l($this->getAttributeFromArray('name'));
    }
}
