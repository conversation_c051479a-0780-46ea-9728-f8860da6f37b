<?php

namespace App\Core\Data\Models\Contracts;

use App\Core\Data\Models\User;
use Carbon\Carbon;

interface TimelineModelContract
{
    /**
     * @return int
     */
    public function getId(): int;

    /**
     * @return array
     */
    public function getLangParams(): array;

    /**
     * Method return description text.
     * Description could be Html. If larger Html you could use
     * \Illuminate\View\View render method.
     * Create blade template, put html inside and call:
     *
     * return view('view-name', $optionalData)->render();
     * or
     * return (string)view('view-name', $optionalData);
     *
     * @return string
     */
    public function getDescription(): string;

    /**
     * Method returns event time
     *
     * @return Carbon
     */
    public function getTime(): Carbon;

    /**
     * Method returns event created user
     *
     * @return User
     */
    public function getUser(): User;

    public function getTimelineResourceId(): ?int;

    public function getExtraData(): array;
}
