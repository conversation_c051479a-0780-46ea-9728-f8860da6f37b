<?php

namespace App\Core\Data\Models\Contracts;

use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model as BaseModel;
use Illuminate\Support\Facades\Schema;
use League\Fractal\TransformerAbstract;

/**
 *
 * @method static Builder|Model newModelQuery()
 * @method static Builder|Model newQuery()
 * @method static Builder|Model query()
 * @mixin \Eloquent
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @mixin \Illuminate\Database\Query\Builder
 */
class Model extends BaseModel
{
    public $timestamps = false;

    protected $fillable = ['*'];

    protected static $unguarded = true;

    public bool $ifFileAppendId = true;

    public static function getTableName(): string
    {
        return with(new static)->getTable();
    }

    public function transformData(TransformerAbstract $transformer): TransformedObjectContract
    {
        return transform_data($this, $transformer);
    }

    public function reloadRelations(): self
    {
        $relations = $this->getAllRelationsNames();
        $this->unsetRelations();
        $this->load($relations);

        return $this;
    }

    public static function getAllTableColumns(): array
    {
        $instance = new static();

        return Schema::getColumnListing($instance->table);
    }

    public function getAllRelationsNames(): array
    {
        return array_keys($this->getRelations());
    }
}
