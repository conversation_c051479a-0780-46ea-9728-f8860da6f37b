<?php

namespace App\Core\Data\Models\Contracts;

use Illuminate\Support\Collection;

interface CurrentUserContract
{
    /**
     * @return int
     */
    public function getId(): int;

    /**
     * @return string
     */
    public function getEmail(): string;

    /**
     * @return string
     */
    public function getFullName(): string;

    /**
     * @return string
     */
    public function getLocale(): string;

    /**
     * @return bool
     */
    public function isDeveloper(): bool;

    /**
     * @return array
     */
    public function toArray(): array;

    /**
     * @return Collection
     */
    public function getAllInstitutionInstitutionTypeUserRoles(): Collection;

    /**
     * @return bool
     */
    public function isClientType(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderType(): bool;

    /**
     * @return bool
     */
    public function isCrmType(): bool;

    /**
     * @return bool
     */
    public function isCrmOrServiceProviderType(): bool;

    /**
     * Returns roles for selected company/institution
     *
     * @return Collection
     */
    public function getCurrentAvailableRoles(): Collection;

    /**
     * @param int $roleId
     * @return bool
     */
    public function hasRole(int $roleId): bool;

    /**
     * @return bool
     */
    public function isAdministrative(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderAdmin(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderTeamMember(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderRegistrationTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderRegistrationTeamMember(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderComplianceTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderComplianceTeamMember(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderCustomerSupportTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isServiceProviderCustomerSupportTeamMember(): bool;

    // Checks partner

    /**
     * @return bool
     */
    public function isCrmAdmin(): bool;

    /**
     * @return bool
     */
    public function isCrmTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isCrmTeamMember(): bool;

    /**
     * @return bool
     */
    public function isCrmRegistrationTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isCrmRegistrationTeamMember(): bool;

    /**
     * @return bool
     */
    public function isPartnerComplianceTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isCrmComplianceTeamMember(): bool;

    /**
     * @return bool
     */
    public function isCrmCustomerSupportTeamLeader(): bool;

    /**
     * @return bool
     */
    public function isCrmCustomerSupportTeamMember(): bool;

    // Checks client

    /**
     * @return bool
     */
    public function isClientAdmin(): bool;

    /**
     * DEV purpose only
     * DUMP-s all roles for user
     */
    public function dumpAllRoles(): void;

    /**
     * Checks if is in admins roles (partner or accountant admin)
     *
     * @return bool
     */
    public function hasOneOfAdminRoles(): bool;

    /**
     * @return bool
     */
    public function hasEvatAdminRoles(): bool;

    /**
     * is user partner Registration Team Leader or partner Registration Team Member
     *
     * @return bool
     */
    public function hasPartnerRegistrationTeamRole(): bool;

    /**
     * @return bool
     */
    public function hasPartnerComplianceTeamRole(): bool;

    /**
     * @return bool
     */
    public function hasComplianceTeamLeadRole(): bool;

    /**
     * is user partner Customer Support Team Leader or partner Customer Support Team Member
     *
     * @return bool
     */
    public function hasPartnerCustomerSupportRole(): bool;

    /**
     * is user accountant Registration Team Leader or accountant Registration Team Member
     *
     * @return bool
     */
    public function hasAccountantRegistrationTeamRole(): bool;

    /**
     * @return bool
     */
    public function hasAccountantComplianceTeamRole(): bool;

    /**
     * is user accountant Customer Support Team Leader or accountant Customer Support Team Member
     *
     * @return bool
     */
    public function hasAccountantCustomerSupportRole(): bool;

    /**
     * Has partner/accountant customer support role(member or leader)
     *
     * @return bool
     */
    public function hasCustomerSupportRole(): bool;

    /**
     * @return bool
     */
    public function hasComplianceTeamRole(): bool;

    /**
     * has any partner team leader role
     *
     * @return bool
     */
    public function hasPartnerTeamLeaderRole(): bool;

    /**
     * has any accountant team leader role
     *
     * @return bool
     */
    public function hasAccountantTeamLeaderRole(): bool;

    /**
     * has any partner|accountant team leader role
     *
     * @return bool
     */
    public function hasTeamLeaderRole(): bool;

    /**
     * @return bool
     */
    public function hasAnyPartnerRole(): bool;

    /**
     * @return array
     */
    public function getAvailableCompaniesIds(): array;

    public function getAvailableCompaniesIdsPerCountries(): Collection;
}
