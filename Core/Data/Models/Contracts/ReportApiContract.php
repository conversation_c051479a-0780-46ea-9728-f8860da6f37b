<?php

namespace App\Core\Data\Models\Contracts;

use App\Core\Data\Models\Company;
use App\Core\Data\Models\Country;
use Illuminate\Support\Collection;

interface ReportApiContract
{
    public function getDocumentsInCategory(int $categoryId): Collection;

    public function getDocumentsInCategoryCount(int $categoryId): int;

    public function hasDocumentsInCategory(int $categoryId): bool;

    public function getCompany(): Company;

    public function getCountry(): Country;

    public function setStatus(int $statusId): ReportApiContract;

    public function getResource(): string;

    public function getResourceId(): int;
}
