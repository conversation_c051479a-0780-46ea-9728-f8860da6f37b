<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\InvoiceItem
 *
 * @property int $id
 * @property int $invoice_id
 * @property int|null $item_id
 * @property string|null $item_description
 * @property int|null $warehouse_id
 * @property int $invoice_item_type_id
 * @property int $qty
 * @property float|null $vat_percent
 * @property float $item_sales_price_net
 * @property float $item_sales_price_vat_amount
 * @property float|null $discount_net
 * @property float|null $discount_vat_amount
 * @property int $item_tax_code_id
 * @property bool $is_price_inserted_as_gross Needed for frontend. User can choose to insert price in gross or net
 * @property int|null $discount_percentage
 * @property int|null $expense_type_id
 * @property-read \App\Core\Data\Models\CompanyWarehouse|null $companyWarehouse
 * @property-read \App\Core\Data\Models\ExpenseType|null $expenseType
 * @property-read float|null $discount_gross
 * @property-read float|null $gross_total
 * @property-read string|null $gross_total_with_currency
 * @property-read float|null $item_gross_price
 * @property-read string|null $item_gross_price_with_currency
 * @property-read string|null $item_sales_price_net_with_currency
 * @property-read string|null $net_total_with_currency
 * @property-read string|null $vat_amount_with_currency
 * @property-read float|null $vat_percent_float
 * @property-read string $vat_percentage
 * @property-read \App\Core\Data\Models\Invoice $invoice
 * @property-read \App\Core\Data\Models\ItemType $invoiceItemType
 * @property-read \App\Core\Data\Models\Item|null $item
 * @property-read \App\Core\Data\Models\ItemTaxCode $taxCode
 * @method static Builder|InvoiceItem newModelQuery()
 * @method static Builder|InvoiceItem newQuery()
 * @method static Builder|InvoiceItem query()
 * @method static Builder|InvoiceItem whereDiscountNet($value)
 * @method static Builder|InvoiceItem whereDiscountPercentage($value)
 * @method static Builder|InvoiceItem whereDiscountVatAmount($value)
 * @method static Builder|InvoiceItem whereExpenseTypeId($value)
 * @method static Builder|InvoiceItem whereId($value)
 * @method static Builder|InvoiceItem whereInvoiceId($value)
 * @method static Builder|InvoiceItem whereInvoiceItemTypeId($value)
 * @method static Builder|InvoiceItem whereIsPriceInsertedAsGross($value)
 * @method static Builder|InvoiceItem whereItemDescription($value)
 * @method static Builder|InvoiceItem whereItemId($value)
 * @method static Builder|InvoiceItem whereItemSalesPriceNet($value)
 * @method static Builder|InvoiceItem whereItemSalesPriceVatAmount($value)
 * @method static Builder|InvoiceItem whereItemTaxCodeId($value)
 * @method static Builder|InvoiceItem whereQty($value)
 * @method static Builder|InvoiceItem whereVatPercent($value)
 * @method static Builder|InvoiceItem whereWarehouseId($value)
 * @mixin \Eloquent
 */
class InvoiceItem extends Model
{
    protected $table = 'invoice_items';

    protected $casts = [
        'qty'                         => 'integer',
        'item_sales_price_net'        => 'float',
        'item_sales_price_vat_amount' => 'float',
        'vat_percent'                 => 'float',
        'vat_percent_float'           => 'float',
        'discount_percent'            => 'float',
        'discount_net'                => 'float',
        'discount_vat_amount'         => 'float',
    ];

    protected $appends = [
        'gross_total',
        'gross_total_with_currency',
        'net_total_with_currency',
        'vat_amount_with_currency',
        'item_sales_price_net_with_currency',
        'item_gross_price_with_currency',
        'item_gross_price',
        'discount_gross'
    ];

    /*
    |--------------------------------------------------------------------------
    | RELATIONSHIPS
    |--------------------------------------------------------------------------
    |
    | Eloquent relationships.
    |
    */

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    }

    public function item(): HasOne
    {
        return $this->hasOne(Item::class, 'id', 'item_id');
    }

    /** @noinspection PhpUnused */
    public function companyWarehouse(): BelongsTo
    {
        return $this->belongsTo(CompanyWarehouse::class, 'warehouse_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function invoiceItemType(): BelongsTo
    {
        return $this->belongsTo(ItemType::class, 'invoice_item_type_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function taxCode(): BelongsTo
    {
        return $this->belongsTo(ItemTaxCode::class, 'item_tax_code_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function expenseType(): HasOne
    {
        return $this->hasOne(ExpenseType::class, 'id', 'expense_type_id');
    }

    /*
    |--------------------------------------------------------------------------
    | HELPERS
    |--------------------------------------------------------------------------
    |
    | Helper methods without some complex calculations.
    |
    */

    public function getCurrencyCode(): ?string
    {
        if (!$this->relationLoaded('invoice') || !$this->invoice->relationLoaded('currency')) {
            return null;
//            throw new Exception('LOAD ERROR - load "invoice.currency" relation on invoiceItem model');
        }

        return $this->invoice->currency->code;
    }

    public function getVatPercentageAsFloat(): ?float
    {
        if (is_null($this->getDbOriginalVatPercentage())) {
            return null;
        }

        return $this->getDbOriginalVatPercentage() / 100;
    }

    public function getVatPercentage(): ?string
    {
        $itemVatPercentage = $this->getDbOriginalVatPercentage();
        if (is_null($itemVatPercentage)) {
            return null;
        }

        $itemVatPercentageRounded = intval($itemVatPercentage);
        $itemVatPercentageDigits = 2;
        if ($itemVatPercentage == $itemVatPercentageRounded) {
            $itemVatPercentageDigits = 0;
        }

        return number_format($itemVatPercentage, $itemVatPercentageDigits) . '%';
    }

    private function toMoney(?float $val): ?string
    {
        if (is_null($this->getCurrencyCode())) {
            return null;
        }

        return evat_money($val, $this->getCurrencyCode());
    }

    public function getQty(): ?int
    {
        return $this->qty;
    }

    public function getDescription(): string
    {
        return $this->item_description ?? '';
    }

    /*
    |--------------------------------------------------------------------------
    | DB ORIGINALS
    |--------------------------------------------------------------------------
    |
    | Methods for fetching DB original, raw data.
    |
    */

    public function getDbOriginalVatPercentage(): ?float
    {
        return $this->vat_percent;
    }

    public function getDbOriginalItemSalesPriceNet(): ?float
    {
        return $this->item_sales_price_net;
    }

    public function getDbOriginalItemSalesPriceVatAmount(): ?float
    {
        return $this->item_sales_price_vat_amount;
    }

    public function getDbOriginalDiscountNet(): ?float
    {
        return $this->discount_net;
    }

    public function getDbOriginalDiscountVatAmount(): ?float
    {
        return $this->discount_vat_amount;
    }

    /*
    |--------------------------------------------------------------------------
    | GROSS
    |--------------------------------------------------------------------------
    |
    | Gross calculation and manipulation methods.
    |
    */

    public function getGross(): ?float
    {
        $net = $this->getDbOriginalItemSalesPriceNet();
        if (is_null($net)) {
            return null;
        }

        return $net + ($this->getDbOriginalItemSalesPriceVatAmount() ?? 0);
    }

    public function getGrossWithCurrency(): ?string
    {
        return $this->toMoney($this->getGross());
    }

    public function getGrossTotal(): ?float
    {
        $gross = $this->getGross();
        if (is_null($gross)) {
            return null;
        }

        return $gross * ($this->getQty() ?? 1);
    }

    public function getGrossTotalWithCurrency(): ?string
    {
        return $this->toMoney($this->getGrossTotal());
    }

    public function getGrossTotalDiscountApplied(): float
    {
        return $this->getGrossTotal() - $this->getDiscountGross();
    }

    /** @noinspection PhpUnused */
    public function getGrossTotalDiscountAppliedWithCurrency(): ?string
    {
        return $this->toMoney($this->getGrossTotalDiscountApplied());
    }

    /*
    |--------------------------------------------------------------------------
    | NET
    |--------------------------------------------------------------------------
    |
    | Net calculation and manipulation methods.
    |
    */

    public function getNet(): float
    {
        return $this->getGross() - round($this->getDbOriginalItemSalesPriceVatAmount(), 2);
    }

    public function getNetWithCurrency(): ?string
    {
        return $this->toMoney($this->getNet());
    }

    public function getNetTotal(): float
    {
        return $this->getNet() * $this->getQty();
    }

    public function getNetTotalWithCurrency(): ?string
    {
        return $this->toMoney($this->getNetTotal());
    }

    public function getNetTotalDiscountApplied(): float
    {
        return $this->getNetTotal() - $this->getDiscountNet();
    }

    /** @noinspection PhpUnused */
    public function getNetTotalDiscountAppliedWithCurrency(): ?string
    {
        return $this->toMoney($this->getNetTotalDiscountApplied());
    }

    /*
    |--------------------------------------------------------------------------
    | VAT AMOUNT
    |--------------------------------------------------------------------------
    |
    | VAT calculation and manipulation methods.
    |
    */

    public function getVatAmount(): float
    {
        return $this->getGross() - $this->getNet();
    }

    public function getVatAmountWithCurrency(): ?string
    {
        return $this->toMoney($this->getVatAmount());
    }

    public function getVatAmountTotal(): float
    {
        return $this->getVatAmount() * $this->getQty();
    }

    /** @noinspection PhpUnused */
    public function getVatAmountTotalWithCurrency(): ?string
    {
        return $this->toMoney($this->getVatAmountTotal());
    }

    public function getVatAmountTotalDiscountApplied(): float
    {
        return $this->getGrossTotalDiscountApplied() - $this->getNetTotalDiscountApplied();
    }

    /** @noinspection PhpUnused */
    public function getVatAmountTotalDiscountAppliedWithCurrency(): ?string
    {
        return $this->toMoney($this->getVatAmountTotalDiscountApplied());
    }

    /*
    |--------------------------------------------------------------------------
    | DISCOUNT
    |--------------------------------------------------------------------------
    |
    | Discount calculation and manipulation methods.
    |
    */

    public function getDiscountGross(): ?float
    {
        $discount = $this->getDbOriginalDiscountNet();
        if (is_null($discount)) {
            return null;
        }

        return $discount + ($this->getDbOriginalDiscountVatAmount() ?? 0);
    }

    public function getDiscountNet(): ?float
    {
        $discount = $this->getDiscountGross();
        if (is_null($discount)) {
            return null;
        }

        return $discount - round($this->getDbOriginalDiscountVatAmount() ?? 0, 2);
    }

    /*
    |--------------------------------------------------------------------------
    | ATTRIBUTES
    |--------------------------------------------------------------------------
    |
    | Getters.
    |
    */

    /** @noinspection PhpUnused */
    public function getVatPercentFloatAttribute(): ?float
    {
        return $this->getVatPercentageAsFloat();
    }

    /** @noinspection PhpUnused */
    public function getVatPercentageAttribute(): ?string
    {
        return $this->getVatPercentage();
    }

    /** @noinspection PhpUnused */
    public function getItemGrossPriceAttribute(): ?float
    {
        $net = $this->getDbOriginalItemSalesPriceNet();
        if (is_null($net)) {
            return null;
        }

        return round($net + $this->getDbOriginalItemSalesPriceVatAmount() ?? 0.0, PHP_ROUND_HALF_EVEN);
    }

    /** @noinspection PhpUnused */
    public function getGrossTotalAttribute(): ?float
    {
        $total = $this->getGrossTotal();
        if (is_null($total)) {
            return null;
        }

        return round($total, 2, PHP_ROUND_HALF_EVEN);
    }

    /** @noinspection PhpUnused */
    public function getItemSalesPriceNetWithCurrencyAttribute(): ?string
    {
        return $this->toMoney($this->getDbOriginalItemSalesPriceNet());
    }

    /** @noinspection PhpUnused */
    public function getItemGrossPriceWithCurrencyAttribute(): ?string
    {
        return $this->toMoney($this->getDbOriginalItemSalesPriceNet() + $this->getDbOriginalItemSalesPriceVatAmount());
    }

    /** @noinspection PhpUnused */
    public function getGrossTotalWithCurrencyAttribute(): ?string
    {
        return $this->getGrossTotalWithCurrency();
    }

    /** @noinspection PhpUnused */
    public function getNetTotalWithCurrencyAttribute(): ?string
    {
        return $this->getNetTotalWithCurrency();
    }

    /** @noinspection PhpUnused */
    public function getVatAmountWithCurrencyAttribute(): ?string
    {
        return $this->getVatAmountWithCurrency();
    }

    /** @noinspection PhpUnused */
    public function getDiscountGrossAttribute(): ?float
    {
        if (is_null($this->getDiscountGross())) {
            return null;
        }

        return round($this->getDiscountGross() ?? 0, 2);
    }
}
