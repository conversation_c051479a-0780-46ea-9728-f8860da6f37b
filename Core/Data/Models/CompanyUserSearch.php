<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyUserSearch
 *
 * @property int $id
 * @property int $user_id
 * @property int $company_id
 * @property int $clicks
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch whereClicks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyUserSearch whereUserId($value)
 * @mixin \Eloquent
 */
class CompanyUserSearch extends Model
{
    protected $table = 'companies_user_search';
}
