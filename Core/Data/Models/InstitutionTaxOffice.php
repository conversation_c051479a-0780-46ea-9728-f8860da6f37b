<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\InstitutionTaxOffice
 *
 * @property \App\Core\Data\Models\Institution $institution
 * @property \App\Core\Data\Models\InstitutionBranch $institutionBranch
 * @property \App\Core\Data\Models\TaxOffice|null $taxOffice
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionTaxOffice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionTaxOffice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionTaxOffice query()
 * @mixin \Eloquent
 */
class InstitutionTaxOffice extends Model
{
    protected $table = 'institution_tax_offices';

    public function institution()
    {
        return $this->belongsTo(Institution::class, 'institution_id', 'id');
    }

    public function institutionBranch()
    {
        return $this->belongsTo(InstitutionBranch::class, 'institution_branch_id', 'id');
    }

    public function taxOffice()
    {
        return $this->hasOne(TaxOffice::class, 'id', 'tax_office_id');
    }

}
