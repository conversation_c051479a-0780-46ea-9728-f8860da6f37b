<?php /** @noinspection PhpRedundantMethodOverrideInspection */

namespace App\Core\Data\Models;

use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Companies\Contracts\CompanyServiceContract;
use App\Core\Data\Models\Contracts\CurrentUserContract;
use App\Core\Data\Models\Scopes\UserAccountNotLockedScope;
use App\Core\Data\Models\Scopes\UserNotDeletedScope;
use App\Core\Data\Models\Scopes\UserVisibleScope;
use App\Core\Data\Models\Traits\UserACLTrait;
use App\Core\System\Exceptions\PartnerCompanyCountryDevException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Collection;
use Laravel\Cashier\Billable;
use League\Fractal\TransformerAbstract;
use Throwable;

/**
 * App\Core\Data\Models\User
 *
 * @property int $id
 * @property string $email
 * @property string|null $email_verified_at
 * @property string $password
 * @property bool $visible
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property bool $has_token
 * @property string|null $seed
 * @property bool $is_developer
 * @property bool $is_admin
 * @property string|null $last_login
 * @property bool $deleted
 * @property string|null $password_reset_time
 * @property string|null $password_reset_token
 * @property bool $is_client_type
 * @property string|null $phone
 * @property string|null $email_confirmation_token
 * @property string|null $email_token_sent_at
 * @property string|null $login_token
 * @property string $first_name
 * @property string $last_name
 * @property bool $reminder_sent
 * @property string|null $stripe_id
 * @property string|null $pm_type
 * @property string|null $pm_last_four
 * @property string|null $trial_ends_at
 * @property array $preferences
 * @property string|null $unlock_token When this value is NULL, account is not locked. Otherwise - it's locked
 * @property bool $active
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CompanyUserRole> $companiesUserRoles
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CompanyUserRole> $currentCompanyUserRoles
 * @property int|null $companies_users_roles_count
 * @property int|null $current_company_users_roles_count
 * @property mixed $full_name
 * @property bool $is_product_tour_done
 * @property mixed $locale
 * @property mixed $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InstitutionInstitutionTypeUserRole> $institutionInstitutionTypeUserRoles
 * @property int|null $institution_institution_type_user_roles_count
 * @property \App\Core\Data\Models\Person|null $person
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Company> $registrationCompanies
 * @property int|null $registration_companies_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \Laravel\Cashier\Subscription> $subscriptions
 * @property int|null $subscriptions_count
 * @method static Builder|User hasExpiredGenericTrial()
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User notAdministrative()
 * @method static Builder|User onGenericTrial()
 * @method static Builder|User query()
 * @method static Builder|User whereActive($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereDeleted($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailConfirmationToken($value)
 * @method static Builder|User whereEmailTokenSentAt($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereFirstName($value)
 * @method static Builder|User whereHasToken($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereIsAdmin($value)
 * @method static Builder|User whereIsClientType($value)
 * @method static Builder|User whereIsDeveloper($value)
 * @method static Builder|User whereLastLogin($value)
 * @method static Builder|User whereLastName($value)
 * @method static Builder|User whereLoginToken($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePasswordResetTime($value)
 * @method static Builder|User wherePasswordResetToken($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePmLastFour($value)
 * @method static Builder|User wherePmType($value)
 * @method static Builder|User wherePreferences($value)
 * @method static Builder|User whereReminderSent($value)
 * @method static Builder|User whereSeed($value)
 * @method static Builder|User whereStripeId($value)
 * @method static Builder|User whereTrialEndsAt($value)
 * @method static Builder|User whereUnlockToken($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereVisible($value)
 * @mixin \Eloquent
 */
class User extends Authenticatable implements CurrentUserContract
{
    use UserACLTrait;
    use Billable;

    const DUMMY_PASSWORD = 'didntChange1234';
    const USER_SYSTEM = 1;

    protected $table = 'users';

    protected $hidden = [
        'password'
    ];

    protected $casts = [
        'preferences' => 'array'
    ];

    protected $appends = ['full_name', 'locale'];

    /** @noinspection PhpUnused */
    public function getLocaleAttribute(): string
    {
        return $this->preferences['locale'] ?? 'en';
    }

    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope(new UserVisibleScope());
        static::addGlobalScope(new UserNotDeletedScope());
        static::addGlobalScope(new UserAccountNotLockedScope());
    }

    /** @noinspection PhpUnused */
    public function scopeNotAdministrative($query)
    {
        return $query->where('users.is_admin', false)
            ->where('users.is_developer', false);
    }

    /** @noinspection PhpUnused */
    public function companiesUserRoles(): HasMany
    {
        return $this->hasMany(CompanyUserRole::class, 'user_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function currentCompanyUserRoles(): HasMany
    {
        return $this->hasMany(CompanyUserRole::class, 'user_id', 'id')
            ->where('company_id', sd()->getFilterCompany()?->id ?? 0);
    }

    public function person(): HasOne
    {
        return $this->hasOne(Person::class);
    }

    /** @noinspection PhpUnused */
    public function getFullNameAttribute(): string
    {
        return $this->resolveFullName();
    }

    public function getNameAttribute(): string
    {
        return $this->resolveFullName();
    }

    private function resolveFullName(): string
    {
        $firstName = $this->getRawOriginal('first_name');
        $lastName = $this->getRawOriginal('last_name');
        $fullName = $lastName . ' ' . $firstName;

        return trim($fullName);
    }

    /** @noinspection PhpUnused */
    public function registrationCompanies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_registration_users', 'user_id', 'company_id');
    }

    /** @noinspection PhpUnused */
    public function institutionInstitutionTypeUserRoles(): HasMany
    {
        return $this->hasMany(InstitutionInstitutionTypeUserRole::class, 'user_id', 'id');
    }

    public function getInstitutionInstitutionTypeUserRoles(int $institutionInstitutionId, bool $setAsRelation = false): Collection
    {
        if (is_null($this->id)) {
            return collect();
        }

        $roles = $this->institutionInstitutionTypeUserRoles
            ->filter(function (InstitutionInstitutionTypeUserRole $institutionInstitutionTypeUserRole) use ($institutionInstitutionId) {
                return $institutionInstitutionTypeUserRole->institutionInstitutionType->id === $institutionInstitutionId;
            });

        if ($setAsRelation) {
            $this->setRelation('institutionInstitutionTypeUserRoles', $roles);
        }

        return $roles;
    }

    /**
     * @inheritDoc
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @inheritDoc
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @inheritDoc
     */
    public function getFullName(): string
    {
        return $this->full_name;
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return parent::toArray();
    }

    /**
     * @inheritDoc
     */
    public function hasOneOfAdminRoles(): bool
    {
        return $this->isServiceProviderAdmin() || $this->isCrmAdmin();
    }

    public function hasEvatAdminRoles(): bool
    {
        return $this->isServiceProviderAdmin()
            || $this->isCrmAdmin();
    }

    /**
     * @inheritDoc
     */
    public function hasPartnerRegistrationTeamRole(): bool
    {
        return $this->isCrmRegistrationTeamMember() || $this->isCrmRegistrationTeamLeader();
    }

    /**
     * @inheritDoc
     */
    public function hasPartnerComplianceTeamRole(): bool
    {
        return $this->isPartnerComplianceTeamLeader() || $this->isCrmComplianceTeamMember();
    }

    /**
     * @inheritDoc
     */
    public function hasComplianceTeamLeadRole(): bool
    {
        return $this->isPartnerComplianceTeamLeader() || $this->isServiceProviderComplianceTeamLeader();
    }

    /**
     * @inheritDoc
     */
    public function hasPartnerCustomerSupportRole(): bool
    {
        return $this->isCrmCustomerSupportTeamLeader()
            || $this->isCrmCustomerSupportTeamMember();
    }

    /**
     * @inheritDoc
     */
    public function hasAccountantRegistrationTeamRole(): bool
    {
        return $this->isServiceProviderRegistrationTeamMember()
            || $this->isServiceProviderRegistrationTeamLeader();
    }

    /**
     * @inheritDoc
     */
    public function hasAccountantComplianceTeamRole(): bool
    {
        return $this->isServiceProviderComplianceTeamMember()
            || $this->isServiceProviderComplianceTeamLeader();
    }

    /**
     * @inheritDoc
     */
    public function hasAccountantCustomerSupportRole(): bool
    {
        return $this->isServiceProviderCustomerSupportTeamLeader()
            || $this->isServiceProviderCustomerSupportTeamMember();
    }

    /**
     * @inheritDoc
     */
    public function hasCustomerSupportRole(): bool
    {
        return $this->hasPartnerCustomerSupportRole() || $this->hasAccountantCustomerSupportRole();
    }

    /**
     * @inheritDoc
     */
    public function hasComplianceTeamRole(): bool
    {
        return $this->hasPartnerComplianceTeamRole() || $this->hasAccountantComplianceTeamRole();
    }

    /**
     * @inheritDoc
     */
    public function hasPartnerTeamLeaderRole(): bool
    {
        return $this->isPartnerComplianceTeamLeader()
            || $this->isCrmRegistrationTeamLeader()
            || $this->isCrmCustomerSupportTeamLeader();
    }

    /**
     * @inheritDoc
     */
    public function hasAccountantTeamLeaderRole(): bool
    {
        return $this->isServiceProviderComplianceTeamLeader()
            || $this->isServiceProviderRegistrationTeamLeader()
            || $this->isServiceProviderCustomerSupportTeamLeader();
    }

    /**
     * @inheritDoc
     */
    public function hasTeamLeaderRole(): bool
    {
        return $this->hasPartnerTeamLeaderRole() || $this->hasAccountantTeamLeaderRole();
    }

    /**
     * @inheritDoc
     */
    public function hasAnyPartnerRole(): bool
    {
        return $this->isCrmAdmin()
            || $this->hasPartnerComplianceTeamRole()
            || $this->hasPartnerRegistrationTeamRole()
            || $this->hasPartnerCustomerSupportRole();
    }

    /**
     * @inheritDoc
     */
    public function getLocale(): string
    {
        return $this->locale;
    }

    /**
     * @return array
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function getAvailableCompaniesIds(): array
    {
        /**
         * @var CompanyServiceContract $companyService
         */
        $companyService = app()->make(CompanyServiceContract::class);

        return $companyService->resolveCompaniesIdsForUserOnInstitution($this, sd()->getCurrentInstitutionInstitutionTypeId());
    }

    /**
     * @return Collection
     * @throws BindingResolutionException
     * @throws PartnerCompanyCountryDevException
     */
    public function getAvailableCompaniesIdsPerCountries(): Collection
    {
        if (sd()->isCrm()) {
            throw new PartnerCompanyCountryDevException();
        }
        /**
         * @var CompanyServiceContract $companyService
         */
        $companyService = app()->make(CompanyServiceContract::class);

        return $companyService->resolveCompaniesIdsWithCountryByUserIdOnInstitution(
            $this->getId(),
            sd()->getCurrentInstitutionInstitutionTypeId()
        );
    }

    /**
     * @return bool
     * @noinspection PhpUnused
     */
    public function getIsProductTourDoneAttribute(): bool
    {
        $preference = collect($this->preferences);
        $done = $preference->filter(function ($value, $key) {
            return $key === 'productTourDone';
        });

        if ($done->isEmpty()) {
            return false;
        }

        return $preference['productTourDone'];
    }

    /** @noinspection PhpUnused */
    public function setIsProductTourDoneAttribute($value): void
    {
        $preference = $this->preferences ?? [];
        $preference['productTourDone'] = $value;
        $this->preferences = $preference;
    }

    public function transformData(TransformerAbstract $transformer): TransformedObjectContract
    {
        return transform_data($this, $transformer);
    }
}
