<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\TeledecResponseType
 *
 * @property int $id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|TeledecResponseType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TeledecResponseType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TeledecResponseType query()
 * @method static \Illuminate\Database\Eloquent\Builder|TeledecResponseType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TeledecResponseType whereName($value)
 * @mixin \Eloquent
 */
class TeledecResponseType extends Model
{
    protected $table = 'teledec_response_types';

    const TYPE_PROOF = 1;
    const TYPE_SUCCESS = 2;
    const TYPE_FAILED = 3;
    const TYPE_FAILED_SUBMIT = 4;
}
