<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\InseeTerritoryCode
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property int $country_id
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode query()
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InseeTerritoryCode whereName($value)
 * @mixin \Eloquent
 */
class InseeTerritoryCode extends Model
{
    protected $table = 'insee_territory_codes';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }
}
