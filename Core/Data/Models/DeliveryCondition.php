<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\DeliveryCondition
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @method static Builder|DeliveryCondition newModelQuery()
 * @method static Builder|DeliveryCondition newQuery()
 * @method static Builder|DeliveryCondition query()
 * @method static Builder|DeliveryCondition whereCode($value)
 * @method static Builder|DeliveryCondition whereId($value)
 * @method static Builder|DeliveryCondition whereName($value)
 * @mixin \Eloquent
 */
class DeliveryCondition extends Model
{
    protected $table = 'delivery_conditions';
    public $timestamps = false;

    const XXX = 1;
    const DDP = 2;
    const DAP = 3;
}
