<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\ImportFromCountry
 *
 * @property int $id
 * @property int $country_id
 * @property int $company_id
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|ImportFromCountry newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImportFromCountry newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ImportFromCountry query()
 * @method static \Illuminate\Database\Eloquent\Builder|ImportFromCountry whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImportFromCountry whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ImportFromCountry whereId($value)
 * @mixin \Eloquent
 */
class ImportFromCountry extends Model
{
    protected $table = 'import_from_countries';

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
