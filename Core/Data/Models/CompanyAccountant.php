<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\CompanyAccountant
 *
 * @property int $id
 * @property int $company_id
 * @property int $accountant_id institution_type_country_partner_id
 * @property int|null $institution_branch_id
 * @property \App\Core\Data\Models\InstitutionTypeCountryPartner $accountant
 * @property \App\Core\Data\Models\Company $company
 * @property mixed $country_name
 * @property \App\Core\Data\Models\InstitutionBranch|null $institutionBranch
 * @method static Builder|CompanyAccountant newModelQuery()
 * @method static Builder|CompanyAccountant newQuery()
 * @method static Builder|CompanyAccountant query()
 * @method static Builder|CompanyAccountant whereAccountantId($value)
 * @method static Builder|CompanyAccountant whereCompanyId($value)
 * @method static Builder|CompanyAccountant whereId($value)
 * @method static Builder|CompanyAccountant whereInstitutionBranchId($value)
 * @mixin \Eloquent
 */
class CompanyAccountant extends Model
{
    protected $table = 'company_accountants';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function accountant(): BelongsTo
    {
        return $this->belongsTo(InstitutionTypeCountryPartner::class, 'accountant_id', 'id');
    }

    public function institutionBranch(): BelongsTo
    {
        return $this->belongsTo(InstitutionBranch::class, 'institution_branch_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getCountryNameAttribute(): string
    {
        return $this->accountant->country->name;
    }
}
