<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\TaxationMethod
 *
 * @property int $id
 * @property string $name
 * @method static Builder|TaxationMethod newModelQuery()
 * @method static Builder|TaxationMethod newQuery()
 * @method static Builder|TaxationMethod query()
 * @method static Builder|TaxationMethod whereId($value)
 * @method static Builder|TaxationMethod whereName($value)
 * @mixin \Eloquent
 */
class TaxationMethod extends Model
{
    protected $table = 'taxation_methods';

    const GENERAL = 1;
    const CUSTOMER_LOCATION = 2;
    const N_A = 3;
    const PROPERTY_LOCATION = 4;
    const DISTANCES_COVERED = 5;
}
