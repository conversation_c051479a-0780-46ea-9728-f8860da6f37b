<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\ReportStatus
 *
 * @property int $id
 * @property string $name
 * @property string $key
 * @property string $css_text_class
 * @method static Builder|ReportStatus newModelQuery()
 * @method static Builder|ReportStatus newQuery()
 * @method static Builder|ReportStatus query()
 * @method static Builder|ReportStatus whereId($value)
 * @method static Builder|ReportStatus whereKey($value)
 * @method static Builder|ReportStatus whereName($value)
 * @mixin \Eloquent
 */
class ReportStatus extends Model
{
    protected $table = 'report_statuses';

    protected $appends = [
        'cssTextClass'
    ];

    const REPORT_MISSING = 1;
    const REVIEW_PENDING = 4;
    const APPROVED = 5;
    const SUBMITTED = 6;
    const PAID = 7;
    const NIL_SUBMITTED = 8;
    const SUBMITTING_PROCESS = 9;
    const NEW = 10;

    /** @noinspection PhpUnused */
    public function getCssTextClassAttribute(): string
    {
        $id = $this->id;
        $colorClass = 'text-dark-gray';
        if (is_null($id)) {
            return $colorClass;
        }

        $indigoStatuses = [
            ReportStatus::SUBMITTING_PROCESS,
            ReportStatus::REVIEW_PENDING,
            ReportStatus::APPROVED,
        ];
        if (in_array($id, $indigoStatuses)) {
            $colorClass = 'text-indigo';
        }

        $dangerStatuses = [
            ReportStatus::REPORT_MISSING
        ];
        if (in_array($id, $dangerStatuses)) {
            $colorClass = 'text-danger';
        }

        $tealStatuses = [
            ReportStatus::PAID
        ];
        if (in_array($id, $tealStatuses)) {
            $colorClass = 'text-teal';
        }

        $successStatuses = [
            ReportStatus::SUBMITTED
        ];
        if (in_array($id, $successStatuses)) {
            $colorClass = 'text-success';
        }

        $infoStatuses = [
            ReportStatus::NEW
        ];
        if (in_array($id, $infoStatuses)) {
            $colorClass = 'text-success';
        }

        return $colorClass;
    }
}
