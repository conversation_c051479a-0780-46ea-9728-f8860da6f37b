<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyBulkFileType
 *
 * @property int $id
 * @property string $type
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileType whereType($value)
 * @mixin \Eloquent
 */
class ShopifyBulkFileType extends Model
{
    const ORDERS = 1;
    const PRODUCTS = 2;

    protected $table = 'shopify_bulk_file_types';
}
