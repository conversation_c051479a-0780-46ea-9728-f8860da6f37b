<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\InstitutionBranch
 *
 * @property int $id
 * @property int $institution_id
 * @property string|null $branch_code
 * @property string $branch_name
 * @property string|null $branch_short_name
 * @property int $address_id
 * @property int $authorised_person_id
 * @property int|null $default_tax_office_id
 * @property \App\Core\Data\Models\Address|null $address
 * @property \App\Core\Data\Models\Person|null $authorisedPerson
 * @property \App\Core\Data\Models\TaxOffice|null $defaultTaxOffice
 * @property \App\Core\Data\Models\Institution $institution
 * @method static Builder|InstitutionBranch newModelQuery()
 * @method static Builder|InstitutionBranch newQuery()
 * @method static Builder|InstitutionBranch query()
 * @method static Builder|InstitutionBranch whereAddressId($value)
 * @method static Builder|InstitutionBranch whereAuthorisedPersonId($value)
 * @method static Builder|InstitutionBranch whereBranchCode($value)
 * @method static Builder|InstitutionBranch whereBranchName($value)
 * @method static Builder|InstitutionBranch whereBranchShortName($value)
 * @method static Builder|InstitutionBranch whereDefaultTaxOfficeId($value)
 * @method static Builder|InstitutionBranch whereId($value)
 * @method static Builder|InstitutionBranch whereInstitutionId($value)
 * @mixin \Eloquent
 */
class InstitutionBranch extends Model
{
    protected $table = 'institution_branches';


    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class, 'institution_id', 'id');
    }

    public function authorisedPerson(): HasOne
    {
        return $this->hasOne(Person::class, 'id', 'authorised_person_id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class, 'id', 'address_id');
    }

    /** @noinspection PhpUnused */
    public function defaultTaxOffice(): HasOne
    {
        return $this->hasOne(TaxOffice::class, 'id', 'default_tax_office_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
