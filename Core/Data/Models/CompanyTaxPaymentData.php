<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\OssReport
 *
 * @property int $id
 * @property int $country_id
 * @property int $company_id
 * @property int $tax_payment_type_id
 * @property int $tax_payment_reference_id
 * @property-read Company $company
 * @property-read Country $country
 * @property-read TaxPaymentType $taxPaymentType
 * @property-read TaxPaymentReference $taxPaymentReference
 * @method static Builder|OssIossReport newModelQuery()
 * @method static Builder|OssIossReport newQuery()
 * @method static Builder|OssIossReport query()
 * @method static Builder|OssIossReport whereCompanyId($value)
 * @method static Builder|OssIossReport whereCountryId($value)
 * @method static Builder|OssIossReport whereCreatedAt($value)
 * @method static Builder|OssIossReport whereData($value)
 * @method static Builder|OssIossReport whereDateFrom($value)
 * @method static Builder|OssIossReport whereDateTo($value)
 * @method static Builder|OssIossReport whereId($value)
 * @method static Builder|OssIossReport whereStatusId($value)
 * @mixin Eloquent
 */
class CompanyTaxPaymentData extends Model
{
    protected $table = 'company_tax_payment_data';

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function company(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    /** @noinspection PhpUnused */
    public function taxPaymentType(): HasOne
    {
        return $this->hasOne(TaxPaymentType::class, 'id', 'tax_payment_type_id');
    }

    /** @noinspection PhpUnused */
    public function taxPaymentReference(): HasOne
    {
        return $this->hasOne(TaxPaymentReference::class, 'id', 'tax_payment_reference_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
