<?php /** @noinspection PhpUnused */

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\TaxReportingScheme
 *
 * @property int $id
 * @property string $name
 * @property string|null $amazon_code
 * @property string|null $description
 * @property string|null $note
 * @method static Builder|TaxReportingScheme newModelQuery()
 * @method static Builder|TaxReportingScheme newQuery()
 * @method static Builder|TaxReportingScheme query()
 * @method static Builder|TaxReportingScheme whereAmazonCode($value)
 * @method static Builder|TaxReportingScheme whereDescription($value)
 * @method static Builder|TaxReportingScheme whereId($value)
 * @method static Builder|TaxReportingScheme whereName($value)
 * @method static Builder|TaxReportingScheme whereNote($value)
 * @mixin \Eloquent
 */
class TaxReportingScheme extends Model
{
    protected $table = 'tax_reporting_schemes';

    public const REGULAR = 1;
    public const UK_VOEC_DOMESTIC = 2;
    public const UK_VOEC_IMPORT = 3;
    public const NO_VOEC = 4;
    public const EU_NU_OSS = 5;
    public const EU_US_OSS = 6;
    public const EU_IS_IOSS = 7;
    public const DEEMED_RESELLER_EU_US_OSS = 8;
    public const DEEMED_RESELLER_REGULAR = 9;
    public const DEEMED_RESELLER_EU_IS_IOSS = 10;
    public const UK_PREPAID_IMPORT_VAT_SERVICE = 11;

    // Amazon only keys
    public const AMAZON_REGULAR = 1;
    public const AMAZON_UK_VOEC_DOMESTIC = 2;
    public const AMAZON_UK_VOEC_IMPORT = 3;
    public const AMAZON_NO_VOEC = 4;
    public const AMAZON_DEEMED_RESELLER = 8;
    public const AMAZON_DEEMED_RESELLER_IOSS = 10;
    public const AMAZON_UNION_OSS = 6;


    // N/A
    public const AMAZON_AU_VOEC = 12;
    public const AMAZON_CH_SUPPLIER_IMPORT = 13;
    public const DEEMED_RESELLER_EU_NU_OSS = 20;
}
