<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\InvoiceType
 *
 * @property int $id
 * @property string $type
 * @property int $sequence
 * @property mixed $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InvoiceSubtype> $invoiceSubtypes
 * @property int|null $invoice_subtypes_count
 * @method static Builder|InvoiceType newModelQuery()
 * @method static Builder|InvoiceType newQuery()
 * @method static Builder|InvoiceType query()
 * @method static Builder|InvoiceType whereId($value)
 * @method static Builder|InvoiceType whereSequence($value)
 * @method static Builder|InvoiceType whereType($value)
 * @mixin \Eloquent
 */
class InvoiceType extends Model
{
    protected $table = 'invoice_types';

    protected $appends = ['name'];

    public const SALES_INVOICE = 1;
    public const PURCHASE_INVOICE = 2;
    public const PRO_FORMA_INVOICE = 3;
    public const CREDIT_NOTE = 4;
    public const DEBIT_NOTE = 5;
    public const CUSTOMS_DECLARATION = 6;
    public const DEEMED_SALES_INVOICE = 7;
    public const DEEMED_CREDIT_NOTE = 8;

    public function getNameAttribute(): string
    {
        return _l($this->type);
    }

    /** @noinspection PhpUnused */
    public function invoiceSubtypes(): HasMany
    {
        return $this->hasMany(InvoiceSubtype::class, 'invoice_type_id', 'id')
            ->orderBy('type');
    }
}
