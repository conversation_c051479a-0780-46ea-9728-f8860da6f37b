<?php

namespace App\Core\Data\Models\Scopes;

use App\Core\Data\Models\CompanyStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class CompanyNotDeactivatedScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $builder->where('companies.status_id', '!=', CompanyStatus::CANCELLED);
    }
}