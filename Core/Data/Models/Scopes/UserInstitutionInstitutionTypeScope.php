<?php

namespace App\Core\Data\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class UserInstitutionInstitutionTypeScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $iitId = sd()->getCurrentInstitutionInstitutionTypeId();
        if (!is_null($iitId)) {
            $builder->where('institution_institution_type.id', '=', $iitId);

        }
    }
}
