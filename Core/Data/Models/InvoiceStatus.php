<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\InvoiceStatus
 *
 * @property int $id
 * @property string $status
 * @property int $sequence
 * @property string $key
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InvoiceStatusInvoiceType> $invoiceStatusTypes
 * @property int|null $invoice_status_types_count
 * @method static Builder|InvoiceStatus newModelQuery()
 * @method static Builder|InvoiceStatus newQuery()
 * @method static Builder|InvoiceStatus query()
 * @method static Builder|InvoiceStatus whereId($value)
 * @method static Builder|InvoiceStatus whereKey($value)
 * @method static Builder|InvoiceStatus whereSequence($value)
 * @method static Builder|InvoiceStatus whereStatus($value)
 * @mixin \Eloquent
 */
class InvoiceStatus extends Model
{
    protected $table = 'invoice_statuses';

    public const DRAFT = 1;
    public const ISSUED = 2;
    public const INBOX = 3;
    public const EXPENSES = 4;
    public const DEEMED_SUPPLIES = 5;

    public static function getAllKeys(): array
    {
        return [
            'expenses',
            'inbox',
            'issued',
            'deemed-supplies',
            'draft',
        ];
    }

    /** @noinspection PhpUnused */
    public function invoiceStatusTypes(): HasMany
    {
        return $this->hasMany(InvoiceStatusInvoiceType::class, 'invoice_status_id', 'id');
    }
}
