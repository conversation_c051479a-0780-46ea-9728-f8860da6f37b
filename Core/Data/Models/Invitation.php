<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Throwable;

/**
 * App\Core\Data\Models\Invitation
 *
 * @property int $id
 * @property int|null $partner_id
 * @property int|null $company_id
 * @property string $recipient_email
 * @property string $created_at
 * @property string $invitation_token
 * @property int $invitation_type_id
 * @property int $inviting_user_id
 * @property int|null $country_id
 * @property string $recipient_first_name
 * @property string $recipient_last_name
 * @property int|null $notification_id
 * @property int $role_id
 * @property int|null $inviting_company_id
 * @property bool $reminder_sent
 * @property array $token_decoded
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\Country|null $country
 * @property mixed $recipient_full_name
 * @property \App\Core\Data\Models\Company|null $invitingCompany
 * @property \App\Core\Data\Models\User $invitingUser
 * @property \App\Core\Data\Models\Notification|null $notification
 * @property \App\Core\Data\Models\InstitutionInstitutionType|null $partner
 * @method static Builder|Invitation newModelQuery()
 * @method static Builder|Invitation newQuery()
 * @method static Builder|Invitation query()
 * @method static Builder|Invitation whereCompanyId($value)
 * @method static Builder|Invitation whereCountryId($value)
 * @method static Builder|Invitation whereCreatedAt($value)
 * @method static Builder|Invitation whereId($value)
 * @method static Builder|Invitation whereInvitationToken($value)
 * @method static Builder|Invitation whereInvitationTypeId($value)
 * @method static Builder|Invitation whereInvitingCompanyId($value)
 * @method static Builder|Invitation whereInvitingUserId($value)
 * @method static Builder|Invitation whereNotificationId($value)
 * @method static Builder|Invitation wherePartnerId($value)
 * @method static Builder|Invitation whereRecipientEmail($value)
 * @method static Builder|Invitation whereRecipientFirstName($value)
 * @method static Builder|Invitation whereRecipientLastName($value)
 * @method static Builder|Invitation whereReminderSent($value)
 * @mixin \Eloquent
 */
class Invitation extends Model
{
    protected $table = 'invitations';

    protected $appends = [
        'recipient_full_name',
        'token_decoded',
    ];

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function invitingUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inviting_user_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function invitingCompany(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'inviting_company_id', 'id');
    }

    public function partner(): BelongsTo
    {
        return $this->belongsTo(InstitutionInstitutionType::class, 'partner_id', 'id');
    }

    public function notification(): HasOne
    {
        return $this->hasOne(Notification::class, 'id', 'notification_id');
    }

    /** @noinspection PhpUnused */
    public function getRecipientFullNameAttribute(): string
    {
        return $this->recipient_last_name . ' ' . $this->recipient_first_name;
    }

    /** @noinspection PhpUnused */
    public function getTokenDecodedAttribute(): array
    {
        $data = $this->invitation_token ?? null;
        if (is_null($data)) {
            return [];
        }

        try {
            $data = base64_decode($data);
            $data = json_decode($data, true);
        } catch (Throwable) {
            $data = [];
        }
        if (!is_array($data)) {
            $data = [];
        }

        return $data;
    }
}
