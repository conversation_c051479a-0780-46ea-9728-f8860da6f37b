<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\InstitutionType
 *
 * @property int $id
 * @property string $name
 * @property int $work_mode_type_id
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Role> $roles
 * @property int|null $roles_count
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionType query()
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InstitutionType whereWorkModeTypeId($value)
 * @mixin \Eloquent
 */
class InstitutionType extends Model
{
    protected $table = 'institution_types';

    const TYPE_ACCOUNTANT = 1;
    const TYPE_PARTNER = 2;

    public function roles(): HasMany
    {
        return $this->hasMany(Role::class, 'institution_type_id', 'id');
    }

    public function getNameAttribute()
    {
        return 'institution-type.' . $this->getRawOriginal('name');
    }
}
