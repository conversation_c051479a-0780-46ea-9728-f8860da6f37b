<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\HasCompanyContract;
use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\OssNumber
 *
 * @property int $id
 * @property int $company_id
 * @property int $issue_country_id
 * @property int $type_id
 * @property string $number
 * @property string $register_date
 * @property string|null $end_date
 * @property-read \App\Core\Data\Models\Company $company
 * @property-read \App\Core\Data\Models\Country $issueCountry
 * @property-read \App\Core\Data\Models\OssNumberType $type
 * @method static Builder|OssNumber newModelQuery()
 * @method static Builder|OssNumber newQuery()
 * @method static Builder|OssNumber query()
 * @method static Builder|OssNumber whereCompanyId($value)
 * @method static Builder|OssNumber whereEndDate($value)
 * @method static Builder|OssNumber whereId($value)
 * @method static Builder|OssNumber whereIssueCountryId($value)
 * @method static Builder|OssNumber whereNumber($value)
 * @method static Builder|OssNumber whereRegisterDate($value)
 * @method static Builder|OssNumber whereTypeId($value)
 * @mixin \Eloquent
 */
class OssNumber extends Model implements HasCompanyContract
{
    protected $table = 'oss_numbers';

    public function issueCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'issue_country_id', 'id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(OssNumberType::class, 'type_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function getCompany(): Company
    {
        return $this->company;
    }
}
