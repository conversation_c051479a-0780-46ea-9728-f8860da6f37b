<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\FlatVatRate
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string $start_date
 * @property string|null $end_date
 * @property int $value
 * @property bool $active
 * @property \App\Core\Data\Models\Country $country
 * @property mixed $country_name
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FlatVatRate whereValue($value)
 * @mixin \Eloquent
 */
class FlatVatRate extends Model
{
    protected $table = 'flat_vat_rates';

    protected $appends = [
        'country_name',
    ];

    protected $casts = [
        'value' => 'float'
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getCountryNameAttribute(): string
    {
        return $this->country->name;
    }
}
