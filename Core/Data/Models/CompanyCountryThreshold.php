<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyCountryThreshold
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string $date
 * @property int|null $year_id
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country $country
 * @property \App\Core\Data\Models\CountryThreshold $threshold
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountryThreshold whereYearId($value)
 * @mixin \Eloquent
 */
class CompanyCountryThreshold extends Model
{
    protected $table = 'company_country_thresholds';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function threshold()
    {
        return $this->belongsTo(CountryThreshold::class, 'country_id', 'country_id');
    }

}
