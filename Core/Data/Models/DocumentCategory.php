<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\DocumentCategory
 *
 * @property int $id
 * @property string $name
 * @property int $sequence
 * @property int|null $parent_category_id
 * @property int|null $tmp_document_type_id
 * @property int|null $sample_file_type_id
 * @property bool $generatable
 * @property bool $uploadable
 * @property bool $deletable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, DocumentCategory> $childDocumentCategories
 * @property-read int|null $child_document_categories_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\DocumentCategoryCountry> $documentCategoryCompanyCountries
 * @property-read int|null $document_category_company_countries_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\DocumentCategoryCountry> $documentCategoryCountries
 * @property-read int|null $document_category_countries_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\DocumentCategoryPlatform> $documentCategoryPlatforms
 * @property-read int|null $document_category_platforms_count
 * @property-read string|null $tooltip
 * @property-read \App\Core\Data\Models\DocumentCategoryOptions|null $options
 * @property-read DocumentCategory|null $parentDocumentCategory
 * @property-read \App\Core\Data\Models\SampleFileType|null $sampleFileType
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereDeletable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereGeneratable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereParentCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereSampleFileTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereSequence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereTmpDocumentTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereUploadable($value)
 * @property bool $is_visible
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategory whereIsVisible($value)
 * @mixin \Eloquent
 */
class DocumentCategory extends Model
{
    protected $table = 'document_categories';

    public const CATEGORY_TAX_AUTHORITY = 1;
    public const CATEGORY_VAT_REGISTRATION = 2;
    public const CATEGORY_VAT_REPORT = 3;
    public const CATEGORY_INCORPORATION_AND_LEGAL = 4;
    public const CATEGORY_TAX_AUTHORITY_API_SUBMITTED = 5;
    public const CATEGORY_OTHER = 6;

    public const SUB_CATEGORY_VAT_SUBMITTED = 121;
    public const SUB_CATEGORY_ELSTER_VAT_REQUEST_XML = 127;
    public const SUB_CATEGORY_ELSTER_VAT_RESPONSE_XML = 128;

    public const SUB_CATEGORY_ESL_SUBMITTED = 132;
    public const SUB_CATEGORY_ELSTER_ESL_REQUEST_XML = 133;
    public const SUB_CATEGORY_ELSTER_ESL_RESPONSE_XML = 134;

    public const SEQUENCE_INCREMENT = 5;
    public const MISCELLANEOUS_CATEGORIES_IDS = [11, 26, 90];

    protected $appends = [
        'tooltip'
    ];

    public function getModalData(): string
    {
        $data = [
            'id' => $this->id
        ];

        return evat_json_encode($data);
    }

    public function options(): HasOne
    {
        return $this->hasOne(DocumentCategoryOptions::class, 'document_category_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function documentCategoryCountries(): HasMany
    {
        return $this->hasMany(DocumentCategoryCountry::class, 'document_category_id', 'id')->where('is_company_country', false);
    }

    /** @noinspection PhpUnused */
    public function documentCategoryCompanyCountries(): HasMany
    {
        return $this->hasMany(DocumentCategoryCountry::class, 'document_category_id', 'id')->where('is_company_country', true);
    }

    /** @noinspection PhpUnused */
    public function documentCategoryPlatforms(): HasMany
    {
        return $this->hasMany(DocumentCategoryPlatform::class, 'document_category_id', 'id');
    }

    public function sampleFileType(): HasOne
    {
        return $this->hasOne(SampleFileType::class, 'id', 'sample_file_type_id');
    }

    /** @noinspection PhpUnused */
    public function parentDocumentCategory(): BelongsTo
    {
        return $this->belongsTo(DocumentCategory::class, 'parent_category_id', 'id')->orderBy('sequence');
    }

    /** @noinspection PhpUnused */
    public function childDocumentCategories(): HasMany
    {
        return $this->hasMany(DocumentCategory::class, 'parent_category_id', 'id')->orderBy('sequence');
    }

    /** @noinspection PhpUnused */
    public function getTooltipAttribute(): ?string
    {
        $tooltips = [
            self::CATEGORY_INCORPORATION_AND_LEGAL     => _l('document-category.Incorporation and legal tooltip'),
            self::CATEGORY_VAT_REGISTRATION            => _l('registration.VAT registration tooltip'),
            self::CATEGORY_TAX_AUTHORITY               => _l('registration.Tax Authority tooltip'),
            self::CATEGORY_VAT_REPORT                  => _l('document-category.VAT report tooltip'),
            self::CATEGORY_TAX_AUTHORITY_API_SUBMITTED => _l('document-category.Tax authority API submitted tooltip')
        ];

        return $tooltips[$this->id] ?? null;
    }

    public function isValidForVueComponent(bool $showHidden = false, ?int $countryId = null, ?array $showAllDocumentsFromParentCategoriesIds = null): bool
    {
        if (!$showHidden && !$this->is_visible) {
            return false;
        }

        if (is_null($countryId)) {
            return true;
        }

        if (!is_null($showAllDocumentsFromParentCategoriesIds) && in_array($this->parent_category_id, $showAllDocumentsFromParentCategoriesIds)) {
            return true;
        }

        $countriesIds = $this->documentCategoryCountries->pluck('country_id')->toArray();
        if (in_array(Country::ALL, $countriesIds)) {
            return true;
        }

        return in_array($countryId, $countriesIds);
    }
}
