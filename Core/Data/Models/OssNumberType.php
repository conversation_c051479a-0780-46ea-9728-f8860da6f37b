<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\OssNumberType
 *
 * @property int $id
 * @property string $lang_key
 * @property string|null $code
 * @method static Builder|OssNumberType newModelQuery()
 * @method static Builder|OssNumberType newQuery()
 * @method static Builder|OssNumberType query()
 * @method static Builder|OssNumberType whereCode($value)
 * @method static Builder|OssNumberType whereId($value)
 * @method static Builder|OssNumberType whereLangKey($value)
 * @mixin \Eloquent
 */
class OssNumberType extends Model
{
    public const UNION_SCHEME = 1;
    public const NON_UNION_SCHEME = 2;

    protected $table = 'oss_number_types';
}
