<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Core\Data\Models\Supplier
 *
 * @property int $id
 * @property string|null $company_name
 * @property int $company_id
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $customer_number
 * @property bool $is_person
 * @property bool $is_global
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Contact> $contacts
 * @property int|null $contacts_count
 * @property mixed $full_name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $identificationNumbers
 * @property int|null $identification_numbers_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\SupplierAddress> $supplierAddress
 * @property int|null $supplier_address_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $taxNumbers
 * @property int|null $tax_numbers_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $vatNumbers
 * @property int|null $vat_numbers_count
 * @property \App\Core\Data\Models\IdentificationNumber|null $voesNumber
 * @method static Builder|Supplier newModelQuery()
 * @method static Builder|Supplier newQuery()
 * @method static Builder|Supplier query()
 * @method static Builder|Supplier whereCompanyId($value)
 * @method static Builder|Supplier whereCompanyName($value)
 * @method static Builder|Supplier whereCustomerNumber($value)
 * @method static Builder|Supplier whereFirstName($value)
 * @method static Builder|Supplier whereId($value)
 * @method static Builder|Supplier whereIsPerson($value)
 * @method static Builder|Supplier whereLastName($value)
 * @mixin \Eloquent
 */
class Supplier extends Model
{
    protected $table = 'suppliers';

    /** @noinspection PhpUnused */
    public function supplierAddress(): HasMany
    {
        return $this->hasMany(SupplierAddress::class, 'supplier_id', 'id');
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class, 'supplier_id', 'id');
    }

    public function identificationNumbers(): MorphMany
    {
        return $this->morphMany(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id');
    }

    public function vatNumbers(): MorphMany
    {
        return $this->morphMany(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id')
            ->where('identification_number_type_id', IdentificationNumberType::VAT_NUMBER);
    }

    public function taxNumbers(): MorphMany
    {
        return $this->morphMany(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id')
            ->where('identification_number_type_id', IdentificationNumberType::TAX_NUMBER);
    }

    public function voesNumber(): MorphOne
    {
        return $this->morphOne(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id')
            ->where('identification_number_type_id', IdentificationNumberType::OSS_NUMBER);
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function getFullNameAttribute(): ?string
    {
        if (is_null($this->first_name) || is_null($this->last_name)) {
            return null;
        }

        return $this->first_name . ' ' . $this->last_name;
    }

    protected static function boot(): void
    {
        parent::boot();

        static::deleting(function (Supplier $supplier) {
            $supplier->identificationNumbers()->delete();
        });
    }
}
