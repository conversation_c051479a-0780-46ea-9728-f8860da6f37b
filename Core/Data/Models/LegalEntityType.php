<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\LegalEntityType
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property string $name_en
 * @property int $country_id
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType query()
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LegalEntityType whereNameEn($value)
 * @mixin \Eloquent
 */
class LegalEntityType extends Model
{
    protected $table = 'legal_entity_types';

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }
}
