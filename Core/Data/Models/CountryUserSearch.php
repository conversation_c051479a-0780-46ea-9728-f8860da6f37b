<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CountryUserSearch
 *
 * @property int $id
 * @property int $user_id
 * @property int $country_id
 * @property int $clicks
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch query()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch whereClicks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryUserSearch whereUserId($value)
 * @mixin \Eloquent
 */
class CountryUserSearch extends Model
{
    protected $table = 'countries_user_search';
}
