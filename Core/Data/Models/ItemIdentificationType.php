<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ItemIdentificationType
 *
 * @property int $id
 * @property string $name make it uppercase ALWAYS!
 * @property int|null $sequence
 * @method static \Illuminate\Database\Eloquent\Builder|ItemIdentificationType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemIdentificationType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemIdentificationType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemIdentificationType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemIdentificationType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemIdentificationType whereSequence($value)
 * @mixin \Eloquent
 */
class ItemIdentificationType extends Model
{
    protected $table = 'item_identification_types';

    const ASIN = 1;
    const ISBN = 2;
    const GTIN = 3;
    const EAN = 4;
    const MPN = 5;
    const SKU = 6;
    const EBAY_IN = 7;

    public function setNameAttribute($value)
    {
        $this->attributes['name'] = mb_strtoupper($value);
    }
}
