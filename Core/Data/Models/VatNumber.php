<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\HasCompanyContract;
use App\Core\Data\Models\Contracts\Model;
use App\Core\VatNumbers\Validator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\VatNumber
 *
 * @property int $id
 * @property int|null $company_id
 * @property string|null $first_time_in_report
 * @property string|null $register_date
 * @property int|null $institution_id
 * @property string|null $end_date
 * @property int $country_id
 * @property string $vat_number vat_number is unique only if active. Inactive has end_date. You can have single active and multiple inactive with same vat_number column
 * @property bool $permanent_establishment If false, then vat_number is for "other facilities"
 * @property-read \App\Core\Data\Models\Company|null $company
 * @property-read \App\Core\Data\Models\Country $country
 * @property-read string $calculated_register_date
 * @property-read string $country_name
 * @property-read mixed $issued_in_eu
 * @property-read string $number
 * @method static Builder|VatNumber newModelQuery()
 * @method static Builder|VatNumber newQuery()
 * @method static Builder|VatNumber query()
 * @method static Builder|VatNumber whereCompanyId($value)
 * @method static Builder|VatNumber whereCountryId($value)
 * @method static Builder|VatNumber whereEndDate($value)
 * @method static Builder|VatNumber whereFirstTimeInReport($value)
 * @method static Builder|VatNumber whereId($value)
 * @method static Builder|VatNumber whereInstitutionId($value)
 * @method static Builder|VatNumber wherePermanentEstablishment($value)
 * @method static Builder|VatNumber whereRegisterDate($value)
 * @method static Builder|VatNumber whereVatNumber($value)
 * @property bool $guessed
 * @method static Builder|VatNumber whereGuessed($value)
 * @property bool $valid
 * @property string|null $validation_date
 * @method static Builder|VatNumber whereValid($value)
 * @method static Builder|VatNumber whereValidationDate($value)
 * @mixin \Eloquent
 */
class VatNumber extends Model implements HasCompanyContract
{
    protected $table = 'vat_numbers';

    protected $appends = [
        'calculated_register_date'
    ];

    /** @noinspection PhpUnused */
    public function getCountryNameAttribute(): string
    {
        return $this->country->name;
    }

    /** @noinspection PhpUnused */
    public function getIssuedInEuAttribute()
    {
        return $this->country->in_eu;
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function getCalculatedRegisterDateAttribute(): string
    {
        $date = $this->getAttribute('register_date') ?? null;
        if (is_null($date)) {
            $date = $this->getAttribute('first_time_in_report') ?? null;
        }

        return $date ?? date('Y-m-d', time());
    }

    /** @noinspection PhpUnused */
    public function getNumberAttribute(): string
    {
        return substr($this->vat_number, 2);
    }

    public function isValid(): bool
    {
        return $this->isFormatValid() && $this->valid;
    }

    public function isFormatValid(): bool
    {
        return Validator::validateFormat($this->vat_number ?? '');
    }

    public function getValidationErrorAsString(): string
    {
        $valid = 'success';
        $formatValid = $this->isFormatValid();

        if (!$formatValid) {
            $valid = 'danger';
        } elseif (!$this->valid) {
            $valid = 'warning';
        }

        return $valid;
    }

    public function getCompany(): Company
    {
        return $this->company;
    }
}
