<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\OnlineShop
 *
 * @property int $id
 * @property int $company_id
 * @property string $web
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineShop newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineShop newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineShop query()
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineShop whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineShop whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OnlineShop whereWeb($value)
 * @mixin \Eloquent
 */
class OnlineShop extends Model
{
    protected $table = 'online_shops';
}
