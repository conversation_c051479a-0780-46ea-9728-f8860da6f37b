<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\CountryItemTaxCode
 *
 * @property int $id
 * @property int $item_tax_code_id
 * @property int $country_id
 * @property int $vat_rate_type_id
 * @property string|null $from_date
 * @property string|null $to_date
 * @property \App\Core\Data\Models\Country $country
 * @property \App\Core\Data\Models\ItemTaxCode $itemTaxCodes
 * @property \App\Core\Data\Models\VatRateType $vatRateType
 * @method static Builder|CountryItemTaxCode newModelQuery()
 * @method static Builder|CountryItemTaxCode newQuery()
 * @method static Builder|CountryItemTaxCode query()
 * @method static Builder|CountryItemTaxCode whereCountryId($value)
 * @method static Builder|CountryItemTaxCode whereFromDate($value)
 * @method static Builder|CountryItemTaxCode whereId($value)
 * @method static Builder|CountryItemTaxCode whereItemTaxCodeId($value)
 * @method static Builder|CountryItemTaxCode whereToDate($value)
 * @method static Builder|CountryItemTaxCode whereVatRateTypeId($value)
 * @mixin \Eloquent
 */
class CountryItemTaxCode extends Model
{
    protected $table = 'country_item_tax_codes';

    /** @noinspection PhpUnused */
    public function itemTaxCodes(): BelongsTo
    {
        return $this->belongsTo(ItemTaxCode::class, 'item_tax_code_id', 'id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function vatRateType(): BelongsTo
    {
        return $this->belongsTo(VatRateType::class, 'vat_rate_type_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
