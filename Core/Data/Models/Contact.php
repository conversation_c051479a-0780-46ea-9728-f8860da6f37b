<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\Contact
 *
 * @property int $id
 * @property string $value
 * @property int|null $person_id
 * @property int|null $company_id
 * @property int $contact_type_id
 * @property int|null $institution_id
 * @property int|null $customer_id
 * @property int|null $supplier_id
 * @property \App\Core\Data\Models\ContactType $contactType
 * @property mixed $contact_type_name
 * @method static Builder|Contact newModelQuery()
 * @method static Builder|Contact newQuery()
 * @method static Builder|Contact query()
 * @method static Builder|Contact whereCompanyId($value)
 * @method static Builder|Contact whereContactTypeId($value)
 * @method static Builder|Contact whereCustomerId($value)
 * @method static Builder|Contact whereId($value)
 * @method static Builder|Contact whereInstitutionId($value)
 * @method static Builder|Contact wherePersonId($value)
 * @method static Builder|Contact whereSupplierId($value)
 * @method static Builder|Contact whereValue($value)
 * @mixin \Eloquent
 */
class Contact extends Model
{
    protected $table = 'contacts';

    public function contactType(): BelongsTo
    {
        return $this->belongsTo(ContactType::class, 'contact_type_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getContactTypeNameAttribute(): string
    {
        return $this->contactType->name;
    }
}
