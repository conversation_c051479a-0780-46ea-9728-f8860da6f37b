<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\PlatformType
 *
 * @property int $id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformType query()
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PlatformType whereName($value)
 * @mixin \Eloquent
 */
class PlatformType extends Model
{
    protected $table = 'platform_types';

    const MARKETPLACE = 1;
    const WEBSHOP = 2;
}
