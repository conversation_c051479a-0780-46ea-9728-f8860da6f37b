<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\CompanyApiConsumers
 *
 * @property int $id
 * @property int $company_id
 * @property int $api_consumer_id
 * @property-read \App\Core\Data\Models\ApiConsumer $apiConsumer
 * @property-read \App\Core\Data\Models\Company $company
 * @method static Builder|CompanyApiConsumers newModelQuery()
 * @method static Builder|CompanyApiConsumers newQuery()
 * @method static Builder|CompanyApiConsumers query()
 * @method static Builder|CompanyApiConsumers whereApiConsumerId($value)
 * @method static Builder|CompanyApiConsumers whereCompanyId($value)
 * @method static Builder|CompanyApiConsumers whereId($value)
 * @mixin \Eloquent
 */
class CompanyApiConsumers extends Model
{
    public $table = 'companies_api_consumers';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function apiConsumer(): BelongsTo
    {
        return $this->belongsTo(ApiConsumer::class, 'api_consumer_id', 'id');
    }
}
