<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\WarehouseOperator
 *
 * @property int $id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|WarehouseOperator newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WarehouseOperator newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WarehouseOperator query()
 * @method static \Illuminate\Database\Eloquent\Builder|WarehouseOperator whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WarehouseOperator whereName($value)
 * @mixin \Eloquent
 */
class WarehouseOperator extends Model
{
    protected $table = 'warehouse_operators';

    public $timestamps = false;

    const OPERATOR_AMAZON = 1;
}
