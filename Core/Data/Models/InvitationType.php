<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\InvitationType
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @method static \Illuminate\Database\Eloquent\Builder|InvitationType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InvitationType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InvitationType query()
 * @method static \Illuminate\Database\Eloquent\Builder|InvitationType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InvitationType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InvitationType whereName($value)
 * @mixin \Eloquent
 */
class InvitationType extends Model
{
    protected $table = 'invitation_types';

    const PARTNER_TO_ACCOUNTANT = 1;
    const PARTNER_TO_CLIENT = 2;
    const CLIENT_TO_ACCOUNTANT = 3;
    const CLIENT_TO_CLIENT = 4;
}
