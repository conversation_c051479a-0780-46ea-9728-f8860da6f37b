<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\AmazonSalesChannel
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonSalesChannel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonSalesChannel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonSalesChannel query()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonSalesChannel whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonSalesChannel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonSalesChannel whereName($value)
 * @mixin \Eloquent
 */
class AmazonSalesChannel extends Model
{
    protected $table = 'amazon_sales_channels';

    const MFN = 1;
    const AFN = 2;
    const MCF = 3;
}
