<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyCountrySale
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string|null $first_sale_date
 * @property int|null $current_year_sale_estimate
 * @property int|null $next_year_sale_estimate
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale whereCurrentYearSaleEstimate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale whereFirstSaleDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCountrySale whereNextYearSaleEstimate($value)
 * @mixin \Eloquent
 */
class CompanyCountrySale extends Model
{
    protected $table = 'company_country_sales';

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

}
