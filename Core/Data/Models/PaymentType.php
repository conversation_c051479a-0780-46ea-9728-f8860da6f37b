<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\PaymentType
 *
 * @property int $id
 * @property int $payment_type_status_id
 * @property string $name
 * @property string $full_name
 * @property-read \App\Core\Data\Models\PaymentTypeStatus $paymentTypeStatus
 * @method static Builder|PaymentType newModelQuery()
 * @method static Builder|PaymentType newQuery()
 * @method static Builder|PaymentType query()
 * @method static Builder|PaymentType whereId($value)
 * @method static Builder|PaymentType whereName($value)
 * @method static Builder|PaymentType wherePaymentTypeStatusId($value)
 * @mixin \Eloquent
 */
class PaymentType extends Model
{
    protected $table = 'payment_types';

    public const PAID_BANK_ACCOUNT = 1;
    public const PAID_CREDIT_OR_DEBIT_CARD = 2;
    public const PAID_CASH = 3;
    public const PAID_PRIVATELY = 4;

    public const NOT_PAID_PAYMENT_PENDING = 5;
    public const NOT_PAID_BANK_ACCOUNT_DIRECT_DEBIT = 6;
    public const NOT_PAID_CREDIT_OR_DEBIT_CARD_RECURRING_PAYMENT = 7;

    /** @noinspection PhpUnused */
    public function getFullNameAttribute(): string
    {
        return _l($this->name) . ' (' . _l($this->paymentTypeStatus->name) . ')';
    }

    /** @noinspection PhpUnused */
    public function paymentTypeStatus(): BelongsTo
    {
        return $this->belongsTo(PaymentTypeStatus::class, 'payment_type_status_id', 'id');
    }
}
