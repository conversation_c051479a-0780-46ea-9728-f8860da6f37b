<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\AmazonReportsApiQueue
 *
 * @property int $id
 * @property string $month_start
 * @property int $sales_channel_id
 * @property int|null $upload_company_id
 * @property int|null $upload_institution_institution_type_id
 * @property int|null $upload_user_id
 * @property string $status
 * @property string|null $amazon_id
 * @property string|null $batch_id
 * @property \App\Core\Data\Models\SalesChannel $salesChannel
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue query()
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereAmazonId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereMonthStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereSalesChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereUploadCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereUploadInstitutionInstitutionTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AmazonReportsApiQueue whereUploadUserId($value)
 * @mixin \Eloquent
 */
class AmazonReportsApiQueue extends Model
{
    protected $table = 'amazon_reports_api_queue';

    public const STATUS_QUEUED = 'QUEUED';
    public const STATUS_REQUESTED = 'REQUESTED';
    public const STATUS_FETCHED = 'FETCHED';

    public function salesChannel()
    {
        return $this->belongsTo(SalesChannel::class, 'sales_channel_id', 'id');
    }
}
