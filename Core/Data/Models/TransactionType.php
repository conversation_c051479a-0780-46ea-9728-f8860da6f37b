<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\TransactionType
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionType query()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionType whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionType whereName($value)
 * @mixin \Eloquent
 */
class TransactionType extends Model
{
    protected $table = 'transaction_types';
    public $timestamps = false;

    const SALE = 1;
    const RETURN = 2;
    const REFUND = 3;
    const FC_TRANSFER = 4;
    const INBOUND = 5;
    const COMMINGLING_SELL = 6;
    const COMMINGLING_BUY = 7;
}
