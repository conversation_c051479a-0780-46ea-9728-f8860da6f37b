<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Report
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string $date_from
 * @property string $date_to
 * @property mixed $data
 * @property string $created_at
 * @property int|null $year_id
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country $country
 * @method static \Illuminate\Database\Eloquent\Builder|Report newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Report newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Report query()
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereDateFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereDateTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Report whereYearId($value)
 * @mixin \Eloquent
 */
class Report extends Model
{
    protected $table = 'reports';

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }
}
