<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyContactPersonType
 *
 * @property int $id
 * @property string $type_lang_key
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPersonType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPersonType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPersonType query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPersonType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyContactPersonType whereTypeLangKey($value)
 * @mixin \Eloquent
 */
class CompanyContactPersonType extends Model
{
    protected $table = 'company_contact_person_types';

    const GENERAL = 1;
    const REPORTING = 2;
    const INVOICING = 3;
}
