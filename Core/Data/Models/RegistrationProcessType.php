<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\RegistrationProcessType
 *
 * @property int $id
 * @property string $registration_type
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcessType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcessType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcessType query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcessType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationProcessType whereRegistrationType($value)
 * @mixin \Eloquent
 */
class RegistrationProcessType extends Model
{
    protected $table = 'registration_process_types';

    const VAT_REGISTRATION = 1;
    const OSS_NUMBER_REGISTRATION = 2;
    const IOSS_NUMBER_REGISTRATION = 3;
}
