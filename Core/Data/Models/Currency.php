<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Currency
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property string $symbol
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CountryCurrency> $countryCurrency
 * @property int|null $country_currency_count
 * @property mixed $code_with_name
 * @property mixed $name_with_code
 * @method static \Illuminate\Database\Eloquent\Builder|Currency newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Currency newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Currency query()
 * @method static \Illuminate\Database\Eloquent\Builder|Currency whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Currency whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Currency whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Currency whereSymbol($value)
 * @mixin \Eloquent
 */
class Currency extends Model
{
    protected $table = 'currencies';

    protected $appends = [
        'code_with_name'
    ];

    const AFN = 1;
    const ALL = 2;
    const ANG = 3;
    const ARS = 4;
    const AUD = 5;
    const AWG = 6;
    const AZN = 7;
    const BAM = 8;
    const BBD = 9;
    const BGN = 10;
    const BMD = 11;
    const BND = 12;
    const BOB = 13;
    const BRL = 14;
    const BSD = 15;
    const BWP = 16;
    const BYR = 17;
    const BZD = 18;
    const CAD = 19;
    const CHF = 20;
    const CLP = 21;
    const CNY = 22;
    const COP = 23;
    const CRC = 24;
    const CUP = 25;
    const CZK = 26;
    const DKK = 27;
    const DOP = 28;
    const EGP = 29;
    const EUR = 30;
    const FJD = 31;
    const FKP = 32;
    const GBP = 33;
    const GIP = 34;
    const GTQ = 35;
    const GYD = 36;
    const HKD = 37;
    const HNL = 38;
    const HRK = 39;
    const HUF = 40;
    const IDR = 41;
    const ILS = 42;
    const IRR = 43;
    const ISK = 44;
    const JMD = 45;
    const JPY = 46;
    const KGS = 47;
    const KHR = 48;
    const KPW = 49;
    const KRW = 50;
    const KYD = 51;
    const KZT = 52;
    const LAK = 53;
    const LBP = 54;
    const LKR = 55;
    const LRD = 56;
    const LTL = 57;
    const LVL = 58;
    const MKD = 59;
    const MNT = 60;
    const MUR = 61;
    const MXN = 62;
    const MYR = 63;
    const MZN = 64;
    const NGN = 65;
    const NIO = 66;
    const NOK = 67;
    const NPR = 68;
    const NZD = 69;
    const OMR = 70;
    const PAB = 71;
    const PEN = 72;
    const PHP = 73;
    const PKR = 74;
    const PLN = 75;
    const PYG = 76;
    const QAR = 77;
    const RMB = 78;
    const RON = 79;
    const RSD = 80;
    const RUB = 81;
    const SAR = 82;
    const SBD = 83;
    const SCR = 84;
    const SEK = 85;
    const SGD = 86;
    const SHP = 87;
    const SOS = 88;
    const SRD = 89;
    const SVC = 90;
    const SYP = 91;
    const THB = 92;
    const TRY = 93;
    const TTD = 94;
    const TWD = 95;
    const UAH = 96;
    const USD = 97;
    const UYU = 98;
    const UZS = 99;
    const VEF = 100;
    const VND = 101;
    const XCD = 102;
    const YER = 103;
    const ZAR = 104;
    const IMP = 105;
    const INR = 106;
    const AED = 107;

    public function countryCurrency()
    {
        return $this->hasMany(CountryCurrency::class, 'currency_id', 'id');
    }

    public function getCodeWithNameAttribute()
    {
        return $this->code . ' (' . $this->name . ')';
    }

    public function getNameWithCodeAttribute()
    {
        return $this->name . ' (' . $this->code . ')';
    }
}
