<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * App\Core\Data\Models\Document
 *
 * @property int $id
 * @property int $document_category_id
 * @property int $company_id
 * @property int $update_user_id
 * @property string $updated_at
 * @property string $date
 * @property int|null $resource_id
 * @property string|null $resource
 * @property bool $deletable
 * @property bool $editable
 * @property bool $uploaded
 * @property bool $generated
 * @property \App\Core\Data\Models\DocumentCategory $category
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\DocumentData> $data
 * @property int|null $data_count
 * @property \App\Core\Data\Models\File|null $file
 * @property \Illuminate\Database\Eloquent\Model|\Eloquent $source
 * @property \App\Core\Data\Models\User|null $updateUser
 * @method static Builder|Document newModelQuery()
 * @method static Builder|Document newQuery()
 * @method static Builder|Document query()
 * @method static Builder|Document whereCompanyId($value)
 * @method static Builder|Document whereDate($value)
 * @method static Builder|Document whereDeletable($value)
 * @method static Builder|Document whereDocumentCategoryId($value)
 * @method static Builder|Document whereEditable($value)
 * @method static Builder|Document whereGenerated($value)
 * @method static Builder|Document whereId($value)
 * @method static Builder|Document whereResource($value)
 * @method static Builder|Document whereResourceId($value)
 * @method static Builder|Document whereUpdateUserId($value)
 * @method static Builder|Document whereUpdatedAt($value)
 * @method static Builder|Document whereUploaded($value)
 * @property bool $visible
 * @method static Builder|Document whereVisible($value)
 * @mixin \Eloquent
 */
class Document extends Model implements UploadableContract
{
    protected $table = 'documents';

    use HasFileTrait;

    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('documents/' . $dir . '/' . $this->getUploadResourceId()) . '/document/';
    }

    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function source(): MorphTo
    {
        return $this->morphTo('source', 'resource', 'resource_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function updateUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'update_user_id', 'id')->withoutGlobalScope(\App\Core\Data\Models\Scopes\UserVisibleScope::class);
    }

    public function data(): HasMany
    {
        return $this->hasMany(DocumentData::class, 'document_id', 'id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(DocumentCategory::class, 'document_category_id', 'id');
    }
}
