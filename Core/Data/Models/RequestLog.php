<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\RequestLog
 *
 * @property int $id
 * @property string $timestamp
 * @property int|null $user_id
 * @property string $duration_request
 * @property string $duration_sql
 * @property string $duration_php
 * @property string $memory
 * @property int $queries
 * @property string $url
 * @property string $method
 * @property int $status_code
 * @property array $additional
 * @property mixed $user_name
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereAdditional($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereDurationPhp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereDurationRequest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereDurationSql($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereMemory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereQueries($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereStatusCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereTimestamp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RequestLog whereUserId($value)
 * @mixin \Eloquent
 */
class RequestLog extends Model
{
    protected $table = 'request_log';

    protected $casts = [
        'additional' => 'array',
    ];

    public function getUserNameAttribute()
    {
        $user = $this->user;
        if (!is_null($user)) {
            return $user->full_name . ' (' . $user->email . ')';
        }

        return $this->additional['userName'] . ' (' . $this->additional['userEmail'] . ')';
    }

    public function user()
    {
        $this->belongsTo(User::class, 'user_id', 'id');
    }
}
