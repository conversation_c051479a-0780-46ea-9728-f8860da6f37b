<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\HasCompanyContract;
use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\TaxNumber
 *
 * @property int $id
 * @property int|null $company_id
 * @property int $country_id
 * @property string $number number is unique only if active. Inactive has end_date. You can have single active and multiple inactive with same number column
 * @property string|null $register_date
 * @property int|null $institution_id
 * @property string|null $france_direct_debit when null direct debit disabled, C watches company payment data, A accountants
 * @property string|null $end_date
 * @property \App\Core\Data\Models\Company|null $company
 * @property \App\Core\Data\Models\Country $country
 * @property mixed $country_name
 * @method static Builder|TaxNumber newModelQuery()
 * @method static Builder|TaxNumber newQuery()
 * @method static Builder|TaxNumber query()
 * @method static Builder|TaxNumber whereCompanyId($value)
 * @method static Builder|TaxNumber whereCountryId($value)
 * @method static Builder|TaxNumber whereEndDate($value)
 * @method static Builder|TaxNumber whereFranceDirectDebit($value)
 * @method static Builder|TaxNumber whereId($value)
 * @method static Builder|TaxNumber whereInstitutionId($value)
 * @method static Builder|TaxNumber whereNumber($value)
 * @method static Builder|TaxNumber whereRegisterDate($value)
 * @mixin \Eloquent
 */
class TaxNumber extends Model implements HasCompanyContract
{
    protected $table = 'tax_numbers';

    protected $appends = [
        //'country_name',
    ];

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function getCountryNameAttribute(): string
    {
        return $this->country->name;
    }

    public function getCompany(): Company
    {
        return $this->company;
    }
}
