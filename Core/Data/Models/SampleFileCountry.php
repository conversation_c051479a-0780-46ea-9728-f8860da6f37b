<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\SampleFileCountry
 *
 * @property int $id
 * @property int $sample_file_id
 * @property int $country_id
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFileCountry newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFileCountry newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFileCountry query()
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFileCountry whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFileCountry whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SampleFileCountry whereSampleFileId($value)
 * @mixin \Eloquent
 */
class SampleFileCountry extends Model
{
    protected $table = 'sample_file_country';

}
