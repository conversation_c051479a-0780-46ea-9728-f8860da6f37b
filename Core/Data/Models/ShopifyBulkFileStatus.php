<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyBulkFileStatus
 *
 * @property int $id
 * @property string $name_lang_key
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyBulkFileStatus whereNameLangKey($value)
 * @mixin \Eloquent
 */
class ShopifyBulkFileStatus extends Model
{
    protected $table = 'shopify_bulk_file_statuses';

    const REQUESTED = 1;

    const DOWNLOADED = 2;

    const IMPORTED = 3;

    const ERROR = 4;

    const NO_DATA = 5;
}
