<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\OssReport
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property int $tax_scheme_id
 * @property string $bank_name
 * @property string $bank_address
 * @property string $recipient_name
 * @property string $recipient_address
 * @property string $swift
 * @property string|null $iban
 * @property string|null $account_number
 * @property string|null $routing_number
 * @property-read Company $company
 * @property-read Company $country
 * @property-read TaxSchemeName $taxScheme
 * @method static Builder|OssIossReport newModelQuery()
 * @method static Builder|OssIossReport newQuery()
 * @method static Builder|OssIossReport query()
 * @method static Builder|OssIossReport whereCompanyId($value)
 * @method static Builder|OssIossReport whereCountryId($value)
 * @method static Builder|OssIossReport whereCreatedAt($value)
 * @method static Builder|OssIossReport whereData($value)
 * @method static Builder|OssIossReport whereDateFrom($value)
 * @method static Builder|OssIossReport whereDateTo($value)
 * @method static Builder|OssIossReport whereId($value)
 * @method static Builder|OssIossReport whereStatusId($value)
 * @mixin Eloquent
 */
class PaymentRecipient extends Model
{
    protected $table = 'payment_recipients';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function country(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    /** @noinspection PhpUnused */
    public function taxScheme(): HasOne
    {
        return $this->hasOne(TaxScheme::class, 'id', 'tax_scheme_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
