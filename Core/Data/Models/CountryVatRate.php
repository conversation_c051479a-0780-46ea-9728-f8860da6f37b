<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CountryVatRate
 *
 * @property int $id
 * @property int $country_id
 * @property float $value
 * @property string $effective_from
 * @property int $vat_rate_type_id
 * @property string|null $end_date
 * @property \App\Core\Data\Models\Country|null $country
 * @property \App\Core\Data\Models\VatRateType|null $type
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate query()
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate whereEffectiveFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CountryVatRate whereVatRateTypeId($value)
 * @mixin \Eloquent
 */
class CountryVatRate extends Model
{
    protected $table = 'country_vat_rates';

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function country()
    {
        return $this->hasOne(Country::class, 'id', 'country_id');
    }

    public function type()
    {
        return $this->hasOne(VatRateType::class, 'id', 'vat_rate_type_id');
    }
}
