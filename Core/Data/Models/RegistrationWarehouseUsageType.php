<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\RegistrationWarehouseUsageType
 *
 * @property int $id
 * @property string $type
 * @property string $display_name
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsageType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsageType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsageType query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsageType whereDisplayName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsageType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationWarehouseUsageType whereType($value)
 * @mixin \Eloquent
 */
class RegistrationWarehouseUsageType extends Model
{
    protected $table = 'registration_warehouse_usage_types';

    const PAN = 1;
    const MCI = 2;
    const EFN = 3;
    const OTHER = 4;
}
