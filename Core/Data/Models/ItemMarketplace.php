<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\property
 *
 * @property int $id
 * @property int $item_id
 * @property int $marketplace_id
 * @property string $description
 * @property string $created_at
 * @property string|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemItemIdentificationType[] $itemItemIdentificationTypes
 * @property-read int|null $item_item_identification_types_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemPurchasePrice[] $itemPurchasePrices
 * @property-read int|null $item_purchase_prices_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ItemSalePrice[] $itemSalePrices
 * @property-read int|null $item_sale_prices_count
 * @property-read \App\Core\Data\Models\Marketplace $marketplace
 * @method static Builder|ItemMarketplace newModelQuery()
 * @method static Builder|ItemMarketplace newQuery()
 * @method static Builder|ItemMarketplace query()
 * @method static Builder|ItemMarketplace whereCreatedAt($value)
 * @method static Builder|ItemMarketplace whereDescription($value)
 * @method static Builder|ItemMarketplace whereId($value)
 * @method static Builder|ItemMarketplace whereItemId($value)
 * @method static Builder|ItemMarketplace whereMarketplaceId($value)
 * @method static Builder|ItemMarketplace whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ItemMarketplace extends Model
{
    protected $table = 'item_marketplaces';

    public function marketplace(): BelongsTo
    {
        return $this->belongsTo(Marketplace::class, 'marketplace_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function itemItemIdentificationTypes(): HasMany
    {
        return $this->hasMany(ItemItemIdentificationType::class, 'item_marketplace_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function itemPurchasePrices(): HasMany
    {
        return $this->hasMany(ItemPurchasePrice::class, 'item_marketplace_id', 'id')->orderBy('start_date', 'DESC');
    }

    /** @noinspection PhpUnused */
    public function itemSalePrices(): HasMany
    {
        return $this->hasMany(ItemSalePrice::class, 'item_marketplace_id', 'id')->orderBy('start_date', 'DESC');
    }
}
