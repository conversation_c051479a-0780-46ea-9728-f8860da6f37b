<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\DocumentCategoryPlatform
 *
 * @property int $id
 * @property int $platform_id
 * @property int $document_category_id
 * @property \App\Core\Data\Models\Platform|null $platform
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryPlatform newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryPlatform newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryPlatform query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryPlatform whereDocumentCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryPlatform whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCategoryPlatform wherePlatformId($value)
 * @mixin \Eloquent
 */
class DocumentCategoryPlatform extends Model
{
    protected $table = 'document_category_platforms';

    public function platform()
    {
        return $this->belongsTo(Platform::class, 'platform_id', 'id');
    }
}
