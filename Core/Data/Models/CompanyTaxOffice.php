<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyTaxOffice
 *
 * @property int $id
 * @property int $country_id
 * @property string $tax_office
 * @property int $address_id
 * @property string|null $payment_collection_institution
 * @property int|null $payment_collection_institution_address_id
 * @property string|null $bank
 * @property string|null $iban
 * @property string|null $swift
 * @property string|null $phone
 * @property string|null $email
 * @property string|null $web
 * @property string|null $recipient_id
 * @property string|null $code
 * @property string|null $de_special_email
 * @property string|null $epuap
 * @property string|null $jpk_vat
 * @property string|null $pec
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereBank($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereDeSpecialEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereEpuap($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereIban($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereJpkVat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice wherePaymentCollectionInstitution($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice wherePaymentCollectionInstitutionAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice wherePec($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereRecipientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereSwift($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereTaxOffice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyTaxOffice whereWeb($value)
 * @mixin \Eloquent
 */
class CompanyTaxOffice extends Model
{
    protected $table = 'tax_offices';
}
