<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Institution
 *
 * @property int $id
 * @property string $full_legal_name
 * @property string|null $short_name
 * @property string|null $web
 * @property int $address_id
 * @property int|null $authorised_person_id
 * @property int|null $contact_person_id
 * @property int|null $default_tax_office_id
 * @property string|null $registration_number
 * @property string|null $legal_type
 * @property int|null $business_type_id
 * @property string|null $trade_name
 * @property string|null $incorporation_date
 * @property float|null $share_capital
 * @property int|null $share_capital_currency_id
 * @property int|null $national_law_governing_country_id
 * @property string|null $registration_number_issuer
 * @property string|null $place_of_commercial_registry
 * @property string|null $krs_number
 * @property string|null $national_eu_identification_number
 * @property string|null $company_description
 * @property int|null $auditing_address_id
 * @property int|null $billing_address_id
 * @property string|null $eori_number
 * @property string|null $uk_eori_number
 * @property string|null $siret
 * @property bool|null $work_mode_crm
 * @property bool|null $work_mode_service_provider
 * @property \App\Core\Data\Models\Address $address
 * @property \App\Core\Data\Models\Person|null $authorisedPerson
 * @property \App\Core\Data\Models\Bank|null $bank
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InstitutionBranch> $branches
 * @property int|null $branches_count
 * @property \App\Core\Data\Models\Person|null $contactPerson
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ContactType> $contactTypes
 * @property int|null $contact_types_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Contact> $contacts
 * @property int|null $contacts_count
 * @property \App\Core\Data\Models\TaxOffice|null $defaultTaxOffice
 * @property int $companies_count
 * @property mixed $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InstitutionInstitutionType> $institutionInstitutionTypes
 * @property int|null $institution_institution_types_count
 * @property \App\Core\Data\Models\InstitutionInstitutionType|null $partnerInstitutionInstitutionType
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\TaxNumber> $taxNumbers
 * @property int|null $tax_numbers_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\VatNumber> $vatNumbers
 * @property int|null $vat_numbers_count
 * @method static Builder|Institution newModelQuery()
 * @method static Builder|Institution newQuery()
 * @method static Builder|Institution query()
 * @method static Builder|Institution whereAddressId($value)
 * @method static Builder|Institution whereAuditingAddressId($value)
 * @method static Builder|Institution whereAuthorisedPersonId($value)
 * @method static Builder|Institution whereBillingAddressId($value)
 * @method static Builder|Institution whereBusinessTypeId($value)
 * @method static Builder|Institution whereCompanyDescription($value)
 * @method static Builder|Institution whereContactPersonId($value)
 * @method static Builder|Institution whereDefaultTaxOfficeId($value)
 * @method static Builder|Institution whereEoriNumber($value)
 * @method static Builder|Institution whereFullLegalName($value)
 * @method static Builder|Institution whereId($value)
 * @method static Builder|Institution whereIncorporationDate($value)
 * @method static Builder|Institution whereKrsNumber($value)
 * @method static Builder|Institution whereLegalType($value)
 * @method static Builder|Institution whereNationalEuIdentificationNumber($value)
 * @method static Builder|Institution whereNationalLawGoverningCountryId($value)
 * @method static Builder|Institution wherePlaceOfCommercialRegistry($value)
 * @method static Builder|Institution whereRegistrationNumber($value)
 * @method static Builder|Institution whereRegistrationNumberIssuer($value)
 * @method static Builder|Institution whereShareCapital($value)
 * @method static Builder|Institution whereShareCapitalCurrencyId($value)
 * @method static Builder|Institution whereShortName($value)
 * @method static Builder|Institution whereSiret($value)
 * @method static Builder|Institution whereTradeName($value)
 * @method static Builder|Institution whereUkEoriNumber($value)
 * @method static Builder|Institution whereWeb($value)
 * @method static Builder|Institution whereWorkModeCrm($value)
 * @method static Builder|Institution whereWorkModeServiceProvider($value)
 * @mixin \Eloquent
 */
class Institution extends Model
{
    protected $table = 'institutions';

    public const PULLUS_INSTITUTION_ID = 1;

    protected $appends = [
        'name',
    ];

    public function institutionInstitutionTypes(): HasMany
    {
        return $this->hasMany(InstitutionInstitutionType::class, 'institution_id', 'id')->orderBy('id');
    }

    /** @noinspection PhpUnused */
    public function partnerInstitutionInstitutionType(): HasOne
    {
        return $this->hasOne(InstitutionInstitutionType::class, 'institution_id', 'id')->where('institution_type_id', InstitutionType::TYPE_PARTNER);
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class, 'institution_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function contactTypes(): HasManyThrough
    {
        return $this->hasManyThrough(ContactType::class, Contact::class, 'partner_id', 'id');
    }

    public function authorisedPerson(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'authorised_person_id', 'id');
    }

    public function contactPerson(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'contact_person_id', 'id');
    }

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'address_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function defaultTaxOffice(): HasOne
    {
        return $this->hasOne(TaxOffice::class, 'id', 'default_tax_office_id');
    }

    public function branches(): HasMany
    {
        return $this->hasMany(InstitutionBranch::class, 'institution_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getCompaniesCountAttribute(): int
    {
        return count($this->companies);
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function taxNumbers(): HasMany
    {
        return $this->hasMany(TaxNumber::class, 'institution_id', 'id');
    }

    public function vatNumbers(): HasMany
    {
        return $this->hasMany(VatNumber::class, 'institution_id', 'id');
    }

    public function bank(): HasOne
    {
        return $this->hasOne(Bank::class, 'institution_id', 'id');
    }

    public function getNameAttribute(): string
    {
        return $this->full_legal_name;
    }
}
