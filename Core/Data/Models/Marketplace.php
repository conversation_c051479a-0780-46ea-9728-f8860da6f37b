<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Marketplace
 *
 * @property int $id
 * @property int $platform_id
 * @property string $name
 * @property int|null $currency_id
 * @property string|null $uid
 * @property int|null $platform_region_id
 * @property bool $global
 * @property bool $is_marketplace_incorporated_in_eu
 * @property int|null $ioss_country_id
 * @property int|null $oss_country_id
 * @property string|null $ioss_number
 * @property string|null $oss_number
 * @property string|null $vat_number
 * @property int|null $voes_country_id
 * @property string|null $voes_number
 * @property-read \App\Core\Data\Models\Currency|null $currency
 * @property-read \App\Core\Data\Models\Platform $platform
 * @method static Builder|Marketplace newModelQuery()
 * @method static Builder|Marketplace newQuery()
 * @method static Builder|Marketplace query()
 * @method static Builder|Marketplace whereCurrencyId($value)
 * @method static Builder|Marketplace whereGlobal($value)
 * @method static Builder|Marketplace whereId($value)
 * @method static Builder|Marketplace whereIossCountryId($value)
 * @method static Builder|Marketplace whereIossNumber($value)
 * @method static Builder|Marketplace whereIsMarketplaceIncorporatedInEu($value)
 * @method static Builder|Marketplace whereName($value)
 * @method static Builder|Marketplace whereNuOssCountryId($value)
 * @method static Builder|Marketplace whereNuOssNumber($value)
 * @method static Builder|Marketplace whereOssCountryId($value)
 * @method static Builder|Marketplace whereOssNumber($value)
 * @method static Builder|Marketplace wherePlatformId($value)
 * @method static Builder|Marketplace wherePlatformRegionId($value)
 * @method static Builder|Marketplace whereUid($value)
 * @method static Builder|Marketplace whereVatNumber($value)
 * @mixin \Eloquent
 */
class Marketplace extends Model
{
    protected $table = 'marketplaces';

    public function currency(): HasOne
    {
        return $this->hasOne(Currency::class, 'id', 'currency_id');
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(Platform::class, 'platform_id', 'id');
    }
}
