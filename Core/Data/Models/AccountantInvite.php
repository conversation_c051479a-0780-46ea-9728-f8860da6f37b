<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\AccountantInvite
 *
 * @method static \Illuminate\Database\Eloquent\Builder|AccountantInvite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountantInvite newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccountantInvite query()
 * @mixin \Eloquent
 */
class AccountantInvite extends Model
{
    protected $table = 'accountant_invites';
}
