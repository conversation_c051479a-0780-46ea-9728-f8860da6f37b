<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Reports\DataTransfer\SalesChannelAmazonFetchedReport;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\SalesChannel
 *
 * @property int $id
 * @property int $company_id
 * @property int $platform_id
 * @property string|null $uaid
 * @property string|null $account_name
 * @property string|null $amazon_refresh_token
 * @property int|null $platform_region_id
 * @property int|null $marketplace_id
 * @property array $data
 * @property string|null $shopify_access_token
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\AmazonReportsApiQueue> $amazonApiQueuedReports
 * @property int|null $amazon_api_queued_reports_count
 * @property \App\Core\Data\Models\Company $company
 * @property mixed $amazon_fetched_reports
 * @property mixed $platform_name
 * @property \App\Core\Data\Models\Marketplace|null $marketplace
 * @property \App\Core\Data\Models\Platform $platform
 * @property \App\Core\Data\Models\PlatformRegion|null $platformRegion
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Product> $products
 * @property int|null $products_count
 * @property-write mixed $amazon_fetched_report
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel amazon()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel query()
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereAmazonRefreshToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereMarketplaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel wherePlatformId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel wherePlatformRegionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereShopifyAccessToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SalesChannel whereUaid($value)
 * @mixin \Eloquent
 */
class SalesChannel extends Model
{
    protected $table = 'ecommerce_accounts';

    protected $casts = [
        'data' => 'array'
    ];

    protected $appends = [
        'amazon_fetched_reports'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function platform()
    {
        return $this->belongsTo(Platform::class, 'platform_id', 'id');
    }

    public function marketplace()
    {
        return $this->hasOne(Marketplace::class, 'id', 'marketplace_id');
    }

    public function platformRegion()
    {
        return $this->belongsTo(PlatformRegion::class, 'platform_region_id', 'id');
    }

    public function getPlatformNameAttribute()
    {
        return $this->platform->name;
    }

    public function scopeAmazon($query)
    {
        return $query->where('platform_id', '=', Platform::PLATFORM_AMAZON);
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'ecommerce_account_id', 'id');
    }

    public function amazonApiQueuedReports()
    {
        return $this->hasMany(AmazonReportsApiQueue::class, 'sales_channel_id', 'id');
    }

    /**
     * @inheritDoc
     */
    public function getStatus(): string
    {
        return 'Sales channel status';
    }

    /**
     * @inheritDoc
     */
    public function getDescription(): string
    {
        return $this->displayName ?? 'Sales channel desc.';
    }

    /**
     * @inheritDoc
     */
    public function getTime(): \Carbon\Carbon
    {
        return \Carbon\Carbon::now()->subHours(3);
    }

    /**
     * @inheritDoc
     */
    public function getId(): int
    {
        return $this->id;
    }

    public function getAmazonFetchedReportsAttribute()
    {
        $data = $this->data ?? [];
        $reportsData = $data['amazon_fetched_reports'] ?? [];
        $reports = [];
        foreach ($reportsData as $report) {
            $reports[] = new SalesChannelAmazonFetchedReport(
                $report['status'],
                $report['reportQueueMonthStartDate'],
                $report['reportStartDate'],
                $report['reportEndDate'],
                $report['fileId'],
                $report['documentId'],
                $report['documentUrl'],
            );
        }

        return collect($reports)
            ->sortBy(function (SalesChannelAmazonFetchedReport $salesChannelAmazonFetchedReport) {
                return $salesChannelAmazonFetchedReport->getReportStartDateAsCarbon()->timestamp;
            }, SORT_NATURAL, true);
    }

    public function setAmazonFetchedReportsAttribute(Collection|array $amazonFetchedReports)
    {
        if ($amazonFetchedReports instanceof Collection) {
            $amazonFetchedReports = $amazonFetchedReports->toArray();
        }
        $data = $this->data ?? [];
        $data['amazon_fetched_reports'] = $amazonFetchedReports;

        $this->attributes['data'] = json_encode($data);
    }

    public function setAmazonFetchedReportAttribute(array $amazonFetchedReports)
    {
        $status = $amazonFetchedReports['status'];
        $reportQueueMonthStartDate = $amazonFetchedReports['reportQueueMonthStartDate'];
        $reportStartDate = $amazonFetchedReports['reportStartDate'];
        $reportEndDate = $amazonFetchedReports['reportEndDate'];
        $fileId = $amazonFetchedReports['fileId'];
        $documentId = $amazonFetchedReports['documentId'];
        $documentUrl = $amazonFetchedReports['documentUrl'];

        $report = new SalesChannelAmazonFetchedReport(
            $status,
            $reportQueueMonthStartDate,
            $reportStartDate,
            $reportEndDate,
            $fileId,
            $documentId,
            $documentUrl
        );

        $amazonFetchedReports = $this->amazon_fetched_reports;
        $amazonFetchedReports->push($report);

        $data = $this->data ?? [];
        $data['amazon_fetched_reports'] = $amazonFetchedReports->toArray();

        $this->attributes['data'] = json_encode($data);
    }
}
