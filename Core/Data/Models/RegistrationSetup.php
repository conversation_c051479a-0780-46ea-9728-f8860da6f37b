<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\RegistrationSetup
 *
 * @property int $id
 * @property string $key
 * @property string $value
 * @property string|null $description
 * @property int $accountant_id
 * @property int $country_id
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup whereAccountantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegistrationSetup whereValue($value)
 * @mixin \Eloquent
 */
class RegistrationSetup extends Model
{
    protected $table = 'registration_setups';
}
