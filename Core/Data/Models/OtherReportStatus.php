<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\OtherReport
 *
 * @property int $id
 * @property string $name
 * @method static Builder|AmazonReport newModelQuery()
 * @method static Builder|AmazonReport newQuery()
 * @method static Builder|AmazonReport query()
 * @method static Builder|AmazonReport whereBatchId($value)
 * @method static Builder|AmazonReport whereDebug($value)
 * @method static Builder|AmazonReport whereDescription($value)
 * @method static Builder|AmazonReport whereEndDate($value)
 * @method static Builder|AmazonReport whereErrorReason($value)
 * @method static Builder|AmazonReport whereId($value)
 * @method static Builder|AmazonReport whereParsingEndedAt($value)
 * @method static Builder|AmazonReport whereParsingStartedAt($value)
 * @method static Builder|AmazonReport whereReportStatusId($value)
 * @method static Builder|AmazonReport whereSalesChannelId($value)
 * @method static Builder|AmazonReport whereStartDate($value)
 * @method static Builder|AmazonReport whereUploadCompanyId($value)
 * @method static Builder|AmazonReport whereUploadInstitutionInstitutionTypeId($value)
 * @method static Builder|AmazonReport whereUploadUserId($value)
 * @method static Builder|AmazonReport whereUploadedAt($value)
 * @method static Builder|AmazonReport whereUserMessages($value)
 * @mixin Eloquent
 */
class OtherReportStatus extends Model
{
    protected $table = 'other_report_statuses';

    const NEW = 1;
    const REVIEW_PENDING = 2;
    const PARSED = 3;
    const FAILED = 4;
}
