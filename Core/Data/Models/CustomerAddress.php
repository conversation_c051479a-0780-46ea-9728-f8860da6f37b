<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\CustomerAddress
 *
 * @property int $id
 * @property int $customer_id
 * @property int $address_id
 * @property int $address_type_id
 * @property \App\Core\Data\Models\Address $address
 * @property \App\Core\Data\Models\AddressType $addressType
 * @property \App\Core\Data\Models\Customer $customer
 * @method static Builder|CustomerAddress newModelQuery()
 * @method static Builder|CustomerAddress newQuery()
 * @method static Builder|CustomerAddress query()
 * @method static Builder|CustomerAddress whereAddressId($value)
 * @method static Builder|CustomerAddress whereAddressTypeId($value)
 * @method static Builder|CustomerAddress whereCustomerId($value)
 * @method static Builder|CustomerAddress whereId($value)
 * @mixin \Eloquent
 */
class CustomerAddress extends Model
{
    protected $table = 'customers_addresses';

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class, 'id', 'address_id');
    }

    public function addressType(): HasOne
    {
        return $this->hasOne(AddressType::class, 'id', 'address_type_id');
    }
}
