<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\TimelineModelContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model as BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\History
 *
 * @property int $id
 * @property string $description
 * @property string $changed_at
 * @property int $change_user_id
 * @property int $resource_id
 * @property string $resource_class
 * @property string $action
 * @property array $data
 * @property array $lang_params
 * @property \App\Core\Data\Models\User $changeUser
 * @property mixed $timestamp
 * @method static Builder|History newModelQuery()
 * @method static Builder|History newQuery()
 * @method static Builder|History query()
 * @method static Builder|History whereAction($value)
 * @method static Builder|History whereChangeUserId($value)
 * @method static Builder|History whereChangedAt($value)
 * @method static Builder|History whereData($value)
 * @method static Builder|History whereDescription($value)
 * @method static Builder|History whereId($value)
 * @method static Builder|History whereLangParams($value)
 * @method static Builder|History whereResourceClass($value)
 * @method static Builder|History whereResourceId($value)
 * @mixin \Eloquent
 */
class History extends Model implements TimelineModelContract
{
    protected $table = 'history';
    protected $casts = [
        'data'        => 'array',
        'lang_params' => 'array',
    ];
    protected $appends = [
        'timestamp',
    ];
    protected $with = ['changeUser'];

    public function getId(): int
    {
        return $this->id;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getChangedAt(): Carbon
    {
        return Carbon::parse($this->changed_at);
    }

    /** @noinspection PhpUnused */
    public function getChangeUserId(): int
    {
        return $this->change_user_id;
    }

    public function getChangeUser(): User
    {
        return $this->changeUser;
    }

    public function getTimelineResourceId(): int
    {
        return $this->resource_id;
    }

    public function getResourceClass(): ?string
    {
        return $this->resource_class;
    }

    public function getResource(): ?BaseModel
    {
        return $this->resource;
    }

    /** @noinspection PhpUnused */
    public function getAction(): string
    {
        return $this->action;
    }

    public function getData(): array
    {
        return $this->data;
    }

    /** @noinspection PhpUnused */
    public function changeUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'change_user_id', 'id')->withoutGlobalScopes();
    }

    public function resource(): ?BelongsTo
    {
        $resourceClass = $this->getResourceClass();
        if (is_null($resourceClass)) {
            return null;
        }

        return $this->belongsTo($this->getResourceClass(), 'resource_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getTimestampAttribute(): float|int|string
    {
        return Carbon::parse($this->changed_at)->timestamp;
    }

    public function getLangParams(): array
    {
        return $this->lang_params;
    }

    public function getTime(): Carbon
    {
        return Carbon::parse($this->changed_at);
    }

    public function getUser(): User
    {
        return $this->changeUser;
    }

    public function getExtraData(): array
    {
        return $this->data['extraData'] ?? [];
    }
}
