<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Person
 *
 * @property int $id
 * @property string $first_name
 * @property string $last_name
 * @property string|null $position
 * @property string|null $birth_date
 * @property int|null $user_id
 * @property int|null $address_id
 * @property int|null $birth_country_id
 * @property string|null $place_of_birth
 * @property string|null $passport_number
 * @property string|null $passport_valid_from
 * @property string|null $passport_valid_until
 * @property int|null $gender_type_id
 * @property int|null $passport_country_id
 * @property string|null $passport_issued_by
 * @property string|null $nationality
 * @property bool $passport_is_id_card If this field is "true" passport columns are ID card columns
 * @property string|null $pesel_number This is a national identification number used in Poland (PESEL)
 * @property string|null $nip_number This is the Polish tax identification number (NIP)
 * @property string|null $nif_number Tax Identification Number (NIF) for Spanish citizens
 * @property string|null $nie_number Tax Identification Number (NIE) for Non-Spanish citizens
 * @property \App\Core\Data\Models\Address|null $address
 * @property \App\Core\Data\Models\Country|null $birthCountry
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ContactType> $contactTypes
 * @property int|null $contact_types_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Contact> $contacts
 * @property int|null $contacts_count
 * @property \App\Core\Data\Models\GenderType|null $genderType
 * @property mixed $name
 * @property \App\Core\Data\Models\Country|null $passportCountry
 * @method static Builder|Person newModelQuery()
 * @method static Builder|Person newQuery()
 * @method static Builder|Person query()
 * @method static Builder|Person whereAddressId($value)
 * @method static Builder|Person whereBirthCountryId($value)
 * @method static Builder|Person whereBirthDate($value)
 * @method static Builder|Person whereFirstName($value)
 * @method static Builder|Person whereGenderTypeId($value)
 * @method static Builder|Person whereId($value)
 * @method static Builder|Person whereLastName($value)
 * @method static Builder|Person whereNationality($value)
 * @method static Builder|Person whereNieNumber($value)
 * @method static Builder|Person whereNifNumber($value)
 * @method static Builder|Person whereNipNumber($value)
 * @method static Builder|Person wherePassportCountryId($value)
 * @method static Builder|Person wherePassportIsIdCard($value)
 * @method static Builder|Person wherePassportIssuedBy($value)
 * @method static Builder|Person wherePassportNumber($value)
 * @method static Builder|Person wherePassportValidFrom($value)
 * @method static Builder|Person wherePassportValidUntil($value)
 * @method static Builder|Person wherePeselNumber($value)
 * @method static Builder|Person wherePlaceOfBirth($value)
 * @method static Builder|Person wherePosition($value)
 * @method static Builder|Person whereUserId($value)
 * @mixin \Eloquent
 */
class Person extends Model
{
    protected $table = 'people';

    protected $appends = [
        'name'
    ];

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class, 'person_id', 'id');
    }

    /** @noinspection PhpUnused  FOR DELETE CHECK */
//    public function contactTypes(): HasManyThrough
//    {
//        return $this->hasManyThrough(ContactType::class, Contact::class, 'person_id', 'id');
//    }

    public function getNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class);
    }

    public function birthCountry(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'birth_country_id');
    }

    public function passportCountry(): HasOne
    {
        return $this->hasOne(Country::class, 'id', 'passport_country_id');
    }

    public function genderType(): HasOne
    {
        return $this->hasOne(GenderType::class, 'id', 'gender_type_id');
    }
}
