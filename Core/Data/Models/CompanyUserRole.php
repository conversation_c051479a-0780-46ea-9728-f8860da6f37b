<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\CompanyUserRole
 *
 * @property int $id
 * @property int $company_id
 * @property int $user_id
 * @property int $role_id
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Role|null $role
 * @method static Builder|CompanyUserRole newModelQuery()
 * @method static Builder|CompanyUserRole newQuery()
 * @method static Builder|CompanyUserRole query()
 * @method static Builder|CompanyUserRole whereCompanyId($value)
 * @method static Builder|CompanyUserRole whereId($value)
 * @method static Builder|CompanyUserRole whereRoleId($value)
 * @method static Builder|CompanyUserRole whereUserId($value)
 * @mixin \Eloquent
 */
class CompanyUserRole extends Model
{
    protected $table = 'companies_users_roles';

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function role(): HasOne
    {
        return $this->hasOne(Role::class, 'id', 'role_id');
    }
}
