<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\TemplateContract;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;
use App\Core\Mappers\Base\Mapper;
use App\Core\Period\PeriodInterval;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\ReportTemplate
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $context
 * @property string|null $valid_from
 * @property string $resource
 * @property int $resource_id
 * @property string $interval_enum
 * @property int $on_day
 * @property \App\Core\Data\Models\File|null $file
 * @property string|null $interval_enum_name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ReportTemplateField> $templateFields
 * @property int|null $report_fields_count
 * @method static Builder|ReportTemplate newModelQuery()
 * @method static Builder|ReportTemplate newQuery()
 * @method static Builder|ReportTemplate query()
 * @method static Builder|ReportTemplate whereContext($value)
 * @method static Builder|ReportTemplate whereDescription($value)
 * @method static Builder|ReportTemplate whereDevDescription($value)
 * @method static Builder|ReportTemplate whereHasOwnView($value)
 * @method static Builder|ReportTemplate whereId($value)
 * @method static Builder|ReportTemplate whereIntervalEnum($value)
 * @method static Builder|ReportTemplate whereIsLocked($value)
 * @method static Builder|ReportTemplate whereName($value)
 * @method static Builder|ReportTemplate whereOnDay($value)
 * @method static Builder|ReportTemplate whereResource($value)
 * @method static Builder|ReportTemplate whereResourceId($value)
 * @method static Builder|ReportTemplate whereValidFrom($value)
 * @method static Builder|ReportTemplate whereValidTo($value)
 * @property int|null $report_type_id
 * @property-read \App\Core\Data\Models\ReportType|null $reportType
 * @property-read int|null $template_fields_count
 * @method static Builder|ReportTemplate whereReportTypeId($value)
 * @mixin \Eloquent
 */
class ReportTemplate extends Model implements UploadableContract, TemplateContract
{
    use HasFileTrait;

    protected $table = 'report_templates';

    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('report-templates/' . $dir . '/' . $this->getUploadResourceId()) . '/';
    }

    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function templateFields(): HasMany
    {
        return $this->hasMany(ReportTemplateField::class, 'report_template_id', 'id')->orderBy('id');
    }

    public function reportType()
    {
        return $this->hasOne(ReportType::class, 'id', 'report_type_id');
    }

    /** @noinspection PhpUnused */
    public function getIntervalEnumNameAttribute(): ?string
    {
        $enum = $this->interval_enum;
        if (is_null($enum)) {
            return null;
        }

        $interval = PeriodInterval::getTaxPeriodsIntervalByEnum($enum);
        if (!is_null($interval)) {
            $interval = $interval['name'] ?? null;
        }

        return $interval;
    }

    public function getFields(): Collection
    {
        return $this->templateFields;
    }

    public function getExample(): Mapper
    {
        /**
         * @var Mapper $context
         */
        $context = $this->context;

        return $context::getExample();
    }

    public function getContext(): ?string
    {
        return $this->context;
    }

    public function getFile(): File
    {
        return $this->file;
    }
}
