<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\PostalCodeException
 *
 * @property string|null $postal_code_start_with
 * @property int|null $postal_code_country_id
 * @property string|null $place
 * @property int|null $vat_country_id
 * @property string|null $in_vat_country_start_date
 * @property string|null $in_vat_country_end_date
 * @property int $id
 * @property \App\Core\Data\Models\Country|null $postalCodeCountry
 * @property \App\Core\Data\Models\Country|null $vatCountry
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException query()
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException whereInVatCountryEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException whereInVatCountryStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException wherePlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException wherePostalCodeCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException wherePostalCodeStartWith($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostalCodeException whereVatCountryId($value)
 * @mixin \Eloquent
 */
class PostalCodeException extends Model
{
    protected $table = 'postal_code_exceptions';

    public $timestamps = false;

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    public function vatCountry()
    {
        return $this->belongsTo(Country::class, 'vat_country_id');
    }

    public function postalCodeCountry()
    {
        return $this->belongsTo(Country::class, 'postal_code_country_id');
    }
}
