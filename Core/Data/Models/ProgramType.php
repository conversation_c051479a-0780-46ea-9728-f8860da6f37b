<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ProgramType
 *
 * @property int $id
 * @property string $value
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProgramType whereValue($value)
 * @mixin \Eloquent
 */
class ProgramType extends Model
{
    protected $table = 'program_types';

    const TYPE_REGULAR = 1;
    const TYPE_LIQUIDATION = 2;
    const TYPE_DONATION = 3;
    const TYPE_COMMINGLING = 4;
}
