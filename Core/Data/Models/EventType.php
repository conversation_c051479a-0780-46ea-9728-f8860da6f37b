<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\EventType
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string|null $note
 * @property string|null $amazon_code
 * @property int|null $invoice_type_id
 * @method static \Illuminate\Database\Eloquent\Builder|EventType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EventType query()
 * @method static \Illuminate\Database\Eloquent\Builder|EventType whereAmazonCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventType whereInvoiceTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EventType whereNote($value)
 * @mixin \Eloquent
 */
class EventType extends Model
{
    protected $table = 'event_types';

    public const SALE = 1;
    public const RETURN = 5;
    public const REFUND = 6;
    public const FC_TRANSFER = 7;
    public const INBOUND = 8;
    public const COMMINGLING_SELL = 9;
    public const COMMINGLING_BUY = 10;
    public const LIQUIDATION_SALE = 11;
    public const LIQUIDATION_REFUND = 12;
    public const DONATION = 13;
}
