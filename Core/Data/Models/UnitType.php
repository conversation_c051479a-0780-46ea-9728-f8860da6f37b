<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\UnitType
 *
 * @property int $id
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|UnitType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UnitType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UnitType query()
 * @method static \Illuminate\Database\Eloquent\Builder|UnitType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UnitType whereName($value)
 * @mixin \Eloquent
 */
class UnitType extends Model
{
    protected $table = 'unit_types';

    const WEIGHT = 1;
    const DIMENSION = 2;
    const QUANTITY = 3;
    const VOLUME = 4;
    const TIME = 5;
    const DEGREE = 6;
    const CARAT = 7;
}
