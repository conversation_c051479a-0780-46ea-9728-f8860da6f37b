<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\TemplateContract;
use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;
use App\Core\Mappers\Base\Mapper;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * App\Core\Data\Models\DocumentTemplate
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $context
 * @property int $document_category_id
 * @property string $valid_from
 * @property-read \App\Core\Data\Models\DocumentCategory $documentCategory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\DocumentTemplateField> $templateFields
 * @property-read int|null $document_template_fields_count
 * @property-read \App\Core\Data\Models\File|null $file
 * @method static Builder|DocumentTemplate newModelQuery()
 * @method static Builder|DocumentTemplate newQuery()
 * @method static Builder|DocumentTemplate query()
 * @method static Builder|DocumentTemplate whereAdminDescription($value)
 * @method static Builder|DocumentTemplate whereContext($value)
 * @method static Builder|DocumentTemplate whereDescription($value)
 * @method static Builder|DocumentTemplate whereDocumentCategoryId($value)
 * @method static Builder|DocumentTemplate whereId($value)
 * @method static Builder|DocumentTemplate whereIsLocked($value)
 * @method static Builder|DocumentTemplate whereName($value)
 * @method static Builder|DocumentTemplate whereValidFrom($value)
 * @mixin \Eloquent
 */
class DocumentTemplate extends Model implements UploadableContract, TemplateContract
{
    use HasFileTrait;

    protected $table = 'document_templates';

    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        return upload_path('document-templates/' . $dir . '/' . $this->getUploadResourceId()) . '/';
    }

    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function templateFields(): HasMany
    {
        return $this->hasMany(DocumentTemplateField::class, 'document_template_id', 'id')->orderBy('id');
    }

    public function documentCategory(): BelongsTo
    {
        return $this->belongsTo(DocumentCategory::class, 'document_category_id', 'id');
    }

    public function getFields(): Collection
    {
        return $this->templateFields;
    }

    public function getExample(): Mapper
    {
        /**
         * @var Mapper $context
         */
        $context = $this->context;

        return $context::getExample();
    }

    public function getContext(): ?string
    {
        return $this->context;
    }

    public function getFile(): File
    {
        return $this->file;
    }
}
