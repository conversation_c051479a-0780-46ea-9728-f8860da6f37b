<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Traits\HasContractsTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\InstitutionInstitutionType
 *
 * @property int $id
 * @property int $institution_id
 * @property int $institution_type_id
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Invitation> $accountantInvitations
 * @property int|null $accountant_invitations_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Company> $companies
 * @property int|null $companies_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Contract> $contracts
 * @property int|null $contracts_count
 * @property mixed $full_name
 * @property mixed $name
 * @property mixed $vat_registration_country_ids
 * @property \App\Core\Data\Models\Institution $institution
 * @property \App\Core\Data\Models\InstitutionType $institutionType
 * @property \Illuminate\Database\Eloquent\Collection<\App\Core\Data\Models\InstitutionBranch> $branches
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InstitutionTypeCountryPartner> $institutionTypeAccountants
 * @property int|null $institution_type_accountants_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InstitutionTypeCountryPartner> $institutionTypePartners
 * @property int|null $institution_type_partners_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\InstitutionInstitutionTypeUserRole> $institutionTypeUsersRoles
 * @property int|null $institution_type_users_roles_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\RegistrationSetup> $registrationSetup
 * @property int|null $registration_setup_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Team> $teams
 * @property int|null $teams_count
 * @method static Builder|InstitutionInstitutionType newModelQuery()
 * @method static Builder|InstitutionInstitutionType newQuery()
 * @method static Builder|InstitutionInstitutionType query()
 * @method static Builder|InstitutionInstitutionType whereId($value)
 * @method static Builder|InstitutionInstitutionType whereInstitutionId($value)
 * @method static Builder|InstitutionInstitutionType whereInstitutionTypeId($value)
 * @mixin \Eloquent
 */
class InstitutionInstitutionType extends Model
{
    use HasContractsTrait;

    const PULLUS_PARTNER_ID = 1;

    protected $table = 'institution_institution_type';

    protected static function boot(): void
    {
        parent::boot();

        //static::addGlobalScope(new UserInstitutionInstitutionTypeScope());
    }

    /**
     * Must load country, institutionType, and institution
     *
     * @noinspection PhpUnused*/
    public function getFullNameAttribute(): string
    {
        return $this->institution->full_legal_name . ' [' . _l($this->institutionType->name) . ']';
    }

    public function getNameAttribute(): string
    {
        return $this->institution->full_legal_name;
    }

    public function institutionType(): BelongsTo
    {
        return $this->belongsTo(InstitutionType::class, 'institution_type_id', 'id');
    }

    public function institutionTypePartners(): HasMany
    {
        return $this->hasMany(InstitutionTypeCountryPartner::class, 'institution_institution_type_id', 'id');
    }

    public function institutionTypeAccountants(): HasMany
    {
        return $this->hasMany(InstitutionTypeCountryPartner::class, 'partner_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function institutionTypeUsersRoles(): HasMany
    {
        return $this->hasMany(InstitutionInstitutionTypeUserRole::class, 'institution_institution_type_id', 'id');
    }

    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class, 'institution_id', 'id');
    }

    public function teams(): HasMany
    {
        return $this->hasMany(Team::class, 'institution_institution_type_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getVatRegistrationCountryIdsAttribute(): array
    {
        return $this->institutionTypeAccountants()->pluck('country_id')->unique()->toArray();
    }

    public function companies(): HasMany
    {
        return $this->hasMany(Company::class, 'partner_id', 'id')->orderBy('id');
    }

    /** @noinspection PhpUnused */
    public function registrationSetup(): HasMany
    {
        return $this->hasMany(RegistrationSetup::class, 'accountant_id', 'id');
    }

    public function branches(): HasMany
    {
        return $this->institution->branches();
    }

    /** @noinspection PhpUnused */
    public function accountantInvitations(): HasMany
    {
        return $this->hasMany(Invitation::class, 'partner_id', 'id')
            ->where('invitation_type_id', InvitationType::PARTNER_TO_ACCOUNTANT);
    }
}
