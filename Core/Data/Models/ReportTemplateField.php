<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\TemplateFieldContract;

/**
 * App\Core\Data\Models\ReportTemplateField
 *
 * @property int $id
 * @property int $report_template_id
 * @property string|null $context_key
 * @property string $template_key
 * @property \App\Core\Data\Models\ReportTemplate $report
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField query()
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField whereContextKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField whereReportTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReportTemplateField whereTemplateKey($value)
 * @mixin \Eloquent
 */
class ReportTemplateField extends Model implements TemplateFieldContract
{
    protected $table = 'report_template_fields';

    public function report()
    {
        return $this->belongsTo(ReportTemplate::class, 'report_template_id', 'id');
    }

    public function getContextKey(): ?string
    {
        return $this->context_key;
    }

    public function getTemplateKey(): string
    {
        return $this->template_key;
    }
}
