<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Traits\HasSourceTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Core\Data\Models\Invoice
 *
 * @property int $id
 * @property string $invoice_date
 * @property string|null $invoice_number
 * @property int|null $customer_id
 * @property string|null $bill_to_name
 * @property string|null $bill_to_address
 * @property int|null $marketplace_id
 * @property int|null $delivery_condition_id
 * @property int $tax_reporting_scheme_id
 * @property int $tax_collection_responsibility_id
 * @property string $invoice_promotion_amount
 * @property string $invoice_promotion_percentage
 * @property int $invoice_status_id
 * @property string|null $ship_from_vat_number
 * @property string|null $ship_to_vat_number
 * @property string|null $ship_to_postal_code
 * @property int $ship_to_country_id
 * @property array $data
 * @property int|null $tax_calculation_country_id
 * @property int|null $tax_reporting_country_id
 * @property int|null $customer_type_id
 * @property int $sales_currency_id
 * @property int $invoice_subtype_id
 * @property string|null $reporting_date
 * @property string|null $tax_calculation_date
 * @property int|null $ship_from_country_id
 * @property int $company_id
 * @property string|null $shopify_order_id
 * @property string $created_at
 * @property int $create_user_id
 * @property string|null $resource
 * @property int|null $resource_id
 * @property string|null $order_number
 * @property string|null $order_date
 * @property int|null $buyer_vat_number_country_id
 * @property array $messages Json field with messages. Will be shown to user. Format for one message is {type: "success", message: "Some message", devOnly: false - optional, ref: "someCustomReferenceIfNeeded"}
 * @property int|null $bill_to_country_id
 * @property string|null $bill_to_postal_code
 * @property string|null $bill_to_vat_number
 * @property string|null $ship_to_name
 * @property string|null $comment
 * @property int $invoice_number_sequence
 * @property string|null $ship_to_address
 * @property string|null $parsing_invoice_number
 * @property bool $shipping_is_same_as_bill_to
 * @property string|null $description
 * @property string|null $ship_from_tax_number
 * @property int|null $supplier_id
 * @property bool $shipping_is_same_as_bill_from
 * @property int|null $payment_type_id
 * @property string|null $bill_to_tax_number
 * @property string|null $bill_to_voes_number
 * @property string|null $bill_from_name
 * @property string|null $bill_from_address
 * @property string|null $bill_from_postal_code
 * @property int|null $bill_from_country_id
 * @property string|null $bill_from_vat_number
 * @property string|null $bill_from_tax_number
 * @property string|null $bill_from_voes_number
 * @property string|null $ship_to_tax_number
 * @property string|null $ship_to_voes_number
 * @property string|null $ship_from_name
 * @property string|null $ship_from_address
 * @property string|null $ship_from_postal_code
 * @property string|null $ship_from_voes_number
 * @property string|null $supplier_voes_number
 * @property int|null $connected_invoice_id
 * @property string|null $connected_invoice_type
 * @property int|null $bill_to_bank_country_id
 * @property int|null $bill_to_sim_country_id
 * @property string|null $service_period_from
 * @property string|null $service_period_to
 * @property int $item_type_id
 * @property-read \App\Core\Data\Models\Country|null $billFromCountry
 * @property-read \App\Core\Data\Models\Country|null $billToCountry
 * @property-read \App\Core\Data\Models\Company $company
 * @property-read Invoice|null $connectedDeemedIssuedStatusInvoice
 * @property-read Invoice|null $connectedDeemedSuppliesStatusInvoice
 * @property-read \App\Core\Data\Models\Currency $currency
 * @property-read \App\Core\Data\Models\Customer|null $customer
 * @property-read \App\Core\Data\Models\CustomerType|null $customerType
 * @property-read \App\Core\Data\Models\DeliveryCondition|null $deliveryCondition
 * @property-read \App\Core\Data\Models\EventType|null $eventType
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\ExchangeRate[] $exchangeRates
 * @property-read int|null $exchange_rates_count
 * @property-read string|null $oss_ioss_type
 * @property-read string $resource_human
 * @property-read string $resource_icon
 * @property-read float $total
 * @property-read float $total_net
 * @property-read float $total_vat
 * @property-read float $total_without_shipping_and_wrap
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\InvoiceItem[] $invoiceItems
 * @property-read int|null $invoice_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Core\Data\Models\InvoiceItem[] $invoiceItemsWithoutShippingAndWrap
 * @property-read int|null $invoice_items_without_shipping_and_wrap_count
 * @property-read \App\Core\Data\Models\InvoiceSubtype $invoiceSubtype
 * @property-read \App\Core\Data\Models\Marketplace|null $marketplace
 * @property-read \App\Core\Data\Models\PaymentType|null $paymentType
 * @property-read \App\Core\Data\Models\Country|null $shipFromCountry
 * @property-read \App\Core\Data\Models\Country $shipToCountry
 * @property-read \Illuminate\Database\Eloquent\Model $source
 * @property-read \App\Core\Data\Models\InvoiceStatus $status
 * @property-read \App\Core\Data\Models\Supplier|null $supplier
 * @property-read \App\Core\Data\Models\TaxCollectionResponsibility $taxCollectionResponsibility
 * @property-read \App\Core\Data\Models\Country|null $taxReportingCountry
 * @property-read \App\Core\Data\Models\TaxReportingScheme $taxReportingScheme
 * @property-read \App\Core\Data\Models\Country|null $taxCalculationCountry
 * @property-read \App\Core\Data\Models\ItemType|null $itemType
 * @method static Builder|Invoice newModelQuery()
 * @method static Builder|Invoice newQuery()
 * @method static Builder|Invoice query()
 * @method static Builder|Invoice whereBillFromAddress($value)
 * @method static Builder|Invoice whereBillFromCountryId($value)
 * @method static Builder|Invoice whereBillFromName($value)
 * @method static Builder|Invoice whereBillFromPostalCode($value)
 * @method static Builder|Invoice whereBillFromTaxNumber($value)
 * @method static Builder|Invoice whereBillFromVatNumber($value)
 * @method static Builder|Invoice whereBillFromVoesNumber($value)
 * @method static Builder|Invoice whereBillToAddress($value)
 * @method static Builder|Invoice whereBillToBankCountryId($value)
 * @method static Builder|Invoice whereBillToCountryId($value)
 * @method static Builder|Invoice whereBillToName($value)
 * @method static Builder|Invoice whereBillToPostalCode($value)
 * @method static Builder|Invoice whereBillToSimCountryId($value)
 * @method static Builder|Invoice whereBillToTaxNumber($value)
 * @method static Builder|Invoice whereBillToVatNumber($value)
 * @method static Builder|Invoice whereBillToVoesNumber($value)
 * @method static Builder|Invoice whereBuyerVatNumberCountryId($value)
 * @method static Builder|Invoice whereComment($value)
 * @method static Builder|Invoice whereCompanyId($value)
 * @method static Builder|Invoice whereConnectedInvoiceId($value)
 * @method static Builder|Invoice whereConnectedInvoiceType($value)
 * @method static Builder|Invoice whereCreateUserId($value)
 * @method static Builder|Invoice whereCreatedAt($value)
 * @method static Builder|Invoice whereCustomerId($value)
 * @method static Builder|Invoice whereCustomerTypeId($value)
 * @method static Builder|Invoice whereData($value)
 * @method static Builder|Invoice whereDeliveryConditionId($value)
 * @method static Builder|Invoice whereDescription($value)
 * @method static Builder|Invoice whereId($value)
 * @method static Builder|Invoice whereInvoiceDate($value)
 * @method static Builder|Invoice whereInvoiceNumber($value)
 * @method static Builder|Invoice whereInvoiceNumberSequence($value)
 * @method static Builder|Invoice whereInvoicePromotionAmount($value)
 * @method static Builder|Invoice whereInvoicePromotionPercentage($value)
 * @method static Builder|Invoice whereInvoiceStatusId($value)
 * @method static Builder|Invoice whereInvoiceSubtypeId($value)
 * @method static Builder|Invoice whereMarketplaceId($value)
 * @method static Builder|Invoice whereMessages($value)
 * @method static Builder|Invoice whereOrderDate($value)
 * @method static Builder|Invoice whereOrderNumber($value)
 * @method static Builder|Invoice whereParsingInvoiceNumber($value)
 * @method static Builder|Invoice wherePaymentTypeId($value)
 * @method static Builder|Invoice whereReportingDate($value)
 * @method static Builder|Invoice whereResource($value)
 * @method static Builder|Invoice whereResourceId($value)
 * @method static Builder|Invoice whereSalesCurrencyId($value)
 * @method static Builder|Invoice whereServicePeriodFrom($value)
 * @method static Builder|Invoice whereServicePeriodTo($value)
 * @method static Builder|Invoice whereShipFromAddress($value)
 * @method static Builder|Invoice whereShipFromCountryId($value)
 * @method static Builder|Invoice whereShipFromName($value)
 * @method static Builder|Invoice whereShipFromPostalCode($value)
 * @method static Builder|Invoice whereShipFromTaxNumber($value)
 * @method static Builder|Invoice whereShipFromVatNumber($value)
 * @method static Builder|Invoice whereShipFromVoesNumber($value)
 * @method static Builder|Invoice whereShipToAddress($value)
 * @method static Builder|Invoice whereShipToCountryId($value)
 * @method static Builder|Invoice whereShipToName($value)
 * @method static Builder|Invoice whereShipToPostalCode($value)
 * @method static Builder|Invoice whereShipToTaxNumber($value)
 * @method static Builder|Invoice whereShipToVatNumber($value)
 * @method static Builder|Invoice whereShipToVoesNumber($value)
 * @method static Builder|Invoice whereShippingIsSameAsBillFrom($value)
 * @method static Builder|Invoice whereShippingIsSameAsBillTo($value)
 * @method static Builder|Invoice whereShopifyOrderId($value)
 * @method static Builder|Invoice whereSupplierId($value)
 * @method static Builder|Invoice whereSupplierVoesNumber($value)
 * @method static Builder|Invoice whereTaxCalculationDate($value)
 * @method static Builder|Invoice whereTaxCollectionResponsibilityId($value)
 * @method static Builder|Invoice whereTaxReportingCountryId($value)
 * @method static Builder|Invoice whereTaxReportingSchemeId($value)
 * @method static Builder|Invoice whereTaxCalculationCountryId($value)
 * @method static Builder|Invoice whereItemTypeId($value)
 */
class Invoice extends Model
{
    use HasSourceTrait;

    public const INVOICE_PREFIX = [
        InvoiceType::SALES_INVOICE        => 'INV',
        InvoiceType::PURCHASE_INVOICE     => null,
        InvoiceType::PRO_FORMA_INVOICE    => 'PRO_INV',
        InvoiceType::CREDIT_NOTE          => 'CN',
        InvoiceType::DEBIT_NOTE           => null,
        InvoiceType::CUSTOMS_DECLARATION  => null,
        InvoiceType::DEEMED_SALES_INVOICE => null,
        InvoiceType::DEEMED_CREDIT_NOTE   => null,
    ];

    protected $table = 'invoices';

    protected $casts = [
        'data'     => 'array',
        'messages' => 'array',
    ];

    protected $appends = [
        'resource_human',
        'resource_icon'
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function eventType(): BelongsTo
    {
        return $this->belongsTo(EventType::class, 'event_type_id', 'id');
    }

    public function invoiceSubtype(): BelongsTo
    {
        return $this->belongsTo(InvoiceSubtype::class, 'invoice_subtype_id', 'id');
    }

    public function status(): BelongsTo
    {
        return $this->belongsTo(InvoiceStatus::class, 'invoice_status_id', 'id');
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id');
    }

    public function marketplace(): BelongsTo
    {
        return $this->belongsTo(Marketplace::class, 'marketplace_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function deliveryCondition(): BelongsTo
    {
        return $this->belongsTo(DeliveryCondition::class, 'delivery_condition_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function taxReportingScheme(): BelongsTo
    {
        return $this->belongsTo(TaxReportingScheme::class, 'tax_reporting_scheme_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function taxCollectionResponsibility(): BelongsTo
    {
        return $this->belongsTo(TaxCollectionResponsibility::class, 'tax_collection_responsibility_id', 'id');
    }

    public function invoiceItems(): HasMany
    {
        return $this->hasMany(InvoiceItem::class, 'invoice_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function invoiceItemsWithoutShippingAndWrap(): HasMany
    {
        return $this->hasMany(InvoiceItem::class, 'invoice_id', 'id')
            ->whereIn('invoice_item_type_id', [
                ItemType::GOODS,
                ItemType::SERVICES,
            ]);
    }

    /** @noinspection PhpUnused */
    public function taxReportingCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'tax_reporting_country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function taxCalculationCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'tax_calculation_country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function shipToCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'ship_to_country_id', 'id');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'sales_currency_id', 'id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function customerType(): HasOne
    {
        return $this->hasOne(CustomerType::class, 'id', 'customer_type_id');
    }

    /** @noinspection PhpUnused */
    public function billToCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'bill_to_country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function billFromCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'bill_from_country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function shipFromCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'ship_from_country_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function exchangeRates(): HasMany
    {
        return $this->hasMany(ExchangeRate::class, 'date', 'invoice_date');
    }

    /** @noinspection PhpUnused */
    public function getTotalNetAttribute(): float
    {
        return (float)$this->invoiceItems
            ->sum(function (InvoiceItem $invoiceItem) {
                return $invoiceItem->getNetTotalDiscountApplied();
            });
    }

    /** @noinspection PhpUnused */
    public function getTotalVatAttribute(): float
    {
        return (float)$this->invoiceItems
            ->sum(function (InvoiceItem $invoiceItem) {
                return $invoiceItem->getVatAmountTotalDiscountApplied();
            });
    }

    /** @noinspection PhpUnused */
    public function getTotalAttribute(): float
    {
        return (float)$this->invoiceItems
            ->sum(function (InvoiceItem $invoiceItem) {
                return $invoiceItem->getGrossTotalDiscountApplied();
            });
    }

    /** @noinspection PhpUnused */
    public function getTotalWithoutShippingAndWrapAttribute(): float
    {
        return (float)$this->invoiceItems
            ->filter(function (InvoiceItem $invoiceItem) {
                return in_array($invoiceItem->invoice_item_type_id, [ItemType::GOODS, ItemType::SERVICES]);
            })
            ->sum(function (InvoiceItem $invoiceItem) {
                return $invoiceItem->getGrossTotalDiscountApplied();
            });
    }

    /** @noinspection PhpUnused */
    public function getOssIossTypeAttribute(): ?string
    {
        $invoiceItem = $this->invoiceItems->filter(function (InvoiceItem $invoiceItem) {
            return in_array($invoiceItem->invoice_item_type_id, [ItemType::GOODS, ItemType::SERVICES]);
        })->first();

        if (is_null($invoiceItem)) {
            return null;
        }
        $invoiceGoodType = $invoiceItem->invoice_item_type_id;
        $invoicesTypes = [
            InvoiceSubtype::EU_B2C_SALES_INVOICE    => 'oss',
            InvoiceSubtype::CN_EU_B2C_SALES_INVOICE => 'oss',
            InvoiceSubtype::IOSS_EXPORT_INVOICE     => 'ioss',
            InvoiceSubtype::CN_IOSS_EXPORT_INVOICE  => 'ioss'
        ];

        if ($invoiceGoodType === ItemType::SERVICES) {
            $invoicesTypes = [
                InvoiceSubtype::EU_B2C_SALES_INVOICE    => 'voes',
                InvoiceSubtype::CN_EU_B2C_SALES_INVOICE => 'voes'
            ];
        }
        $type = $invoicesTypes[$this->invoice_subtype_id] ?? null;
        if (is_null($type)) {
            return null;
        }
        $company = $this->company;
        $date = Carbon::parse($this->tax_calculation_date);

        $ossNumber = $company->active_oss_number;
        $voesNumber = $company->active_voes_number;
        $iossNumber = $company->active_ioss_number;

        /**
         * @var OssNumber|IossNumber|null $ossIossNumber
         */
        $ossIossNumber = match ($type) {
            'oss' => $ossNumber,
            'ioss' => $iossNumber,
            'voes' => $voesNumber,
            default => null
        };

        if (is_null($ossIossNumber)) {
            return null;
        }

        if ($date->lt(Carbon::parse($ossIossNumber->register_date))) {
            return null;
        }

        return $type;
    }

    // Ovo treba obrisati
    public static function boot(): void
    {
        parent::boot();

        static::creating(function ($invoice) {
            /** @noinspection PhpDynamicAsStaticMethodCallInspection */
            $invoice->invoice_number_sequence = Invoice::where('company_id', $invoice->company_id)->max('invoice_number_sequence') + 1;
        });
    }

    /** @noinspection PhpUnused */
    public function getInvoiceNumberAttribute(): ?string
    {
        $invoiceNumber = $this->getRawOriginal('invoice_number');
        if (!is_null($invoiceNumber)) {
            return $invoiceNumber;
        }

        $invoiceNumber = $this->getAttributes()['invoice_number'] ?? null;
        if (!is_null($invoiceNumber)) {
            return $invoiceNumber;
        }

        $invoiceTypeId = $this->invoiceSubtype?->invoice_type_id;

        $data = self::generateInvoiceNumberAndSequence($invoiceTypeId, $this->company_id, now()->year);
        $number = $data->getNumber();
        $sequenceNumber = $data->getSequenceNumber();

        if (!is_null($this->id) && !is_null($number) && !is_null($sequenceNumber)) {
            $this->setAttribute('invoice_number', $number);
            $this->invoice_number_sequence = $sequenceNumber;
            $this->save();
        }

        return $number;
    }

    public static function generateInvoiceNumberAndSequence(int $invoiceTypeId, int $companyId, int $year): object
    {
        $prefix = self::INVOICE_PREFIX[$invoiceTypeId];
        $number = null;
        $sequenceNumber = null;
        if (!is_null($prefix)) {
            /** @noinspection PhpDynamicAsStaticMethodCallInspection */
            $sequenceNumber = Invoice::where('company_id', $companyId)->max('invoice_number_sequence');
            $sequenceNumber = (int)$sequenceNumber + 1;
            $prefix = $prefix . '-';
            $number = $prefix . $year . '-' . str_pad($sequenceNumber, 6, '0', STR_PAD_LEFT);
        }

        return new class($number, $sequenceNumber) {
            private ?string $number;
            private ?int $sequenceNumber;

            public function __construct(?string $number, ?int $sequenceNumber)
            {
                $this->number = $number;
                $this->sequenceNumber = $sequenceNumber;
            }

            public function getNumber(): ?string
            {
                return $this->number;
            }

            public function getSequenceNumber(): ?int
            {
                return $this->sequenceNumber;
            }
        };
    }

    /** @noinspection PhpUnused */
    public function getResourceHumanAttribute(): string
    {
        $resource = $this->resource ?? 'evat';
        $resource = mb_strtolower($resource);
        $resource = str_replace('\\', '-', $resource);

        return _l('invoices.source-' . $resource);
    }

    /** @noinspection PhpUnused */
    public function getResourceIconAttribute(): string
    {
        $resource = $this->resource ?? 'evat';
        $icons = [
            'App\Core\Data\Models\AmazonReport' => 'fa-brands fa-amazon',
            'evat'                              => 'far fa-circle-e'
        ];

        return $icons[$resource];
    }

    public function supplier(): HasOne
    {
        return $this->hasOne(Supplier::class, 'id', 'supplier_id');
    }

    /** @noinspection PhpUnused */
    public function paymentType(): BelongsTo
    {
        return $this->belongsTo(PaymentType::class, 'payment_type_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function connectedDeemedIssuedStatusInvoice(): HasOne
    {
        return $this->hasOne(Invoice::class, 'id', 'connected_invoice_id');
    }

    /** @noinspection PhpUnused */
    public function connectedDeemedSuppliesStatusInvoice(): HasOne
    {
        return $this->hasOne(Invoice::class, 'connected_invoice_id', 'id')->where('connected_invoice_type', 'DEEMED');
    }

    /** @noinspection PhpUnused */
    public function itemType(): BelongsTo
    {
        return $this->belongsTo(ItemType::class, 'item_type_id', 'id');
    }
}
