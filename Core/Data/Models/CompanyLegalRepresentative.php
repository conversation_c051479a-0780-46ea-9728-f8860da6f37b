<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\CompanyLegalRepresentative
 *
 * @property int $id
 * @property int $company_id
 * @property int $person_id
 * @property bool $default
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Person $person
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative whereDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyLegalRepresentative wherePersonId($value)
 * @mixin \Eloquent
 */
class CompanyLegalRepresentative extends Model
{
    protected $table = 'company_legal_representatives';

    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'person_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
