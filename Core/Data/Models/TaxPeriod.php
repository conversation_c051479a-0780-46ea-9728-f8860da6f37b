<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Builder;
use stdClass;

/**
 * App\Core\Data\Models\TaxPeriod
 *
 * @property int $id
 * @property int $value
 * @property int $tax_period_type_id
 * @property string $lang_key
 * @property mixed $ustva_value
 * @property \App\Core\Data\Models\TaxPeriodType|null $type
 * @method static Builder|TaxPeriod newModelQuery()
 * @method static Builder|TaxPeriod newQuery()
 * @method static Builder|TaxPeriod query()
 * @method static Builder|TaxPeriod whereId($value)
 * @method static Builder|TaxPeriod whereLangKey($value)
 * @method static Builder|TaxPeriod whereTaxPeriodTypeId($value)
 * @method static Builder|TaxPeriod whereValue($value)
 * @mixin \Eloquent
 */
class TaxPeriod extends Model
{
    protected $table = 'tax_periods';

    const M1 = 1;
    const M2 = 2;
    const M3 = 3;
    const M4 = 4;
    const M5 = 5;
    const M6 = 6;
    const M7 = 7;
    const M8 = 8;
    const M9 = 9;
    const M10 = 10;
    const M11 = 11;
    const M12 = 12;
    const Q1 = 13;
    const Q2 = 14;
    const Q3 = 15;
    const Q4 = 16;
    const A = 17;

    public function type()
    {
        return $this->hasOne(TaxPeriodType::class, 'id', 'tax_period_type_id');
    }

    public function getUstvaValueAttribute()
    {
        if (in_array($this->id, range(1, 12))) {
            return sprintf('%02d', $this->value);
        }
        switch ($this->id) {
            case 13:
                return '41';
                break;
            case 14:
                return '42';
                break;
            case 15:
                return '43';
                break;
            case 16:
                return '44';
                break;
        }

        return null;
    }

    public function calculatePeriodDatesForYear(string $year)
    {
        return self::getPeriodDatesForYear($this, $year);
    }

    public static function getPeriodDatesForYearByTaxPeriodId(int $taxPeriodId, ?string $year = null)
    {
        $taxPeriod = TaxPeriod::find($taxPeriodId);

        return self::getPeriodDatesForYear($taxPeriod, $year);
    }

    public static function getPeriodDatesForYear(TaxPeriod $taxPeriod, ?string $year = null)
    {
        if (is_null($year)) {
            $year = Carbon::now()->year;
        }
        $startMonth = 1;
        $endMonth = 12;
        $value = $taxPeriod->value;
        if ($taxPeriod->id < 13) {
            $startMonth = $value;
            $endMonth = $value;
        }
        if ($taxPeriod->id > 12 && $taxPeriod->id < 17) {
            $value = $value * 3;
            $startMonth = $value - 2;
            $endMonth = $value;
        }
        $startMonth = $year . '-' . $startMonth . '-01';
        $endMonth = $year . '-' . $endMonth . '-01';
        $startDate = Carbon::parse($startMonth)->startOfMonth();
        $endDate = Carbon::parse($endMonth)->endOfMonth();

        $dates = new stdClass();
        $dates->start = $startDate;
        $dates->end = $endDate;

        return $dates;
    }

    public static function getTaxPeriodByDates(Carbon|CarbonImmutable $startDate, Carbon|CarbonImmutable $endDate): ?TaxPeriod
    {
        $endDateCheck = $endDate->clone();
        $startDate = $startDate->startOfMonth();
        $endDate = $endDate->endOfMonth();
        if ($startDate->day !== 1 || $endDate->day !== $endDateCheck->day) {
            return null;
        }

        $taxPeriod = null;
        if ($startDate->month === 1 && $endDate->month === 12) {
            $taxPeriod = self::A;
        } elseif ($startDate->month === $endDate->month) {
            $taxPeriod = constant('self::M' . $startDate->month);
        } elseif ($endDate->month === ($startDate->month + 2)) {
            $taxPeriod = $endDate->month / 3;
            $taxPeriod = constant('self::Q' . $taxPeriod);
        }

        if (is_null($taxPeriod)) {
            return null;
        }

        return self::find($taxPeriod);
    }

    public static function getTaxPeriodByDatesOrCustom(CarbonImmutable $taxPeriodStartDate, CarbonImmutable $taxPeriodEndDate): TaxPeriod|array
    {
        $customDates = [
            'from' => $taxPeriodStartDate->format(_l_date_format('date')),
            'to'   => $taxPeriodEndDate->format(_l_date_format('date'))
        ];
        $taxPeriod = self::getTaxPeriodByDates($taxPeriodStartDate, $taxPeriodEndDate);
        if (is_null($taxPeriod)) {
            return $customDates;
        }

        return $taxPeriod;
    }

    public function getElsterTaxPeriodCode()
    {
        return self::getElsterTaxPeriodCodeById($this->id);
    }

    public static function getElsterTaxPeriodCodeById(int $taxPeriodId, string $version = 'USTvA'): string
    {
        $taxPeriod = Carbon::now()->month - 1;
        $taxPeriod = str_pad($taxPeriod, 2, '0', STR_PAD_LEFT);

        if ($version == 'USTvA') {
            //mjesecni 01-12
            //kvartalni 41,42,43,44
            $periods = [
                TaxPeriod::M1  => '01',
                TaxPeriod::M2  => '02',
                TaxPeriod::M3  => '03',
                TaxPeriod::M4  => '04',
                TaxPeriod::M5  => '05',
                TaxPeriod::M6  => '06',
                TaxPeriod::M7  => '07',
                TaxPeriod::M8  => '08',
                TaxPeriod::M9  => '09',
                TaxPeriod::M10 => '10',
                TaxPeriod::M11 => '11',
                TaxPeriod::M12 => '12',
                TaxPeriod::Q1  => '41',
                TaxPeriod::Q2  => '42',
                TaxPeriod::Q3  => '43',
                TaxPeriod::Q4  => '44',
            ];
        } elseif ($version == 'ZM') {
            $periods = [
                TaxPeriod::M1  => '21',
                TaxPeriod::M2  => '22',
                TaxPeriod::M3  => '23',
                TaxPeriod::M4  => '24',
                TaxPeriod::M5  => '25',
                TaxPeriod::M6  => '26',
                TaxPeriod::M7  => '27',
                TaxPeriod::M8  => '28',
                TaxPeriod::M9  => '29',
                TaxPeriod::M10 => '30',
                TaxPeriod::M11 => '31',
                TaxPeriod::M12 => '32',
                TaxPeriod::Q1  => '1',
                TaxPeriod::Q2  => '2',
                TaxPeriod::Q3  => '3',
                TaxPeriod::Q4  => '4',
                TaxPeriod::A   => '5',
            ];
        }

        if (array_key_exists($taxPeriodId, $periods)) {
            $taxPeriod = $periods[$taxPeriodId];
        }

        return $taxPeriod;
    }

    public static function isElsterTaxPeriodMonthly(int $taxPeriodId, string $version = 'ZM'): string
    {
        if ($version == 'ZM') {
            $periods = [
                TaxPeriod::M1  => '21',
                TaxPeriod::M2  => '22',
                TaxPeriod::M3  => '23',
                TaxPeriod::M4  => '24',
                TaxPeriod::M5  => '25',
                TaxPeriod::M6  => '26',
                TaxPeriod::M7  => '27',
                TaxPeriod::M8  => '28',
                TaxPeriod::M9  => '29',
                TaxPeriod::M10 => '30',
                TaxPeriod::M11 => '31',
                TaxPeriod::M12 => '32',
            ];
        }

        if (array_key_exists($taxPeriodId, $periods)) {
            return true;
        }

        return false;
    }

    public static function allConstantsPeriodsIds(): array
    {
        return [
            'M1'  => self::M1,
            'M2'  => self::M2,
            'M3'  => self::M3,
            'M4'  => self::M4,
            'M5'  => self::M5,
            'M6'  => self::M6,
            'M7'  => self::M7,
            'M8'  => self::M8,
            'M9'  => self::M9,
            'M10' => self::M10,
            'M11' => self::M11,
            'M12' => self::M12,
            'Q1'  => self::Q1,
            'Q2'  => self::Q2,
            'Q3'  => self::Q3,
            'Q4'  => self::Q4,
            'A'   => self::A,
        ];
    }

    public static function monthPeriodsIds(): array
    {
        return [
            'M1'  => self::M1,
            'M2'  => self::M2,
            'M3'  => self::M3,
            'M4'  => self::M4,
            'M5'  => self::M5,
            'M6'  => self::M6,
            'M7'  => self::M7,
            'M8'  => self::M8,
            'M9'  => self::M9,
            'M10' => self::M10,
            'M11' => self::M11,
            'M12' => self::M12
        ];
    }

    public static function monthPeriodsShortNames(int $forMonth = 0): array
    {
        $result = [];

        if ($forMonth === 0) {
            for ($i = 0; $i < 12; $i++) {
                $month = $i + 1;

                if ($month < 10) {
                    $month = '0' . $month;
                }

                $date = '2000-' . $month . '-15';
                $date = Carbon::parse($date)->isoFormat('MMM');

                $result[$i + 1] = $date;
            }
        } elseif ($forMonth === 13) {
            $result[$forMonth] = _l('period.Q1');
        } elseif ($forMonth === 14) {
            $result[$forMonth] = _l('period.Q2');
        } elseif ($forMonth === 15) {
            $result[$forMonth] = _l('period.Q3');
        } elseif ($forMonth === 16) {
            $result[$forMonth] = _l('period.Q4');
        } elseif ($forMonth === 17) {
            $result[$forMonth] = _l('period.Annual');
        } else {
            $saveMonth = $forMonth;

            if ($forMonth < 10) {
                $forMonth = '0' . $forMonth;
            }

            $date = '2000-' . $forMonth . '-15';
            $date = Carbon::parse($date)->isoFormat('MMM');

            $result[$saveMonth] = $date;
        }

        return $result;
    }
}
