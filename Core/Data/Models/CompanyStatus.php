<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\CompanyStatus
 *
 * @property int $id
 * @property string $status
 * @property string $name
 * @property string $description
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Company> $companies
 * @property int|null $companies_count
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyStatus whereStatus($value)
 * @mixin \Eloquent
 */
class CompanyStatus extends Model
{
    const ACTIVE = 4;
    const CANCELLED = 5;
    const DEACTIVATED = 6;

    protected $table = 'company_statuses';

    public function companies()
    {
        return $this->hasMany(Company::class, 'status_id', 'id');
    }
}
