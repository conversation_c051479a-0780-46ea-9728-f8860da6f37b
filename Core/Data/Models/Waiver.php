<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\Waiver
 *
 * @property int $id
 * @property int $company_id
 * @property int $country_id
 * @property string $start_date
 * @property string|null $end_date
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country $country
 * @method static Builder|Waiver newModelQuery()
 * @method static Builder|Waiver newQuery()
 * @method static Builder|Waiver query()
 * @method static Builder|Waiver whereCompanyId($value)
 * @method static Builder|Waiver whereCountryId($value)
 * @method static Builder|Waiver whereEndDate($value)
 * @method static Builder|Waiver whereId($value)
 * @method static Builder|Waiver whereStartDate($value)
 * @mixin \Eloquent
 */
class Waiver extends Model
{
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }
}
