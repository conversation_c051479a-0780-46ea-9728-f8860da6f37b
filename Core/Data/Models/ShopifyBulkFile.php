<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\Traits\HasFileTrait;
use App\Core\Data\Models\Traits\HasHistoryTrait;
use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * App\Core\Data\Models\ShopifyBulkFile
 *
 * @property int $id
 * @property int $ecommerce_account_id
 * @property int $bulk_file_type_id
 * @property string $bulk_operation_id
 * @property bool $operation_completed
 * @property string|null $download_url
 * @property string|null $downloaded_at
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $file_parsing_started_at
 * @property string|null $file_parsing_ended_at
 * @property string|null $raw_data_parsing_started_at
 * @property string|null $raw_data_parsing_ended_at
 * @property int|null $user_id
 * @property string|null $error
 * @property string $created_at
 * @property int $status_id
 * @property \App\Core\Data\Models\File|null $file
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\History> $history
 * @property int|null $history_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ShopifyOrderItemRawData> $orderItemsRawData
 * @property int|null $order_items_raw_data_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ShopifyOrderRawData> $ordersRawData
 * @property int|null $orders_raw_data_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ShopifyProductVariantRawData> $productVariantsRawData
 * @property int|null $product_variants_raw_data_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\ShopifyProductRawData> $productsRawData
 * @property int|null $products_raw_data_count
 * @property \App\Core\Data\Models\SalesChannel $salesChannel
 * @property \App\Core\Data\Models\ShopifyBulkFileStatus|null $status
 * @property \App\Core\Data\Models\User|null $user
 * @method static Builder|ShopifyBulkFile newModelQuery()
 * @method static Builder|ShopifyBulkFile newQuery()
 * @method static Builder|ShopifyBulkFile query()
 * @method static Builder|ShopifyBulkFile whereBulkFileTypeId($value)
 * @method static Builder|ShopifyBulkFile whereBulkOperationId($value)
 * @method static Builder|ShopifyBulkFile whereCreatedAt($value)
 * @method static Builder|ShopifyBulkFile whereDownloadUrl($value)
 * @method static Builder|ShopifyBulkFile whereDownloadedAt($value)
 * @method static Builder|ShopifyBulkFile whereEcommerceAccountId($value)
 * @method static Builder|ShopifyBulkFile whereEndDate($value)
 * @method static Builder|ShopifyBulkFile whereError($value)
 * @method static Builder|ShopifyBulkFile whereFileParsingEndedAt($value)
 * @method static Builder|ShopifyBulkFile whereFileParsingStartedAt($value)
 * @method static Builder|ShopifyBulkFile whereId($value)
 * @method static Builder|ShopifyBulkFile whereOperationCompleted($value)
 * @method static Builder|ShopifyBulkFile whereRawDataParsingEndedAt($value)
 * @method static Builder|ShopifyBulkFile whereRawDataParsingStartedAt($value)
 * @method static Builder|ShopifyBulkFile whereStartDate($value)
 * @method static Builder|ShopifyBulkFile whereStatusId($value)
 * @method static Builder|ShopifyBulkFile whereUserId($value)
 * @mixin \Eloquent
 */
class ShopifyBulkFile extends Model implements UploadableContract
{
    protected $table = 'shopify_bulk_files';

    use HasFileTrait, HasHistoryTrait;


    /**
     * @inheritDoc
     */
    public function getUploadPath(): string
    {
        $dir = (int)ceil($this->getUploadResourceId() / 1000);

        if ($this->bulk_file_type_id === ShopifyBulkFileType::PRODUCTS) {
            return upload_path('shopify/bulk_product_files/' . $dir . '/' . $this->getUploadResourceId()) . '/bulk_product_file/';
        }

        return upload_path('shopify/bulk_order_files/' . $dir . '/' . $this->getUploadResourceId()) . '/bulk_order_file/';
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceId(): int
    {
        return $this->id;
    }

    /**
     * @inheritDoc
     */
    public function getUploadResourceName(): string
    {
        return __CLASS__;
    }

    public function ordersRawData()
    {
        return $this->hasMany(ShopifyOrderRawData::class, 'shopify_bulk_files_id', 'id');
    }

    public function orderItemsRawData()
    {
        return $this->hasMany(ShopifyOrderItemRawData::class, 'shopify_bulk_files_id', 'id');
    }

    public function salesChannel()
    {
        return $this->belongsTo(SalesChannel::class, 'ecommerce_account_id', 'id');
    }

    public function productsRawData()
    {
        return $this->hasMany(ShopifyProductRawData::class, 'shopify_bulk_file_id', 'id');
    }

    public function productVariantsRawData()
    {
        return $this->hasMany(ShopifyProductVariantRawData::class, 'shopify_bulk_file_id', 'id');
    }

    public function status()
    {
        return $this->hasOne(ShopifyBulkFileStatus::class, 'id', 'status_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
