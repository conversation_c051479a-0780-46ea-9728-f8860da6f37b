<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Core\Data\Models\CompanyTaxPeriodType
 *
 * @property int $id
 * @property int $company_id
 * @property string $start_date
 * @property int $tax_period_type_id
 * @property int $country_id
 * @property \App\Core\Data\Models\Company $company
 * @property \App\Core\Data\Models\Country $country
 * @property mixed $period_type
 * @property \App\Core\Data\Models\TaxPeriodType $taxPeriodType
 * @method static Builder|CompanyTaxPeriodType newModelQuery()
 * @method static Builder|CompanyTaxPeriodType newQuery()
 * @method static Builder|CompanyTaxPeriodType query()
 * @method static Builder|CompanyTaxPeriodType whereCompanyId($value)
 * @method static Builder|CompanyTaxPeriodType whereCountryId($value)
 * @method static Builder|CompanyTaxPeriodType whereId($value)
 * @method static Builder|CompanyTaxPeriodType whereStartDate($value)
 * @method static Builder|CompanyTaxPeriodType whereTaxPeriodTypeId($value)
 * @mixin \Eloquent
 */
class CompanyTaxPeriodType extends Model
{
    protected $table = 'company_tax_period_types';

    /** @noinspection PhpUnused */
    public function taxPeriodType(): BelongsTo
    {
        return $this->belongsTo(TaxPeriodType::class, 'tax_period_type_id', 'id');
    }

    /** @noinspection PhpUnused */
    public function getPeriodTypeAttribute(): string
    {
        return _l($this->taxPeriodType->name);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
