<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\ShopifyMandatoryWebhookRequest
 *
 * @property int $id
 * @property int $type_id
 * @property array $webhook_payload
 * @property string $request_received_at
 * @property string|null $request_completed_at
 * @property int|null $completed_by_user_id
 * @property \App\Core\Data\Models\ShopifyMandatoryWebhookRequestType|null $type
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest whereCompletedByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest whereRequestCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest whereRequestReceivedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest whereTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ShopifyMandatoryWebhookRequest whereWebhookPayload($value)
 * @mixin \Eloquent
 */
class ShopifyMandatoryWebhookRequest extends Model
{
    protected $table = 'shopify_mandatory_webhook_requests';

    const HAS_UNAPPROVED_REQUESTS_CACHE_KEY = 'hasUnapprovedRequests';

    protected $casts = [
        'webhook_payload' => 'array'
    ];

    public function type()
    {
        return $this->hasOne(ShopifyMandatoryWebhookRequestType::class, 'id', 'type_id');
    }
}
