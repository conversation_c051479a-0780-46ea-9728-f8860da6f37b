<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\Month
 *
 * @property int $id
 * @property int $value
 * @property string $name
 * @method static \Illuminate\Database\Eloquent\Builder|Month newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Month newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Month query()
 * @method static \Illuminate\Database\Eloquent\Builder|Month whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Month whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Month whereValue($value)
 * @mixin \Eloquent
 */
class Month extends Model
{
    protected $table = 'months';
    public $timestamps = false;
}
