<?php

namespace App\Core\Data\Models\Traits;

use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\File;
use App\Core\File\Contracts\FileServiceContract;
use Throwable;

trait HasFilesTrait
{
    public function files()
    {
        return $this->morphMany(File::class, 'files', 'resource', 'resource_id', 'id');
    }

    protected static function boot()
    {
        parent::boot();
        /**
         * @var FileServiceContract $fileService
         */
        $fileService = app()->make(FileServiceContract::class);
        static::deleting(function (UploadableContract $model) use ($fileService) {
            /**
             * @var \Illuminate\Support\Collection
             */
            $files = $model->files ?? collect();
            foreach ($files as $file) {
                if (!is_null($file)) {
                    /**
                     * @var File $file
                     */
                    try {
                        $fileService->deleteFileFromFilesystem($file->file_path);
                        $file->delete();
                    } catch (Throwable) {
                    }
                }
            }
        });
    }
}
