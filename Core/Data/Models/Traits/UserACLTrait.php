<?php

namespace App\Core\Data\Models\Traits;

use App\Core\Data\Models\Role;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

trait UserACLTrait
{
    /**
     * @inheritDoc
     */
    public function isDeveloper(): bool
    {
        return $this->is_developer ?? false;
    }

    /**
     * @inheritDoc
     */
    public function getAllInstitutionInstitutionTypeUserRoles(): Collection
    {
        return sd()->getAllInstitutionsRoles();
    }

    /**
     * @inheritDoc
     */
    public function isClientType(): bool
    {
        return $this->is_client_type;
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderType(): bool
    {
        return sd()->isServiceProvider();
    }

    /**
     * @inheritDoc
     */
    public function isCrmType(): bool
    {
        return sd()->isCrm();
    }

    /**
     * @inheritDoc
     */
    public function isCrmOrServiceProviderType(): bool
    {
        return $this->getAllInstitutionInstitutionTypeUserRoles()->count() > 0;
    }

    /**
     * @inheritDoc
     */
    public function getCurrentAvailableRoles(): Collection
    {
        if ($this->isAdministrative()) {
            return collect();
        }

        if ($this->isCrmOrServiceProviderType()) {
            $roles = sd()->getCurrentInstitutionRoles()
                ->pluck('role')
                ->keyBy('id');
        } else {
            $roles = sd()->getCurrentClientCompanyRoles()
                ->pluck('role')
                ->keyBy('id');
        }

        return $roles;
    }

    /**
     * @inheritDoc
     */
    public function hasRole(int $roleId): bool
    {
        return $this->getCurrentAvailableRoles()->has($roleId);
    }

    /**
     * @inheritDoc
     */
    public function isAdministrative(): bool
    {
        return $this->is_admin || $this->isDeveloper();
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderAdmin(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_ADMIN);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderTeamLeader(): bool
    {
        return $this->hasRole(Role::ROLE_ACCOUNTANT_REGISTRATION_TEAM_LEADER) || $this->hasRole(Role::ROLE_ACCOUNTANT_COMPLIANCE_TEAM_LEADER) || $this->hasRole(Role::ROLE_ACCOUNTANT_CUSTOMER_SUPPORT_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderTeamMember(): bool
    {
        return $this->hasRole(Role::ROLE_ACCOUNTANT_REGISTRATION_TEAM_MEMBER) || $this->hasRole(Role::ROLE_ACCOUNTANT_COMPLIANCE_TEAM_MEMBER) || $this->hasRole(Role::ROLE_ACCOUNTANT_CUSTOMER_SUPPORT_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderRegistrationTeamLeader(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_REGISTRATION_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderRegistrationTeamMember(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_REGISTRATION_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderComplianceTeamLeader(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_COMPLIANCE_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderComplianceTeamMember(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_COMPLIANCE_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderCustomerSupportTeamLeader(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_CUSTOMER_SUPPORT_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isServiceProviderCustomerSupportTeamMember(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_ACCOUNTANT_CUSTOMER_SUPPORT_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmAdmin(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_ADMIN);
    }

    /**
     * @inheritDoc
     */
    public function isCrmTeamLeader(): bool
    {
        return $this->hasRole(Role::ROLE_PARTNER_REGISTRATION_TEAM_LEADER) || $this->hasRole(Role::ROLE_PARTNER_COMPLIANCE_TEAM_LEADER) || $this->hasRole(Role::ROLE_PARTNER_CUSTOMER_SUPPORT_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmTeamMember(): bool
    {
        return $this->hasRole(Role::ROLE_PARTNER_REGISTRATION_TEAM_MEMBER) || $this->hasRole(Role::ROLE_PARTNER_COMPLIANCE_TEAM_MEMBER) || $this->hasRole(Role::ROLE_PARTNER_CUSTOMER_SUPPORT_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmRegistrationTeamLeader(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_REGISTRATION_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmRegistrationTeamMember(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_REGISTRATION_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isPartnerComplianceTeamLeader(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_COMPLIANCE_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmComplianceTeamMember(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_COMPLIANCE_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmCustomerSupportTeamLeader(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_CUSTOMER_SUPPORT_TEAM_LEADER);
    }

    /**
     * @inheritDoc
     */
    public function isCrmCustomerSupportTeamMember(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_PARTNER_CUSTOMER_SUPPORT_TEAM_MEMBER);
    }

    /**
     * @inheritDoc
     */
    public function isClientAdmin(): bool
    {
        return $this->isAdministrative() || $this->hasRole(Role::ROLE_CLIENT_ADMIN);
    }

    /**
     * @inheritDoc
     */
    public function dumpAllRoles(): void
    {
        $rolesDb = Role::all()->load('institutionType');
        $rolesT = [];
        foreach ($rolesDb as $role) {
            $type = explode('.', $role->institutionType->name)[1];
            $name = $role->name;
            $roleName = $name;
            $name = Str::camel($name);
            $name = ucfirst($name);
            $method = 'is' . $type . $name;
            $rolesT[$type][$roleName] = $method;
        }
        $roles = [];
        foreach ($rolesT as $type => $rolesM) {
            foreach ($rolesM as $key => $role) {
                $roles[$type][$key] = user()->{$role}();
            }
        }
        dd($roles);
    }
}
