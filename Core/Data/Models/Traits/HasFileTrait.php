<?php

namespace App\Core\Data\Models\Traits;

use App\Core\Data\Models\Contracts\UploadableContract;
use App\Core\Data\Models\File;
use App\Core\File\Contracts\FileServiceContract;
use Throwable;

trait HasFileTrait
{
    public function file()
    {
        return $this->morphOne(File::class, 'files', 'resource', 'resource_id', 'id');
    }

    protected static function boot()
    {
        parent::boot();
        /**
         * @var FileServiceContract $fileService
         */
        $fileService = app()->make(FileServiceContract::class);
        static::deleting(function (UploadableContract $model) use ($fileService) {
            /**
             * @var File $file
             */
            $file = $model->file;
            if (!is_null($file)) {
                try {
                    $fileService->deleteFileFromFilesystem($file->file_path);
                    $file->delete();
                } catch (Throwable ) {
                }
            }

            return true;
        });
    }
}
