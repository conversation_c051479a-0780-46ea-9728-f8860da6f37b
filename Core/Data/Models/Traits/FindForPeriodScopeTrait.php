<?php

namespace App\Core\Data\Models\Traits;

use App\Core\Data\Models\TaxPeriod;

trait FindForPeriodScopeTrait
{
    public function scopeWhereTaxPeriodId(
        $query,
        int $taxPeriodId,
        int $yearId,
        string $field = 'date',
        ?string $periodStart = null,
        ?string $periodEnd = null
    ) {
        /**
         * @var TaxPeriod $taxPeriod
         */
        $taxPeriod = TaxPeriod::find($taxPeriodId);
        $period = $taxPeriod->calculatePeriodDatesForYear($yearId);
        $startDate = $period->start->toDateString();
        $endDate = $period->end->toDateString();
        if (!is_null($periodStart)) {
            $startDate = $periodStart;
        }
        if (!is_null($periodEnd)) {
            $endDate = $periodEnd;
        }

        $query = $query->where(function ($q) use ($startDate, $endDate, $field) {
            return $q->where($field, '>=', $startDate)
                ->where($field, '<=', $endDate);
        });

        return $query;
    }
}
