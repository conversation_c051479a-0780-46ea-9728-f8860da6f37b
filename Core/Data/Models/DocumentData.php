<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Throwable;

/**
 * App\Core\Data\Models\DocumentData
 *
 * @property int $id
 * @property int $document_id
 * @property int $document_data_field_id
 * @property bool $editable
 * @property mixed|null $raw_value
 * @property \App\Core\Data\Models\DocumentDataField $field
 * @property mixed|null $data_value
 * @property mixed|null $print_value
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData whereDocumentDataFieldId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData whereDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData whereEditable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentData whereRawValue($value)
 * @mixin \Eloquent
 */
class DocumentData extends Model
{
    protected $table = 'document_data';

    protected $appends = [
        'data_value'
    ];

    public function field(): BelongsTo
    {
        return $this->belongsTo(DocumentDataField::class, 'document_data_field_id', 'id');
    }

    public function getDataValueAttribute(): mixed
    {
        $value = $this->raw_value;
        if (is_null($value)) {
            return null;
        }

        $dataType = $this->field->data_type;
        $value = json_decode($value, true);

        if ($dataType === DocumentDataField::DATA_TYPE_INT) {
            $value = (int)filter_var($value, FILTER_SANITIZE_NUMBER_INT);
        }

        if ($dataType === DocumentDataField::DATA_TYPE_FLOAT) {
            $value = (float)filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        }

        if ($dataType === DocumentDataField::DATA_TYPE_BOOL) {
            $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
        }

        if ($dataType === DocumentDataField::DATA_TYPE_DATE) {
            try {
                $value = Carbon::parse($value);
            } catch (Throwable) {
                $value = null;
            }
        }

        return $value;
    }

    public function getPrintValueAttribute(): mixed
    {
        $value = $this->data_value;
        $dataType = $this->field->data_type;
        $print = $value;

        $skipDataFields = [
            DocumentDataField::FIELD_DESCRIPTION
        ];
        if (is_null($value) || in_array($this->document_data_field_id, $skipDataFields)) {
            return null;
        }

        if ($dataType === DocumentDataField::DATA_TYPE_DATE) {
            try {
                $print = Carbon::parse($value)->format(_l_date_format('date'));
            } catch (Throwable) {
            }
        }

        if ($this->document_data_field_id === DocumentDataField::FIELD_COUNTRY) {
            $print = $value['name'] ?? null;
        }

        if ($this->document_data_field_id === DocumentDataField::FIELD_AMOUNT) {
            $code = $value['currency']['code'] ?? null;
            $amount = $value['value'] ?? null;
            if (!is_null($amount) && !is_null($code)) {
                $print = evat_money($amount, $code);
            }
        }

        return $print;
    }
}
