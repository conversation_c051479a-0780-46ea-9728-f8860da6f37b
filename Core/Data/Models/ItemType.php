<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Core\Data\Models\ItemType
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Item> $items
 * @property int|null $items_count
 * @method static Builder|ItemType newModelQuery()
 * @method static Builder|ItemType newQuery()
 * @method static Builder|ItemType query()
 * @method static Builder|ItemType whereId($value)
 * @method static Builder|ItemType whereName($value)
 * @mixin \Eloquent
 */
class ItemType extends Model
{
    protected $table = 'item_types';

    public const GOODS = 1;
    public const DIGITAL_GOODS = 2;
    public const SERVICES = 3;
    public const SHIPPING = 4;
    public const PACKAGING = 5;

    public function items(): HasMany
    {
        return $this->hasMany(Item::class, 'item_type_id', 'id');
    }
}
