<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * App\Core\Data\Models\Customer
 *
 * @property int $id
 * @property string|null $company_name
 * @property int $company_id
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $additional_data
 * @property bool $is_person
 * @property \App\Core\Data\Models\Company $company
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\Contact> $contacts
 * @property int|null $contacts_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\CustomerAddress> $customerAddresses
 * @property int|null $customer_addresses_count
 * @property mixed $name
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $identificationNumbers
 * @property int|null $identification_numbers_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $taxNumbers
 * @property int|null $tax_numbers_count
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Core\Data\Models\IdentificationNumber> $vatNumbers
 * @property int|null $vat_numbers_count
 * @property \App\Core\Data\Models\IdentificationNumber|null $voesNumber
 * @method static Builder|Customer newModelQuery()
 * @method static Builder|Customer newQuery()
 * @method static Builder|Customer query()
 * @method static Builder|Customer whereAdditionalData($value)
 * @method static Builder|Customer whereCompanyId($value)
 * @method static Builder|Customer whereCompanyName($value)
 * @method static Builder|Customer whereFirstName($value)
 * @method static Builder|Customer whereId($value)
 * @method static Builder|Customer whereIsPerson($value)
 * @method static Builder|Customer whereLastName($value)
 * @mixin \Eloquent
 */
class Customer extends Model
{
    protected $table = 'customers';

    protected $appends = ['full_name'];

    /** @noinspection PhpUnused */
    public function customerAddresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'customer_id', 'id');
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(Contact::class, 'customer_id', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function vatNumbers(): MorphMany
    {
        return $this->morphMany(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id')
            ->where('identification_number_type_id', IdentificationNumberType::VAT_NUMBER);
    }

    public function identificationNumbers(): MorphMany
    {
        return $this->morphMany(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id');
    }

    public function taxNumbers(): MorphMany
    {
        return $this->morphMany(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id')
            ->where('identification_number_type_id', IdentificationNumberType::TAX_NUMBER);
    }

    public function voesNumber(): MorphOne
    {
        return $this->morphOne(IdentificationNumber::class, 'identificationNumbers', 'resource', 'resource_id')
            ->where('identification_number_type_id', IdentificationNumberType::OSS_NUMBER);
    }

    public function getModalData(): string
    {
        return json_encode(['id' => $this->id]);
    }

    /** @noinspection PhpUnused */
    public function getNameAttribute(): ?string
    {
        if (is_null($this->first_name) && is_null($this->last_name) && is_null($this->company_name)) {
            return null;
        }

        if (!is_null($this->company_name)) {
            return $this->company_name;
        }

        return $this->first_name . ' ' . $this->last_name;
    }

    protected static function boot(): void
    {
        parent::boot();

        static::deleting(function (Customer $customer) {
            $customer->identificationNumbers()->delete();
        });
    }
}
