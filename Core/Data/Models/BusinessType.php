<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;

/**
 * App\Core\Data\Models\BusinessType
 *
 * @property int $id
 * @property string $lang_key
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessType query()
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BusinessType whereLangKey($value)
 * @mixin \Eloquent
 */
class BusinessType extends Model
{
    const LIMITED = 1;
    const CORPORATION = 2;
    const SOLE_TRADER = 3;
    const FREELANCER = 4;
    const CHARITY = 5;
    const PARTNERSHIP = 6;
    const BRANCH = 7;

    protected $table = 'business_types';
}
