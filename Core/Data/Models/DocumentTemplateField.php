<?php

namespace App\Core\Data\Models;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\Contracts\TemplateFieldContract;

/**
 * App\Core\Data\Models\DocumentTemplateField
 *
 * @property int $id
 * @property int $document_template_id
 * @property string|null $context_key
 * @property string $template_key
 * @property \App\Core\Data\Models\DocumentTemplate $documentTemplate
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField whereContextKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField whereDocumentTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentTemplateField whereTemplateKey($value)
 * @mixin \Eloquent
 */
class DocumentTemplateField extends Model implements TemplateFieldContract
{
    protected $table = 'document_template_fields';

    public function documentTemplate()
    {
        return $this->belongsTo(DocumentTemplate::class, 'document_template_id', 'id');
    }

    public function getContextKey(): ?string
    {
        return $this->context_key;
    }

    public function getTemplateKey(): string
    {
        return $this->template_key;
    }
}
