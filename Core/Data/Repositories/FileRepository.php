<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\File;
use App\Core\Data\Repositories\Contracts\FileRepositoryContract;

class FileRepository implements FileRepositoryContract
{
    /**
     * @var File
     */
    private $file;

    public function __construct(File $file)
    {
        $this->file = $file;
    }

    /**
     * @inheritDoc
     */
    public function getNewFileModel(): File
    {
        return $this->file->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteFileById(int $fileId): void
    {
        $this->file->where('id', $fileId)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getFileById(int $fileId): ?File
    {
        return $this->file->find($fileId);
    }
}