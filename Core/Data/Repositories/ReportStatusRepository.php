<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ReportStatus;
use App\Core\Data\Repositories\Contracts\ReportStatusRepositoryContract;
use Illuminate\Database\Eloquent\Collection;

class ReportStatusRepository implements ReportStatusRepositoryContract
{
    private ReportStatus $reportStatus;

    public function __construct(ReportStatus $reportStatus)
    {
        $this->reportStatus = $reportStatus;
    }

    public function getAllReportStatuses(): Collection
    {
        return $this->reportStatus->orderBy('id')->get();
    }
}
