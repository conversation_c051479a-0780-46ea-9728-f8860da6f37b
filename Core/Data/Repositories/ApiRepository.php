<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ApiConsumer;
use App\Core\Data\Models\CompanyApiConsumers;
use App\Core\Data\Repositories\Contracts\ApiRepositoryContract;

class ApiRepository implements ApiRepositoryContract
{
    private ApiConsumer $apiConsumer;
    private CompanyApiConsumers $companyApiConsumers;

    public function __construct(ApiConsumer $apiConsumer, CompanyApiConsumers $companyApiConsumers)
    {
        $this->apiConsumer = $apiConsumer;
        $this->companyApiConsumers = $companyApiConsumers;
    }

    public function getApiConsumerByKey(string $applicationKey): ?ApiConsumer
    {
        return $this->apiConsumer->where('key', $applicationKey)->first();
    }

    public function getEmptyCompanyApiConsumerModel(): CompanyApiConsumers
    {
        return $this->companyApiConsumers->newInstance();
    }
}
