<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Platform;
use App\Core\Data\Repositories\Contracts\PlatformRepositoryContract;
use Illuminate\Support\Collection;

class PlatformRepository implements PlatformRepositoryContract
{

    private Platform $platform;

    /**
     * @param Platform $platform
     */
    public function __construct(Platform $platform)
    {
        $this->platform = $platform;
    }

    /**
     * @inheritDoc
     */
    public function getPlatformById(int $platformId): ?Platform
    {
        return $this->platform->find($platformId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyPlatformModel(): Platform
    {
        return $this->platform->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getAllPlatforms(): Collection
    {
        return $this->platform->all();
    }
}