<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\PaymentType;
use App\Core\Data\Repositories\Contracts\PaymentTypeRepositoryContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class PaymentTypeRepository implements PaymentTypeRepositoryContract
{
    private PaymentType $paymentType;

    public function __construct(PaymentType $paymentType)
    {
        $this->paymentType = $paymentType;
    }

    public function getAllPaymentTypes(): EloquentCollection
    {
        return $this->paymentType
            ->orderBy('payment_type_status_id', 'desc')
            ->orderBy('name')
            ->get();
    }
}
