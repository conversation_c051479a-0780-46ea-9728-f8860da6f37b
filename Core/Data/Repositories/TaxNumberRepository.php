<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\TaxNumber;
use App\Core\Data\Repositories\Contracts\TaxNumberRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TaxNumberRepository implements TaxNumberRepositoryContract
{
    /**
     * @var TaxNumber
     */
    private $taxNumber;

    public function __construct(TaxNumber $taxNumber)
    {
        $this->taxNumber = $taxNumber;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(int $perPage = 40): LengthAwarePaginator
    {
        return $this->taxNumber->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getTaxNumberById(int $taxNumberId): ?TaxNumber
    {
        return $this->taxNumber->find($taxNumberId);
    }

    /**
     * @inheritDoc
     */
    public function deleteTaxNumberById(int $taxNumberId): void
    {
        $this->taxNumber->destroy($taxNumberId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyTaxNumberModel(): TaxNumber
    {
        return $this->taxNumber->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteTaxNumbersByIds(array $ids): void
    {
        if (count($ids) < 1) {
            return;
        }

        $this->taxNumber->whereIn('id', $ids)->delete();
    }

    public function getTaxNumbersByIds(array $taxNumberIds): Collection
    {
        return $this->taxNumber->whereIn('id', $taxNumberIds)->get();
    }

    public function getTaxNumberByTaxNumber(string $number): ?TaxNumber
    {
        return $this->taxNumber->where('number', $number)->first();
    }
}
