<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\GenderType;
use App\Core\Data\Repositories\Contracts\GenderTypeRepositoryContract;
use Illuminate\Support\Collection;


class GenderTypeRepository implements GenderTypeRepositoryContract
{
    /**
     * @var GenderType
     */
    private $genderType;

    public function __construct(GenderType $genderType)
    {
        $this->genderType = $genderType;
    }


    /**
     * @inheritDoc
     */
    public function getAllGenderTypes(): Collection
    {
        return $this->genderType->all();
    }
}