<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\TaxOffice;
use App\Core\Data\Models\CompanyTaxOffice;
use App\Core\Data\Repositories\Contracts\TaxOfficeRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class TaxOfficeRepository implements TaxOfficeRepositoryContract
{
    /**
     * @var TaxOffice
     */
    private $taxOffice;

    /**
     * @var CompanyTaxOffice
     */
    private $companyTaxOffice;

    public function __construct(TaxOffice $taxOffice, CompanyTaxOffice $companyTaxOffice)
    {
        $this->taxOffice = $taxOffice;
        $this->companyTaxOffice = $companyTaxOffice;
    }

    /**
     * @inheritDoc
     */
    public function getTaxOfficeById(int $taxOfficeId): ?TaxOffice
    {
        return $this->taxOffice->find($taxOfficeId);
    }

    /**
     * @param int $taxOfficeId
     */
    public function deleteTaxOfficeById(int $taxOfficeId): void
    {
        $this->taxOffice->destroy($taxOfficeId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyTaxOfficeModel(): TaxOffice
    {
        return $this->taxOffice->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getOfficesInCountryIds(): array
    {
        return $this->taxOffice
            ->groupBy('country_id')
            ->pluck('country_id')
            ->toArray();
    }

    /**
     * @inheritDoc
     */
    public function getOfficesForCountry(int $countryId): Collection
    {
        return $this->taxOffice->where('country_id', $countryId)->get();
    }

    /**
     * @inheritDoc
     */
    public function deleteCompanyOfficeForCountry(int $companyId, int $countryId): void
    {
        $this->companyTaxOffice
            ->where('country_id', $countryId)
            ->where('company_id', $companyId)
            ->delete();
    }

    /**
     * @inheritDoc
     */
    public function getAllOfficesInCountryPaginate(
        int $countryId,
        ?array $with = null,
        ?string $search = null,
        int $perPage = 40
    ): LengthAwarePaginator {
        $query = $this->taxOffice
            ->where('tax_offices.country_id', $countryId);

        if (!is_null($search)) {
            $query
                ->select(
                    'tax_offices.*',
                    'addresses.street as street',
                    'addresses.house_number as house_number',
                    'addresses.addition as addition',
                    'addresses.city_id as city_id',
                    'addresses.postal_code as postal_code',
                    'addresses.state as state',
                    'cities.name as name'
                )
                ->join('addresses', 'tax_offices.address_id', '=', 'addresses.id')
                ->join('cities', 'addresses.city_id', '=', 'cities.id')
                ->where(function ($query) use ($search) {
                    $query->where('tax_offices.tax_office', 'ILIKE', '%' . $search . '%')
                        ->orWhere('tax_offices.bank', 'ILIKE', $search . '%')
                        ->orWhere('tax_offices.iban', 'ILIKE', $search . '%')
                        ->orWhere('tax_offices.swift', 'ILIKE', $search . '%')
                        ->orWhere('tax_offices.phone', 'ILIKE', $search . '%')
                        ->orWhere('tax_offices.email', 'ILIKE', $search . '%')
                        ->orWhere('tax_offices.web', 'ILIKE', $search . '%')
                        ->orWhere(DB::raw('"addresses"."street" || \' \' || "addresses"."house_number"'), 'ILIKE', '%' . $search . '%')
                        ->orWhere('addresses.postal_code', 'ILIKE', $search . '%')
                        ->orWhere('cities.name', 'ILIKE', '%' . $search . '%');
                });
        }

        if (!is_null($with)) {
            $query->with($with);
        }

        return $query->paginate($perPage);
    }

    public function getAllOfficesInCountries(array $countryIds): Collection
    {
        return $this->taxOffice->whereIn('country_id', $countryIds)->get();
    }

    /**
     * @inheritDoc
     */
    public function searchTaxOffices(string $search, ?int $countryId = null, int $limit = 10): Collection
    {
        $q = $this->taxOffice
            ->join('addresses', 'tax_offices.address_id', '=', 'addresses.id')
            ->join('cities', 'addresses.city_id', '=', 'cities.id')
            ->where('tax_offices.country_id', $countryId)
            ->where(function ($query) use ($search) {
                $query->where('tax_offices.tax_office', 'ILIKE', '%' . $search . '%')
                    ->orWhere('tax_offices.bank', 'ILIKE', $search . '%')
                    ->orWhere('tax_offices.iban', 'ILIKE', $search . '%')
                    ->orWhere('tax_offices.swift', 'ILIKE', $search . '%')
                    ->orWhere('tax_offices.phone', 'ILIKE', $search . '%')
                    ->orWhere('tax_offices.email', 'ILIKE', $search . '%')
                    ->orWhere('tax_offices.web', 'ILIKE', $search . '%')
                    ->orWhere(DB::raw('"addresses"."street" || \' \' || "addresses"."house_number"'), 'ILIKE', $search . '%')
                    ->orWhere('addresses.postal_code', 'ILIKE', $search . '%')
                    ->orWhere('cities.name', 'ILIKE', $search . '%');
            });

        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }
}
