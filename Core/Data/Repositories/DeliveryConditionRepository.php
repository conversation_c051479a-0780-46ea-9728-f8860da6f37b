<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\DeliveryCondition;
use App\Core\Data\Repositories\Contracts\DeliveryConditionRepositoryContract;
use Illuminate\Support\Collection;

class DeliveryConditionRepository implements DeliveryConditionRepositoryContract
{
    private DeliveryCondition $deliveryCondition;

    public function __construct(DeliveryCondition $deliveryCondition)
    {
        $this->deliveryCondition = $deliveryCondition;
    }

    /**
     * @inheritDoc
     */
    public function getAll(): Collection
    {
        return $this->deliveryCondition->orderBy('id')->get();
    }
}
