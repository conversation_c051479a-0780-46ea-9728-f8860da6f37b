<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ExchangeRate;
use App\Core\Data\Repositories\Contracts\ExchangeRatesRepositoryContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ExchangeRatesRepository implements ExchangeRatesRepositoryContract
{
    private ExchangeRate $exchangeRate;

    public function __construct(ExchangeRate $exchangeRate)
    {
        $this->exchangeRate = $exchangeRate;
    }

    public function getRatesForDates(array $dates): Collection
    {
        return $this->exchangeRate
            ->whereIn('date', $dates)
            ->orderBy('date')
            ->orderBy('currency_id')
            ->get();
    }

    public function getRatesBetweenDates(string $start, string $end): Collection
    {
        return $this->exchangeRate
            ->where('date', '>=', $start)
            ->where('date', '<=', $end)
            ->get();
    }

    public function getLastDate(): string
    {
        return $this->exchangeRate
            ->select('date')
            ->orderBy('date', 'DESC')
            ->limit(1)
            ->first()->date;
    }

    public function getEmptyExchangeRateModel(): ExchangeRate
    {
        return $this->exchangeRate->newInstance();
    }

    public function deleteAll(): void
    {
        DB::table($this->exchangeRate->getTable())->delete();
    }

    public function getRatesForCurrency(int $currencyId, int $year): EloquentCollection
    {
        $dateFrom = Carbon::parse($year . '-01-01');
        $dateTo = $dateFrom->clone()->endOfYear();

        return $this->exchangeRate
            ->where('currency_id', $currencyId)
            ->where('date', '>=', $dateFrom->toDateString())
            ->where('date', '<=', $dateTo->toDateString())
            ->orderBy('date')
            ->get();
    }
}
