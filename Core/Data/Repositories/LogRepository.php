<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Log;
use App\Core\Data\Repositories\Contracts\LogRepositoryContract;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class LogRepository implements LogRepositoryContract
{
    private Log $log;

    public function __construct(Log $log)
    {
        $this->log = $log;
    }

    public function getAllLogsWithUsersPaginated(): LengthAwarePaginator
    {
        return $this->log->orderBy('created_at', 'DESC')->with('user')->paginate(100);
    }

    public function deleteOldLogs(int $monthsOld = 3): void
    {
        $this->log->where('created_at', '<=', Carbon::now()->subMonths($monthsOld))->delete();
    }
}
