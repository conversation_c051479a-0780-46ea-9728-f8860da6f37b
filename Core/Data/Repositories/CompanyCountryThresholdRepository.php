<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CompanyCountryThreshold;
use App\Core\Data\Repositories\Contracts\CompanyCountryThresholdRepositoryContract;
use Illuminate\Support\Collection;

class CompanyCountryThresholdRepository implements CompanyCountryThresholdRepositoryContract
{
    private CompanyCountryThreshold $threshold;

    public function __construct(CompanyCountryThreshold $threshold)
    {
        $this->threshold = $threshold;
    }

    public function getActiveCompanyThresholds(int $companyId, int $yearId): Collection
    {
        return $this->threshold->where('company_id', '=', $companyId)
            ->whereYear('date', '<=', $yearId)
            ->get()
            ->groupBy('country_id')
            ->transform(function ($item) {
                return $item->sortByDesc('date')->first();
            });
    }
}
