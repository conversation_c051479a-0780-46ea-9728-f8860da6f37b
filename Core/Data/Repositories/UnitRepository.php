<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Unit;
use App\Core\Data\Repositories\Contracts\UnitRepositoryContract;
use Illuminate\Support\Collection;

class UnitRepository implements UnitRepositoryContract
{
    private Unit $unit;

    public function __construct(Unit $unit)
    {
        $this->unit = $unit;
    }

    /**
     * @inheritDoc
     */
    public function getAllUnits(): Collection
    {
        return $this->unit
            ->orderBy('short_name')
            ->get();
    }
}