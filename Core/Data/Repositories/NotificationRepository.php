<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Notification;
use App\Core\Data\Repositories\Contracts\NotificationRepositoryContract;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class NotificationRepository implements NotificationRepositoryContract
{
    /**
     * @var Notification
     */
    private $notification;

    public function __construct(Notification $notification)
    {
        $this->notification = $notification;
    }

    /**
     * @inheritDoc
     */
    public function getNewNotificationModel(): Notification
    {
        return $this->notification->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getById(int $notificationId): ?Notification
    {
        return $this->notification->find($notificationId);
    }

    /**
     * @inheritDoc
     */
    public function getAllByUserId(int $userId, ?bool $read = null): Collection
    {
        $q = $this->notification->where('user_id', $userId);

        if (!is_null($read)) {
            if ($read) {
                $q->whereNotNull('read_at');
            } else {
                $q->whereNull('read_at');
            }
        }

        return $q
            ->limit(500)
            ->orderBy('created_at', 'DESC')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function updateNotificationsByIdsForUser(array $notificationsIds, int $userId, array $columns): void
    {
        $this->notification->where('user_id', $userId)
            ->whereIn('id', $notificationsIds)
            ->update($columns);
    }

    /**
     * @inheritDoc
     */
    public function getAll(): Collection
    {
        return $this->notification->all();
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginated(string $type, int $perPage = 20, ?int $forUser = null): LengthAwarePaginator
    {
        $q = $this->notification->with('sentFromUser');

        if ($type === 'unread') {
            $q = $q->whereNull('read_at');
        } else {
            $q = $q->whereNotNull('read_at');
        }

        if (!is_null($forUser)) {
            $q = $q->where('user_id', $forUser);
        }

        return $q->orderByDesc('created_at')->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function insertNotifications(array $notifications): void
    {
        $this->notification->insert($notifications);
    }

    /**
     * @inheritDoc
     */
    public function deleteNotificationsOlderThen(Carbon $dateTime, ?bool $read = null): void
    {
        $query = $this->notification
            ->where('created_at', '<', $dateTime->toDateTimeString())
            ->orderBy('created_at', 'DESC');

        if (!is_null($read)) {
            if ($read) {
                $query = $query->whereNotNull('read_at');
            } else {
                $query = $query->whereNull('read_at');
            }
        }

        $query->delete();
    }

    public function deleteNotificationById(int $notificationId): void
    {
        $this->notification->where('id', $notificationId)->delete();
    }
}
