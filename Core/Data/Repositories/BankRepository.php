<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\Bank;
use App\Core\Data\Repositories\Contracts\BankRepositoryContract;


class BankRepository implements BankRepositoryContract
{
    /**
     * @var Bank
     */
    private $bank;

    public function __construct(Bank $bank)
    {
        $this->bank = $bank;
    }


    /**
     * @inheritDoc
     */
    public function getBankById(int $bankId): ?Bank
    {
        return $this->bank->find($bankId);
    }


    /**
     * @inheritDoc
     */
    public function getEmptyBankModel(): Bank
    {
        return $this->bank->newInstance();
    }

    /**
     * @param int $bankId
     */
    public function deleteBankById(int $bankId): void
    {
        $this->bank->destroy($bankId);
    }

}