<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Invitation;
use App\Core\Data\Models\InvitationType;
use App\Core\Data\Repositories\Contracts\InvitationRepositoryContract;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class InvitationRepository implements InvitationRepositoryContract
{
    /**
     * @var Invitation
     */
    private $invitation;

    public function __construct(Invitation $invitation)
    {
        $this->invitation = $invitation;
    }

    /**
     * @inheritDoc
     */
    public function getInvitationById(int $invitationId): ?Invitation
    {
        return $this->invitation->find($invitationId);
    }

    /**
     * @inheritDoc
     */
    public function getInvitationByToken(string $invitationToken): ?Invitation
    {
        return $this->invitation
            ->where('invitation_token', $invitationToken)
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyInvitationModel(): Invitation
    {
        return $this->invitation->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteInvitationByIds(array $invitationIds): void
    {
        $this->invitation->whereIn('id', $invitationIds)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getClientInvitationsByEmail(string $email): Collection
    {
        return $this->invitation
            ->where('invitation_type_id', InvitationType::PARTNER_TO_CLIENT)
            ->where('recipient_email', $email)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAccountantInvitationsByEmail(string $email): Collection
    {
        return $this->invitation
            ->where('invitation_type_id', InvitationType::PARTNER_TO_ACCOUNTANT)
            ->where('recipient_email', $email)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAccountantInvitationByPartnerIdAndEmail(int $partnerId, string $email): ?Invitation
    {
        return $this->invitation
            ->where('recipient_email', $email)
            ->where('partner_id', $partnerId)
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getClientInvitationByCompanyIdAndEmail(int $companyId, string $email): ?Invitation
    {
        return $this->invitation
            ->where('recipient_email', $email)
            ->where('company_id', $companyId)
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getUnacceptedInvitations(): Collection
    {
        $ttl = config('evat.emails.invitations.expirePeriod');
        $ttl = Carbon::now()
            ->subHours($ttl)
            ->startOfDay();

        return $this->invitation
            ->where('created_at', '<', $ttl->toDateTimeString())
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getUnacceptedInvitationsForReminder(): Collection
    {
        $ttl = (int)config('evat.emails.invitations.ttl');
        $ttl = Carbon::now()
            ->subHours($ttl)
            ->startOfHour();

        return $this->invitation
            ->where('created_at', '<', $ttl->toDateTimeString())
            ->where('reminder_sent', false)
            ->get();
    }
}