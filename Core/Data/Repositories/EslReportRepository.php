<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\EslReport;
use App\Core\Data\Repositories\Contracts\EslReportRepositoryContract;
use Illuminate\Database\Eloquent\Collection;
use DB;

class EslReportRepository implements EslReportRepositoryContract
{
    private EslReport $eslReport;

    public function __construct(EslReport $eslReport)
    {
        $this->eslReport = $eslReport;
    }

    public function getAllReportsForCompany(
        int $companyId,
        ?int $countryId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): Collection {
        $query = $this->eslReport
            ->where('company_id', $companyId);

        if (!is_null($countryId)) {
            $query->where('country_id', $countryId);
        }

        if (!is_null($dateFrom)) {
            $query->where('date_from', '>=', $dateFrom);
        }

        if (!is_null($dateTo)) {
            $query->where('date_to', '<=', $dateTo);
        }

        return $query->orderBy('date_from', 'ASC')->get();
    }

    public function insertReports(array $toInsert): void
    {
        if (count($toInsert) < 1) {
            return;
        }

        $this->eslReport->insert($toInsert);
    }

    public function massUpdateVatReportDataAndStatus(array $data): void
    {
        if (count($data) < 1) {
            return;
        }

        $table = $this->eslReport->getTable();

        $data = array_map(function (array $report) {
            $report['data'] = "'" . $report['data'] . "'";

            return '(' . implode(', ', $report) . ')';
        }, $data);
        $data = implode(', ', $data);
        $q = 'UPDATE ' . $table . ' SET status_id = tmp.status_id::int, data = tmp.data::jsonb FROM (VALUES ' . $data . ') as tmp (id, status_id, data) where ' . $table . '.id = tmp.id';

        DB::statement($q);
    }

    public function getReportForCompanyByCountryAndDates(int $companyId, int $countryId, string $dateFrom, string $dateTo): ?EslReport
    {
        return $this->eslReport
            ->where('company_id', $companyId)
            ->where('country_id', $countryId)
            ->where('date_from', $dateFrom)
            ->where('date_to', $dateTo)
            ->first();
    }

    public function getNewEslReportModel(): EslReport
    {
        return $this->eslReport->newInstance();
    }

    public function getReportsByIds(array $reportsIds): Collection
    {
        if (count($reportsIds) < 1) {
            $reportsIds = [0];
        }

        return $this->eslReport->whereIn('id', $reportsIds)->get();
    }

    public function getReportByIdAndCompanyCheck($reportId, int $companyId): ?EslReport
    {
        return $this->eslReport
            ->where('id', $reportId)
            ->where('company_id', $companyId)
            ->first();
    }
}
