<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Role;
use App\Core\Data\Repositories\Contracts\RoleRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

class RoleRepository implements RoleRepositoryContract
{
    private Role $role;

    public function __construct(Role $role)
    {
        $this->role = $role;
    }

    /**
     * @inheritDoc
     */
    public function getAllRolesPaginate(int $perPage = 40): LengthAwarePaginator
    {
        return $this->role->orderBy('name')->paginate($perPage);
    }

    /**
     * @return Collection
     */
    public function getAllRoles(): Collection
    {
        return $this->role->orderBy('name')->get();
    }

    /**
     * @inheritDoc
     */
    public function getRoleById(int $roleId): ?Role
    {
        return $this->role->find($roleId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyRoleModel(): Role
    {
        return $this->role->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteRoleById(int $roleId): void
    {
        $this->role->destroy($roleId);
    }

    public function getRolesForCompany(): EloquentCollection
    {
        return $this->role
            ->whereNull('institution_type_id')
            ->orderBy('id')
            ->get();
    }
}
