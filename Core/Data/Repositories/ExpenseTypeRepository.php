<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ExpenseType;
use App\Core\Data\Repositories\Contracts\ExpenseTypeRepositoryContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class ExpenseTypeRepository implements ExpenseTypeRepositoryContract
{
    private ExpenseType $expenseType;

    public function __construct(ExpenseType $expenseType)
    {
        $this->expenseType = $expenseType;
    }

    public function getAllParentExpenseTypes(): EloquentCollection
    {
        return $this->expenseType
            ->whereNull('parent_expense_type_id')
            ->orderBy('name')
            ->get();
    }
}
