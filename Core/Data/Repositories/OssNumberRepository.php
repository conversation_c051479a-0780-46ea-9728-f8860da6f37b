<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\OssNumber;
use App\Core\Data\Models\OssNumberType;
use App\Core\Data\Repositories\Contracts\OssNumberRepositoryContract;
use Illuminate\Support\Collection;

class OssNumberRepository implements OssNumberRepositoryContract
{
    private OssNumber $ossNumber;
    private OssNumberType $ossNumberType;

    public function __construct(OssNumber $ossNumber, OssNumberType $ossNumberType)
    {
        $this->ossNumber = $ossNumber;
        $this->ossNumberType = $ossNumberType;
    }

    public function getOssNumberById(int $ossNumberId): ?OssNumber
    {
        return $this->ossNumber->find($ossNumberId);
    }

    public function getEmptyOssNumberModel(): OssNumber
    {
        return $this->ossNumber->newInstance();
    }

    public function deleteOssNumberById(int $ossNumberId): void
    {
        $this->ossNumber->destroy($ossNumberId);
    }

    public function getAllOssNumberTypes(): Collection
    {
        return $this->ossNumberType->all();
    }

    public function getEuCompanyOssNumberTypes(): Collection
    {
        return $this->ossNumberType->where('id', OssNumberType::UNION_SCHEME)->get();
    }

    public function getNonEuCompanyOssNumberTypes(): Collection
    {
        return $this->ossNumberType->all();
    }

    public function getMainOssNumber(int $companyId): ?OssNumber
    {
        return $this->ossNumber->where('company_id', $companyId)->whereNull('end_date')->latest('register_date')->first();
    }

    public function getOssNumberByOssNumber(string $number): ?OssNumber
    {
        return $this->ossNumber->where('number', $number)->first();
    }
}
