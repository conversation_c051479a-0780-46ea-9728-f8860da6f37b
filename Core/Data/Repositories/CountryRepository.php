<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\CountryCurrency;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use stdClass;

class CountryRepository implements CountryRepositoryContract
{
    private Country $country;
    private CountryCurrency $countryCurrency;

    public function __construct(
        Country $country,
        CountryCurrency $countryCurrency
    ) {
        $this->country = $country;
        $this->countryCurrency = $countryCurrency;
    }

    /**
     * @inheritdoc
     */
    public function getAllCountriesByIds(array $ids): Collection
    {
        return $this->country
            ->whereIn('id', $ids)
            ->orderBy('name')
            ->get();
    }

    public function getAllInEu(bool $includeCommunities = false): Collection
    {
        $query = $this->country
            ->whereNotIn('id', Country::GROUPED_IDS)
            ->whereNotNull('in_eu_start_date')
            ->whereNull('in_eu_end_date')
            ->orderBy('name');

        if (!$includeCommunities) {
            $query->where('is_community', false);
        }

        return $query->get();
    }

    /**
     * @inheritdoc
     */
    public function getAllNonEu(): Collection
    {
        return $this->country
            ->whereNotIn('id', Country::GROUPED_IDS)
            ->whereNull('in_eu_start_date')
            ->orWhereNotNull('in_eu_end_date')
            ->orderBy('name')
            ->get();
    }


    public function getAllInEuForYear(string $year): Collection
    {
        $yearDate = Carbon::createFromDate($year, 1, 1);

        return $this->country
            ->whereNotNull('in_eu_start_date')
            ->whereNotIn('id', Country::GROUPED_IDS)
            ->whereDate('in_eu_start_date', '<=', $yearDate)
            ->where(function ($query) use ($yearDate) {
                $query->whereNull('in_eu_end_date')
                    ->orWhereDate('in_eu_end_date', '>=', $yearDate);
            })
            ->orderBy('name')
            ->get();
    }

    public function getAllNonEuForYear(string $year): Collection
    {
        $allCountries = $this->getAllCountries();
        $allInEuForYear = $this->getAllInEuForYear($year);

        $difference = $allCountries->diff($allInEuForYear);

        return collect($difference->all());
    }

    /**
     * @inheritdoc
     */
    public function getAllCountriesGroupedByEuMembership(string $year = null): Collection
    {
        $countriesEu = new StdClass();
        $countriesEu->id = Country::EU;
        $countriesEu->type = _l('common.EU');
        $countriesEu->options = empty($year) ? $this->getAllInEu() : $this->getAllInEuForYear($year);

        $countriesNoEu = new StdClass();
        $countriesNoEu->id = Country::NON_EU;
        $countriesNoEu->type = _l('common.Non EU');
        $countriesNoEu->options = empty($year) ? $this->getAllNonEu() : $this->getAllNonEuForYear($year);

        return collect([$countriesEu, $countriesNoEu]);
    }

    /**
     * @inheritdoc
     */
    public function getAllCountriesWhereDoingTax(?string $search = null): Collection
    {
        $query = $this->country
            ->where('doing_tax', '=', true)
            ->whereNotIn('id', Country::GROUPED_IDS)
            ->orderBy('name');

        if (!is_null($search)) {
            $query->where(function (Builder $query) use ($search) {
                $query->where('name', 'ILIKE', $search . '%')
                    ->orWhere('code', 'ILIKE', $search . '%');
            });
        }

        return $query->get();
    }

    /**
     * @inheritDoc
     */
    public function getDoingTaxCountriesByUserSearch(int $userId, ?string $search = null, ?int $limit = null): Collection
    {
        $q = $this->country
            ->from('countries as countrs')
            ->select([
                'countrs.*',
                DB::raw('(SELECT "clicks" from "countries_user_search" where "country_id" = "countrs"."id" and "user_id" = ' . $userId . ') as "clicks"')
            ])
            ->where('countrs.doing_tax', true)
            ->whereNotIn('id', Country::GROUPED_IDS);

        if (!is_null($search)) {
            $q->where(function ($query) use ($search) {
                $query->where('countrs.name', 'ILIKE', $search . '%')
                    ->orWhere('countrs.code', 'ILIKE', $search . '%');
            });
        }

        $q->orderByRaw('"clicks" DESC NULLS LAST')
            ->orderBy('countrs.name', 'ASC');

        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }

    /**
     * @inheritDoc
     */
    public function getCountryById(int $countryId): ?Country
    {
        return $this->country->find($countryId);
    }

    /**
     * @inheritDoc
     */
    public function getCountryByCode(string $countryCode): ?Country
    {
        return $this->country->where('code', $countryCode)->first();
    }

    public function getAllCountries(bool $showAll = false, bool $showEu = false, bool $showNonEu = false, ?string $search = null): EloquentCollection
    {
        $q = $this->country
            ->orderBy(DB::raw('id = ' . Country::ALL), 'DESC')
            ->orderBy(DB::raw('id = ' . Country::EU), 'DESC')
            ->orderBy(DB::raw('id = ' . Country::NON_EU), 'DESC')
            ->orderBy('name');

        if (!$showAll) {
            $q->where('id', '!=', Country::ALL);
        }

        if (!$showEu) {
            $q->where('id', '!=', Country::EU);
        }

        if (!$showNonEu) {
            $q->where('id', '!=', Country::NON_EU);
        }

        if (!is_null($search)) {
            $q->where(function (Builder $query) use ($search) {
                $query->where('name', 'ILIKE', $search . '%')
                    ->orWhere('code', 'ILIKE', $search . '%');
            });
        }

        return $q->get();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCountryModel(): Country
    {
        return $this->country->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCountryCurrencyModel(): CountryCurrency
    {
        return $this->countryCurrency->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCountryCurrencyById(int $countryCurrencyId): ?CountryCurrency
    {
        return $this->countryCurrency->find($countryCurrencyId);
    }

    /**
     * @inheritDoc
     */
    public function deleteCountryCurrencyById(int $countryCurrencyId): void
    {
        $this->countryCurrency->destroy($countryCurrencyId);
    }

    /**
     * @inheritDoc
     */
    public function isCountryInEu(int $countryId, string $date): bool
    {
        $country = $this->country
            ->whereNotIn('id', Country::GROUPED_IDS)
            ->where('id', '=', $countryId)
            ->where('in_eu_start_date', '<=', $date)
            ->where(function ($query) use ($date) {
                return $query->where('in_eu_end_date', '>=', $date)
                    ->orWhereNull('in_eu_end_date');
            })
            ->first();
        if (!is_null($country)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    public function searchCountryByNameOrCode(string $search, ?int $perPage = 40): LengthAwarePaginator
    {
        return $this->country
            ->whereNotIn('id', Country::GROUPED_IDS)
            ->where(function (Builder $query) use ($search) {
                $query->where('name', 'ILIKE', $search . '%')
                    ->orWhere('code', 'ILIKE', $search . '%');

            })
            ->paginate($perPage);
    }
}
