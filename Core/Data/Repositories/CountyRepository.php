<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\County;
use App\Core\Data\Repositories\Contracts\CountyRepositoryContract;

class CountyRepository implements CountyRepositoryContract
{
    /**
     * @var County
     */
    private $county;

    public function __construct(County $county)
    {
        $this->county = $county;
    }


    /**
     * @inheritDoc
     */
    public function getCountyById(int $countyId): ?County
    {
        return $this->county->find($countyId);
    }

    /**
     * @inheritDoc
     */
    public function getCountyByNameAndCountry(string $countyName, int $countryId): ?County
    {
        return $this->county->where('country_id', $countryId)->where('name', $countyName)->first();
    }

    /**
     * @param int $countyId
     */
    public function deleteCountyById(int $countyId): void
    {
        $this->county->destroy($countyId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCountyModel(): County
    {
        return $this->county->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function createCountyIfNotExists(string $name, int $countryId): County
    {
        $name = trim($name);
        $name = mb_strtoupper($name);
        $county = $this->getCountyByNameAndCountry($name, $countryId);
        if (!is_null($county)) {
            return $county;
        }
        $county = $this->getEmptyCountyModel();
        $county->name = $name;
        $county->country_id = $countryId;
        $county->save();

        return $county;
    }
}
