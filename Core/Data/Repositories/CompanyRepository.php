<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Company;
use App\Core\Data\Models\CompanyAccountant;
use App\Core\Data\Models\CompanyLegalRepresentative;
use App\Core\Data\Models\CompanyUserRole;
use App\Core\Data\Models\CompanyUserSearch;
use App\Core\Data\Models\ImportFromCountry;
use App\Core\Data\Models\ImportGoodsReverseDocument;
use App\Core\Data\Models\InstitutionType;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyRepository implements CompanyRepositoryContract
{
    private Company $company;
    private CompanyUserSearch $companyUserSearch;
    private CompanyUserRole $companyUserRole;
    private CompanyAccountant $companyAccountant;
    private ImportGoodsReverseDocument $importGoodsReverseDocument;
    private CompanyLegalRepresentative $legalRepresentative;
    private ImportFromCountry $importFromCountry;
    private UserRepositoryContract $userRepo;

    public function __construct(
        Company $company,
        CompanyUserSearch $companyUserSearch,
        CompanyUserRole $companyUserRole,
        CompanyAccountant $companyAccountant,
        ImportGoodsReverseDocument $importGoodsReverseDocument,
        CompanyLegalRepresentative $legalRepresentative,
        ImportFromCountry $importFromCountry,
        UserRepositoryContract $userRepo,
    ) {
        $this->company = $company;
        $this->companyUserSearch = $companyUserSearch;
        $this->companyUserRole = $companyUserRole;
        $this->companyAccountant = $companyAccountant;
        $this->importGoodsReverseDocument = $importGoodsReverseDocument;
        $this->legalRepresentative = $legalRepresentative;
        $this->importFromCountry = $importFromCountry;
        $this->userRepo = $userRepo;
    }

    public function getAll(
        ?array $with = null,
        ?array $companyStatusIds = null,
        ?int $partnerId = null
    ): ?Collection {
        $q = $this->company->orderBy('partner_code');
        if (!is_null($with)) {
            $q->with($with);
        }

        if (!is_null($companyStatusIds)) {
            $q->whereIn('companies.status_id', $companyStatusIds);
        }

        if (!is_null($partnerId)) {
            $q->where('companies.partner_id', $partnerId);
        }

        return $q->get();
    }

    public function getCompanyById(int $companyId): ?Company
    {
        return $this->company->whereId($companyId)->first();
    }

    public function getCompaniesByUserSearch(
        int $userId,
        ?string $search = null,
        ?array $companyIds = null,
        ?int $limit = null
    ): Collection {
        $q = $this->getCompaniesByUserSearchQuery(
            $userId,
            $search,
            $companyIds
        );
        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }

    private function getCompaniesByUserSearchQuery(
        int $userId,
        ?string $search = null,
        ?array $companyIds = null,
        ?array $companyStatusIds = null,
        ?int $partnerId = null,
        ?int $countryId = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): Builder {
        $user = $this->userRepo->getUserById($userId);
        $company = $this->company;
        /** @var Builder $company */
        $q = $company
            ->from('companies as comps')
            ->select([
                DB::raw('(SELECT "clicks" from "companies_user_search" where "company_id" = "comps"."id" and "user_id" = ' . $userId . ') as "clicks"'),
                'comps.*',
                'company_statuses.name'
            ])
            ->join('institution_institution_type', 'comps.partner_id', '=', 'institution_institution_type.id')
            ->join('institutions', 'institution_institution_type.institution_id', '=', 'institutions.id')
            ->join('company_statuses', 'comps.status_id', '=', 'company_statuses.id')
            ->join('addresses as ad', 'comps.address_id', '=', 'ad.id')
            ->join('countries', 'ad.country_id', '=', 'countries.id')
            ->leftJoin('ecommerce_accounts as ea', 'comps.id', '=', 'ea.company_id')
            ->leftJoin('tax_numbers as tn', 'comps.id', '=', 'tn.company_id')
            ->leftJoin('vat_numbers as vn', 'comps.id', '=', 'vn.company_id');

        if (!is_null($orderBy) && !is_null($orderDirection)) {
            if ($orderBy === 'full_legal_name') {
                $orderClause = 'upper(comps.full_legal_name) ' . $orderDirection;
                $q->orderByRaw($orderClause);
                if (!$user->isClientType()) {
                    $orderClausePartner = 'upper(comps.partner_code) ' . $orderDirection;
                    $q->orderByRaw($orderClausePartner);
                }
            } elseif ($orderBy === 'status_name') {
                $q->orderBy('company_statuses.name', $orderDirection);
            } elseif ($orderBy === 'country_name') {
                $q->orderBy('countries.name', $orderDirection);
            } elseif ($orderBy === 'partner_name') {
                $q->orderBy('institutions.full_legal_name', $orderDirection);
            }
        }

        if (!is_null($search)) {
            $q->where(function ($query) use ($search) {
                $query->where('comps.full_legal_name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('comps.partner_code', 'ILIKE', '%' . $search . '%')
                    ->orWhere('comps.short_name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('comps.eori_number', 'ILIKE', '%' . $search . '%')
                    ->orWhere('comps.registration_number', 'ILIKE', '%' . $search . '%')
                    ->orWhere('ea.uaid', 'ILIKE', $search . '%')
                    ->orWhere('tn.number', 'ILIKE', '%' . $search . '%');

                if (ctype_digit($search)) {
                    $query->orWhere('comps.id', $search);
                }
            });

        }

        if (!is_null($companyIds)) {
            $q->whereIn('comps.id', $companyIds);
        }

        if (!is_null($companyStatusIds)) {
            $q->whereIn('comps.status_id', $companyStatusIds);
        }

        if (!is_null($partnerId)) {
            $q->where('comps.partner_id', $partnerId);
        }

        if (!is_null($countryId)) {
            $q->where('ad.country_id', $countryId);
        }

        return $q->groupBy(['comps.id', 'company_statuses.name', 'countries.name', 'institutions.full_legal_name'])
            ->orderByRaw('"clicks" DESC NULLS LAST')
            ->orderBy('comps.full_legal_name', 'ASC');
    }

    public function getCompaniesByUserSearchPaginate(
        int $userId,
        ?string $search = null,
        ?array $companyIds = null,
        ?int $perPage = null,
        ?array $with = [],
        ?array $companyStatusIds = null,
        ?int $partnerId = null,
        ?int $countryId = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): LengthAwarePaginator {
        return $this->getCompaniesByUserSearchQuery(
            $userId,
            $search,
            $companyIds,
            $companyStatusIds,
            $partnerId,
            $countryId,
            $orderBy,
            $orderDirection
        )
            ->with($with)
            ->paginate($perPage);
    }

    public function getCompaniesForExportByUserSearch(
        int $userId,
        ?array $companyIds = null,
        ?array $with = [],
        ?array $companyStatusIds = null,
        ?int $partnerId = null
    ): Collection {
        return $this->getCompaniesByUserSearchQuery(
            $userId,
            null,
            $companyIds,
            $companyStatusIds,
            $partnerId
        )
            ->with($with)
            ->get();
    }

    public function incrementCompanyUserSearch(int $companyId, int $userId): void
    {
        $search = $this->companyUserSearch
            ->where('company_id', $companyId)
            ->where('user_id', $userId)
            ->first();
        if (is_null($search)) {
            $search = $this->companyUserSearch->newInstance();
            $search->user_id = $userId;
            $search->company_id = $companyId;
            $search->clicks = 0;
        }

        $search->clicks = $search->clicks + 1;

        $search->save();
    }

    public function searchCompaniesBy(
        string $search,
        ?array $companyIds = null,
        ?int $perPage = 40
    ): ?LengthAwarePaginator {

        if (!is_null($companyIds) && count($companyIds) < 1) {
            return null;
        }

        $query = $this->company
            ->select('companies.*')
            ->join('addresses', 'companies.address_id', '=', 'addresses.id')
            ->join('cities', 'addresses.city_id', '=', 'cities.id')
            ->leftJoin('shareholders', 'shareholders.company_id', '=', 'companies.id')
            ->leftJoin('banks', 'banks.company_id', '=', 'companies.id')
            ->leftJoin('ecommerce_accounts', 'ecommerce_accounts.company_id', '=', 'companies.id')
            ->leftJoin('tax_numbers', 'tax_numbers.company_id', '=', 'companies.id')
            ->leftJoin('contacts', 'contacts.company_id', '=', 'companies.id')
            ->leftJoin('vat_numbers', 'vat_numbers.company_id', '=', 'companies.id');

        if (!is_null($companyIds)) {
            $query->whereIn('companies.id', $companyIds);
        }

        $query->where(function (Builder $q) use ($search) {
            $q->where('companies.partner_code', 'ILIKE', '%' . $search . '%')
                ->orWhere('companies.full_legal_name', 'ILIKE', '%' . $search . '%')
                ->orWhere('companies.partner_code', 'ILIKE', '%' . $search . '%')
                ->orWhere('ecommerce_accounts.uaid', 'ILIKE', $search . '%')
                ->orWhere('tax_numbers.number', 'ILIKE', '%' . $search . '%')
                ->orWhere('companies.short_name', 'ILIKE', '%' . $search . '%')
                ->orWhere('companies.eori_number', 'ILIKE', '%' . $search . '%')
                ->orWhere('companies.registration_number', '%' . $search . '%');

            if (ctype_digit($search)) {
                $q->orWhere('companies.id', $search);
            }
        })
            ->orderBy('companies.full_legal_name')
            ->orderBy('companies.partner_code');

        return $query->groupBy('companies.id')->paginate($perPage);
    }

    public function getEmptyCompanyModel(): Company
    {
        return $this->company->newInstance();
    }

    /** @noinspection PhpUnusedParameterInspection */
    public function getAllCompaniesByPartnerId(int $institutionInstitutionTypeId, int $institutionTypeId = InstitutionType::TYPE_PARTNER): Collection
    {
        return $this->company
            ->where('partner_id', $institutionInstitutionTypeId)
            ->orderBy('full_legal_name')
            ->get();
    }

    public function getCompaniesByIds(array $companyIds): ?Collection
    {
        return $this->company->whereIn('id', $companyIds)->get();
    }

    public function deleteCompanyUserRoles(int $userId): void
    {
        $this->companyUserRole->where('user_id', $userId)->delete();
    }

    public function getCompanyByPartnerCodeAndPartnerId(string $partnerCode, int $partnerInstitutionInstitutionTypeId): ?Company
    {
        return $this->company
            ->where('partner_code', $partnerCode)
            ->where('partner_id', $partnerInstitutionInstitutionTypeId)
            ->first();
    }

    public function getAllCompaniesIdsByAccountantInstitutionInstitutionTypeId(int $institutionInstitutionTypeId): array
    {
        $select = [DB::raw('distinct company_id')];

        return $this->companyAccountant->select($select)
            ->join('institution_type_country_partner', 'company_accountants.accountant_id', '=', 'institution_type_country_partner.id')
            ->where('institution_type_country_partner.institution_institution_type_id', $institutionInstitutionTypeId)
            ->get()
            ->pluck('company_id')
            ->toArray();
    }

    public function insertCompanyUserRoles(array $roles): void
    {
        $this->companyUserRole->insert($roles);
    }

    public function getImportGoodsReverseDocumentById(int $importGoodsReverseDocumentId): ?ImportGoodsReverseDocument
    {
        return $this->importGoodsReverseDocument->find($importGoodsReverseDocumentId);
    }

    public function deleteImportGoodsReverseDocument(int $importGoodsReverseDocumentId): void
    {
        $this->importGoodsReverseDocument->destroy($importGoodsReverseDocumentId);
    }

    public function getEmptyImportGoodsReverseDocument(): ImportGoodsReverseDocument
    {
        return $this->importGoodsReverseDocument->newInstance();
    }

    public function createLegalRepresentative(int $companyId, int $personId): CompanyLegalRepresentative
    {
        $legalRepresentative = new CompanyLegalRepresentative();

        $legalRepresentative->company_id = $companyId;
        $legalRepresentative->person_id = $personId;

        $legalRepresentative->save();

        return $legalRepresentative;
    }

    public function getLegalRepresentative(int $companyId, int $personId): ?CompanyLegalRepresentative
    {
        return $this->legalRepresentative
            ->where('company_id', $companyId)
            ->where('person_id', $personId)
            ->first();
    }

    public function deleteLegalRepresentative(int $companyId, int $personId): void
    {
        $legalRepresentative = $this->getLegalRepresentative($companyId, $personId);
        $this->legalRepresentative->destroy($legalRepresentative->id);
    }

    public function insertImportFromCountries(array $data): void
    {
        $this->importFromCountry->insert($data);
    }

    public function getCompanyUserRoleByUserCompanyAndRoleId(int $userId, int $companyId, int $roleId): ?CompanyUserRole
    {
        return $this->companyUserRole
            ->where('company_id', $companyId)
            ->where('user_id', $userId)
            ->where('role_id', $roleId)
            ->first();
    }

    public function getEmptyCompanyUserRoleModel(): CompanyUserRole
    {
        return $this->companyUserRole->newInstance();
    }
}
