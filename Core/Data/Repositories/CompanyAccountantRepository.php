<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CompanyAccountant;
use App\Core\Data\Repositories\Contracts\CompanyAccountantRepositoryContract;

class CompanyAccountantRepository implements CompanyAccountantRepositoryContract
{
    /**
     * @var CompanyAccountant
     */
    private $companyAccountant;

    public function __construct(CompanyAccountant $companyAccountant)
    {
        $this->companyAccountant = $companyAccountant;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCompanyAccountantModel(): CompanyAccountant
    {
        return $this->companyAccountant->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyAccountantById(?int $companyAccountantId): ?CompanyAccountant
    {
        return $this->companyAccountant->find($companyAccountantId);
    }
}