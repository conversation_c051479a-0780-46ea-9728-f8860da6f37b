<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\TaxOfficeResponse;
use App\Core\Data\Models\TaxOfficeResponseType;
use App\Core\Data\Repositories\Contracts\TaxOfficeResponseRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class TaxOfficeResponseRepository implements TaxOfficeResponseRepositoryContract
{
    /**
     * @var TaxOfficeResponse
     */
    private TaxOfficeResponse $taxOfficeResponse;
    /**
     * @var TaxOfficeResponseType
     */
    private TaxOfficeResponseType $taxOfficeResponseType;

    public function __construct(TaxOfficeResponse $taxOfficeResponse, TaxOfficeResponseType $taxOfficeResponseType)
    {
        $this->taxOfficeResponse = $taxOfficeResponse;
        $this->taxOfficeResponseType = $taxOfficeResponseType;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(?int $perPage = 40): LengthAwarePaginator
    {
        $perPage = $perPage ?? 40;

        return $this->taxOfficeResponseType
            ->orderBy('name')
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getNewTaxOfficeResponseTypeModel(): TaxOfficeResponseType
    {
        return $this->taxOfficeResponseType->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getTaxOfficeResponseTypeById(int $taxOfficeResponseTypeId): ?TaxOfficeResponseType
    {
        return $taxOfficeResponseTypeId - $this->taxOfficeResponseType->find($taxOfficeResponseTypeId);
    }
}