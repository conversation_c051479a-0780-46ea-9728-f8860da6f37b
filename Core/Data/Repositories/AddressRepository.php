<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\Address;
use App\Core\Data\Models\AddressType;
use App\Core\Data\Repositories\Contracts\AddressRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class AddressRepository implements AddressRepositoryContract
{
    /**
     * @var Address
     */
    private $address;

    /**
     * @var AddressType
     */
    private $addressType;

    public function __construct(Address $address, AddressType $addressType)
    {
        $this->address = $address;
        $this->addressType = $addressType;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(int $perPage = 40): LengthAwarePaginator
    {
        return $this->address->with('city', 'country')->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getaddressById(int $addressId): ?Address
    {
        return $this->address->find($addressId);
    }

    /**
     * @param int $addressId
     */
    public function deleteAddressById(int $addressId): void
    {
        $this->address->destroy($addressId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyAddressModel(): Address
    {
        return $this->address->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getAllAddressTypes(): Collection
    {
        return $this->addressType->all();
    }

}
