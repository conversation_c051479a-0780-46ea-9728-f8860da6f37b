<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportRawData;
use App\Core\Data\Models\AmazonReportsApiQueue;
use App\Core\Data\Models\AmazonReportStatus;
use App\Core\Data\Repositories\Contracts\AmazonReportRepositoryContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AmazonReportRepository implements AmazonReportRepositoryContract
{
    private AmazonReport $amazonReport;
    private AmazonReportStatus $reportStatus;
    private AmazonReportRawData $amazonReportRawData;
    private AmazonReportsApiQueue $amazonReportsApiQueue;

    public function __construct(
        AmazonReport $amazonReport,
        AmazonReportStatus $reportStatus,
        AmazonReportRawData $amazonReportRawData,
        AmazonReportsApiQueue $amazonReportsApiQueue
    ) {
        $this->amazonReport = $amazonReport;
        $this->reportStatus = $reportStatus;
        $this->amazonReportRawData = $amazonReportRawData;
        $this->amazonReportsApiQueue = $amazonReportsApiQueue;
    }

    public function getNewAmazonReportModel(): AmazonReport
    {
        return $this->amazonReport->newInstance();
    }

    public function getReportById(int $reportId): ?AmazonReport
    {
        return $this->amazonReport->find($reportId);
    }

    public function getQueuedReports(?int $limit = null): Collection
    {
        $q = $this->amazonReport
            ->where('report_status_id', AmazonReportStatus::STATUS_QUEUED)
            ->orderBy('id', 'ASC');

        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }

    public function getReadReports(?int $limit = null): Collection
    {
        $q = $this->amazonReport
            ->whereIn('report_status_id',
                [
                    AmazonReportStatus::STATUS_READ,
                ]
            )
            ->orderBy('report_status_id', 'DESC')
            ->orderBy('id', 'ASC');

        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }

    public function getAllResolvedReports(?int $limit = null): Collection
    {
        $q = $this->amazonReport
            ->whereIn('report_status_id',
                [
                    AmazonReportStatus::STATUS_RESOLVED,
                ]
            )
            ->orderBy('id', 'ASC');

        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }

    public function getAllSyncedReports(?int $limit = null): Collection
    {
        $q = $this->amazonReport
            ->whereIn('report_status_id',
                [
                    AmazonReportStatus::STATUS_SYNCED_DB,
                ]
            )
            ->orderBy('id', 'ASC');

        if (!is_null($limit)) {
            $q->limit($limit);
        }

        return $q->get();
    }

    public function getAmazonReportRawDataInvoices(int $amazonReportId, int $amazonReportStatusId, int $cycle, int $limit): Collection
    {
        return $this->amazonReportRawData
            ->select([DB::raw("'''' || amazon_reports_raw_data.unique_invoice_key || '''' as key")])
            ->join('amazon_reports', 'amazon_reports.id', '=', 'amazon_reports_raw_data.amazon_report_id')
            ->where('amazon_reports.report_status_id', $amazonReportStatusId)
            ->where('amazon_reports_raw_data.success', true)
            ->where('amazon_reports_raw_data.cycle', $cycle)
            ->where('amazon_reports_raw_data.amazon_report_id', $amazonReportId)
            ->whereNotNull('amazon_reports_raw_data.unique_invoice_key')
            ->groupBy('amazon_reports_raw_data.unique_invoice_key')
            ->orderBy('amazon_reports_raw_data.unique_invoice_key')
            ->limit($limit)
            ->get();
    }

    public function getRawAmazonReportRawDataByUniqueInvoiceKeys(int $amazonReportId, string $keys): array
    {
        return DB::select('SELECT * FROM amazon_reports_raw_data WHERE amazon_report_id = ' . $amazonReportId . ' AND  unique_invoice_key IN(' . $keys . ') ORDER BY amazon_reports_raw_data.id');
    }

    public function getReportsCountPerStatusForInstitution(
        string $startDate,
        string $endDate,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $companyId = null,
        bool $checkCompaniesInstitutionsSalesChannels = true,
    ): EloquentCollection {
        if (is_null($institutionInstitutionTypeId) && is_null($companyId)) {
            return new EloquentCollection([]);
        }

        $select = [
            'report_status_id',
            'original_report_id',
            DB::raw('count(report_status_id) as count')
        ];

        $q = $this->getReportsQueryForInstitution(
            $select,
            $institutionInstitutionTypeId,
            $search,
            $companyId,
            $checkCompaniesInstitutionsSalesChannels,
        );

        $statuses = $this->resolveStatusesWithOrWithoutDates();
        $q->where(function (Builder $query) use ($startDate, $endDate, $statuses) {
            $query->whereIn('amazon_reports.report_status_id', $statuses->getStatusesWithoutDates())
                ->where(function (Builder $query) use ($startDate, $endDate) {
                    $query->where(function (Builder $query) use ($startDate) {
                        $query->where('amazon_reports.start_date', '>', $startDate)
                            ->orWhereNull('amazon_reports.start_date');
                    })->where(function (Builder $query) use ($endDate) {
                        $query->where('amazon_reports.end_date', '<=', $endDate)
                            ->orWhereNull('amazon_reports.end_date');
                    });
                });

        })->orWhere(function (Builder $query) use ($statuses, $startDate, $endDate, $companyId) {
            $query->whereIn('amazon_reports.report_status_id', $statuses->getStatusesWithDates())
                ->where('amazon_reports.start_date', '>', $startDate)
                ->where('amazon_reports.end_date', '<=', $endDate);

            if (!is_null($companyId)) {
                $query->where(function (Builder $builder) use ($companyId) {
                    $builder->where('amazon_reports.upload_company_id', $companyId)
                        ->orWhere('ecommerce_accounts.company_id', $companyId);
                });
            }
        });

        $q
            ->groupBy('amazon_reports.report_status_id')
            ->groupBy('amazon_reports.original_report_id')
            ->orderBy('amazon_reports.report_status_id');

        return $q->get();
    }

    public function getReportsForStatusForInstitution(
        array $reportStatusIds,
        string $startDate,
        string $endDate,
        ?int $institutionInstitutionTypeId = null,
        ?array $with = null,
        ?string $search = null,
        ?int $companyId = null,
        bool $checkCompaniesInstitutionsSalesChannels = true,
    ): LengthAwarePaginator {
        $select = [
            'amazon_reports.original_report_id',
            'amazon_reports.chunk_name',
            DB::raw('count("amazon_reports"."id") as "count"'),
            DB::raw('max("amazon_reports"."report_status_id") as "report_status_id"')
        ];
        $statuses = $this->resolveStatusesWithOrWithoutDates();

        $q = $this->getReportsQueryForInstitution(
            $select,
            $institutionInstitutionTypeId,
            $search,
            $companyId,
            $checkCompaniesInstitutionsSalesChannels
        )->whereIn('report_status_id', $reportStatusIds)
            ->where(function (Builder $query) use ($startDate, $endDate, $statuses) {
                $query->where(function (Builder $query) use ($startDate, $endDate, $statuses) {
                    $query->whereIn('amazon_reports.report_status_id', $statuses->getStatusesWithoutDates())
                        ->where(function (Builder $query) use ($startDate, $endDate) {
                            $query->where(function (Builder $query) use ($startDate) {
                                $query->where('amazon_reports.start_date', '>', $startDate)
                                    ->orWhereNull('amazon_reports.start_date');
                            })->where(function (Builder $query) use ($endDate) {
                                $query->where('amazon_reports.end_date', '<=', $endDate)
                                    ->orWhereNull('amazon_reports.end_date');
                            });
                        });

                })->orWhere(function (Builder $query) use ($statuses, $startDate, $endDate) {
                    $query->whereIn('amazon_reports.report_status_id', $statuses->getStatusesWithDates())
                        ->where('amazon_reports.start_date', '>', $startDate)
                        ->where('amazon_reports.end_date', '<=', $endDate);
                });
            });

        if (!is_null($with)) {
            $q->with($with);
        }

        return $q
            ->orderByRaw('max("amazon_reports"."uploaded_at") DESC')
            ->paginate(config('evat.perPage'));
    }

    /**
     * @param array $select
     * @param int|null $institutionInstitutionTypeId
     * @param string|null $search
     * @param int|null $companyId
     * @param bool $checkCompaniesInstitutionsSalesChannels
     * @return Builder|AmazonReport
     */
    private function getReportsQueryForInstitution(
        array $select,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $companyId = null,
        bool $checkCompaniesInstitutionsSalesChannels = true,
    ): AmazonReport|Builder {
        /**
         * @var Builder $q
         */
        $q = $this->amazonReport
            ->select($select)
            ->leftJoin('ecommerce_accounts', 'ecommerce_accounts.id', '=', 'amazon_reports.sales_channel_id')
            ->leftJoin('companies', 'ecommerce_accounts.company_id', '=', 'companies.id')
            ->groupBy([
                'amazon_reports.original_report_id',
                'amazon_reports.chunk_name'
            ]);

        if (!is_null($search)) {
            $q->where(function (Builder $query) use ($search) {
                $query->where(DB::raw('"f"."name" || \'.\' || "f"."extension"'), 'ILIKE', '%' . $search . '%')
                    ->orWhere('amazon_reports.id', 'ILIKE', '%' . (int)$search . '%');
            });
        }

        if (!is_null($companyId)) {
            $q->where(function (Builder $query) use ($companyId) {
                $query->where('amazon_reports.upload_company_id', $companyId)
                    ->orWhere('ecommerce_accounts.company_id', $companyId);
            });
        } elseif (!is_null($institutionInstitutionTypeId) && $checkCompaniesInstitutionsSalesChannels) {
            // Repetitive subQuery extraction
            $subQueriesBuilder = new class ($institutionInstitutionTypeId) {
                public function __construct(private readonly int $institutionInstitutionTypeId)
                {
                }

                public function resolvePartnerCompaniesIdsSubQuery(QueryBuilder $query): void
                {
                    $query->select('id')->from('companies')->where('partner_id', $this->institutionInstitutionTypeId);
                }

                public function resolveAccountantsCompaniesIdsSubQuery(QueryBuilder $query): void
                {
                    $query->select('company_accountants.company_id')->from('company_accountants')
                        ->join('institution_type_country_partner', 'company_accountants.accountant_id', '=', 'institution_type_country_partner.id')
                        ->where('institution_type_country_partner.institution_institution_type_id', $this->institutionInstitutionTypeId)
                        ->orWhere('institution_type_country_partner.partner_id', $this->institutionInstitutionTypeId);
                }
            };

            $q->where(function (Builder $query) use ($institutionInstitutionTypeId, $subQueriesBuilder) {
                // Wrapper
                $query->where(function (Builder $query) use ($institutionInstitutionTypeId, $subQueriesBuilder) {

                    // Sales channel ID is null, check others
                    $query->whereNull('ecommerce_accounts.company_id')
                        ->where(function (Builder $query) use ($institutionInstitutionTypeId, $subQueriesBuilder) {
                            // Check if uploaded by current institution
                            $query->where('amazon_reports.upload_institution_institution_type_id', $institutionInstitutionTypeId)

                                // Check if upload company belongs to current institution as partner
                                ->orWhereIn('amazon_reports.upload_company_id', function (QueryBuilder $query) use ($subQueriesBuilder) {
                                    $subQueriesBuilder->resolvePartnerCompaniesIdsSubQuery($query);
                                })
                                // Check if upload company belongs to current institution as accountant
                                ->orWhereIn('amazon_reports.upload_company_id', function (QueryBuilder $query) use ($subQueriesBuilder) {
                                    $subQueriesBuilder->resolveAccountantsCompaniesIdsSubQuery($query);
                                });
                        });
                })->orWhere(function (Builder $query) use ($subQueriesBuilder) {
                    $query->whereNotNull('ecommerce_accounts.company_id')
                        ->where(function (Builder $query) use ($subQueriesBuilder) {

                            // Check if upload company belongs to current institution as partner
                            $query->whereIn('ecommerce_accounts.company_id', function (QueryBuilder $query) use ($subQueriesBuilder) {
                                $subQueriesBuilder->resolvePartnerCompaniesIdsSubQuery($query);
                            })
                                // Check if upload company belongs to current institution as accountant
                                ->orWhereIn('ecommerce_accounts.company_id', function (QueryBuilder $query) use ($subQueriesBuilder) {
                                    $subQueriesBuilder->resolveAccountantsCompaniesIdsSubQuery($query);
                                });
                        });
                });
            });
        }

        return $q;
    }

    private function resolveStatusesWithOrWithoutDates(): object
    {
        return new class {
            public function getStatusesWithDates(): array
            {
                return [AmazonReportStatus::STATUS_SUCCESS, AmazonReportStatus::STATUS_DOUBLE];
            }

            public function getStatusesWithoutDates(): array
            {
                return array_merge(AmazonReportStatus::getProcessingStatusIds(), [AmazonReportStatus::STATUS_FAILED, AmazonReportStatus::STATUS_QUEUED]);
            }
        };
    }

    public function getParsedCount(
        int $yearId = null,
        ?array $companiesIds = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $users = null
    ): int {
        $second = DB::raw('(select "companies"."id" from "companies" join "ecommerce_accounts" "ea" on "companies"."id" = "ea"."company_id" join "amazon_reports_transactions" "art" on "ea"."id" = "art"."ecommerce_account_id" where "art"."amazon_report_id" = "amazon_reports"."id" limit 1)');
        $q = $this->amazonReport->leftJoin('companies', 'companies.id', '=', $second);


        if (!is_null($companiesIds)) {
            $q->whereIn('companies.id', $companiesIds);
        }

        if (!is_null($yearId)) {
            $start = Carbon::parse($yearId . '-01-01')->year;
            $q->whereYear('amazon_reports.start_date', $start);
        }

        if (!is_null($startDate)) {
            $q->where('amazon_reports.start_date', $startDate);
        }

        if (!is_null($endDate)) {
            $q->where('amazon_reports.end_date', $endDate);
        }

        if (!is_null($users)) {
            $q->whereIn('amazon_reports.upload_user_id', $users);
        }

        $statuses = [
            AmazonReportStatus::STATUS_PARSING_STARTED,
            AmazonReportStatus::STATUS_READ,
            AmazonReportStatus::STATUS_RESOLVED,
            AmazonReportStatus::STATUS_SYNCED_DB,
            AmazonReportStatus::STATUS_SUCCESS,
        ];

        return $q
            ->whereIn('report_status_id', $statuses)
            ->orderBy('amazon_reports.parsing_ended_at', 'DESC')
            ->orderBy('amazon_reports.id', 'DESC')
            ->count();
    }

    public function getYearReportsPerStatusCount(int $userId, int $yearId): Collection
    {
        return $this->amazonReport
            ->select([
                    'amazon_reports.report_status_id',
                    DB::raw('EXTRACT(MONTH FROM files.created_at) as month'),
                    DB::raw('count(amazon_reports.report_status_id) as count')
                ]
            )->join('files', function (JoinClause $query) {
                $query->on('amazon_reports.id', '=', 'files.resource_id')
                    ->where('files.resource', AmazonReport::class);
            })->whereYear('files.created_at', $yearId)
            ->where('upload_user_id', '=', $userId)
            ->groupBy('month', 'amazon_reports.report_status_id')
            ->get();
    }

    public function getAllReportStatuses(): Collection
    {
        return $this->reportStatus->orderBy('id')->get();
    }

    public function addMonthsToAmazonReportsQueue(array $rows): void
    {
        $this->amazonReportsApiQueue->insert($rows);
    }

    public function getMonthsFromAmazonReportsQueue(int $limit, string $status = null): Collection
    {
        $query = $this->amazonReportsApiQueue
            ->orderBy('id')
            ->limit($limit);

        if (!is_null($status)) {
            $query = $query->where('status', $status);
        }

        return $query->get();
    }

    public function getAllReportsForSalesChannels(array $salesChannelsIds): Collection
    {
        return $this->amazonReport->whereIn('sales_channel_id', $salesChannelsIds)->get();
    }

    public function deleteAllAmazonApiQueueBySalesChannelIds(array $salesChannelsIds): void
    {
        $this->amazonReportsApiQueue->where('sales_channel_id', $salesChannelsIds)->delete();
    }

    public function getAmazonReportRawDataById(int $amazonReportRawDataId): ?AmazonReportRawData
    {
        return $this->amazonReportRawData->find($amazonReportRawDataId);
    }

    public function getFailedAmazonReportRawDataPaginate(int $amazonReportId): LengthAwarePaginator
    {
        return $this->amazonReportRawData
            ->where('amazon_report_id', $amazonReportId)
            ->where(function (Builder $query) {
                $query->where('success', false)
                    ->orWhereNotNull('error_reasons');
            })
            ->paginate(config('evat.perPage'));
    }

    public function getCountOfQueuedReportsByBatchId(string $batchId): int
    {
        return $this->amazonReport
            ->whereIn('report_status_id', [
                AmazonReportStatus::STATUS_QUEUED,
                AmazonReportStatus::STATUS_PARSING_STARTED,
                AmazonReportStatus::STATUS_READ,
                AmazonReportStatus::STATUS_RESOLVED,
                AmazonReportStatus::STATUS_SYNCED_DB,
            ])
            ->where('batch_id', $batchId)
            ->count();
    }

    public function deleteAmazonReportRawDataByAmazonReportId(int $amazonReportId): void
    {
        $this->amazonReportRawData->where('amazon_report_id', $amazonReportId)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getAmazonReportsForStatus(array $statusIds): EloquentCollection
    {
        return $this->amazonReport
            ->select([
                'amazon_reports.id AS id',
                'files.size as size'
            ])
            ->selectRaw('files.name || \'.\' || files.extension AS name')
            ->selectRaw('ROUND((files.size::float / 1024 / 1024)::numeric, 2) AS size_mb')
            ->selectRaw('ROUND((files.size::float / 1024 / 1024)::numeric, 2) || \' MB\' AS size_mb_pretty')
            ->selectRaw('pg_size_pretty(files.size) AS size_pretty')
            ->with('file')
            ->whereIn('report_status_id', $statusIds)
            ->join('files', function (JoinClause $join) {
                $join->on('files.resource_id', '=', 'amazon_reports.id')
                    ->where('files.resource', AmazonReport::class);
            })
            ->orderBy('amazon_reports.id', 'ASC')
            ->orderBy('files.size', 'ASC')
            ->get();
    }

    public function getReportsByOriginalId(int $originalId): EloquentCollection
    {
        return $this->amazonReport
            ->orderBy('id')
            ->where('original_report_id', $originalId)
            ->get();
    }
}
