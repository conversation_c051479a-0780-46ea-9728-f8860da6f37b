<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ReportTemplate;
use App\Core\Data\Models\ReportTemplateField;
use App\Core\Data\Models\ReportType;
use App\Core\Data\Repositories\Contracts\ReportTemplateRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ReportTemplateRepository implements ReportTemplateRepositoryContract
{
    private ReportTemplate $reportTemplate;
    private ReportTemplateField $reportTemplateField;
    /**
     * @var \App\Core\Data\Models\ReportType
     */
    private ReportType $reportType;

    public function __construct(ReportTemplate $reportTemplate, ReportTemplateField $reportTemplateField, ReportType $reportType)
    {
        $this->reportTemplate = $reportTemplate;
        $this->reportTemplateField = $reportTemplateField;
        $this->reportType = $reportType;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyReportTemplateModel(): ReportTemplate
    {
        return $this->reportTemplate->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getReportTemplateById(int $reportId): ?ReportTemplate
    {
        return $this->reportTemplate->find($reportId);
    }

    /**
     * @inheritDoc
     */
    public function getAllReportTemplatesPaginate(
        int $resourceId,
        string $resourceClass,
        int $perPage = 40,
        bool $userIsAdministrative = false
    ): LengthAwarePaginator {
        $query = $this->reportTemplate
            ->orderBy('valid_from', 'DESC')
            ->orderBy('interval_enum')
            ->orderBy('name');
        if (!$userIsAdministrative) {
            $query = $query->where('resource', $resourceClass)
                ->where('resource_id', $resourceId);
        }

        return $query->paginate($perPage);
    }

    public function deleteReportTemplateById(int $reportId): void
    {
        $this->reportTemplate->destroy($reportId);
    }

    /**
     * @inheritDoc
     */
    public function deleteReportFieldsByReportTemplateId(int $reportId): void
    {
        $this->reportTemplateField->where('report_template_id', $reportId)->delete();
    }

    public function insertReportTemplateFields(array $fields): void
    {
        $this->reportTemplateField->insert($fields);
    }

    public function getAllReportTypes(): Collection
    {
        return $this->reportType->orderBy('name')->get();
    }

    public function getReportTemplateForTypeOnDate(int $reportTypeId, string $dateFrom, string $interval): ?ReportTemplate
    {
        return $this->reportTemplate
            ->where('report_type_id', $reportTypeId)
            ->where('interval_enum', $interval)
            ->where('valid_from', '<=', $dateFrom)
            ->orderBy('valid_from', 'DESC')
            ->first();
    }
}
