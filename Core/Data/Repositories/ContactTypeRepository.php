<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Repositories\Contracts\ContactTypeRepositoryContract;
use App\Core\Data\Models\ContactType;
use Illuminate\Support\Collection;

class ContactTypeRepository implements ContactTypeRepositoryContract
{
    /**
     * @var ContactType
     */
    private $contactType;

    public function __construct(ContactType $contactType)
    {
        $this->contactType = $contactType;
    }

    /**
     * @inheritDoc
     */
    public function getAllContactTypes(): Collection
    {
        return $this->contactType->all();
    }

    /**
     * @inheritDoc
     */
    public function getContactTypeById(int $contactTypeId): ?ContactType
    {
        return $this->contactType->find($contactTypeId);
    }

    /**
     * @inheritDoc
     */
    public function getContactTypesByIds(array $contactTypeIds): Collection
    {
        return $this->contactType->whereIn('id', $contactTypeIds)->get();
    }
}
