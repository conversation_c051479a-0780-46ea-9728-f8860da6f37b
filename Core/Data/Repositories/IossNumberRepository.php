<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\IossNumber;
use App\Core\Data\Models\IossNumberType;
use App\Core\Data\Repositories\Contracts\IossNumberRepositoryContract;
use Illuminate\Support\Collection;

class IossNumberRepository implements IossNumberRepositoryContract
{
    private IossNumber $iossNumber;
    private IossNumberType $iossNumberType;

    public function __construct(IossNumber $iossNumber, IossNumberType $iossNumberType)
    {
        $this->iossNumber = $iossNumber;
        $this->iossNumberType = $iossNumberType;
    }

    public function getIossNumberById(int $iossNumberId): ?IossNumber
    {
        return $this->iossNumber->find($iossNumberId);
    }

    public function getEmptyIossNumberModel(): IossNumber
    {
        return $this->iossNumber->newInstance();
    }

    public function deleteIossNumberById(int $iossNumberId): void
    {
        $this->iossNumber->destroy($iossNumberId);
    }

    public function getAllIossNumberTypes(): Collection
    {
        return $this->iossNumberType->all();
    }

    public function getEuCompanyIossNumberTypes(): Collection
    {
        /*
            INFO:
            Add Union scheme OSS number - to će biti broj istog formata kao VAT Number (DExxxx..)
            Add Import scheme IOSS (IM) number - to će biti broj formata IMxxxx...
            Add Intermediary IOSS (IN) number - to će biti broj formata INxxxx...
            Add Taxable person intermediated IOSS (IM) number - to će biti broj formata IMxxxx...
            **ograniciti tipove, country je za sve tipove country u kojoj je kompanija registrirana
        */

        return $this->iossNumberType->all();
    }

    public function getNonEuCompanyIossNumberTypes(): Collection
    {
        /*
            INFO:
            Kompanija NON EU:
            Add Union scheme OSS number - to će biti broj istog formata kao VAT Number (DExxxx..)
            Add Non-Union scheme OSS (VOES, NETP) number - to će biti broj formata EUxxxxx...
            Add Intermediary IOSS (IN) number - to će biti broj formata INxxxx...
            Add Taxable person intermediated IOSS (IM) number - to će biti broj formata IMxxxx...
            **nema ogranicenje za country, samo treba ograniciti tipove
        */

        return $this->iossNumberType->whereIn('id', [IossNumberType::INTERMEDIARY, IossNumberType::TAXABLE_PERSON])->get();
    }

    public function getMainIossNumber(int $companyId): IossNumber|null
    {
        return $this->iossNumber->where('company_id', $companyId)->whereNull('end_date')->latest('register_date')->first();
    }

    public function getIossNumberByIossNumber(string $number): ?IossNumber
    {
        return $this->iossNumber->where('number', $number)->first();
    }
}
