<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\City;
use App\Core\Data\Repositories\Contracts\CityRepositoryContract;

class CityRepository implements CityRepositoryContract
{
    /**
     * @var City
     */
    private $city;

    public function __construct(City $city)
    {
        $this->city = $city;
    }


    /**
     * @inheritDoc
     */
    public function getCityById(int $cityId): ?City
    {
        return $this->city->find($cityId);
    }

    /**
     * @inheritDoc
     */
    public function getCityByNameAndCountry(string $cityName, int $countryId): ?City
    {
        $cityName = trim($cityName);
        $cityName = mb_strtoupper($cityName);

        return $this->city->where('country_id', $countryId)->where('name', $cityName)->first();
    }

    /**
     * @param int $cityId
     */
    public function deleteCityById(int $cityId): void
    {
        $this->city->destroy($cityId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCityModel(): City
    {
        return $this->city->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function createCityIfNotExists(string $name, int $countryId): City
    {
        $name = trim($name);
        $name = mb_strtoupper($name);
        $city = $this->getCityByNameAndCountry($name, $countryId);
        if (!is_null($city)) {
            return $city;
        }

        $city = $this->getEmptyCityModel();
        $city->name = $name;
        $city->country_id = $countryId;
        $city->save();

        return $city;
    }
}
