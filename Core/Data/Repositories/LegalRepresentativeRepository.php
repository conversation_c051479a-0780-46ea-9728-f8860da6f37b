<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Repositories\Contracts\LegalRepresentativeRepositoryContract;
use App\Core\Data\Models\CompanyLegalRepresentative;

class LegalRepresentativeRepository implements LegalRepresentativeRepositoryContract
{
    /**
     * @var CompanyLegalRepresentative
     */
    private CompanyLegalRepresentative $legalRepresentative;

    public function __construct(CompanyLegalRepresentative $legalRepresentative)
    {
        $this->legalRepresentative = $legalRepresentative;
    }

    /**
     * @return CompanyLegalRepresentative
     */
    public function getEmptyLegalRepresentativeModel(): CompanyLegalRepresentative
    {
        return $this->legalRepresentative->newInstance();
    }

    public function getLegalRepresentativeById(int $legalRepresentativeId): ?CompanyLegalRepresentative
    {
        return $this->legalRepresentative->find($legalRepresentativeId);
    }

    public function getLegalRepresentativeByPersonAndCompanyIds(int $personId, int $companyId)
    {
        return $this->legalRepresentative
            ->where('person_id', $personId)
            ->where('company_id', $companyId)
            ->get();
    }
}
