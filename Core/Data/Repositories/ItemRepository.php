<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\CommodityCodeType;
use App\Core\Data\Models\CountryItemTaxCode;
use App\Core\Data\Models\Item;
use App\Core\Data\Models\ItemCategory;
use App\Core\Data\Models\ItemCodeCategory;
use App\Core\Data\Models\ItemCommodityCodeType;
use App\Core\Data\Models\ItemIdentificationType;
use App\Core\Data\Models\ItemItemIdentificationType;
use App\Core\Data\Models\ItemMarketplace;
use App\Core\Data\Models\ItemPurchasePrice;
use App\Core\Data\Models\ItemSalePrice;
use App\Core\Data\Models\ItemTaxCode;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\ExchangeRates\Contracts\ExchangeRatesServiceContract;
use DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class ItemRepository implements ItemRepositoryContract
{
    private Item $item;
    private CommodityCodeType $commodityCodeType;
    private ItemIdentificationType $itemIdentificationType;
    private ItemTaxCode $itemTaxCode;
    private ItemType $itemType;
    private ItemCodeCategory $itemCodeCategory;
    private CountryItemTaxCode $countryItemTaxCode;
    private ItemCommodityCodeType $itemCommodityCodeType;
    private ItemItemIdentificationType $itemItemIdentificationType;
    private ItemSalePrice $itemSalePrice;
    private ItemMarketplace $itemMarketplace;
    private ItemCategory $itemCategory;
    private ItemPurchasePrice $itemPurchasePrice;
    private ExchangeRatesServiceContract $exchangeRateService;
    private CountryRepository $countryRepo;

    public function __construct(
        Item $item,
        CommodityCodeType $commodityCodeType,
        ItemIdentificationType $itemIdentificationType,
        ItemTaxCode $itemTaxCode,
        ItemType $itemType,
        ItemCodeCategory $itemCodeCategory,
        CountryItemTaxCode $countryItemTaxCode,
        ItemCommodityCodeType $itemCommodityCodeType,
        ItemItemIdentificationType $itemItemIdentificationType,
        ItemPurchasePrice $itemPurchasePrice,
        ItemSalePrice $itemSalePrice,
        ItemMarketplace $itemMarketplace,
        ItemCategory $itemCategory,
        ExchangeRatesServiceContract $exchangeRateService,
        CountryRepository $countryRepo,
    ) {
        $this->item = $item;
        $this->commodityCodeType = $commodityCodeType;
        $this->itemIdentificationType = $itemIdentificationType;
        $this->itemTaxCode = $itemTaxCode;
        $this->itemType = $itemType;
        $this->itemCodeCategory = $itemCodeCategory;
        $this->countryItemTaxCode = $countryItemTaxCode;
        $this->itemCommodityCodeType = $itemCommodityCodeType;
        $this->itemItemIdentificationType = $itemItemIdentificationType;
        $this->itemSalePrice = $itemSalePrice;
        $this->itemMarketplace = $itemMarketplace;
        $this->itemCategory = $itemCategory;
        $this->itemPurchasePrice = $itemPurchasePrice;
        $this->exchangeRateService = $exchangeRateService;
        $this->countryRepo = $countryRepo;
    }

    /**
     * @inheritDoc
     */
    public function getItemById(int $itemId): ?Item
    {
        return $this->item->find($itemId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyItemModel(): Item
    {
        return $this->item->newInstance();
    }

    public function getAllItemTaxCodes(): Collection
    {
        return $this->itemTaxCode->all();
    }

    /**
     * @inheritDoc
     */
    public function getAllCountryItemTaxCodesInCountryPaginate(
        int $countryId,
        int $perPage = 40,
        ?string $search = null
    ): LengthAwarePaginator {
        $columns = [
            'country_item_tax_codes.*',
            'item_tax_codes.id as item_tax_codes_id',
            'item_tax_codes.code as code',
            'item_tax_codes.item_type_id as item_tax_codes_item_type_id',
            'countries.name as country_name',
            'vat_rate_types.name as vat_rate_types_name',
            'item_types.name as item_types_name',
            'item_code_categories.name as item_code_categories_name',
            'taxation_methods.name as taxation_methods_name',
            'item_tax_codes.description as item_tax_code_description'
        ];

        $query = $this->countryItemTaxCode
            ->select($columns)
            ->where('country_item_tax_codes.country_id', $countryId)
            ->whereNull('country_item_tax_codes.to_date');
        $query->join('item_tax_codes', 'country_item_tax_codes.item_tax_code_id', '=', 'item_tax_codes.id');
        $query->leftJoin('item_types', 'item_tax_codes.item_type_id', '=', 'item_types.id');
        $query->leftJoin('item_code_categories', 'item_tax_codes.item_code_category_id', '=', 'item_code_categories.id');
        $query->leftJoin('taxation_methods', 'item_tax_codes.taxation_method_id', '=', 'taxation_methods.id');
        $query->join('countries', 'country_item_tax_codes.country_id', '=', 'countries.id');
        $query->join('vat_rate_types', 'country_item_tax_codes.vat_rate_type_id', '=', 'vat_rate_types.id');
        if (!is_null($search)) {
            $query->where(function (Builder $query) use ($search) {

                $query->orwhere('item_tax_codes.description', 'ILIKE', '%' . $search . '%')
                    ->orWhere('item_tax_codes.code', 'ILIKE', '%' . $search . '%')
                    ->orWhere('vat_rate_types.name', 'ILIKE', '%' . $search . '%');
            });
        }
        $query->orderBy('country_item_tax_codes.from_date', 'desc')
            ->orderBy('country_item_tax_codes.id', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getAllItemTypes(): Collection
    {
        return $this->itemType->all();
    }

    /**
     * @inheritDoc
     */
    public function getAllItemCodeCategories(): Collection
    {
        return $this->itemCodeCategory
            ->orderBy('name')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCountryItemTaxCodes(): CountryItemTaxCode
    {
        return $this->countryItemTaxCode->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCountryItemTaxCodeById(int $countryItemTaxCodeId): ?CountryItemTaxCode
    {
        return $this->countryItemTaxCode->find($countryItemTaxCodeId);
    }

    /**
     * @inheritDoc
     */
    public function deleteCountryItemTaxCodeById(int $countryItemTaxCodeId): void
    {
        $this->countryItemTaxCode->destroy($countryItemTaxCodeId);
    }

    public function getCountryItemTaxCodeByCountryIdAndItemTaxCodeId(int $itemTaxCodeId, int $countryId, string $orderBy = 'from_date', string $direction = 'ASC'): Collection
    {
        return $this->countryItemTaxCode
            ->where('item_tax_code_id', '=', $itemTaxCodeId)
            ->where('country_id', '=', $countryId)
            ->orderBy($orderBy, $direction)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllItemIdentificationTypes(): Collection
    {
        return $this->itemIdentificationType
            ->orderBy('sequence')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllCommodityCodes(): Collection
    {
        return $this->commodityCodeType
            ->orderBy('sequence')
            ->get();
    }

    public function emptyItemCommodityCodeType(): ItemCommodityCodeType
    {
        return $this->itemCommodityCodeType->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getItemCommodityCodeTypeById(int $itemCommodityCodeTypeId): ?ItemCommodityCodeType
    {
        return $this->itemCommodityCodeType->find($itemCommodityCodeTypeId);
    }

    /**
     * @inheritDoc
     */
    public function getAllAvailableItemItemIdentificationTypes(): Collection
    {
        $itemIdentificationTypes = $this->getAllItemIdentificationTypes();

        $iIITypes = [];
        foreach ($itemIdentificationTypes as $itemIdentificationType) {
            $itemItemIdentificationType = $this->getEmptyItemItemIdentificationTypeModel();
            $itemItemIdentificationType->item_identification_type_id = $itemIdentificationType->id;

            $iIITypes[] = $itemItemIdentificationType;
        }

        $iIITypes = new EloquentCollection($iIITypes);
        $iIITypes->load('itemIdentificationType');

        return $iIITypes;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyItemItemIdentificationTypeModel(): ItemItemIdentificationType
    {
        return $this->itemItemIdentificationType->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getItemItemIdentificationTypeById(int $itemItemIdentificationTypeId): ?ItemItemIdentificationType
    {
        return $this->itemItemIdentificationType->find($itemItemIdentificationTypeId);
    }

    /**
     * @inheritDoc
     */
    public function deleteItemCommodityCodeTypeById(int $itemCommodityCodeTypeId): void
    {
        $this->itemCommodityCodeType->destroy($itemCommodityCodeTypeId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyItemPurchasePrice(): ItemPurchasePrice
    {
        return $this->itemPurchasePrice->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getItemPurchasePriceById(int $itemPurchasePriceId): ?ItemPurchasePrice
    {
        return $this->itemPurchasePrice->find($itemPurchasePriceId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyItemSalePrice(): ItemSalePrice
    {
        return $this->itemSalePrice->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyItemMarketplace(): ItemMarketplace
    {
        return $this->itemMarketplace->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getItemMarketplaceById(int $itemMarketplaceId): ?ItemMarketplace
    {
        return $this->itemMarketplace->find($itemMarketplaceId);
    }

    public function getAllItemsByCompanyId(int $companyId, ?int $marketplaceId = null): Collection
    {
        $q = $this->item
            ->join('item_marketplaces', 'items.id', '=', 'item_marketplaces.item_id')
            ->where('items.company_id', '=', $companyId);
        if (!is_null($marketplaceId)) {
            $q->where('item_marketplaces.marketplace_id', '=', $marketplaceId);
        }

        return $q->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllItemsPaginateByCompanyId(
        int $companyId,
        ?array $with = null,
        ?string $search = null,
        ?int $perPage = null,
    ): LengthAwarePaginator {
        $with = $with ?? [];
        $perPage = $perPage ?? config('evat.perPage');

        return $this->item
            ->with(['itemType', ...$with])
            ->where('items.company_id', $companyId)
            ->when($search, function ($q, $search) {
                $q->where(function ($q) use ($search) {
                    $q->where('items.name', 'ILIKE', '%' . $search . '%')
                        ->orWhereRelation('itemType', 'name', 'ILIKE', '%' . $search . '%');
                });
            })
            ->orderBy('name')
            ->paginate($perPage);
    }

    public function getItemSalePriceById(int $itemSalePriceId): ?ItemSalePrice
    {
        return $this->itemSalePrice->find($itemSalePriceId);
    }

    /**
     * @inheritDoc
     */
    public function deleteItemById(int $itemId): void
    {
        $this->item->destroy($itemId);
    }

    public function getEmptyItemCategoryModel(): ItemCategory
    {
        return $this->itemCategory->newInstance();
    }

    public function getProductCategories(?int $id = null, ?string $search = null): Collection
    {
        return $this->itemCategory
            ->with('parentCategory')
            ->withCount('subCategories')
            ->when($search, function ($q, $search) {
                $q->where('name', 'ILIKE', '%' . $search . '%');
            }, function ($q) use ($id) {
                $q->where('parent_category_id', $id);
            })
            ->get();
    }

    public function getProductCategoryParents(int $productCategoryId): array
    {
        $category = $this->itemCategory->withCount('subCategories')->where('id', $productCategoryId)->firstOrFail();
        $parents[] = $category;
        while ($category->parentCategory) {
            array_unshift($parents, $category->parentCategory()->withCount('subCategories')->first());
            $category = $category->parentCategory;
        }

        return $parents;
    }

    public function searchItemsPaginated(
        string $searchQuery,
        int $marketplaceId,
        int $invoiceCurrencyId,
        string $invoiceDate,
        int $companyId,
        ?int $itemTypeId = null
    ): LengthAwarePaginator {
        $select = [
            'items.*',
            'item_marketplaces.id as item_marketplaces_id'
        ];

        return $this->item
            ->select($select)
            ->with([
                'itemMarketplaceSalePrices',
                'itemMarketplaceSalePrices.currency',
                'itemMarketplaceSalePrices.itemMarketplace.marketplace',
            ])
            ->join('item_marketplaces', 'items.id', '=', 'item_marketplaces.item_id')
            ->join('item_sale_prices', 'item_marketplaces.id', '=', 'item_sale_prices.item_marketplace_id')
            ->join('marketplaces', 'marketplaces.id', '=', 'item_marketplaces.marketplace_id')
            ->where('items.company_id', $companyId)
            ->when(!is_null($itemTypeId), function (Builder $query, $itemTypeId) {
                $query->where('items.item_type_id', $itemTypeId);
            })
            ->where(function (Builder $query) use ($searchQuery) {
                $query->where('items.name', 'ilike', '%' . $searchQuery . '%')
                    ->orWhere('items.description', 'ilike', '%' . $searchQuery . '%')
                    ->orWhere('item_marketplaces.description', 'ilike', '%' . $searchQuery . '%')
                    ->orWhere('items.identifiers_text', 'ilike', '/%' . $searchQuery . '%/');
            })
            ->groupBy(
                'items.id',
                'items.company_id',
                'item_marketplaces.id',
                'item_marketplaces.marketplace_id',
            )
            ->orderBy(DB::raw('item_marketplaces.marketplace_id = ' . $marketplaceId), 'DESC')
            ->orderBy('items.description')
            ->orderBy('items.name')
            ->paginate(config('evat.perPage'))
            ->through(function ($item) use ($invoiceDate, $invoiceCurrencyId) {
                return $this->exchangeCurrencies($item, $invoiceDate, $invoiceCurrencyId);
            });
    }

    private function exchangeCurrencies(Item $item, string $invoiceDate, int $invoiceCurrencyId): Item
    {
        if ($item->itemMarketplaceSalePrices->isEmpty()) {
            return $item;
        }

        $salePrice = $item->itemMarketplaceSalePrices->first();

        if ($salePrice->currency_id === $invoiceCurrencyId) {
            return $item;
        }

        if ($salePrice->net_price > 0) {
            $exchangedPrice = $this->exchangeToInvoiceCurrency(
                $invoiceDate,
                $salePrice->currency_id,
                $invoiceCurrencyId,
                $salePrice->net_price);

            $salePrice->net_price = round($exchangedPrice, 2, PHP_ROUND_HALF_EVEN);
        } else {
            $exchangedPrice = $this->exchangeToInvoiceCurrency(
                $invoiceDate,
                $salePrice->currency_id,
                $invoiceCurrencyId,
                $salePrice->gross_price
            );

            $salePrice->gross_price = round($exchangedPrice, 2, PHP_ROUND_HALF_EVEN);
        }

        return $item;
    }

    /** @noinspection PhpUndefinedMethodInspection */
    private function exchangeToInvoiceCurrency(string $invoiceDate, int $exchangeFromCurrencyId, int $exchangeToCurrencyId, float $value): float
    {
        if ($exchangeFromCurrencyId === $exchangeToCurrencyId) {
            return $value;
        }

        $exchangeRates = $this->exchangeRateService->getExchangeRatesForDates([$invoiceDate]);
        $exchangeFromCurrency = $exchangeRates->first()->where('currency_id', $exchangeFromCurrencyId)->first();
        $exchangeToCurrency = $exchangeRates->first()->where('currency_id', $exchangeToCurrencyId)->first();

        return $this->exchangeRateService->convertCurrencies($value, $exchangeFromCurrency, $exchangeToCurrency);
    }

    public function getItemsBySkusAndCompaniesIds(array $skus): Collection
    {
        $query = $this->item;
        foreach ($skus as $sku) {
            $query = $query->orWhere(function (Builder $builder) use ($sku) {
                $builder->where('company_id', $sku['companyId'])
                    ->where('identifiers_text', 'ILIKE', '%/' . $sku['sku'] . '/%');
            });
        }

        return $query->get();
    }

    public function getItemsByAsinsAndCompaniesIds(array $asins): Collection
    {
        $query = $this->item;
        foreach ($asins as $asin) {
            $query = $query->orWhere(function (Builder $builder) use ($asin) {
                $builder->where('company_id', $asin['companyId'])
                    ->where('identifiers_text', 'ILIKE', '%/' . $asin['asin'] . '/%');
            });
        }

        return $query->get();
    }

    public function deleteItemMarketplaceById(int $id, Item $item): ?bool
    {
        return $item->itemMarketplaces()->find($id)?->delete();
    }

    public function recreateTaxCodeForAllCountries(
        int $taxCodeId,
        ?int $vatRateTypeId = null,
        ?string $vatFromDate = null,
        ?string $vatToDate = null
    ): void {
        $allCountryIds = $this->countryRepo->getAllCountries()->pluck('id');
        $insertData = [];

        foreach ($allCountryIds as $countryId) {
            $insertData[] = [
                'country_id'       => $countryId,
                'item_tax_code_id' => $taxCodeId,
                'vat_rate_type_id' => $vatRateTypeId,
                'from_date'        => $vatFromDate,
                'to_date'          => $vatToDate
            ];
        }

        $this->countryItemTaxCode->insert($insertData);
    }

    /**
     * @inheritDoc
     */
    public function deleteNotConnectedItemsByAmazonReportId(int $amazonReportId): void
    {
        $this->item
            ->from('items')
            ->leftJoin('invoice_items', 'items.id', '=', 'invoice_items.item_id')
            ->where('items.resource', AmazonReport::class)
            ->where('items.resource_id', $amazonReportId)
            ->whereNull('invoice_items.id')
            ->delete();
    }

    /**
     * @inheritDoc
     */
    public function getHistoryVatRate(int $countryId, ?int $itemTaxCodeId = null): Collection
    {
        return $this->countryItemTaxCode
            ->where('item_tax_code_id', $itemTaxCodeId)
            ->where('country_id', $countryId)
            ->whereNotNull('to_date')
            ->get();
    }

    public function getItemTaxCodesWithTaxRateForCountryOnDate(int $countryId, string $date, ?int $itemTypeId = null): EloquentCollection
    {
        $q = $this->itemTaxCode->select([
            'item_tax_codes.*',
            'country_vat_rates.value AS rate'
        ])->join('country_item_tax_codes', function (JoinClause $join) use ($countryId, $date) {
            $join->on('item_tax_codes.id', '=', 'country_item_tax_codes.item_tax_code_id')
                ->where('country_item_tax_codes.country_id', $countryId)
                ->where('country_item_tax_codes.from_date', '<=', $date)
                ->where(function (JoinClause $join) use ($date) {
                    $join->whereNull('country_item_tax_codes.to_date')
                        ->orWhere('country_item_tax_codes.to_date', '>', $date);
                });
        })->join('country_vat_rates', function (JoinClause $join) use ($countryId, $date) {
            $join->on('country_item_tax_codes.country_id', '=', 'country_vat_rates.country_id')
                ->whereColumn('country_item_tax_codes.vat_rate_type_id', '=', 'country_vat_rates.vat_rate_type_id')
                ->where('country_vat_rates.effective_from', '<=', $date)
                ->where(function (JoinClause $join) use ($date) {
                    $join->whereNull('country_vat_rates.end_date')
                        ->orWhere('country_vat_rates.end_date', '>', $date);
                });
        })
            ->orderBy('item_tax_codes.id')
            ->withCasts(['rate' => 'float']);

        if ($itemTypeId !== null) {
            $q->where('item_tax_codes.item_type_id', $itemTypeId);
        }

        return $q->get();
    }

    public function getItemTaxCodeById(int $id): ?ItemTaxCode
    {
        return $this->itemTaxCode->find($id);
    }
}
