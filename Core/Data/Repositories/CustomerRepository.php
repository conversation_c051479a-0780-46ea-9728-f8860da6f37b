<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Customer;
use App\Core\Data\Models\CustomerAddress;
use App\Core\Data\Models\IdentificationNumber;
use App\Core\Data\Repositories\Contracts\CustomerRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class CustomerRepository implements CustomerRepositoryContract
{
    private Customer $customer;
    private CustomerAddress $customerAddress;
    private IdentificationNumber $identificationNumber;

    public function __construct(
        Customer $customer,
        CustomerAddress $customerAddress,
        IdentificationNumber $identificationNumber
    ) {
        $this->customer = $customer;
        $this->customerAddress = $customerAddress;
        $this->identificationNumber = $identificationNumber;
    }

    /**
     * @inheritDoc
     */
    public function getCustomerById(int $customerId): ?Customer
    {
        return $this->customer->find($customerId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCustomerModel(): Customer
    {
        return $this->customer->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCustomersByCompanyId(?int $companyId): Collection
    {
        return $this->customer->where('company_id', '=', $companyId)->get();
    }

    /**
     * @inheritDoc
     */
    public function getCustomers(string $search): Collection
    {
        return $this->customer
            ->where('company_name', 'ILIKE', '%' . $search . '%')
            ->get();
    }

    public function getCustomersByCompanyIdSearch(int $companyId, string $searchTerm): LengthAwarePaginator
    {
        return $this->customer
            ->where('company_id', $companyId)
            ->where(function ($query) use ($searchTerm) {
                $query->where('company_name', 'ilike', '%' . $searchTerm . '%')
                    ->orWhere('first_name', 'ilike', '%' . $searchTerm . '%')
                    ->orWhere('last_name', 'ilike', '%' . $searchTerm . '%');
            })
            ->with([
                'customerAddresses.addressType',
                'customerAddresses' => function ($query) {
                    $query->addSelect(['vat_number' => $this->identificationNumber
                        ->select('value')
                        ->where('resource', '=', Customer::class)
                        ->whereColumn('resource_id', 'customers_addresses.customer_id')
                        ->take(1)
                    ])->with('address');
                }])
            ->paginate(config('evat.perPage'));
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCustomerAddressModel(): CustomerAddress
    {
        return $this->customerAddress->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCustomerAddressById(int $customerAddressId): ?CustomerAddress
    {
        return $this->customerAddress->find($customerAddressId);
    }

    /**
     * @inheritDoc
     */
    public function deleteCustomerAddressById(int $customerAddressId): void
    {
        $this->customerAddress->destroy($customerAddressId);
    }

    /**
     * @inheritDoc
     */
    public function getAllCustomersPaginate(
        ?int $companyId,
        int $perPage = 40
    ): LengthAwarePaginator {
        $q = $this->customer
            ->where('company_id', '=', $companyId)
            ->orderBy('id', 'DESC');

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function deleteCustomerById(int $customerId): void
    {
        $this->customer->destroy($customerId);
    }

    /**
     * @inheritDoc
     */
    public function getCustomersAddressesByCustomerId(int $customerId): Collection
    {
        return $this->customerAddress->where('customer_id', $customerId)->get();
    }

    public function deleteCustomerIdentificationNumbersByCustomerId(int $customerId): void
    {
        $this->identificationNumber
            ->where('resource_id', $customerId)
            ->where('resource', Customer::class)
            ->delete();
    }
}
