<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\Waiver;
use App\Core\Data\Repositories\Contracts\WaiverRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class WaiverRepository implements WaiverRepositoryContract
{
    /**
     * @var Waiver
     */
    private $waiver;

    public function __construct(Waiver $waiver)
    {
        $this->waiver = $waiver;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(?int $companyId, ?int $countryId, int $perPage = 40): LengthAwarePaginator
    {
        $q = $this->waiver->with('company', 'country');
        if (!is_null($companyId)) {
            $q->where('company_id', '=', $companyId);
        }
        if (!is_null($countryId)) {
            $q->where('country_id', '=', $countryId);
        }

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getWaiverById(int $waiverId): ?Waiver
    {
        return $this->waiver->find($waiverId);
    }

    /**
     * @param int $waiverId
     */
    public function deleteWaiverById(int $waiverId): void
    {
        $this->waiver->destroy($waiverId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyWaiverModel(): Waiver
    {
        return $this->waiver->newInstance();
    }

}
