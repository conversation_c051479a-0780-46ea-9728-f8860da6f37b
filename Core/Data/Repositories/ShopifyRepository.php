<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ShopifyAccountInit;
use App\Core\Data\Models\ShopifyBulkFile;
use App\Core\Data\Models\ShopifyBulkFileStatus;
use App\Core\Data\Models\ShopifyBulkFileType;
use App\Core\Data\Models\ShopifyInstallRequest;
use App\Core\Data\Models\ShopifyMandatoryWebhookRequest;
use App\Core\Data\Models\ShopifyMandatoryWebhookRequestType;
use App\Core\Data\Repositories\Contracts\ShopifyRepositoryContract;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class ShopifyRepository implements ShopifyRepositoryContract
{
    /**
     * @var ShopifyBulkFile
     */
    private ShopifyBulkFile $shopifyBulkFile;

    /**
     * @var ShopifyBulkFileStatus
     */
    private ShopifyBulkFileStatus $shopifyBulkFileStatus;

    /**
     * @var ShopifyAccountInit
     */
    private ShopifyAccountInit $shopifyAccountInit;

    /**
     * @var ShopifyMandatoryWebhookRequest
     */
    private ShopifyMandatoryWebhookRequest $shopifyMandatoryWebhookRequest;

    /**
     * @var ShopifyMandatoryWebhookRequestType
     */
    private ShopifyMandatoryWebhookRequestType $shopifyMandatoryWebhookRequestType;

    /**
     * @var ShopifyInstallRequest
     */
    private ShopifyInstallRequest $shopifyInstallRequest;

    public function __construct(
        ShopifyBulkFile $shopifyBulkFile,
        ShopifyBulkFileStatus $shopifyBulkFileStatus,
        ShopifyAccountInit $shopifyAccountInit,
        ShopifyMandatoryWebhookRequest $shopifyMandatoryWebhookRequest,
        ShopifyMandatoryWebhookRequestType $shopifyMandatoryWebhookRequestType,
        ShopifyInstallRequest $shopifyInstallRequest
    )
    {
        $this->shopifyBulkFile = $shopifyBulkFile;
        $this->shopifyBulkFileStatus = $shopifyBulkFileStatus;
        $this->shopifyAccountInit = $shopifyAccountInit;
        $this->shopifyMandatoryWebhookRequest = $shopifyMandatoryWebhookRequest;
        $this->shopifyMandatoryWebhookRequestType = $shopifyMandatoryWebhookRequestType;
        $this->shopifyInstallRequest = $shopifyInstallRequest;
    }

    /**
     * @inheritDoc
     */
    public function getShopifyBulkFileById(int $shopifyBulkFileId): ?ShopifyBulkFile
    {
        return $this->shopifyBulkFile->find($shopifyBulkFileId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyShopifyBulkFileModel(): ShopifyBulkFile
    {
        return $this->shopifyBulkFile->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteShopifyBulkFileById(int $shopifyBulkFileId): void
    {
        $this->shopifyBulkFile->destroy($shopifyBulkFileId);
    }

    /**
     * @inheritDoc
     */
    public function getAllCompletedBulkFilesReadyForDownload(): Collection
    {
        return $this
            ->shopifyBulkFile
            ->where('status_id', ShopifyBulkFileStatus::REQUESTED)
            ->whereNotNull('download_url')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getShopifyBulkFilesForFileParsing(int $bulkFileTypeId): Collection
    {
        return $this
            ->shopifyBulkFile
            ->where('bulk_file_type_id', $bulkFileTypeId)
            ->where('status_id', ShopifyBulkFileStatus::DOWNLOADED)
            ->whereNull('file_parsing_started_at')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getShopifyBulkFilesForRawDataParsing(int $bulkFileTypeId): Collection
    {
        return $this
            ->shopifyBulkFile
            ->select('shopify_bulk_files.*')
            ->join('shopify_accounts_init', 'shopify_accounts_init.ecommerce_account_id', '=', 'shopify_bulk_files.ecommerce_account_id')
            ->where('bulk_file_type_id', $bulkFileTypeId)
            ->whereNotNull('shopify_accounts_init.warehouses_imported_at')
            ->whereNotNull('file_parsing_ended_at')
            ->whereNull('raw_data_parsing_started_at')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyShopifyAccountInitModel(): ShopifyAccountInit
    {
        return $this->shopifyAccountInit->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getAllBulkFileStatuses(): Collection
    {
        return $this->shopifyBulkFileStatus->all();
    }

    /**
     * @inheritDoc
     */
    public function getAllShopifyBulkOrdersFilesPaginate(
        ?array $companyIds = null,
        ?int $yearId = null,
        ?int $perPage = 40,
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $ecommerceAccountId = null,
        ?int $bulkFileStatusId = null
    ): LengthAwarePaginator
    {
        $columns = [
            'shopify_bulk_files.*',
            'companies.id as company_id',
            'companies.partner_code',
            'companies.full_legal_name',
            'ecommerce_accounts.uaid as shop_name',
            'users.first_name as user_first_name',
            'users.last_name as user_last_name'
        ];

        $q = $this->shopifyBulkFile
            ->select($columns)
            ->join('users', 'users.id', '=', 'shopify_bulk_files.user_id')
            ->join('ecommerce_accounts', 'ecommerce_accounts.id', '=', 'shopify_bulk_files.ecommerce_account_id')
            ->join('companies', 'companies.id', '=', 'ecommerce_accounts.company_id');

        $with = [
            'status',
            'file'
        ];

        $q->with($with);

        if (!is_null($companyIds)) {
            $q->whereIn('companies.id', $companyIds);
        }

        if (!is_null($yearId)) {
            $start = Carbon::parse($yearId . '-01-01')->year;
            $q->where(function ($subQuery) use ($start) {
                $subQuery->whereYear('shopify_bulk_files.start_date', '<=', $start)
                    ->whereYear('shopify_bulk_files.end_date', '>=', $start);
            });
        }

        if (!is_null($startDate)) {
            $q->where('shopify_bulk_files.end_date', '>=', $startDate);
        }

        if (!is_null($endDate)) {
            $q->where('shopify_bulk_files.start_date', '<=', $endDate);
        }

        if (!is_null($ecommerceAccountId)) {
            $q->where('ecommerce_accounts.id', $ecommerceAccountId);
        }

        if (!is_null($bulkFileStatusId)) {
            $q->where('shopify_bulk_files.status_id', $bulkFileStatusId);
        }

        return $q
            ->where('bulk_file_type_id', ShopifyBulkFileType::ORDERS)
            ->orderBy('shopify_bulk_files.raw_data_parsing_ended_at', 'DESC')
            ->orderBy('shopify_bulk_files.id', 'DESC')
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getShopifyBulkFileByBulkOperationId(string $bulkOperationId): ?ShopifyBulkFile
    {
        return $this->shopifyBulkFile
            ->where('bulk_operation_id', $bulkOperationId)
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getAllRequestedWithoutUrlBulkFiles(): Collection
    {
        return $this->shopifyBulkFile
            ->where('status_id', ShopifyBulkFileStatus::REQUESTED)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getOrderBulkFilesForExport(
        ?array $companyIds = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $userIds = null,
        ?array $bulkFileStatusIds = null
    ): Collection
    {
        $columns = [
            'shopify_bulk_files.*',
            'companies.id as company_id',
            'companies.partner_code',
            'companies.full_legal_name',
            'ecommerce_accounts.uaid as shop_name',
            'users.first_name as user_first_name',
            'users.last_name as user_last_name'
        ];

        $q = $this->shopifyBulkFile
            ->select($columns)
            ->join('users', 'users.id', '=', 'shopify_bulk_files.user_id')
            ->join('ecommerce_accounts', 'ecommerce_accounts.id', '=', 'shopify_bulk_files.ecommerce_account_id')
            ->join('companies', 'companies.id', '=', 'ecommerce_accounts.company_id');

        $with = [
            'status',
            'file'
        ];

        $q->with($with);

        if (!is_null($companyIds)) {
            $q->whereIn('companies.id', $companyIds);
        }

        if (!is_null($userIds)) {
            $q->whereIn('users.id', $userIds);
        }

        if (!is_null($bulkFileStatusIds)) {
            $q->whereIn('shopify_bulk_files.status_id', $bulkFileStatusIds);
        }

        if (!is_null($startDate)) {
            $q->where('shopify_bulk_files.end_date', '>=', $startDate);
        }

        if (!is_null($endDate)) {
            $q->where('shopify_bulk_files.start_date', '<=', $endDate);
        }

        return $q
            ->where('bulk_file_type_id', ShopifyBulkFileType::ORDERS)
            ->orderBy('shopify_bulk_files.raw_data_parsing_ended_at', 'DESC')
            ->orderBy('shopify_bulk_files.id', 'DESC')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getShopifyBulkOrdersFilesSalesChannels(
        string $search,
        ?array $companyIds = null
    ): Collection
    {
        $columns = [
            'ecommerce_accounts.uaid as ecommerce_account_name',
            'ecommerce_accounts.id as ecommerce_account_id'
        ];

        $q = $this->shopifyBulkFile
            ->select($columns)
            ->join('ecommerce_accounts', 'ecommerce_accounts.id', '=', 'shopify_bulk_files.ecommerce_account_id')
            ->join('companies', 'companies.id', '=', 'ecommerce_accounts.company_id');

        if (!is_null($companyIds)) {
            $q->whereIn('companies.id', $companyIds);
        }

        return $q
            ->where('bulk_file_type_id', ShopifyBulkFileType::ORDERS)
            ->groupBy(['ecommerce_accounts.id', 'ecommerce_accounts.uaid'])
            ->orderBy('ecommerce_accounts.uaid', 'DESC')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyShopifyMandatoryWebhookRequestModel(): ShopifyMandatoryWebhookRequest
    {
        return $this->shopifyMandatoryWebhookRequest->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getAllShopifyMandatoryWebhookRequests(
        bool $paginate = true,
        ?string $receivedBeforeDate = null,
        ?string $receivedAfterDate = null,
        ?int $requestTypeId = null,
        ?bool $completed = null,
        ?int $perPage = 40
    ): LengthAwarePaginator|Collection
    {
        $columns = [
            'shopify_mandatory_webhook_requests.*',
            'users.first_name as completion_user_first_name',
            'users.last_name as completion_user_last_name'
        ];

        $q = $this->shopifyMandatoryWebhookRequest
            ->select($columns)
            ->leftJoin('users', 'users.id', '=', 'shopify_mandatory_webhook_requests.completed_by_user_id');

        $with = [
            'type'
        ];

        $q->with($with);

        if (!is_null($receivedBeforeDate)) {
            $q->where('shopify_mandatory_webhook_requests.request_received_at', '<=', $receivedBeforeDate);
        }

        if (!is_null($receivedAfterDate)) {
            $q->where('shopify_mandatory_webhook_requests.request_received_at', '>=', $receivedAfterDate);
        }

        if (!is_null($requestTypeId)) {
            $q->where('shopify_mandatory_webhook_requests.type_id', $requestTypeId);
        }

        if (!is_null($completed)) {
            if ($completed) {
                $q->whereNotNull('shopify_mandatory_webhook_requests.request_completed_at');
            } else {
                $q->whereNull('shopify_mandatory_webhook_requests.request_completed_at');
            }
        }

        $q->orderBy('shopify_mandatory_webhook_requests.id', 'DESC');

        return $paginate ?
            $q->paginate($perPage) :
            $q->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllShopifyMandatoryWebhookRequestTypes(): Collection
    {
        return $this->shopifyMandatoryWebhookRequestType->all();
    }

    /**
     * @inheritDoc
     */
    public function getShopifyMandatoryWebhookRequestById(int $shopifyMandatoryWebhookRequestId): ?ShopifyMandatoryWebhookRequest
    {
        return $this->shopifyMandatoryWebhookRequest->find($shopifyMandatoryWebhookRequestId);
    }

    /**
     * @inheritDoc
     */
    public function getShopifyAccountInitModelBySalesChannelId(int $salesChannelId): ?ShopifyAccountInit
    {
        return $this->shopifyAccountInit->where('ecommerce_account_id', $salesChannelId)->first();
    }

    /**
     * @inheritDoc
     */
    public function getShopifyBulkFileModelBySalesChannelId(int $salesChannelId): ?ShopifyBulkFile
    {
        return $this->shopifyBulkFile->where('ecommerce_account_id', $salesChannelId)->first();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyShopifyInstallRequestModel(): ShopifyInstallRequest
    {
        return $this->shopifyInstallRequest->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function setShopifyAccessTokenForInstallRequestId(int $shopifyInstallRequestId, string $shopifyAccessToken): ?ShopifyInstallRequest
    {
        $installRequest = $this->shopifyInstallRequest->find($shopifyInstallRequestId);
        if (is_null($installRequest)) {
            return null;
        }
        $installRequest->shopify_access_token = $shopifyAccessToken;
        $installRequest->save();

        return $installRequest;
    }

    /**
     * @inheritDoc
     */
    public function getShopifyInstallRequestByShopName(string $shopName): ?ShopifyInstallRequest
    {
        return $this->shopifyInstallRequest
            ->where('shop_name', $shopName)
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getShopifyInstallRequestById(int $installRequestId): ?ShopifyInstallRequest
    {
        return $this->shopifyInstallRequest->find($installRequestId);
    }

    /**
     * @throws Exception
     */
    public function getShopOwnerEmailAddress(ShopifyMandatoryWebhookRequest $webhookRequest): string
    {
        $shopUrl = $webhookRequest->webhook_payload['shop_domain'];
        $shopName = explode('.', $shopUrl)[0];
        $apiUrl = 'https://' . $shopUrl . '/admin/api/2022-10/shop.json';

        /** @var ShopifyInstallRequest $shopifyInstallRequest */
        $shopifyInstallRequest = $this->getShopifyInstallRequestByShopName($shopName);
        $shopAccessToken = null;

        if (!is_null($shopifyInstallRequest)) {
            $shopAccessToken = $shopifyInstallRequest->shopify_access_token;
        }

        if (is_null($shopAccessToken)) {
            throw new Exception('Shop access token is missing for shop name: ' . $shopName);
        }

        $response = Http::withHeaders([
            config('evat.shopifyApi.accessTokenRequestHeader') => $shopAccessToken,
            'Content-Type' => 'application/json'
        ])->get($apiUrl, ['fields' => 'email']);

        $responseArray = json_decode($response->body(), true);

        if (!isset($responseArray['shop']) || !isset($responseArray['shop']['email'])) {
            throw new Exception('Shop API response format invalid.');
        }

        return $responseArray['shop']['email'];
    }

    /**
     * @throws Exception
     */
    public function getCustomerShopifyData(ShopifyMandatoryWebhookRequest $webhookRequest): array
    {
        $customerData = [
            'shopOwnerEmail' => $this->getShopOwnerEmailAddress($webhookRequest),
            'data'           => []
        ];

        $allCustomerData = $this->getAllShopifyMandatoryWebhookRequests(false, null, null, ShopifyMandatoryWebhookRequestType::CUSTOMERS_DATA_REQUEST);

        /** @var ShopifyMandatoryWebhookRequest $row */
        foreach ($allCustomerData as $row) {
            if (
                isset($row->webhook_payload['customer']) && isset($webhookRequest->webhook_payload['customer']) &&
                $row->webhook_payload['customer']['email'] === $webhookRequest->webhook_payload['customer']['email']
            ) {
                $customerData['data'] = $row->webhook_payload;
                break;
            }
        }

        return $customerData;
    }

    /**
     * @throws Exception
     */
    public function deleteShopifyCustomerData(ShopifyMandatoryWebhookRequest $webhookRequest): void
    {
        $allCustomerData = $this->getAllShopifyMandatoryWebhookRequests(false);

        /** @var ShopifyMandatoryWebhookRequest $row */
        foreach ($allCustomerData as $row) {
            if (
                isset($row->webhook_payload['customer']) && isset($webhookRequest->webhook_payload['customer']) &&
                $row->webhook_payload['customer']['email'] === $webhookRequest->webhook_payload['customer']['email']
            ) {
                $removeCustomerData = $row->webhook_payload;
                $removeCustomerData['customer'] = null;

                $row->webhook_payload = $removeCustomerData;
                $row->save();
            }
        }
    }
}

