<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\IdentificationNumber;
use App\Core\Data\Models\Supplier;
use App\Core\Data\Models\SupplierAddress;
use App\Core\Data\Repositories\Contracts\SupplierRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class SupplierRepository implements SupplierRepositoryContract
{
    private Supplier $supplier;
    private SupplierAddress $supplierAddress;
    private IdentificationNumber $identificationNumber;

    public function __construct(
        Supplier $supplier,
        SupplierAddress $supplierAddress,
        IdentificationNumber $identificationNumber
    ) {
        $this->supplier = $supplier;
        $this->supplierAddress = $supplierAddress;
        $this->identificationNumber = $identificationNumber;
    }

    /**
     * @inheritDoc
     */
    public function getSupplierById(int $supplierId): ?Supplier
    {
        return $this->supplier->find($supplierId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptySupplierModel(): Supplier
    {
        return $this->supplier->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getEmptySupplierAddressModel(): SupplierAddress
    {
        return $this->supplierAddress->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getSupplierAddressById(int $supplierAddressId): ?SupplierAddress
    {
        return $this->supplierAddress->find($supplierAddressId);
    }

    /**
     * @inheritDoc
     */
    public function deleteSupplierAddressById(int $supplierAddressId): void
    {
        $this->supplierAddress->destroy($supplierAddressId);
    }

    /**
     * @inheritDoc
     */
    public function getAllSuppliersPaginate(
        ?int $companyId,
        int $perPage = 40
    ): LengthAwarePaginator {
        $q = $this->supplier
            ->where('company_id', '=', $companyId)
            ->orWhere('is_global', '=', true)
            ->orderBy('is_global', 'DESC')
            ->orderByRaw("coalesce(company_name, (first_name || ' ' || last_name)) asc");

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getSupplierAddressesBySupplierId(int $supplierId): Collection
    {
        return $this->supplierAddress->where('supplier_id', $supplierId)->get();
    }

    /**
     * @inheritDoc
     */
    public function deleteSupplierById(int $supplierId): void
    {
        $this->supplier->destroy($supplierId);
    }

    public function getSuppliers(string $search, int $companyId): EloquentCollection
    {
        $columns = [
            'suppliers.*'
        ];

        return $this->supplier
            ->select($columns)
            ->join('suppliers_addresses', 'suppliers.id', '=', 'suppliers_addresses.supplier_id')
            ->join('addresses', 'suppliers_addresses.address_id', '=', 'addresses.id')
            ->leftJoin('identification_numbers', function (JoinClause $join) {
                $join->on('suppliers.id', '=', 'identification_numbers.resource_id')
                    ->where('identification_numbers.resource', Supplier::class);
            })
            ->where('suppliers.company_id', $companyId)
            ->where(function (Builder $query) use ($search) {
                $searchTerm = '%' . $search . '%';

                $query->where('suppliers.company_name', 'ILIKE', $searchTerm)
                    ->orWhere('suppliers.first_name', 'ILIKE', $searchTerm)
                    ->orWhere('suppliers.last_name', 'ILIKE', $searchTerm)
                    ->orWhere('suppliers.customer_number', 'ILIKE', $searchTerm)
                    ->orWhere('addresses.street', 'ILIKE', $searchTerm)
                    ->orWhere('addresses.postal_code', 'ILIKE', $searchTerm)
                    ->orWhere('identification_numbers.value', 'ILIKE', $searchTerm);
            })
            ->get();
    }

    public function deleteSuppliersIdentificationNumbersBySupplierId(int $supplierId): void
    {
        $this->identificationNumber
            ->where('resource_id', $supplierId)
            ->where('resource', Supplier::class)
            ->delete();
    }
}
