<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\DocumentCategory;
use App\Core\Data\Models\DocumentCategoryCountry;
use App\Core\Data\Models\DocumentCategoryOptions;
use App\Core\Data\Models\DocumentCategoryPlatform;
use App\Core\Data\Models\Platform;
use App\Core\Data\Repositories\Contracts\DocumentCategoryRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class DocumentCategoryRepository implements DocumentCategoryRepositoryContract
{
    private DocumentCategory $documentCategory;
    private DocumentCategoryOptions $documentCategoryOptions;
    private DocumentCategoryPlatform $documentCategoryPlatform;
    private DocumentCategoryCountry $documentCategoryCountry;

    public function __construct(
        DocumentCategory $documentCategory,
        DocumentCategoryOptions $documentCategoryOptions,
        DocumentCategoryPlatform $documentCategoryPlatform,
        DocumentCategoryCountry $documentCategoryCountry
    ) {
        $this->documentCategory = $documentCategory;
        $this->documentCategoryOptions = $documentCategoryOptions;
        $this->documentCategoryPlatform = $documentCategoryPlatform;
        $this->documentCategoryCountry = $documentCategoryCountry;
    }

    public function getDocumentCategoryById(int $id): ?DocumentCategory
    {
        return $this->documentCategory->find($id);
    }

    // NEW
    public function getParentDocumentCategories(?array $ids = null, bool $showHidden = false): Collection
    {
        $q = $this->documentCategory
            ->whereNull('parent_category_id')
            ->orderBy('sequence');

        if (!is_null($ids)) {
            $q->whereIn('id', $ids);
        }

        if (!$showHidden) {
            $q->where('is_visible', true);
        }

        return $q->get();
    }

    public function getAllChildDocumentCategories(?int $parentCategoryId = null, ?string $search = null): Collection
    {
        $query = $this->getAllChildDocumentCategoriesQuery($search);
        if (is_null($search) && !is_null($parentCategoryId)) {
            $query->where('parent_category_id', $parentCategoryId);
        }

        return $query->get();
    }

    public function getAllChildDocumentCategoriesSearch(string $search, int $limit = 10): LengthAwarePaginator
    {
        return $this->getAllChildDocumentCategoriesQuery($search)
            ->with('parentDocumentCategory')
            ->paginate($limit);
    }

    public function getAllChildDocumentCategoriesQuery(?string $search = null): Builder|QueryBuilder
    {
        $query = $this->documentCategory
            ->whereNotNull('parent_category_id')
            ->orderBy('sequence');

        if (!is_null($search)) {
            $query->where(function (Builder $builder) use ($search) {
                $builder->where('name', 'ILIKE', '%' . $search . '%');
            });
        }

        return $query;
    }

    public function getEmptyDocumentCategoryModel(): DocumentCategory
    {
        return $this->documentCategory->newInstance();
    }

    public function getEmptyDocumentCategoryOptionsModel(): DocumentCategoryOptions
    {
        return $this->documentCategoryOptions->newInstance();
    }

    public function getLatestSequence(?int $parentCategoryId = null): int
    {
        return $this->documentCategory
            ->where('parent_category_id', $parentCategoryId)
            ->orderBy('sequence', 'DESC')
            ->first()?->sequence ?? 0;
    }

    public function insertDocumentCategoryPlatforms(array $insert): void
    {
        $this->documentCategoryPlatform->insert($insert);
    }

    public function deleteDocumentCategoryPlatforms(array $documentCategoryPlatformsIds): void
    {
        $this->documentCategoryPlatform->whereIn('id', $documentCategoryPlatformsIds)->delete();
    }

    public function insertDocumentCategoryCountries(array $insert): void
    {
        $this->documentCategoryCountry->insert($insert);
    }

    public function deleteDocumentCategoryCountries(array $documentCategoryCountriesIds): void
    {
        $this->documentCategoryCountry->whereIn('id', $documentCategoryCountriesIds)->delete();
    }

    public function getEmptyDocumentCategoryCountryModel(): DocumentCategoryCountry
    {
        return $this->documentCategoryCountry->newInstance();
    }

    public function getEmptyDocumentCategoryPlatformModel(): DocumentCategoryPlatform
    {
        return $this->documentCategoryPlatform->newInstance();
    }

    public function deleteDocumentCategory(int $categoryId): void
    {
        $this->documentCategory->where('id', $categoryId)->delete();
    }

    public function getAllDocumentCategoriesForPlatforms(array $platformsIds, ?int $parentCategoryId = null): Collection
    {
        $q = $this->documentCategory->select([
            'document_categories.*',
            'document_category_platforms.platform_id AS platform_id'
        ])
            ->join('document_category_platforms', 'document_category_platforms.document_category_id', '=', 'document_categories.id')
            ->orderBy('document_categories.sequence')
            ->orderBy('document_categories.name')
            ->whereIn('document_category_platforms.platform_id', $platformsIds);

        if (!is_null($parentCategoryId)) {
            $q->where('document_categories.parent_category_id', $parentCategoryId);
        }

        return $q->get();
    }

    public function getAllDocumentCategoriesForCompanyCountryByCountries(int $companyCountryId, array $vatRegistrationCountriesIds, ?int $parentCategoryId = null): Collection
    {
        $companyCountriesIds = [Country::ALL, $companyCountryId];
        $vatRegistrationCountriesIds[] = Country::ALL;
        $q = $this->documentCategory->select([
            'document_categories.*',
            DB::raw('null as platform_id')
        ])->join('document_category_countries AS dcc', function (JoinClause $join) {
            $join->on('document_categories.id', '=', 'dcc.document_category_id')
                ->where('dcc.is_company_country', true);
        })->join('document_category_countries AS dcc2', function (JoinClause $join) {
            $join->on('dcc.document_category_id', '=', 'dcc2.document_category_id')
                ->whereColumn('dcc.id', '!=', 'dcc2.id')
                ->where('dcc2.is_company_country', false);
        })->leftJoin('document_category_platforms', function (JoinClause $join) {
            $join->on('document_categories.id', '=', 'document_category_platforms.document_category_id')
                ->where('document_category_platforms.platform_id', '!=', Platform::PLATFORM_ALL);
        })
            ->whereNull('document_category_platforms.id')
            ->where(function (Builder $query) use ($companyCountriesIds, $vatRegistrationCountriesIds) {
                $query->whereIn('dcc.country_id', $companyCountriesIds)
                    ->whereIn('dcc2.country_id', $vatRegistrationCountriesIds);
            })
            ->groupBy('document_categories.id')
            ->orderBy('document_categories.sequence')
            ->orderBy('document_categories.name');

        if (!is_null($parentCategoryId)) {
            $q->where('document_categories.parent_category_id', $parentCategoryId);
        }

        return $q->get();
    }
}
