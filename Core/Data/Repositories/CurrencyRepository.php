<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Currency;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use Illuminate\Support\Collection;

class CurrencyRepository implements CurrencyRepositoryContract
{
    private Currency $currency;

    public function __construct(Currency $currency)
    {
        $this->currency = $currency;
    }

    /**
     * @inheritDoc
     */
    public function getAllCurrencies(): Collection
    {
        return $this->currency->orderBy('code')->get();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCurrency(): Currency
    {
        return $this->currency->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCurrencyById(int $id): ?Currency
    {
        return $this->currency->find($id);
    }

    /**
     * @inheritDoc
     */
    public function getSafeEvatMoneyCurrencies(): Collection
    {
        return Currency::whereNotIn('code', ['BYR', 'RMB', 'IMP', 'BTC', 'GGP', 'JEP', 'ZMK'])->get();
    }
}
