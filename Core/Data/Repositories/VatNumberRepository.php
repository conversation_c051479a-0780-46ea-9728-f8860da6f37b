<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\VatNumber;
use App\Core\Data\Repositories\Contracts\VatNumberRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class VatNumberRepository implements VatNumberRepositoryContract
{
    private VatNumber $vatNumber;

    public function __construct(VatNumber $vatNumber)
    {
        $this->vatNumber = $vatNumber;
    }

    /**
     * @inheritdoc
     */
    public function exist(int $companyId, int $countryId): bool
    {
        return $this->vatNumber
            ->where('company_id', '=', $companyId)
            ->where('country_id', '=', $countryId)
            ->first()->isNotNull();
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(int $perPage = 40): LengthAwarePaginator
    {
        return $this->vatNumber->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyVatNumberModel(): VatNumber
    {
        return $this->vatNumber->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getVatNumberById(int $vatNumberId): ?VatNumber
    {
        return $this->vatNumber->find($vatNumberId);
    }

    /**
     * @param int $vatNumberId
     */
    public function deleteVatNumberById(int $vatNumberId): void
    {
        $this->vatNumber->destroy($vatNumberId);
    }

    /**
     * @inheritDoc
     */
    public function deleteVatNumbersByIds(array $ids): void
    {
        $this->vatNumber->whereIn('id', $ids)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getVatNumbersByNumbers(array $vatNumbers): EloquentCollection
    {
        return $this->vatNumber->whereIn('vat_number', $vatNumbers)->get();
    }

    public function getVatNumberByVatNumber(string $vatNumber): ?VatNumber
    {
        return $this->vatNumber->where('vat_number', $vatNumber)->first();
    }
}
