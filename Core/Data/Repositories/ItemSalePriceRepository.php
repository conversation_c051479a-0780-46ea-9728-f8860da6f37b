<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ItemSalePrice;
use App\Core\Data\Repositories\Contracts\ItemSalePriceRepositoryContract;

class ItemSalePriceRepository implements ItemSalePriceRepositoryContract
{
    public function __construct(private ItemSalePrice $itemSalePrice)
    {
    }

    public function deleteItemSalePriceById(int $id): void
    {
        $this->itemSalePrice->destroy($id);
    }
}