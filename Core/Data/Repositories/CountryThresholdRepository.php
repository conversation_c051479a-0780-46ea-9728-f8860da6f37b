<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CountryThreshold;
use App\Core\Data\Repositories\Contracts\CountryThresholdRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class CountryThresholdRepository implements CountryThresholdRepositoryContract
{
    /**
     * @var User
     */
    private $countryThreshold;

    public function __construct(CountryThreshold $countryThreshold)
    {
        $this->countryThreshold = $countryThreshold;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(?int $countryId, int $perPage = 40): LengthAwarePaginator
    {
        $q = $this->countryThreshold->with('country');
        if (!is_null($countryId)) {
            $q = $q->where('country_id', '=', $countryId);
        }
        $q = $q->orderBy('country_id');

        return $q->paginate($perPage);
    }


    /**
     * @inheritdoc
     */
    public function getAll(): Collection
    {
        return $this->countryThreshold->orderBy('country_id')->get();
    }

    /**
     * @return CountryThreshold
     */
    public function getEmptyCountryThresholdModel(): CountryThreshold
    {
        return $this->countryThreshold->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCountryThresholdById(int $countryThresholdId): ?CountryThreshold
    {
        return $this->countryThreshold->find($countryThresholdId);
    }

    /**
     * @inheritDoc
     */
    public function deleteCountryThresholdById(int $countryThresholdId): void
    {
        $countryThreshold = $this->getCountryThresholdById($countryThresholdId);
        if (!is_null($countryThreshold)) {
            $this->countryThreshold->destroy($countryThresholdId);
        }
    }

    public function getActiveCountryThresholds(int $yearId): Collection
    {
        return $this->countryThreshold
            ->whereYear('effective_from', '<=', $yearId)
            ->get()
            ->groupBy('country_id')
            ->transform(function ($item) {
                return $item->sortByDesc('effective_from')->first();
            });
    }

    public function getCountryThresholdByCountryAndYearId(int $countryId, int $yearId): ?CountryThreshold
    {
        return $this->countryThreshold
            ->whereYear('effective_from', '<=', $yearId)
            ->where('country_id', '=', $countryId)
            ->orderBy('effective_from', 'DESC')
            ->first();
    }
}
