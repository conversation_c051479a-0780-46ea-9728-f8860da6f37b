<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CompanyTaxPaymentData;
use App\Core\Data\Repositories\Contracts\CompanyTaxPaymentRepositoryContract;

class CompanyTaxPaymentRepository implements CompanyTaxPaymentRepositoryContract
{
    private CompanyTaxPaymentData $companyTaxPaymentData;

    /**
     * @param CompanyTaxPaymentData $companyTaxPaymentData
     */
    public function __construct(CompanyTaxPaymentData $companyTaxPaymentData)
    {
        $this->companyTaxPaymentData = $companyTaxPaymentData;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCompanyTaxPaymentModel(): CompanyTaxPaymentData
    {
        return $this->companyTaxPaymentData->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyTaxPaymentById(int $companyTaxPaymentId): ?CompanyTaxPaymentData
    {
        return $this->companyTaxPaymentData->find($companyTaxPaymentId);
    }
}
