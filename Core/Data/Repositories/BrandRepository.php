<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Brand;
use Illuminate\Support\Collection;
use App\Core\Data\Repositories\Contracts\BrandRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class BrandRepository implements BrandRepositoryContract
{
    private Brand $brand;

    public function __construct(Brand $brand)
    {
        $this->brand = $brand;
    }

    public function getAllBrands(): Collection
    {
        return $this->brand->all();
    }

    public function getAllPaginated(int $perPage = 40, ?string $search = null): LengthAwarePaginator
    {
        return $this->brand
            ->with('files')
            ->when($search, function ($q, $search) {
                $q->where('name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('description', 'ILIKE', '%' . $search . '%');
            })
            ->orderBy('name')
            ->paginate($perPage);
    }

    public function getEmptyBrandModel(): Brand
    {
        return $this->brand->newInstance();
    }

    public function getBrandById(int $brandId, array|string $relationships = null): ?Brand
    {
        return $this->brand->when(!is_null($relationships), function ($q) use ($relationships) {
            $q->with($relationships);
        })->where('id', $brandId)->first();
    }

    public function deleteBrandById(int $brandId): void
    {
        $this->brand->destroy($brandId);
    }
}
