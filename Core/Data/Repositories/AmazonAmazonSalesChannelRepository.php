<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\AmazonSalesChannel;
use App\Core\Data\Repositories\Contracts\AmazonSalesChannelRepositoryContract;
use Illuminate\Support\Collection;

class AmazonAmazonSalesChannelRepository implements AmazonSalesChannelRepositoryContract
{
    private AmazonSalesChannel $amazonSalesChannel;

    public function __construct(AmazonSalesChannel $amazonSalesChannel)
    {
        $this->amazonSalesChannel = $amazonSalesChannel;
    }

    /**
     * @inheritDoc
     */
    public function getAll(): Collection
    {
        return $this->amazonSalesChannel->all();
    }
}
