<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ProductType;
use App\Core\Data\Repositories\Contracts\ProductTypeRepositoryContract;
use Illuminate\Support\Collection;

class ProductTypeRepository implements ProductTypeRepositoryContract
{
    private ProductType $productType;

    public function __construct(ProductType $productType)
    {
        $this->productType = $productType;
    }

    public function getAllProductTypes(): Collection
    {
        return $this->productType->all();
    }
}
