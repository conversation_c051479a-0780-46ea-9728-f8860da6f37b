<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\VatReport;
use App\Core\Data\Repositories\Contracts\VatReportRepositoryContract;
use Carbon\Carbon;
use DB;
use Illuminate\Contracts\Pagination\LengthAwarePaginator as LengthAwarePaginatorContract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Pagination\LengthAwarePaginator;

class VatReportRepository implements VatReportRepositoryContract
{
    private VatReport $vatReport;

    public function __construct(VatReport $vatReport)
    {
        $this->vatReport = $vatReport;
    }

    public function getVatReportById(int $vatReportId): ?VatReport
    {
        return $this->vatReport->find($vatReportId);
    }

    public function getVatReportByIdAndCompanyCheck(int $vatReportId, array $companiesIds): ?VatReport
    {
        if (count($companiesIds) < 1) {
            return null;
        }

        return $this->vatReport
            ->where('id', $vatReportId)
            ->whereIn('company_id', $companiesIds)
            ->first();
    }

    public function getNewVatReportModel(): VatReport
    {
        return $this->vatReport->newInstance();
    }

    public function getAllVatReportsForCompanies(array $companiesIds, ?int $year = null): EloquentCollection
    {
        $query = $this->vatReport
            ->whereIn('company_id', $companiesIds);


        if (!is_null($year)) {
            $dateFrom = $year . '-01-01';
            $dateTo = $year . '-12-31';
            $query->where(function (Builder $q) use ($dateFrom, $dateTo) {

                $q->where(function (Builder $q) use ($dateFrom, $dateTo) {
                    $q->where('date_from', '>=', $dateFrom)
                        ->where('date_from', '<=', $dateTo);
                })->orWhere(function (Builder $q) use ($dateFrom, $dateTo) {

                    $q->where('date_to', '>=', $dateFrom)
                        ->where('date_to', '<=', $dateTo);
                });
            });
        }

        return $query->orderBy('date_from', 'ASC')->get();
    }

    public function insertVatReports(array $toInsert): void
    {
        if (count($toInsert) < 1) {
            return;
        }

        $this->vatReport->insert($toInsert);
    }

    public function massUpdateVatReportDataAndStatus(array $data): void
    {
        if (count($data) < 1) {
            return;
        }

        $table = $this->vatReport->getTable();

        $data = array_map(function (array $report) {
            $report['data'] = "'" . $report['data'] . "'";

            return '(' . implode(', ', $report) . ')';
        }, $data);
        $data = implode(', ', $data);
        $q = 'UPDATE ' . $table . ' SET vat_report_status_id = tmp.vat_report_status_id::int, data = tmp.data::jsonb FROM (VALUES ' . $data . ') as tmp (id, vat_report_status_id, data) where ' . $table . '.id = tmp.id';

        DB::statement($q);
    }

    public function getVatReportForCompanyByCountryAndDates(int $companyId, int $countryId, string $dateFrom, string $dateTo): ?VatReport
    {
        return $this->vatReport
            ->where('company_id', $companyId)
            ->where('country_id', $countryId)
            ->where('date_from', $dateFrom)
            ->where('date_to', $dateTo)
            ->first();
    }

    public function getVatReportsCountPerStatusByCompaniesIds(
        array $companiesIds,
        int $year,
        ?int $countryId = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): EloquentCollection {
        if (!is_null($dateTo) || !is_null($dateFrom)) {
            $year = null;
        }

        $q = $this->vatReport
            ->select('rs.id AS vat_report_status_id')
            ->addSelect(['count' => function (QueryBuilder $builder) use (
                $companiesIds,
                $year,
                $countryId,
                $companyId,
                $dateFrom,
                $dateTo
            ) {
                $builder->from('vat_reports')
                    ->selectRaw('count(*)')
                    ->whereColumn('vat_reports.vat_report_status_id', 'rs.id')
                    ->whereIn('vat_reports.company_id', $companiesIds);

                if (!is_null($year)) {
                    $builder->where('vat_reports.date_from', '>=', $year . '-01-01');
                }

                if (!is_null($companyId)) {
                    $builder->where('vat_reports.company_id', $companyId);
                }

                if (!is_null($countryId)) {
                    $builder->where('vat_reports.country_id', $countryId);
                }

                if (!is_null($dateFrom)) {
                    $builder->where('vat_reports.date_from', '>=', $dateFrom);
                }

                if (!is_null($dateTo)) {
                    $builder->where('vat_reports.date_from', '<=', $dateTo);
                }
            }])
            ->from('report_statuses AS rs')
            ->orderBy('rs.key');

        return $q->get();
    }

    public function getAllVatReportsForCompaniesPaginate(
        array $companiesIds,
        int $year,
        int $statusId,
        ?int $companyId = null,
        ?int $countryId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?string $search = null,
        ?int $perPage = null
    ): LengthAwarePaginatorContract|LengthAwarePaginator {
        $perPage = $perPage ?? 30;
        if (!is_null($search)) {
            $companyId = null;
            $countryId = null;
            $statusId = null;
            $dateFrom = null;
            $dateTo = null;
        }

        $dateFrom = Carbon::parse($dateFrom ?? ($year . '-01-01'));
        $dateTo = Carbon::parse($dateTo ?? ($year . '-12-31'));
        $yearDateFrom = Carbon::parse($year . '-01-01');
        $yearDateTo = $yearDateFrom->clone()->endOfYear();

        if ($dateFrom->lt($yearDateFrom)) {
            $dateFrom = $yearDateFrom;
        }
        if ($dateTo->gt($yearDateTo)) {
            $dateTo = $yearDateTo;
        }

        $query = $this->vatReport
            ->with([
                'company.partner',
                'company.taxNumbers',
                'company.accountants.institutionInstitutionType.institution',
                'country',
                'vatReportStatus',
            ])
            ->select(['vat_reports.*'])
            ->join('companies', 'vat_reports.company_id', '=', 'companies.id')
            ->join('countries', 'vat_reports.country_id', '=', 'countries.id')
            ->whereIn('vat_reports.company_id', $companiesIds)
            ->where('vat_reports.date_from', '>=', $dateFrom->toDateString())
            ->where('vat_reports.date_to', '<=', $dateTo->toDateString())
            ->where('vat_reports.vat_report_status_id', $statusId);

        if (!is_null($companyId)) {
            $query->where('vat_reports.company_id', $companyId);
        }

        if (!is_null($countryId)) {
            $query->where('vat_reports.country_id', $countryId);
        }

        if (!is_null($search)) {
            $query->where(function ($q) {
                dd($q);
            });
        }

        return $query->paginate($perPage);
    }

    public function getAllDistinctCountriesIdsByCompaniesIdsAndDates(array $companiesIds, string $dateFrom, string $dateTo): EloquentCollection
    {
        return $this->vatReport
            ->distinct()
            ->select([
                'vat_reports.country_id',
                'countries.name'
            ])
            ->join('countries', 'countries.id', '=', 'vat_reports.country_id')
            ->whereIn('vat_reports.company_id', $companiesIds)
            ->where('vat_reports.date_from', '>=', $dateFrom)
            ->where('vat_reports.date_to', '<=', $dateTo)
            ->orderBy('countries.name')
            ->get();
    }
}
