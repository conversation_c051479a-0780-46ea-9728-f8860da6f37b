<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\RequestLog;
use App\Core\Data\Repositories\Contracts\RequestLogRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class RequestLogRepository implements RequestLogRepositoryContract
{
    /**
     * @var RequestLog
     */
    private $requestLog;

    public function __construct(RequestLog $requestLog)
    {
        $this->requestLog = $requestLog;
    }

    /**
     * @inheritDoc
     */
    public function getNewRequestLogModel(): RequestLog
    {
        return $this->requestLog->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getGetPostCount(string $time): Collection
    {
        return $this->requestLog
            ->select([
                'rl.method',
                DB::raw('date(rl.timestamp) as "dateSegment"'),
                DB::raw('count(rl.id)'),
            ])
            ->from('request_log as rl')
            ->where('timestamp', '>=', $time)
            ->groupBy('rl.method')
            ->groupBy('dateSegment')
            ->orderBy('dateSegment')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getMostVisitedUrls(int $limit, ?string $dateFrom = null): Collection
    {
        $q = $this->requestLog
            ->select([
                'url',
                DB::raw('count(id) as cnt')
            ]);

        if (!is_null($dateFrom)) {
            $q->where('timestamp', '>=', $dateFrom);
        }

        return $q
            ->groupBy('url')
            ->orderBy('cnt', 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getMostUsedAgents(int $limit, ?string $dateFrom = null): Collection
    {
        $q = $this->requestLog
            ->select([
                'additional->userAgent as ua',
                DB::raw('count(id) as cnt')
            ]);

        if (!is_null($dateFrom)) {
            $q->where('timestamp', '>=', $dateFrom);
        }

        return $q->groupBy('ua')
            ->orderBy('cnt', 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAverageRequestTime(string $date): float
    {
        return $this->requestLog
            ->select([
                DB::raw('avg(duration_request) as avg')
            ])
            ->where('timestamp', '>=', $date)
            ->first()
            ->avg ?? 0.0;
    }

    /**
     * @inheritDoc
     */
    public function getMostMemoryConsumingUrls(int $limit, ?string $dateFrom = null): Collection
    {
        $q = $this->requestLog
            ->select(
                [
                    'url',
                    DB::raw('max(memory) as memory')
                ]
            );

        if (!is_null($dateFrom)) {
            $q->where('timestamp', '>=', $dateFrom);
        }

        return $q->groupBy('url')
            ->orderBy('memory', 'DESC')
            ->orderBy('url', 'ASC')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getMostQueriesConsumingUrls(int $limit, ?string $dateFrom = null): Collection
    {
        $q = $this->requestLog
            ->select(
                [
                    DB::raw("rtrim(url, '/0123456789') as url"),
                    DB::raw('max(queries) as queries')
                ]
            );

        if (!is_null($dateFrom)) {
            $q->where('timestamp', '>=', $dateFrom);
        }

        return $q->groupBy(DB::raw("rtrim(url, '/0123456789')"))
            ->orderBy('queries', 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getHardcoreUsers(int $limit, ?string $dateFrom = null): Collection
    {
        $q = $this->requestLog
            ->select(
                [
                    'users.email',
                    DB::raw('count(request_log.user_id) as count')
                ]
            )
            ->join('users', 'request_log.user_id', '=', 'users.id');

        if (!is_null($dateFrom)) {
            $q->where('request_log.timestamp', '>=', $dateFrom);
        }

        return $q->groupBy('request_log.user_id')
            ->groupBy('users.email')
            ->orderBy(DB::raw('count(request_log.user_id)'), 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getRequestsCount(?string $dateFrom = null): int
    {
        $q = $this->requestLog
            ->select([DB::raw('count(id) as count')]);

        if (!is_null($dateFrom)) {
            $q->where('timestamp', '>=', $dateFrom);
        }

        return $q->first()->count;
    }

    /**
     * @inheritDoc
     */
    public function getLongestLastingUrls(int $limit, ?string $dateFrom = null): Collection
    {
        $q = $this->requestLog
            ->select(
                [
                    'url',
                    'method',
                    DB::raw('duration_request as duration')
                ]
            );

        if (!is_null($dateFrom)) {
            $q->where('timestamp', '>=', $dateFrom);
        }

        return $q->orderBy('duration', 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getOnlineUsers(string $dateFrom): int
    {
        return $this->requestLog
            ->select([DB::raw('count(distinct user_id)')])
            ->where('timestamp', '>=', $dateFrom)
            ->whereNotNull('user_id')
            ->limit(5)
            ->first()->count;
    }

    /**
     * @inheritDoc
     */
    public function getRequestLogPaginated(): LengthAwarePaginator
    {
        $perPage = config('evat.perPage');
        $query = $this->requestLog
            ->orderBy('timestamp', 'DESC')
            ->paginate($perPage);

        return $query;
    }
}
