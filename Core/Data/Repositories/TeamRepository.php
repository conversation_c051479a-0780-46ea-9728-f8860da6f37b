<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Team;
use App\Core\Data\Models\UserTeam;
use App\Core\Data\Repositories\Contracts\TeamRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class TeamRepository implements TeamRepositoryContract
{
    private Team $team;
    private UserTeam $userTeam;

    public function __construct(Team $team, UserTeam $userTeam)
    {
        $this->team = $team;
        $this->userTeam = $userTeam;
    }

    /**
     * @inheritDoc
     */
    public function getAllTeamsPaginate(
        int $perPage = 40,
        ?array $with = null,
        ?int $institutionInstitutionTypeId = null,
        ?int $countryId = null,
        ?string $search = null,
        ?string $order = null,
        ?string $direction = null
    ): LengthAwarePaginator {
        $q = $this->getTeamsQuery(
            $with,
            $institutionInstitutionTypeId,
            $countryId,
            $search,
            $order,
            $direction
        );

        return $q->paginate($perPage);
    }

    private function getTeamsQuery(
        ?array $with = null,
        ?int $institutionInstitutionTypeId = null,
        ?int $countryId = null,
        ?string $search = null,
        ?string $order = null,
        ?string $direction = null
    ): Builder {
        if (is_null($with)) {
            $with = [];
        }

        $select = [
            'teams.*'
        ];
        $q = $this->team
            ->select($select)
            ->leftJoin('user_teams', 'user_teams.team_id', '=', 'teams.id')
            ->leftJoin('users', 'user_teams.user_id', '=', 'users.id')
            ->leftJoin('countries', 'countries.id', '=', 'teams.country_id')
            ->with($with);

        $orderBy = 'teams.name';
        $sortDirection = 'asc';

        if (!is_null($order)) {
            $orderBy = $order;
        }

        if (!is_null($direction)) {
            $sortDirection = $direction;
        }

        if (!is_null($institutionInstitutionTypeId)) {
            $q->where('teams.institution_institution_type_id', $institutionInstitutionTypeId);
        }

        if (!is_null($countryId)) {
            $q->where('teams.country_id', $countryId);
        }

        if (!is_null($search)) {
            $q->where(function (Builder $builder) use ($search) {
                $builder->where('users.first_name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('users.last_name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('users.email', 'ILIKE', '%' . $search . '%')
                    ->orWhere('teams.name', 'ILIKE', '%' . $search . '%');
            });
        }

        return $q
            ->groupBy('teams.id', 'countries.id')
            ->orderBy($orderBy, $sortDirection);
    }

    /**
     * @inheritDoc
     */
    public function getTeamById(int $teamId): ?Team
    {
        return $this->team->find($teamId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyTeamModel(): Team
    {
        return $this->team->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteTeamById(int $teamId): void
    {
        $this->team->destroy($teamId);
    }

    /**
     * @inheritDoc
     */
    public function insertTeamUsers(array $teamUsers): void
    {
        $this->userTeam->insert($teamUsers);
    }

    /**
     * @inheritDoc
     */
    public function deleteTeamUsersByIds(array $teamUsersIds): void
    {
        $this->userTeam->whereIn('id', $teamUsersIds)->delete();
    }

    /**
     * @inheritDoc
     */
    public function deleteTeamUsersByUserId(int $userId): void
    {
        $this->userTeam->where('user_id', $userId)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getTeamsByInstitutionInstitutionTypeId(int $institutionInstitutionTypeId): Collection
    {
        return $this->team
            ->where('institution_institution_type_id', '=', $institutionInstitutionTypeId)
            ->get();
    }
}
