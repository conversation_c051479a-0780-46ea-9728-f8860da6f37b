<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Data\Repositories\Contracts\TaxReportingSchemeRepositoryContract;
use Illuminate\Support\Collection;

class TaxReportingSchemeRepository implements TaxReportingSchemeRepositoryContract
{
    private TaxReportingScheme $taxReportingScheme;

    public function __construct(TaxReportingScheme $taxReportingScheme)
    {
        $this->taxReportingScheme = $taxReportingScheme;
    }

    /**
     * @inheritDoc
     */
    public function getAllForAmazon(): Collection
    {
        return $this->taxReportingScheme->whereNotNull('amazon_code')->get();
    }
}