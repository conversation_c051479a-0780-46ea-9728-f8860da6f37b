<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Document;
use App\Core\Data\Models\DocumentData;
use App\Core\Data\Models\DocumentDataField;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;
use App\Core\Data\Repositories\Contracts\DocumentRepositoryContract;

class DocumentRepository implements DocumentRepositoryContract
{
    private DocumentDataField $documentDataField;
    private Document $document;
    private DocumentData $documentData;

    public function __construct(
        Document $document,
        DocumentDataField $documentDataField,
        DocumentData $documentData
    ) {
        $this->documentDataField = $documentDataField;
        $this->document = $document;
        $this->documentData = $documentData;
    }

    public function getAllDocumentDataFields(?bool $userInput = null): Collection
    {
        $query = $this->documentDataField->orderBy('sequence');

        if (!is_null($userInput)) {
            $query->where('user_input', $userInput);
        }

        return $query->get();
    }

    public function getDocumentsForCompany(
        int $companyId,
        ?int $countryId = null,
        ?array $categoriesIds = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $resource = null,
        ?int $resourceId = null,
        ?array $showAllDocumentsFromParentCategoriesIds = null,
        bool $showHidden = false
    ): EloquentCollection {
        $q = $this->document
            ->select(['documents.*'])
            ->leftJoin('document_data', 'documents.id', '=', 'document_data.document_id')
            ->where('company_id', $companyId)
            ->orderBy('updated_at', 'DESC');

        if (!$showHidden) {
            $q->where('is_visible', true);
        }

        $q->where(function (Builder $query) use (
            $categoriesIds,
            $startDate,
            $endDate,
            $resource,
            $resourceId,
            $countryId,
            $showAllDocumentsFromParentCategoriesIds
        ) {
            $query->where(function (Builder $query) use (
                $categoriesIds,
                $startDate,
                $endDate,
                $resource,
                $resourceId,
                $countryId
            ) {
                if (!is_null($categoriesIds)) {
                    $query->whereIn('document_category_id', $categoriesIds);
                }

                if (!is_null($startDate)) {
                    $query->where('date', '>=', $startDate);
                }

                if (!is_null($endDate)) {
                    $query->where('date', '<=', $endDate);
                }

                if (!is_null($resource) && !is_null($resourceId)) {
                    $query->where('resource', $resource)
                        ->where('resource_id', $resourceId);
                }

                if (!is_null($countryId)) {
                    $query->where('document_data.document_data_field_id', DocumentDataField::FIELD_COUNTRY)
                        ->whereRaw('("document_data"."raw_value"->> \'id\')::int = ?', [$countryId]);
                }
            });

            if (!is_null($showAllDocumentsFromParentCategoriesIds)) {
                $query->orWhereIn('document_category_id', function (QueryBuilder $builder) use ($showAllDocumentsFromParentCategoriesIds) {
                    $builder->select(['id'])
                        ->from('document_categories')
                        ->whereIn('id', $showAllDocumentsFromParentCategoriesIds)
                        ->orWhereIn('parent_category_id', $showAllDocumentsFromParentCategoriesIds);
                });
            }
        });

        return $q->get();
    }

    public function getDocumentById(int $documentId): ?Document
    {
        return $this->document->find($documentId);
    }

    public function deleteAllDocumentDataByDocumentId(int $documentId): void
    {
        $this->documentData->where('document_id', $documentId)->delete();
    }

    public function insertDocumentData(array $data): void
    {
        $this->documentData->insert($data);
    }

    public function getEmptyDocumentModel(): Document
    {
        return $this->document->newInstance();
    }

    public function getEmptyDocumentDataModel(): DocumentData
    {
        return $this->documentData->newInstance();
    }
}
