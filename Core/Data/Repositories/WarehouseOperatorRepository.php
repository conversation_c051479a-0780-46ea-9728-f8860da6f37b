<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\WarehouseOperator;
use App\Core\Data\Repositories\Contracts\WarehouseOperatorRepositoryContract;

class WarehouseOperatorRepository implements WarehouseOperatorRepositoryContract
{
    /**
     * @var WarehouseOperator
     */
    private $warehouseOperator;

    public function __construct(WarehouseOperator $warehouseOperator)
    {
        $this->warehouseOperator = $warehouseOperator;
    }


    /**
     * @inheritDoc
     */
    public function getWarehouseOperatorById(int $warehouseOperatorId): ?WarehouseOperator
    {
        return $this->warehouseOperator->find($warehouseOperatorId);
    }

    /**
     * @inheritDoc
     */
    public function getWarehouseOperatorByName(string $name): ?WarehouseOperator
    {
        return $this->warehouseOperator->where('name', $name)->first();
    }

    /**
     * @param int $warehouseOperatorId
     */
    public function deleteWarehouseOperatorById(int $warehouseOperatorId): void
    {
        $this->warehouseOperator->destroy($warehouseOperatorId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyWarehouseOperatorModel(): WarehouseOperator
    {
        return $this->warehouseOperator->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function createWarehouseOperatorIfNotExists(string $name): WarehouseOperator
    {
        $name = trim($name);
        $name = mb_strtoupper($name);
        $warehouseOperator = $this->getWarehouseOperatorByName($name);
        if (!is_null($warehouseOperator)) {
            return $warehouseOperator;
        }
        $warehouseOperator = $this->getEmptyWarehouseOperatorModel();
        $warehouseOperator->name = $name;
        $warehouseOperator->save();

        return $warehouseOperator;
    }
}
