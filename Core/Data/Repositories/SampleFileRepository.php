<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\SampleFile;
use App\Core\Data\Models\SampleFileType;
use App\Core\Data\Repositories\Contracts\SampleFileRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class SampleFileRepository implements SampleFileRepositoryContract
{
    private SampleFile $sampleFile;
    private SampleFileType $sampleFileType;

    public function __construct(
        SampleFile $sampleFile,
        SampleFileType $sampleFileType
    ) {
        $this->sampleFile = $sampleFile;
        $this->sampleFileType = $sampleFileType;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(
        ?int $countryId = null,
        ?int $sampleFileId = null,
        ?array $with = null,
        int $perPage = 40
    ): LengthAwarePaginator {
        $select = [
            'sample_files.*',
            'sample_file_types.type as type_name',
        ];
        $q = $this->sampleFile
            ->select($select)
            ->leftJoin('sample_file_country', 'sample_file_country.sample_file_id', '=', 'sample_files.id')
            ->join('sample_file_types', 'sample_file_types.id', '=', 'sample_files.sample_file_type_id');

        if (!is_null($with)) {
            $q->with($with);
        }

        if (!is_null($sampleFileId)) {
            $q->where('sample_files.id', $sampleFileId);
        }

        if (is_null($sampleFileId)) {
            if (!is_null($countryId)) {
                $q->where(function ($q1) use ($countryId) {
                    return $q1->where('sample_file_country.country_id', $countryId)
                        ->orWhereNull('sample_file_country.country_id');
                });

            }
        }

        return $q
            ->groupBy('sample_files.id')
            ->groupBy('sample_file_types.type')
            ->orderBy('sample_file_types.type')
            ->paginate($perPage);
    }

    public function getSampleFileById(int $sampleFileId): ?SampleFile
    {
        return $this->sampleFile->find($sampleFileId);
    }

    public function deleteSampleFileById(int $sampleFileId): void
    {
        $this->sampleFile->destroy($sampleFileId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptySampleFileModel(): SampleFile
    {
        return $this->sampleFile->newInstance();
    }

    public function getSampleFileByTypeCountry(int $sampleFileTypeId, int $countryId): ?SampleFile
    {
        return $this->sampleFile
            ->select('sample_files.*')
            ->leftJoin('sample_file_country', 'sample_files.id', '=', 'sample_file_country.sample_file_id')
            ->where('sample_files.sample_file_type_id', '=', $sampleFileTypeId)
            ->where(function (Builder $query) use ($countryId) {
                $query->where('sample_file_country.country_id', '=', $countryId)
                    ->orWhereNull('sample_file_country.country_id');
            })
            ->orderBy('sample_files.id', 'DESC')
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getAllSampleFileTypes(): Collection
    {
        return $this->sampleFileType
            ->orderBy('type')
            ->get();
    }

    public function deleteSampleFileTypeById(int $sampleFileTypeId): void
    {
        $this->sampleFileType->destroy($sampleFileTypeId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptySampleFileTypeModel(): SampleFileType
    {
        return $this->sampleFileType->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getSampleFileTypeById(int $sampleFileTypeId): ?SampleFileType
    {
        return $this->sampleFileType->find($sampleFileTypeId);
    }

    /**
     * @inheritDoc
     */
    public function searchSampleFile(string $search, ?int $perPage = 40): LengthAwarePaginator
    {
        $select = [
            'sample_files.*',
            'sample_file_types.type as type_name'
        ];

        return $this->sampleFile
            ->select($select)
            ->join('sample_file_types', 'sample_file_types.id', '=', 'sample_files.sample_file_type_id')
            ->where('sample_file_types.type', 'ILIKE', '%' . $search . '%')
            ->groupBy('sample_file_types.type')
            ->groupBy('sample_files.id')
            ->orderBy('sample_file_types.type')
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function searchSampleFileTypes(string $search, ?int $perPage = 40): LengthAwarePaginator
    {
        return $this->sampleFileType
            ->where('type', 'ILIKE', $search . '%')
            ->orderBy('type')
            ->paginate($perPage);
    }
}
