<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Year;
use App\Core\Data\Repositories\Contracts\YearRepositoryContract;
use Illuminate\Support\Collection;

class YearRepository implements YearRepositoryContract
{
    /**
     * @var Year
     */
    private $year;

    public function __construct(Year $year)
    {
        $this->year = $year;
    }

    /**
     * @inheritDoc
     */
    public function getAllYears(?string $orderBy = 'value', ?string $direction = 'DESC'): Collection
    {
        return $this->year->orderBy($orderBy, $direction)->get();
    }

    /**
     * @inheritDoc
     */
    public function getLatestYear(): Year
    {
        return $this->year->orderBy('id', 'DESC')->first();
    }

    /**
     * @inheritDoc
     */
    public function getYearById(int $yearId): ?Year
    {
        return $this->year->find($yearId);
    }
}
