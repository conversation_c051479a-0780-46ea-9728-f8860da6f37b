<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ItemType;
use App\Core\Data\Repositories\Contracts\ItemTypeRepositoryContract;
use Illuminate\Support\Collection;

class ItemTypeRepository implements ItemTypeRepositoryContract
{
    protected $itemType;

    public function __construct(ItemType $itemType)
    {
        $this->itemType = $itemType;
    }

    public function getAllItemTypes(): Collection
    {
        return $this->itemType->get();
    }
}
