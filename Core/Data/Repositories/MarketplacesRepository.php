<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Marketplace;
use App\Core\Data\Models\Platform;
use App\Core\Data\Repositories\Contracts\MarketplacesRepositoryContract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class MarketplacesRepository implements MarketplacesRepositoryContract
{
    private Platform $platform;
    private Marketplace $marketplace;

    public function __construct(Platform $platform, Marketplace $marketplace)
    {
        $this->platform = $platform;
        $this->marketplace = $marketplace;
    }

    public function getAllPlatformsForCompanyById(int $companyId): Collection
    {
        return $this->platform
            ->select('platforms.*')
            ->distinct('platforms.id')
            ->join('marketplaces', 'platforms.id', '=', 'marketplaces.platform_id')
            ->leftJoin('ecommerce_accounts', 'ecommerce_accounts.marketplace_id', '=', 'marketplaces.id')
            ->where(function (Builder $query) use ($companyId) {
                $query->where('marketplaces.global', true)
                    ->orWhere('ecommerce_accounts.company_id', $companyId);
            })
            ->get();
    }

    public function getAllForAmazon(): Collection
    {
        return $this->marketplace->where('platform_id', Platform::PLATFORM_AMAZON)->get();
    }

    public function getAllPlatforms(): Collection
    {
        return $this->platform->orderBy('name')->get();
    }

    public function getEmptyMarketplaceModel(): Marketplace
    {
        return $this->marketplace->newInstance();
    }

    public function getAllMarketplacesByCompanyById(int $companyId): Collection
    {
        return $this->marketplace
            ->where('company_id', '=', $companyId)
            ->orWhereNull('company_id')
            ->get();
    }

    public function getMarketplaceById(int $marketplaceId): Marketplace
    {
        return $this->marketplace->find($marketplaceId);
    }

    public function getPlatformById(int $platformId): Platform
    {
        return $this->platform->find($platformId);
    }
}
