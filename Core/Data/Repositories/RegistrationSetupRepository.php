<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\RegistrationSetup;
use App\Core\Data\Repositories\Contracts\RegistrationSetupRepositoryContract;

class RegistrationSetupRepository implements RegistrationSetupRepositoryContract
{
    private RegistrationSetup $registrationSetup;

    public function __construct(RegistrationSetup $registrationSetup)
    {
        $this->registrationSetup = $registrationSetup;
    }

    public function getRegistrationSetupByKey(string $registrationSetupKey): ?RegistrationSetup
    {
        return $this->registrationSetup->where('key', $registrationSetupKey)->first();
    }
}
