<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Store;
use Illuminate\Support\Collection;
use App\Core\Data\Repositories\Contracts\StoreRepositoryContract;

class StoreRepository implements StoreRepositoryContract
{
    private Store $store;

    public function __construct(Store $store)
    {
        $this->store = $store;
    }

    /**
     * @inheritDoc
     */
    public function get($userId, ?string $key = null, ?string $channel = null): Collection
    {
        $query = $this->store->where('user_id', $userId);

        if (!is_null($key)) {
            $query = $query->where('key', $key);
        }
        if (!is_null($channel)) {
            $query = $query->where('channel', $channel);
        }

        return $query->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllKeys(): ?Collection
    {
        return $this->store->select('key')->distinct()->get();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyStoreModel(): Store
    {
        return $this->store->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getStoreById(int $storeId): ?Store
    {
        return $this->store->find($storeId);
    }

    /**
     * @inheritDoc
     */
    public function deleteAllByKeyAndUser(string $key, int $userId): void
    {
        $stores = $this->store->where('key', $key)->where('user_id', $userId)->get();
        /*
         *
         * When issuing a mass update or delete query via Eloquent, the saved, updated, deleting,
         * and deleted model events will not be dispatched for the affected models.
         * This is because the models are never actually retrieved when performing
         * mass updates or deletes.
         *
         */
        foreach ($stores as $store) {
            $store->delete();
        }
    }

    /**
     * @inheritDoc
     */
    public function deleteById(int $storeId): void
    {
        $this->store->where('id', $storeId)->delete();
    }

    /**
     * @inheritDoc
     */
    public function deleteAllByChannelAndUser(string $channel, int $userId): void
    {
        $this->store->where('channel', $channel)->where('user_id', $userId)->delete();
    }
}
