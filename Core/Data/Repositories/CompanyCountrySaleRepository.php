<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\CompanyCountrySale;
use App\Core\Data\Repositories\Contracts\CompanyCountrySaleRepositoryContract;

class CompanyCountrySaleRepository implements CompanyCountrySaleRepositoryContract
{
    /**
     * @var companyCountrySale
     */
    private $companyCountrySale;

    public function __construct(CompanyCountrySale $companyCountrySale)
    {
        $this->companyCountrySale = $companyCountrySale;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyCountrySaleById(int $companyCountrySaleId): ?CompanyCountrySale
    {
        return $this->companyCountrySale->find($companyCountrySaleId);
    }

    /**
     * @param int $companyCountrySaleId
     */
    public function deleteCompanyCountrySaleById(int $companyCountrySaleId): void
    {
        $this->companyCountrySale->destroy($companyCountrySaleId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCompanyCountrySaleModel(): CompanyCountrySale
    {
        return $this->companyCountrySale->newInstance();
    }

}