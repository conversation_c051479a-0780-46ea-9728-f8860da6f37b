<?php

namespace App\Core\Data\Repositories\Providers;

use App\Core\Data\Repositories\AddressRepository;
use App\Core\Data\Repositories\AmazonAmazonSalesChannelRepository;
use App\Core\Data\Repositories\AmazonReportRepository;
use App\Core\Data\Repositories\ApiRepository;
use App\Core\Data\Repositories\BankRepository;
use App\Core\Data\Repositories\BrandRepository;
use App\Core\Data\Repositories\BusinessModelRepository;
use App\Core\Data\Repositories\BusinessTypeRepository;
use App\Core\Data\Repositories\CityRepository;
use App\Core\Data\Repositories\CompanyAccountantRepository;
use App\Core\Data\Repositories\CompanyContactPersonRepository;
use App\Core\Data\Repositories\CompanyCountrySaleRepository;
use App\Core\Data\Repositories\CompanyCountryThresholdRepository;
use App\Core\Data\Repositories\CompanyRepository;
use App\Core\Data\Repositories\CompanyTaxPaymentRepository;
use App\Core\Data\Repositories\CompanyTaxPeriodTypeRepository;
use App\Core\Data\Repositories\ContactRepository;
use App\Core\Data\Repositories\ContactTypeRepository;
use App\Core\Data\Repositories\ContractRepository;
use App\Core\Data\Repositories\Contracts\AddressRepositoryContract;
use App\Core\Data\Repositories\Contracts\AmazonReportRepositoryContract;
use App\Core\Data\Repositories\Contracts\AmazonSalesChannelRepositoryContract;
use App\Core\Data\Repositories\Contracts\ApiRepositoryContract;
use App\Core\Data\Repositories\Contracts\BankRepositoryContract;
use App\Core\Data\Repositories\Contracts\BrandRepositoryContract;
use App\Core\Data\Repositories\Contracts\BusinessModelRepositoryContract;
use App\Core\Data\Repositories\Contracts\BusinessTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\CityRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyAccountantRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyContactPersonRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyCountrySaleRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyCountryThresholdRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyTaxPaymentRepositoryContract;
use App\Core\Data\Repositories\Contracts\CompanyTaxPeriodTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\ContactRepositoryContract;
use App\Core\Data\Repositories\Contracts\ContactTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\ContractRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryThresholdRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryVatRateRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountyRepositoryContract;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\CustomerRepositoryContract;
use App\Core\Data\Repositories\Contracts\DeliveryConditionRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentCategoryRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentTemplateRepositoryContract;
use App\Core\Data\Repositories\Contracts\EslReportRepositoryContract;
use App\Core\Data\Repositories\Contracts\EventTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\ExchangeRatesRepositoryContract;
use App\Core\Data\Repositories\Contracts\ExpenseTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\FileRepositoryContract;
use App\Core\Data\Repositories\Contracts\FlatVatRateRepositoryContract;
use App\Core\Data\Repositories\Contracts\GenderTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\IdentificationNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\InstitutionRepositoryContract;
use App\Core\Data\Repositories\Contracts\InvitationRepositoryContract;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use App\Core\Data\Repositories\Contracts\IossNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemPurchasePriceRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemSalePriceRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\LegalRepresentativeRepositoryContract;
use App\Core\Data\Repositories\Contracts\LogRepositoryContract;
use App\Core\Data\Repositories\Contracts\MarketplacesRepositoryContract;
use App\Core\Data\Repositories\Contracts\NotificationRepositoryContract;
use App\Core\Data\Repositories\Contracts\OnlineShopRepositoryContract;
use App\Core\Data\Repositories\Contracts\OssIossRepositoryContract;
use App\Core\Data\Repositories\Contracts\OssNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\OtherReportRepositoryContract;
use App\Core\Data\Repositories\Contracts\PaymentRecipientsRepositoryContract;
use App\Core\Data\Repositories\Contracts\PaymentTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\PersonRepositoryContract;
use App\Core\Data\Repositories\Contracts\PlatformRepositoryContract;
use App\Core\Data\Repositories\Contracts\PostalCodeRepositoryContract;
use App\Core\Data\Repositories\Contracts\ProductTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\ProgramTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\RegistrationProcessRepositoryContract;
use App\Core\Data\Repositories\Contracts\RegistrationSetupRepositoryContract;
use App\Core\Data\Repositories\Contracts\ReportStatusRepositoryContract;
use App\Core\Data\Repositories\Contracts\ReportTemplateRepositoryContract;
use App\Core\Data\Repositories\Contracts\RequestLogRepositoryContract;
use App\Core\Data\Repositories\Contracts\RoleRepositoryContract;
use App\Core\Data\Repositories\Contracts\SalesChannelRepositoryContract;
use App\Core\Data\Repositories\Contracts\SampleFileRepositoryContract;
use App\Core\Data\Repositories\Contracts\ShareholderRepositoryContract;
use App\Core\Data\Repositories\Contracts\ShopifyRepositoryContract;
use App\Core\Data\Repositories\Contracts\StateRepositoryContract;
use App\Core\Data\Repositories\Contracts\StoreRepositoryContract;
use App\Core\Data\Repositories\Contracts\SupplierRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxCodesRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxCollectionResponsibilityRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxOfficeRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxOfficeResponseRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxPeriodRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxPeriodTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxReportingSchemeRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxSchemesRepositoryContract;
use App\Core\Data\Repositories\Contracts\TeamRepositoryContract;
use App\Core\Data\Repositories\Contracts\TranslationRepositoryContract;
use App\Core\Data\Repositories\Contracts\UnitRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserManualRepositoryContract;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatRateTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\VatReportRepositoryContract;
use App\Core\Data\Repositories\Contracts\WaiverRepositoryContract;
use App\Core\Data\Repositories\Contracts\WarehouseOperatorRepositoryContract;
use App\Core\Data\Repositories\Contracts\WarehouseRepositoryContract;
use App\Core\Data\Repositories\Contracts\WarehouseUsageRepositoryContract;
use App\Core\Data\Repositories\Contracts\YearRepositoryContract;
use App\Core\Data\Repositories\CountryRepository;
use App\Core\Data\Repositories\CountryThresholdRepository;
use App\Core\Data\Repositories\CountryVatRateRepository;
use App\Core\Data\Repositories\CountyRepository;
use App\Core\Data\Repositories\CurrencyRepository;
use App\Core\Data\Repositories\CustomerRepository;
use App\Core\Data\Repositories\DeliveryConditionRepository;
use App\Core\Data\Repositories\DocumentCategoryRepository;
use App\Core\Data\Repositories\DocumentRepository;
use App\Core\Data\Repositories\DocumentTemplateRepository;
use App\Core\Data\Repositories\EslReportRepository;
use App\Core\Data\Repositories\EventTypeRepository;
use App\Core\Data\Repositories\ExchangeRatesRepository;
use App\Core\Data\Repositories\ExpenseTypeRepository;
use App\Core\Data\Repositories\FileRepository;
use App\Core\Data\Repositories\FlatVatRateRepository;
use App\Core\Data\Repositories\GenderTypeRepository;
use App\Core\Data\Repositories\IdentificationNumberRepository;
use App\Core\Data\Repositories\InstitutionRepository;
use App\Core\Data\Repositories\InvitationRepository;
use App\Core\Data\Repositories\InvoiceRepository;
use App\Core\Data\Repositories\IossNumberRepository;
use App\Core\Data\Repositories\ItemPurchasePriceRepository;
use App\Core\Data\Repositories\ItemRepository;
use App\Core\Data\Repositories\ItemSalePriceRepository;
use App\Core\Data\Repositories\ItemTypeRepository;
use App\Core\Data\Repositories\LegalRepresentativeRepository;
use App\Core\Data\Repositories\LogRepository;
use App\Core\Data\Repositories\MarketplacesRepository;
use App\Core\Data\Repositories\NotificationRepository;
use App\Core\Data\Repositories\OnlineShopRepository;
use App\Core\Data\Repositories\OssIossRepository;
use App\Core\Data\Repositories\OssNumberRepository;
use App\Core\Data\Repositories\OtherReportRepository;
use App\Core\Data\Repositories\PaymentRecipientsRepository;
use App\Core\Data\Repositories\PaymentTypeRepository;
use App\Core\Data\Repositories\PersonRepository;
use App\Core\Data\Repositories\PlatformRepository;
use App\Core\Data\Repositories\PostalCodeRepository;
use App\Core\Data\Repositories\ProductTypeRepository;
use App\Core\Data\Repositories\ProgramTypeRepository;
use App\Core\Data\Repositories\RegistrationProcessRepository;
use App\Core\Data\Repositories\RegistrationSetupRepository;
use App\Core\Data\Repositories\ReportStatusRepository;
use App\Core\Data\Repositories\ReportTemplateRepository;
use App\Core\Data\Repositories\RequestLogRepository;
use App\Core\Data\Repositories\RoleRepository;
use App\Core\Data\Repositories\SalesChannelRepository;
use App\Core\Data\Repositories\SampleFileRepository;
use App\Core\Data\Repositories\ShareholderRepository;
use App\Core\Data\Repositories\ShopifyRepository;
use App\Core\Data\Repositories\StateRepository;
use App\Core\Data\Repositories\StoreRepository;
use App\Core\Data\Repositories\SupplierRepository;
use App\Core\Data\Repositories\TaxCodesRepository;
use App\Core\Data\Repositories\TaxCollectionResponsibilityRepository;
use App\Core\Data\Repositories\TaxNumberRepository;
use App\Core\Data\Repositories\TaxOfficeRepository;
use App\Core\Data\Repositories\TaxOfficeResponseRepository;
use App\Core\Data\Repositories\TaxPeriodRepository;
use App\Core\Data\Repositories\TaxPeriodTypeRepository;
use App\Core\Data\Repositories\TaxReportingSchemeRepository;
use App\Core\Data\Repositories\TaxSchemesRepository;
use App\Core\Data\Repositories\TeamRepository;
use App\Core\Data\Repositories\TranslationRepository;
use App\Core\Data\Repositories\UnitRepository;
use App\Core\Data\Repositories\UserManualRepository;
use App\Core\Data\Repositories\UserRepository;
use App\Core\Data\Repositories\VatNumberRepository;
use App\Core\Data\Repositories\VatRateTypeRepository;
use App\Core\Data\Repositories\VatReportRepository;
use App\Core\Data\Repositories\WaiverRepository;
use App\Core\Data\Repositories\WarehouseOperatorRepository;
use App\Core\Data\Repositories\WarehouseRepository;
use App\Core\Data\Repositories\WarehouseUsageRepository;
use App\Core\Data\Repositories\YearRepository;
use App\Core\System\Providers\CoreModuleServiceProvider;

class RepositoriesServiceProvider extends CoreModuleServiceProvider
{
    protected function resolve(): void
    {
        $this->bind(UserRepositoryContract::class, UserRepository::class);
        $this->bind(SalesChannelRepositoryContract::class, SalesChannelRepository::class);
        $this->bind(CompanyRepositoryContract::class, CompanyRepository::class);
        $this->bind(CountryThresholdRepositoryContract::class, CountryThresholdRepository::class);
        $this->bind(ExchangeRatesRepositoryContract::class, ExchangeRatesRepository::class);
        $this->bind(CountryRepositoryContract::class, CountryRepository::class);
        $this->bind(VatNumberRepositoryContract::class, VatNumberRepository::class);
        $this->bind(FileRepositoryContract::class, FileRepository::class);
        $this->bind(YearRepositoryContract::class, YearRepository::class);
        $this->bind(ContactRepositoryContract::class, ContactRepository::class);
        $this->bind(CompanyCountryThresholdRepositoryContract::class, CompanyCountryThresholdRepository::class);
        $this->bind(TeamRepositoryContract::class, TeamRepository::class);
        $this->bind(RoleRepositoryContract::class, RoleRepository::class);
        $this->bind(AmazonReportRepositoryContract::class, AmazonReportRepository::class);
        $this->bind(AddressRepositoryContract::class, AddressRepository::class);
        $this->bind(NotificationRepositoryContract::class, NotificationRepository::class);
        $this->bind(TaxPeriodRepositoryContract::class, TaxPeriodRepository::class);
        $this->bind(WarehouseRepositoryContract::class, WarehouseRepository::class);
        $this->bind(TaxNumberRepositoryContract::class, TaxNumberRepository::class);
        $this->bind(VatRateTypeRepositoryContract::class, VatRateTypeRepository::class);
        $this->bind(CityRepositoryContract::class, CityRepository::class);
        $this->bind(PersonRepositoryContract::class, PersonRepository::class);
        $this->bind(ContactTypeRepositoryContract::class, ContactTypeRepository::class);
        $this->bind(CurrencyRepositoryContract::class, CurrencyRepository::class);
        $this->bind(BankRepositoryContract::class, BankRepository::class);
        $this->bind(ShareholderRepositoryContract::class, ShareholderRepository::class);
        $this->bind(WaiverRepositoryContract::class, WaiverRepository::class);
        $this->bind(WarehouseUsageRepositoryContract::class, WarehouseUsageRepository::class);
        $this->bind(FlatVatRateRepositoryContract::class, FlatVatRateRepository::class);
        $this->bind(RegistrationSetupRepositoryContract::class, RegistrationSetupRepository::class);
        $this->bind(InstitutionRepositoryContract::class, InstitutionRepository::class);
        $this->bind(ProductTypeRepositoryContract::class, ProductTypeRepository::class);
        $this->bind(OnlineShopRepositoryContract::class, OnlineShopRepository::class);
        $this->bind(DocumentCategoryRepositoryContract::class, DocumentCategoryRepository::class);
        $this->bind(DocumentRepositoryContract::class, DocumentRepository::class);
        $this->bind(RegistrationProcessRepositoryContract::class, RegistrationProcessRepository::class);
        $this->bind(CompanyAccountantRepositoryContract::class, CompanyAccountantRepository::class);
        $this->bind(CompanyTaxPeriodTypeRepositoryContract::class, CompanyTaxPeriodTypeRepository::class);
        $this->bind(TaxPeriodTypeRepositoryContract::class, TaxPeriodTypeRepository::class);
        $this->bind(CountryVatRateRepositoryContract::class, CountryVatRateRepository::class);
        $this->bind(RequestLogRepositoryContract::class, RequestLogRepository::class);
        $this->bind(CompanyCountrySaleRepositoryContract::class, CompanyCountrySaleRepository::class);
        $this->bind(SampleFileRepositoryContract::class, SampleFileRepository::class);
        $this->bind(CustomerRepositoryContract::class, CustomerRepository::class);
        $this->bind(SupplierRepositoryContract::class, SupplierRepository::class);
        $this->bind(TaxOfficeRepositoryContract::class, TaxOfficeRepository::class);
        $this->bind(GenderTypeRepositoryContract::class, GenderTypeRepository::class);
        $this->bind(BusinessTypeRepositoryContract::class, BusinessTypeRepository::class);
        $this->bind(TaxOfficeResponseRepositoryContract::class, TaxOfficeResponseRepository::class);
        $this->bind(CountyRepositoryContract::class, CountyRepository::class);
        $this->bind(StateRepositoryContract::class, StateRepository::class);
        $this->bind(PostalCodeRepositoryContract::class, PostalCodeRepository::class);
        $this->bind(WarehouseOperatorRepositoryContract::class, WarehouseOperatorRepository::class);
        $this->bind(InvitationRepositoryContract::class, InvitationRepository::class);
        $this->bind(ItemRepositoryContract::class, ItemRepository::class);
        $this->bind(UnitRepositoryContract::class, UnitRepository::class);
        $this->bind(MarketplacesRepositoryContract::class, MarketplacesRepository::class);
        $this->bind(OssNumberRepositoryContract::class, OssNumberRepository::class);
        $this->bind(IossNumberRepositoryContract::class, IossNumberRepository::class);
        $this->bind(InvoiceRepositoryContract::class, InvoiceRepository::class);
        $this->bind(TaxReportingSchemeRepositoryContract::class, TaxReportingSchemeRepository::class);
        $this->bind(TaxCollectionResponsibilityRepositoryContract::class, TaxCollectionResponsibilityRepository::class);
        $this->bind(ProgramTypeRepositoryContract::class, ProgramTypeRepository::class);
        $this->bind(AmazonSalesChannelRepositoryContract::class, AmazonAmazonSalesChannelRepository::class);
        $this->bind(DeliveryConditionRepositoryContract::class, DeliveryConditionRepository::class);
        $this->bind(EventTypeRepositoryContract::class, EventTypeRepository::class);
        $this->bind(StoreRepositoryContract::class, StoreRepository::class);
        $this->bind(PlatformRepositoryContract::class, PlatformRepository::class);
        $this->bind(CompanyContactPersonRepositoryContract::class, CompanyContactPersonRepository::class);
        $this->bind(TranslationRepositoryContract::class, TranslationRepository::class);
        $this->bind(ShopifyRepositoryContract::class, ShopifyRepository::class);
        $this->bind(ItemTypeRepositoryContract::class, ItemTypeRepository::class);
        $this->bind(BusinessModelRepositoryContract::class, BusinessModelRepository::class);
        $this->bind(BrandRepositoryContract::class, BrandRepository::class);
        $this->bind(LegalRepresentativeRepositoryContract::class, LegalRepresentativeRepository::class);
        $this->bind(BrandRepositoryContract::class, BrandRepository::class);
        $this->bind(ContractRepositoryContract::class, ContractRepository::class);
        $this->bind(UserManualRepositoryContract::class, UserManualRepository::class);
        $this->bind(ReportTemplateRepositoryContract::class, ReportTemplateRepository::class);
        $this->bind(VatReportRepositoryContract::class, VatReportRepository::class);
        $this->bind(ItemPurchasePriceRepositoryContract::class, ItemPurchasePriceRepository::class);
        $this->bind(ItemSalePriceRepositoryContract::class, ItemSalePriceRepository::class);
        $this->bind(DocumentTemplateRepositoryContract::class, DocumentTemplateRepository::class);
        $this->bind(IdentificationNumberRepositoryContract::class, IdentificationNumberRepository::class);
        $this->bind(TaxCodesRepositoryContract::class, TaxCodesRepository::class);
        $this->bind(LogRepositoryContract::class, LogRepository::class);
        $this->bind(OssIossRepositoryContract::class, OssIossRepository::class);
        $this->bind(TaxSchemesRepositoryContract::class, TaxSchemesRepository::class);
        $this->bind(PaymentRecipientsRepositoryContract::class, PaymentRecipientsRepository::class);
        $this->bind(CompanyTaxPaymentRepositoryContract::class, CompanyTaxPaymentRepository::class);
        $this->bind(EslReportRepositoryContract::class, EslReportRepository::class);
        $this->bind(ReportStatusRepositoryContract::class, ReportStatusRepository::class);
        $this->bind(OtherReportRepositoryContract::class, OtherReportRepository::class);
        $this->bind(ApiRepositoryContract::class, ApiRepository::class);
        $this->bind(PaymentTypeRepositoryContract::class, PaymentTypeRepository::class);
        $this->bind(ExpenseTypeRepositoryContract::class, ExpenseTypeRepository::class);
    }
}



