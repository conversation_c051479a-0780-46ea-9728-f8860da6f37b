<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Institution;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Models\InstitutionInstitutionTypeUserRole;
use App\Core\Data\Models\InstitutionType;
use App\Core\Data\Models\InstitutionTypeCountryPartner;
use App\Core\Data\Models\InstitutionBranch;
use App\Core\Data\Repositories\Contracts\InstitutionRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class InstitutionRepository implements InstitutionRepositoryContract
{
    private Institution $institution;
    private InstitutionType $institutionType;
    private InstitutionInstitutionType $institutionInstitutionType;
    private InstitutionInstitutionTypeUserRole $institutionInstitutionTypeUserRole;
    private InstitutionTypeCountryPartner $institutionTypePartner;
    private InstitutionBranch $institutionBranch;

    public function __construct(
        Institution $institution,
        InstitutionTypeCountryPartner $institutionTypePartner,
        InstitutionType $institutionType,
        InstitutionInstitutionType $institutionInstitutionType,
        InstitutionInstitutionTypeUserRole $institutionInstitutionTypeUserRole,
        InstitutionBranch $institutionBranch
    ) {
        $this->institution = $institution;
        $this->institutionType = $institutionType;
        $this->institutionInstitutionType = $institutionInstitutionType;
        $this->institutionInstitutionTypeUserRole = $institutionInstitutionTypeUserRole;
        $this->institutionTypePartner = $institutionTypePartner;
        $this->institutionBranch = $institutionBranch;
    }

    public function searchInstitutions(
        string $search,
        ?array $institutionTypeIds = null,
        ?array $skipIds = null,
        ?array $institutionInstitutionTypeIds = null,
        ?int $limit = 40
    ): Collection {
        $search = $search . '%';
        $q = $this->institutionInstitutionType
            ->select('institution_institution_type.*')
            ->join('institutions', 'institution_institution_type.institution_id', '=', 'institutions.id')
            ->where(function ($query) use ($search) {
                $query->where('institutions.full_legal_name', 'ilike', $search)
                    ->orWhere('institutions.short_name', 'ilike', $search);
            });

        if (!is_null($institutionTypeIds)) {
            $q->whereIn('institution_institution_type.institution_type_id', $institutionTypeIds);
        }

        if (!is_null($skipIds)) {
            $q->whereNotIn('institution_institution_type.id', $skipIds);
        }

        if (!is_null($institutionInstitutionTypeIds)) {
            $q->whereIn('institution_institution_type.id', $institutionInstitutionTypeIds);
        }

        return $q->orderBy('institutions.full_legal_name')
            ->limit($limit)
            ->get();
    }

    public function insertInstitutionInstitutionTypeUserRoles(array $roles): void
    {
        $this->institutionInstitutionTypeUserRole->insert($roles);
    }

    public function deleteInstitutionInstitutionTypeUserRole(int $userId): void
    {
        $this->institutionInstitutionTypeUserRole
            ->where('user_id', $userId)
            ->delete();
    }

    public function getAllPaginate(?int $institutionTypeId = null, int $perPage = 40): LengthAwarePaginator
    {
        $select = [
            'institutions.*'
        ];
        $q = $this->institution
            ->select($select)
            ->with(
                'authorisedPerson',
                'institutionInstitutionTypes',
                'institutionInstitutionTypes.institutionTypeAccountants.institutionInstitutionType.institution',
                'institutionInstitutionTypes.institutionTypeAccountants.country',
                'institutionInstitutionTypes.institutionTypePartners.partner.institution',
                'institutionInstitutionTypes.institutionTypePartners.country'
            )
            ->leftJoin('institution_institution_type', 'institutions.id', '=', 'institution_institution_type.institution_id');

        if (!is_null($institutionTypeId)) {
            $q->where('institution_institution_type.institution_type_id', $institutionTypeId);
        }

        return $q
            ->groupBy('institutions.id')
            ->orderBy('full_legal_name')
            ->paginate($perPage);
    }

    public function getInstitutionById(int $institutionId): ?Institution
    {
        return $this->institution->find($institutionId);
    }

    public function deleteInstitutionById(int $institutionId): void
    {
        $this->institution->destroy($institutionId);
    }

    public function getEmptyInstitutionModel(): Institution
    {
        return $this->institution->newInstance();
    }

    public function getInstitutionTypesForInstitution(): Collection
    {
        return $this->institutionType->get();
    }

    public function getInstitutionInstitutionTypeByInstitutionIdAndInstitutionTypeId(int $institutionId, int $institutionTypeId): ?InstitutionInstitutionType
    {
        return $this->institutionInstitutionType
            ->where('institution_type_id', $institutionTypeId)
            ->where('institution_id', $institutionId)
            ->first();
    }

    public function getEmptyInstitutionInstitutionTypeModel(): InstitutionInstitutionType
    {
        return $this->institutionInstitutionType->newInstance();
    }

    public function deleteInstitutionInstitutionTypeByInstitutionId(int $institutionId): void
    {
        $this->institutionInstitutionType->where('institution_id', $institutionId)->delete();
    }

    public function insertInstitutionTypePartners(array $institutionTypePartners): void
    {
        if (count($institutionTypePartners) < 1) {
            return;
        }

        $this->institutionTypePartner->insert($institutionTypePartners);
    }

    public function getInstitutionTypePartnersByPartnerId(int $partnerId): Collection
    {
        return $this->institutionTypePartner->where('partner_id', $partnerId)->get();
    }

    public function getInstitutionInstitutionTypeById(int $institutionInstitutionTypeId): ?InstitutionInstitutionType
    {
        return $this->institutionInstitutionType->find($institutionInstitutionTypeId);
    }

    public function getInstitutionTypePartnersById(int $institutionTypePartnerId): ?InstitutionTypeCountryPartner
    {
        return $this->institutionTypePartner->find($institutionTypePartnerId);
    }

    public function deleteInstitutionTypePartnersByIds(array $ids): void
    {
        $this->institutionTypePartner->whereIn('id', $ids)->delete();
    }

    public function getInstitutionTypePartnersByTypeByIds(int $institutionInstitutionTypeId): EloquentCollection
    {
        return $this->institutionTypePartner->where('institution_institution_type_id', $institutionInstitutionTypeId)->get();
    }

    public function getInstitutionTypeAccountantByTypeByIds(int $institutionInstitutionTypeId): EloquentCollection
    {
        return $this->institutionTypePartner->where('partner_id', $institutionInstitutionTypeId)->get();
    }

    public function getEmptyInstitutionBranchModel(): InstitutionBranch
    {
        return $this->institutionBranch->newInstance();
    }

    public function getInstitutionBranchById(int $institutionBranchId): ?InstitutionBranch
    {
        return $this->institutionBranch->find($institutionBranchId);
    }

    public function deleteInstitutionBranchById(int $institutionBranchId): void
    {
        $this->institutionBranch->destroy($institutionBranchId);
    }

    public function getAllInstitutionsTypes(): Collection
    {
        return $this->institutionType->get();
    }

    public function searchInstitutionsByFullLegalNameOrAuthorizedPerson(string $search, ?int $perPage = 40): LengthAwarePaginator
    {
        $search = $search . '%';
        $select = [
            'institutions.*',
            DB::raw('"people"."first_name" || \' \' || "people"."last_name" as "authorised_person_full_name"')
        ];

        return $this->institution
            ->select($select)
            ->join('institution_institution_type', 'institution_institution_type.institution_id', '=', 'institutions.id')
            ->leftJoin('people', 'institutions.authorised_person_id', '=', 'people.id')
            ->where('institutions.full_legal_name', 'ILIKE', $search)
            ->orWhere('people.first_name', 'ILIKE', $search)
            ->orWhere('people.last_name', 'ILIKE', $search)
            ->groupBy('institutions.id')
            ->groupBy('people.first_name')
            ->groupBy('people.last_name')
            ->paginate($perPage);
    }

    public function getAllInstitutionInstitutionsTypes(): Collection
    {
        return $this->institutionInstitutionType->all();
    }

    public function deleteInstitutionInstitutionTypeUserRolesByIds(array $institutionInstitutionTypeUserRolesIds): void
    {
        if (count($institutionInstitutionTypeUserRolesIds) < 1) {
            return;
        }

        $this->institutionInstitutionTypeUserRole->whereIn('id', $institutionInstitutionTypeUserRolesIds)->delete();
    }
}
