<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\Person;
use App\Core\Data\Repositories\Contracts\PersonRepositoryContract;

class PersonRepository implements PersonRepositoryContract
{
    /**
     * @var Person
     */
    private $person;

    public function __construct(Person $person)
    {
        $this->person = $person;
    }


    /**
     * @inheritDoc
     */
    public function getPersonById(int $personId): ?Person
    {
        return $this->person->with('contacts')->find($personId);
    }

    /**
     * @param int $personId
     */
    public function deletePersonById(int $personId): void
    {
        $this->person->destroy($personId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyPersonModel(): Person
    {
        return $this->person->newInstance();
    }
}
