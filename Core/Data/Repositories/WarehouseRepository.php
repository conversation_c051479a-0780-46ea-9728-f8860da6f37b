<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CompanyWarehouse;
use App\Core\Data\Models\Warehouse;
use App\Core\Data\Repositories\Contracts\WarehouseRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;

class WarehouseRepository implements WarehouseRepositoryContract
{
    /**
     * @var Warehouse
     */
    private Warehouse $warehouse;

    /**
     * @var CompanyWarehouse
     */
    private CompanyWarehouse $companyWarehouse;

    public function __construct(
        Warehouse $warehouse,
        CompanyWarehouse $companyWarehouse
    ) {
        $this->warehouse = $warehouse;
        $this->companyWarehouse = $companyWarehouse;
    }


    /**
     * @inheritDoc
     */
    public function getWarehouseById(int $warehouseId): ?Warehouse
    {
        return $this->warehouse->find($warehouseId);
    }

    /**
     * @inheritDoc
     */
    public function getWarehouseByNameAndCountry(string $warehouseName, int $countryId): ?Warehouse
    {
        return $this->warehouse->where('country_id', $countryId)->where('name', $warehouseName)->first();
    }

    /**
     * @param int $warehouseId
     */
    public function deleteWarehouseById(int $warehouseId): void
    {
        $this->warehouse->destroy($warehouseId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyWarehouseModel(): Warehouse
    {
        return $this->warehouse->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function createWarehouseIfNotExists(string $name, int $countryId): Warehouse
    {
        $name = mb_strtoupper($name);
        $warehouse = $this->getWarehouseByNameAndCountry($name, $countryId);
        if (!is_null($warehouse)) {
            return $warehouse;
        }
        $warehouse = $this->getEmptyWarehouseModel();
        $warehouse->name = $name;
        $warehouse->country_id = $countryId;
        $warehouse->save();

        return $warehouse;
    }

    /**
     * @inheritDoc
     */
    public function getGlobalWarehouses(?bool $isConfirmed = true): Collection
    {
        $q = $this->warehouse->where('global', true);

        if (!is_null($isConfirmed)) {
            $q->where('confirmed', true);
        }

        return $q->orderBy('id')->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllWarehousesByCompanyId(int $companyId): Collection
    {
        return $this->warehouse
            ->leftJoin('company_warehouses', 'warehouses.id', '=', 'company_warehouses.warehouse_id')
            ->where('company_warehouses.company_id', '=', $companyId)
            ->orderBy('company_warehouses.id')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllProposeWarehousePaginateByCompanyId(int $proposeCompanyId, int $perPage = 40): LengthAwarePaginator
    {
        $q = $this->warehouse
            ->where('propose_company_id', '=', $proposeCompanyId)
            ->where('global', '=', true);

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(
        ?int $companyId,
        ?int $warehouseId,
        int $perPage = 40,
        ?string $search = null
    ): LengthAwarePaginator {
        $q = $this->companyWarehouse
            ->select(['company_warehouses.*'])
            ->join('warehouses', 'company_warehouses.warehouse_id', '=', 'warehouses.id')
            ->with('company', 'warehouse.warehouseOperator');
        if (!is_null($companyId)) {
            $q->where('company_id', '=', $companyId);
        }
        if (!is_null($warehouseId)) {
            $q->where('warehouse_id', '=', $warehouseId);
        }

        if (!is_null($search)) {
            $q->where(function (EloquentBuilder $query) use ($search) {
                $query->where('warehouses.uid', 'ILIKE', '%' . $search . '%')
                    ->orWhere('warehouses.name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('warehouses.postal_code', 'ILIKE', '%' . $search . '%');
            });
        }

        return $q
            ->orderBy('warehouses.name')
            ->orderBy('warehouses.uid')
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyWarehouseById(int $companyWarehouseId): ?CompanyWarehouse
    {
        return $this->companyWarehouse->find($companyWarehouseId);
    }

    /**
     * @param int $companyWarehouseId
     */
    public function deleteCompanyWarehouseById(int $companyWarehouseId): void
    {
        $this->companyWarehouse->destroy($companyWarehouseId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCompanyWarehouseModel(): CompanyWarehouse
    {
        return $this->companyWarehouse->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyWarehouseByCompanyAndWarehouseId(int $companyId, int $warehouseId): ?CompanyWarehouse
    {
        return $this->companyWarehouse
            ->where('company_id', $companyId)
            ->where('warehouse_id', $warehouseId)
            ->first();
    }

    /**
     * @inheritDoc
     */
    public function getGlobalConfirmedWarehousesNotUsedByCompany(
        int $companyId,
        ?int $companyWarehouseId = null,
        ?string $search = null,
        ?int $limit = null
    ): Collection {
        $limit = $limit ?? 40;
        $q = $this->warehouse
            ->where('global', true)
            ->where('confirmed', true)
            ->whereNotIn('id', function (Builder $builder) use ($companyId, $companyWarehouseId) {
                $builder->select('warehouse_id')
                    ->from('company_warehouses')
                    ->where('company_id', $companyId);

                if (!is_null($companyWarehouseId)) {
                    $builder->where('id', '!=', $companyWarehouseId);
                }
            });

        return $q
            ->orderBy('name')
            ->orderBy('uid')
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllGlobalWarehousesPaginate(?int $perPage = 40, ?bool $proposed = false): LengthAwarePaginator
    {
        $q = $this->warehouse
            ->with('warehouseOperator', 'proposeCompany')
            ->where('global', true);

        if ($proposed) {
            $q->where('confirmed', false)
                ->where('propose_company_id', '!=', 0);
        } else {
            $q->where('confirmed', true);

        }

        return $q->orderBy('created_at', 'DESC')
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function searchCompanyWarehouses(string $search, int $companyId, int $limit = 20): Collection
    {
        return $this->companyWarehouse
            ->select(['company_warehouses.*'])
            ->join('warehouses', 'warehouses.id', '=', 'company_warehouses.warehouse_id')
            ->where('company_warehouses.company_id', $companyId)
            ->where(function (EloquentBuilder $query) use ($search) {
                $query->where('warehouses.uid', 'ILIKE', '%' . $search . '%')
                    ->orWhere('warehouses.name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('warehouses.postal_code', 'ILIKE', '%' . $search . '%');
            })
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function searchGlobalWarehouses(string $search, int $limit = 20): Collection
    {
        return $this->warehouse
            ->where('global', true)
            ->where('confirmed', true)
            ->where(function (EloquentBuilder $query) use ($search) {
                $query->where('uid', 'ILIKE', $search . '%')
                    ->orWhere('name', 'ILIKE', $search . '%')
                    ->orWhere('postal_code', 'ILIKE', $search . '%');
            })
            ->limit($limit)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getWarehousesByUids(array $uids): Collection
    {
        return $this->warehouse->whereIn('uid', $uids)->get();
    }

    /**
     * @inheritDoc
     */
    public function searchWarehouseByNameOrUid(string $search, ?int $perPage = 40): LengthAwarePaginator
    {
        return $this->warehouse->select('warehouses.*')
            ->where('global', true)
            ->where('confirmed', true)
            ->where(function (EloquentBuilder $query) use ($search) {
                $query->where('uid', 'ILIKE', $search . '%')
                    ->orWhere('name', 'ILIKE', $search . '%')
                    ->orWhere('postal_code', 'ILIKE', $search . '%');
            })
            ->groupBy('warehouses.id')
            ->paginate($perPage);
    }
}
