<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Repositories\Contracts\TaxCollectionResponsibilityRepositoryContract;
use Illuminate\Support\Collection;

class TaxCollectionResponsibilityRepository implements TaxCollectionResponsibilityRepositoryContract
{
    private TaxCollectionResponsibility $taxCollectionResponsibility;

    public function __construct(TaxCollectionResponsibility $taxCollectionResponsibility)
    {
        $this->taxCollectionResponsibility = $taxCollectionResponsibility;
    }

    /**
     * @inheritDoc
     */
    public function getAllForAmazon(): Collection
    {
        return $this->taxCollectionResponsibility->whereNotNull('amazon_code')->get();
    }
}