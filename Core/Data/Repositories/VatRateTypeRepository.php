<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Repositories\Contracts\VatRateTypeRepositoryContract;
use App\Core\Data\Models\VatRateType;
use Illuminate\Support\Collection;

class VatRateTypeRepository implements VatRateTypeRepositoryContract
{
    private VatRateType $vatRateType;

    public function __construct(VatRateType $vatRateType)
    {
        $this->vatRateType = $vatRateType;
    }

    /**
     * @inheritDoc
     */
    public function getAllVatRateTypes(): Collection
    {
        return $this->vatRateType->orderBy('id')->get();
    }

    /**
     * @inheritDoc
     */
    public function getVatRateTypesByIds(array $vatRateTypeIds): Collection
    {
        return $this->vatRateType->whereIn('id', $vatRateTypeIds)->get();
    }
}
