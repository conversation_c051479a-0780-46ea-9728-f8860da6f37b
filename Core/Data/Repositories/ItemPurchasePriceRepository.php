<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ItemPurchasePrice;
use App\Core\Data\Repositories\Contracts\ItemPurchasePriceRepositoryContract;

class ItemPurchasePriceRepository implements ItemPurchasePriceRepositoryContract
{
    public function __construct(private ItemPurchasePrice $itemPurchasePrice)
    {
    }

    public function deleteItemPurchasePriceById(int $id): void
    {
        $this->itemPurchasePrice->destroy($id);
    }
}