<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\OnlineShop;
use App\Core\Data\Repositories\Contracts\OnlineShopRepositoryContract;

class OnlineShopRepository implements OnlineShopRepositoryContract
{
    /**
     * @var onlineShop
     */
    private $onlineShop;

    public function __construct(OnlineShop $onlineShop)
    {
        $this->onlineShop = $onlineShop;
    }


    /**
     * @inheritDoc
     */
    public function getOnlineShopById(int $onlineShopId): ?OnlineShop
    {
        return $this->onlineShop->find($onlineShopId);
    }

    /**
     * @param int $onlineShopId
     */
    public function deleteOnlineShopById(int $onlineShopId): void
    {
        $this->onlineShop->destroy($onlineShopId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyOnlineShopModel(): OnlineShop
    {
        return $this->onlineShop->newInstance();
    }

}