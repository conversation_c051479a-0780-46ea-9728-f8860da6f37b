<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\State;
use App\Core\Data\Repositories\Contracts\StateRepositoryContract;

class StateRepository implements StateRepositoryContract
{
    private State $state;

    public function __construct(State $state)
    {
        $this->state = $state;
    }

    private function getStateByNameAndCountry(string $name, int $countryId): ?State
    {
        return $this->state->where('country_id', $countryId)->where('name', $name)->first();
    }

    private function getEmptyStateModel(): State
    {
        return $this->state->newInstance();
    }

    public function createStateIfNotExists(string $name, int $countryId, ?string $code = null): State
    {
        $name = mb_strtoupper($name);
        if (is_null($code)) {
            $code = $name;
        }
        $state = $this->getStateByNameAndCountry($name, $countryId);
        if (!is_null($state)) {
            return $state;
        }
        $state = $this->getEmptyStateModel();
        $state->name = $name;
        $state->code = $code;
        $state->country_id = $countryId;
        $state->save();

        return $state;
    }
}
