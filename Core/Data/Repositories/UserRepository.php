<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Scopes\UserAccountNotLockedScope;
use App\Core\Data\Models\Scopes\UserNotDeletedScope;
use App\Core\Data\Models\User;
use App\Core\Data\Repositories\Contracts\UserRepositoryContract;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class UserRepository implements UserRepositoryContract
{
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function getUserByEmail(
        string $email,
        bool $includeAdministrative = false,
        bool $includeDeleted = false,
        bool $forgotPassword = false
    ): ?User {
        $query = $this->user
            ->where('email', $email);

        if (!$includeAdministrative) {
            $query->notAdministrative();
        }

        if ($includeDeleted) {
            $query->withoutGlobalScope(UserNotDeletedScope::class);
        }

        if ($forgotPassword) {
            $query->whereNotNull('email_verified_at')
                ->where(function ($query) {
                    $query->whereNull('trial_ends_at')
                        ->orWhere('trial_ends_at', '<', Carbon::now());
                });
        }

        return $query->first();
    }

    public function getUserByPasswordResetToken(string $token): ?User
    {
        return User::where('password_reset_token', $token)
            ->where('password_reset_time', '>', Carbon::now()->subMinutes(30))
            ->first();
    }

    public function getAllUsersPaginate(
        int $perPage = 40,
        ?array $with = null,
        ?int $institutionInstitutionTypeId = null,
        bool $includeAdministrative = false,
        ?int $roleId = null,
        ?string $search = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): LengthAwarePaginator {

        $query = $this->user
            ->select('users.*');

        if (!is_null($orderBy) && !is_null($orderDirection)) {
            $query->orderBy('users.' . $orderBy, $orderDirection);
        }

        $query = $query->orderBy('users.last_name')
            ->orderBy('users.first_name');


        if (!is_null($with)) {
            /** @noinspection PhpUndefinedMethodInspection */
            $query->with($with);
        }

        if (!$includeAdministrative) {
            /** @noinspection PhpUndefinedMethodInspection */
            $query->notAdministrative()
                ->join('institution_institution_type_user_role', 'users.id', '=', 'institution_institution_type_user_role.user_id')
                ->join('institution_institution_type', 'institution_institution_type_user_role.institution_institution_type_id', '=', 'institution_institution_type.id')
                ->where('institution_institution_type.id', $institutionInstitutionTypeId);
        } else {
            $query->leftJoin('institution_institution_type_user_role', 'users.id', '=', 'institution_institution_type_user_role.user_id');
        }

        if (!is_null($roleId)) {
            $query->where('institution_institution_type_user_role.role_id', $roleId);
        }

        if (!is_null($search)) {
            $query->where(function (Builder $query) use ($search) {
                $query
                    ->where('users.id', (int)$search)
                    ->orWhere('users.first_name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('users.last_name', 'ILIKE', '%' . $search . '%')
                    ->orWhere('users.email', 'ILIKE', '%' . $search . '%')
                    ->orderBy('users.last_name')
                    ->orderBy('users.first_name');
            });
        }

        return $query
            ->groupBy('users.id')
            ->paginate($perPage);
    }

    public function getAllUsersForInstitution(int $institutionInstitutionTypeId, ?int $roleId = null): Collection
    {
        $query = $this->user
            ->select('users.*')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->join('institution_institution_type_user_role', 'users.id', '=', 'institution_institution_type_user_role.user_id')
            ->join('institution_institution_type', 'institution_institution_type_user_role.institution_institution_type_id', '=', 'institution_institution_type.id')
            ->where('institution_institution_type.id', $institutionInstitutionTypeId);

        if (!is_null($roleId)) {
            $query->where('institution_institution_type_user_role.role_id', $roleId);
        }

        return $query
            ->groupBy('users.id')
            ->get();
    }

    public function getUserById(int $userId, bool $includeAdministrative = false): ?User
    {
        $query = $this->user;

        if (!$includeAdministrative) {
            $query->notAdministrative();
        }

        return $query->find($userId);
    }

    public function getEmptyUserModel(): User
    {
        return $this->user->newInstance();
    }

    public function setUserHasToken(int $userId, bool $hasToken = false): User
    {
        $user = $this->getUserById($userId);
        $user->has_token = $hasToken;
        $user->save();

        return $user;
    }

    public function searchUserByName(?string $search = null): ?Collection
    {
        $query = $this->user
            ->where('first_name', 'ILIKE', $search . '%')
            ->orWhere('last_name', 'ILIKE', $search . '%')
            ->orderBy('last_name')
            ->orderBy('first_name');


        return $query->get();
    }

    public function getCompaniesUsers(array $companyIds): Collection
    {
        return $this->user
            ->select('users.*')
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->join('companies_users_roles', 'users.id', '=', 'companies_users_roles.user_id')
            ->whereIn('companies_users_roles.company_id', $companyIds)
            ->groupBy('users.id')
            ->get();
    }

    public function getUserByEmailConfirmationToken(string $token): ?User
    {
        return $this->user
            ->where('email_confirmation_token', $token)
            ->first();
    }

    public function getUserByUnlockToken(string $token): ?User
    {
        return $this->user->where('unlock_token', $token)->withoutGlobalScope(UserAccountNotLockedScope::class)->first();
    }

    public function isUserLocked(string $username): bool
    {
        return $this->user->where('email', $username)->whereNotNull('unlock_token')->withoutGlobalScope(UserAccountNotLockedScope::class)->exists();
    }

    public function getUsersWithUnverifiedEmailForReminder(): Collection
    {
        $ttl = (int)config('evat.emails.reminders.email-confirmation.ttl');
        $ttl = Carbon::now()
            ->subHours($ttl)
            ->startOfHour();

        return $this->user
            ->whereNull('email_verified_at')
            ->whereNotNull('email_confirmation_token')
            ->whereNotNull('email_token_sent_at')
            ->where('email_token_sent_at', '<', $ttl->toDateTimeString())
            ->where('reminder_sent', false)
            ->get();
    }

    public function getUserByLoginOnceToken(string $loginOnceToken): ?User
    {
        return $this->user
            ->where('login_token', $loginOnceToken)
            ->first();
    }

    public function deleteUsersWithUnconfirmedEmail(): void
    {
        $ttl = config('evat.emails.expirePeriod');
        $ttl = Carbon::now()
            ->subHours($ttl)
            ->startOfDay();

        $this->user
            ->whereNull('email_verified_at')
            ->whereNotNull('email_confirmation_token')
            ->whereNotNull('email_token_sent_at')
            ->where('email_token_sent_at', '<', $ttl->toDateTimeString())
            ->delete();
    }

    public function generatePasswordResetToken(User $user): User
    {
        $token = Str::random(64);

        $user->password_reset_token = $token;
        $user->password_reset_time = Carbon::now();

        $user->save();

        return $user;
    }

    public function lockAccount(string $username): User|null
    {
        $user = $this->getUserByEmail($username, true);

        if (!empty($user)) {
            $token = Str::random(64);

            $user->unlock_token = $token;
            $user->save();
        }

        return $user;
    }

    public function unlockAccount(string $token): User|null
    {
        $user = $this->getUserByUnlockToken($token);

        if (!empty($user)) {
            $user->unlock_token = null;
            $user->save();
        }

        return $user;
    }
}
