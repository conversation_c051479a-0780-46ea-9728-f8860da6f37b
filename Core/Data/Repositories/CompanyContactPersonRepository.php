<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CompanyContactPerson;
use App\Core\Data\Models\CompanyContactPersonType;
use App\Core\Data\Repositories\Contracts\CompanyContactPersonRepositoryContract;
use Illuminate\Support\Collection;

class CompanyContactPersonRepository implements CompanyContactPersonRepositoryContract
{
    private CompanyContactPersonType $companyContactPersonType;
    private CompanyContactPerson $companyContactPerson;

    public function __construct(
        CompanyContactPersonType $companyContactPersonType,
        CompanyContactPerson $companyContactPerson
    ) {
        $this->companyContactPersonType = $companyContactPersonType;
        $this->companyContactPerson = $companyContactPerson;
    }


    /**
     * @inheritDoc
     */
    public function getAllContactPersonTypes(): Collection
    {
        return $this->companyContactPersonType->all();
    }

    /**
     * @inheritDoc
     */
    public function deleteCompanyContactPersonById(int $companyContactPersonId): void
    {
        $this->companyContactPerson->destroy($companyContactPersonId);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyContactPersonById(int $companyContactPersonId): ?CompanyContactPerson
    {
        return $this->companyContactPerson->find($companyContactPersonId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCompanyContactPersonModel(): CompanyContactPerson
    {
        return $this->companyContactPerson->newInstance();
    }
}
