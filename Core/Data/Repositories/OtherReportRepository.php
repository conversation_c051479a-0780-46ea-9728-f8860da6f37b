<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\OtherReport;
use App\Core\Data\Models\OtherReportStatus;
use App\Core\Data\Repositories\Contracts\OtherReportRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Database\Eloquent\Builder;

class OtherReportRepository implements OtherReportRepositoryContract
{
    private OtherReport $otherReport;
    private OtherReportStatus $otherReportStatus;

    /**
     * @param OtherReport $otherReport
     * @param OtherReportStatus $otherReportStatus
     */
    public function __construct(OtherReport $otherReport, OtherReportStatus $otherReportStatus)
    {
        $this->otherReport = $otherReport;
        $this->otherReportStatus = $otherReportStatus;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyOtherReportModel(): OtherReport
    {
        return $this->otherReport->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getOtherReportsPaginate(
        int $companyId,
        int $statusId,
        int $uploadUserId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $search = null,
        int $perPage = 40
    ): LengthAwarePaginator {

//        $q = $this->otherReport
//            ->with('company', 'salesChannel', 'companyWarehouse')
//            ->where('company_id', '=', $companyId)
//            ->where('status_id', '=', $statusId)
//            ->where('upload_user_id', '=', $uploadUserId);
//
////        if (!is_null($startDate)) {
////            $q->where('start_date', '>=', $startDate);
////        }
////
////        if (!is_null($endDate)) {
////            $q->where('end_date', '<=', $endDate);
////        }
//
//        $q->orderBy('id');
//
//        return $q->paginate($perPage);


        $select = [
            'other_reports.*',
            'companies.full_legal_name',
            DB::raw('"f"."name" || \'.\' || "f"."extension" as file_name'),
            'f.extension as file_extension',
            'f.id as fileId',
            'f.size as fileSize',
            'company_warehouses.company_id as company_warehouses.company_id',
            'warehouses.name as warehouseName',
            'users.first_name as firstName',
            'users.last_name as lastName',
            'other_report_statuses.name as statusName',
        ];

        $q = $this->otherReport
            ->select($select)
            ->join('files as f', function (JoinClause $query) {
                $query->on('other_reports.id', '=', 'f.resource_id')
                    ->where('f.resource', '=', $this->otherReport->getUploadResourceName());
            })
            ->join('company_warehouses', 'company_warehouses.id', '=', 'other_reports.company_warehouse_id')
            ->join('warehouses', 'warehouses.id', '=', 'company_warehouses.warehouse_id')
            ->leftJoin('ecommerce_accounts', 'ecommerce_accounts.id', '=', 'other_reports.ecommerce_account_id')
            ->leftJoin('companies', 'companies.id', '=', 'other_reports.company_id')
            ->leftJoin('users', 'users.id', '=', 'other_reports.upload_user_id')
            ->leftJoin('other_report_statuses', 'other_report_statuses.id', '=', 'other_reports.status_id')
            ->where('other_reports.company_id', '=', $companyId)
            ->where('other_reports.status_id', '=', $statusId)
            ->where('other_reports.upload_user_id', '=', $uploadUserId);

        if (!is_null($startDate)) {
            $q->where('other_reports.start_date', '>=', $startDate);
        }

        if (!is_null($endDate)) {
            $q->where('other_reports.start_date', '>=', $startDate);
        }

        if (!is_null($search)) {
            $q->where(function (Builder $query) use ($search) {
                $query->where(DB::raw('"f"."name" || \'.\' || "f"."extension"'), 'ILIKE', '%' . $search . '%')
                    ->orWhere('other_reports.id', 'ILIKE', '%' . (int)$search . '%');
            });
        }

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getOtherReportsById(int $id): ?OtherReport
    {
        return $this->otherReport->find($id);
    }

    /**
     * @inheritDoc
     */
    public function getAllOtherReportStatus(): Collection
    {
        return $this->otherReportStatus->all();
    }

    /**
     * @inheritDoc
     */
    public function getOtherReportsPerStatusCount(int $companyId, int $userId): Collection
    {
        $startDate = null;
        $endDate = null;

        $q = $this->otherReport
            ->select([
                'status_id',
                'upload_user_id',
                DB::raw('count(status_id) as count')
            ])
            ->where('company_id', '=', $companyId)
            ->where('upload_user_id', '=', $userId);
        if (!is_null($startDate)) {
            $q->where('other_reports.start_date', '>=', $startDate);
        }

        if (!is_null($endDate)) {
            $q->where('other_reports.start_date', '>=', $startDate);
        }

        return $q->groupBy('status_id', 'upload_user_id')
            ->get();


    }
}
