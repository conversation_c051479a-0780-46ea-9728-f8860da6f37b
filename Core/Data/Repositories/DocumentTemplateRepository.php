<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\DocumentTemplate;
use App\Core\Data\Models\DocumentTemplateField;
use App\Core\Data\Repositories\Contracts\DocumentTemplateRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class DocumentTemplateRepository implements DocumentTemplateRepositoryContract
{
    private DocumentTemplate $documentTemplate;
    private DocumentTemplateField $documentTemplateField;

    /**
     * @param DocumentTemplate $documentTemplate
     * @param DocumentTemplateField $documentTemplateField
     */
    public function __construct(DocumentTemplate $documentTemplate, DocumentTemplateField $documentTemplateField)
    {
        $this->documentTemplate = $documentTemplate;
        $this->documentTemplateField = $documentTemplateField;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyDocumentTemplateModel(): DocumentTemplate
    {
        return $this->documentTemplate->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getDocumentTemplateById(int $documentTemplateId): ?DocumentTemplate
    {
        return $this->documentTemplate->find($documentTemplateId);
    }

    /**
     * @inheritDoc
     */
    public function getAllDocumentTemplatePaginate(int $perPage = 40, ?array $with = null, ?string $search = null): LengthAwarePaginator
    {
        return $this->documentTemplate->with('documentCategory')->paginate($perPage);
    }

    public function deleteDocumentTemplateById(int $documentTemplateId): void
    {
        $this->documentTemplate->destroy($documentTemplateId);
    }

    /**
     * @inheritDoc
     */
    public function deleteDocumentTemplateFieldsByDocumentTemplateId(int $documentTemplateId): void
    {
        $this->documentTemplateField->where('document_template_id', $documentTemplateId)->delete();
    }

    public function insertDocumentTemplateFields(array $fields): void
    {
        $this->documentTemplateField->insert($fields);
    }

    public function getDocumentTemplateByCategoryIdForDate(int $documentCategoryId, string $date): ?DocumentTemplate
    {
        return $this->documentTemplate
            ->where('document_category_id', $documentCategoryId)
            ->where('valid_from', '<=', $date)
            ->orderBy('valid_from', 'DESC')
            ->first();
    }

    public function getAllDocumentTemplates(): EloquentCollection
    {
        return $this->documentTemplate->get();
    }
}
