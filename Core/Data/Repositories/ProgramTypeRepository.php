<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\ProgramType;
use App\Core\Data\Repositories\Contracts\ProgramTypeRepositoryContract;
use Illuminate\Support\Collection;

class ProgramTypeRepository implements ProgramTypeRepositoryContract
{
    private ProgramType $programType;

    public function __construct(ProgramType $programType)
    {
        $this->programType = $programType;
    }

    /**
     * @inheritDoc
     */
    public function getAll(): Collection
    {
        return $this->programType->all();
    }
}