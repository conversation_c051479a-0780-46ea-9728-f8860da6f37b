<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Contract;
use App\Core\Data\Models\Institution;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Repositories\Contracts\ContractRepositoryContract;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

class ContractRepository implements ContractRepositoryContract
{
    private Contract $contract;

    public function __construct(Contract $contract)
    {
        $this->contract = $contract;
    }

    public function getAllContracts(bool $paginate = false, ?int $institutionId = null, ?array $parameters = null): EloquentCollection|LengthAwarePaginator
    {
        $contracts = $this->contract
            ->select('contracts.*')
            ->with('companies')
            ->where('resource_class', Institution::class)
            ->where('resource_id', $institutionId);

        if (!is_null($parameters)) {
            if (isset($parameters['search'])) {
                $search = $parameters['search'];

                $contracts->where(function ($query) use ($search) {
                    /** @var Builder $query */
                    $query->where(function ($query) use ($search) {
                        /** @var Builder $query */
                        $query->where('name', 'ilike', '%' . $search . '%')
                            ->orWhere('contract_number', 'ilike', '%' . $search . '%');
                    })->orWhereHas('companies', function ($subQuery) use ($search) {
                        /** @var Builder $subQuery */
                        $subQuery->where('full_legal_name', 'ilike', '%' . $search . '%');
                    });
                });
            }
        }

        if ($paginate) {
            $perPage = config('evat.perPage');
            $paginatedResults = $contracts->paginate($perPage);
            foreach ($paginatedResults as $contractPaginated) {
                $contractPaginated->startDate = Carbon::parse($contractPaginated->start_date)->format('d.m.Y');
            }

        } else {
            $paginatedResults = $contracts->get();
            foreach ($paginatedResults as $contract) {
                $contract->endDate = Carbon::parse($contract->endDate)->format('d.m.Y');
            }

        }
//        dd(eloquent_to_raw($contracts));

        return $paginatedResults;
    }

    public function getEmptyContractModel(): Contract
    {
        return $this->contract->newInstance();
    }

    public function getContractById(int $contractId): ?Contract
    {
        return $this->contract->find($contractId);
    }

    public function saveContract(array $contractParameters, int $institutionId): void
    {
        $editMode = $contractParameters['id'] !== null;
        $resourceClass = Institution::class;

        if ($editMode) {
            $contract = $this->getContractById($contractParameters['id']);
        } else {
            $contract = $this->getEmptyContractModel();
        }

        $contract->name = $contractParameters['name'];
        $contract->description = $contractParameters['description'];
        $contract->contract_number = $contractParameters['contract_number'];
        $contract->start_date = $contractParameters['start_date'];
        $contract->end_date = $contractParameters['end_date'];
        $contract->resource_id = $institutionId;
        $contract->resource_class = $resourceClass;

        $contract->save();

        if (isset($contractParameters['companies'])) {
            $companyIds = [];

            foreach ($contractParameters['companies'] as $company) {
                $companyIds[] = $company['id'];
            }

            $contract->companies()->sync($companyIds);
        }
    }

    public function deleteContract(int $contractId): void
    {
        $contract = $this->getContractById($contractId);

        if (!empty($contract)) {
            $contract->delete();
        }
    }

    public function searchContractsByNameNumberCompany(string $search, int $institutionId, bool $isAdmin): Collection
    {
        $contracts = $this->contract->with('companies');

        if (!$isAdmin) {
            $contracts->where('resource_class', InstitutionInstitutionType::class)
                ->where('resource_id', $institutionId);
        }

        $contracts->where(function ($query) use ($search) {
            /** @var Builder $query */
            $query->where(function ($query) use ($search) {
                /** @var Builder $query */
                $query->where('name', 'ilike', '%' . $search . '%')
                    ->orWhere('contract_number', 'ilike', '%' . $search . '%');
            })->orWhereHas('companies', function ($subQuery) use ($search) {
                /** @var Builder $subQuery */
                $subQuery->where('full_legal_name', 'ilike', '%' . $search . '%');
            });
        });

        return $contracts->get();
    }
}
