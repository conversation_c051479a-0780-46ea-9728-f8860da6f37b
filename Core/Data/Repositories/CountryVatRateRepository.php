<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CountryVatRate;
use App\Core\Data\Repositories\Contracts\CountryVatRateRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CountryVatRateRepository implements CountryVatRateRepositoryContract
{
    private CountryVatRate $countryVatRate;

    public function __construct(CountryVatRate $countryVatRate)
    {
        $this->countryVatRate = $countryVatRate;
    }

    /**
     * @inheritDoc
     */
    public function getAllCountryVatRatesPaginate(?int $countryId, int $perPage = 40): LengthAwarePaginator
    {
        $q = $this->countryVatRate->with('country');
        if (!is_null($countryId)) {
            $q = $q->where('country_id', '=', $countryId);
        }
        $q = $q->orderBy('country_id');

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCountryVatRatesModel(): CountryVatRate
    {
        return $this->countryVatRate->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCountryVatRatesById(int $countryVatRateId): ?CountryVatRate
    {
        return $this->countryVatRate->find($countryVatRateId);
    }

    /**
     * @inheritDoc
     */
    public function deleteCountryVatRateById(int $countryVatRateId): void
    {
        $countryVatRate = $this->getCountryVatRatesById($countryVatRateId);
        if (!is_null($countryVatRate)) {
            $this->countryVatRate->destroy($countryVatRateId);
        }
    }
}
