<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\BusinessType;
use App\Core\Data\Repositories\Contracts\BusinessTypeRepositoryContract;
use Illuminate\Support\Collection;

class BusinessTypeRepository implements BusinessTypeRepositoryContract
{
    private BusinessType $businessType;

    public function __construct(BusinessType $businessType)
    {
        $this->businessType = $businessType;
    }

    public function getAllBusinessTypes(): Collection
    {
        return $this->businessType->all();
    }
}
