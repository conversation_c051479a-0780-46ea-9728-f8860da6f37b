<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\PostalCodeException;
use App\Core\Data\Repositories\Contracts\PostalCodeRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class PostalCodeRepository implements PostalCodeRepositoryContract
{
    /**
     * @var PostalCodeException
     */
    private $postalCodeException;

    /**
     * PostalCodeRepository constructor.
     *
     * @param PostalCodeException $postalCodeException
     */
    public function __construct(PostalCodeException $postalCodeException)
    {
        $this->postalCodeException = $postalCodeException;
    }

    /**
     * @inheritDoc
     */
    public function getPostalCodeExceptionsFromPostalCode(
        string $postalCode,
        int $postalCodeCountryId,
        int $perPage = 40
    ): LengthAwarePaginator {
        $postalCode = str_replace(' ', '', $postalCode);

        return $this->postalCodeException
            ->select([
                '*',
                DB::raw('char_length(postal_code_start_with) as count_length')
            ])
            ->whereRaw("$postalCode::varchar like concat(postal_codes.postal_code_start_with, '%')")
            ->where('postal_code_country_id', $postalCodeCountryId)
            ->orderBy('count_length', 'DESC')
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getAllPostalCodeExceptionsByCountryPaginate(int $postalCodeCountryId, int $perPage = 40): LengthAwarePaginator
    {
        return $this->postalCodeException
            ->where('postal_code_country_id', $postalCodeCountryId)
            ->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getPostalCodeExceptionById(int $postalCodeExceptionId): ?PostalCodeException
    {
        return $this->postalCodeException->find($postalCodeExceptionId);
    }

    /**
     * @inheritDoc
     */
    public function deletePostalCodeExceptionById(int $postalCodeExceptionId): void
    {
        $this->postalCodeException->destroy($postalCodeExceptionId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyPostalCodeExceptionModel(): PostalCodeException
    {
        return $this->postalCodeException->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function storeUpdatePostalCodeException(
        string $postalCodeStartWith,
        int $postalCodeCountryId,
        string $place,
        int $vatCountryId,
        string $inVatCountryStartDate,
        ?string $inVatCountryEndDate = null,
        ?int $postalCodeExceptionId = null
    ): PostalCodeException {
        if (!is_null($postalCodeExceptionId)) {
            $postCode = $this->getPostalCodeExceptionById($postalCodeExceptionId);
        } else {
            $postCode = $this->getEmptyPostalCodeExceptionModel();
        }

        $postCode->postal_code_start_with = strtoupper(str_replace(' ', '', $postalCodeStartWith));;
        $postCode->postal_code_country_id = $postalCodeCountryId;
        $postCode->place = $place;
        $postCode->vat_country_id = $vatCountryId;
        $postCode->in_vat_country_start_date = $inVatCountryStartDate;
        $postCode->in_vat_country_end_date = $inVatCountryEndDate;

        $postCode->save();

        return $postCode;
    }

    /**
     * @inheritDoc
     */
    public function getPostalCodeExceptionsByCountry(int $postalCodeCountryId): Collection
    {
        return $this->postalCodeException
            ->where('postal_code_country_id', $postalCodeCountryId)
            ->get();
    }


}
