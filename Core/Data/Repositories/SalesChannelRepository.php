<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\SalesChannel;
use App\Core\Data\Repositories\Contracts\SalesChannelRepositoryContract;
use Illuminate\Support\Collection;

class SalesChannelRepository implements SalesChannelRepositoryContract
{
    private SalesChannel $salesChannel;

    public function __construct(SalesChannel $salesChannel)
    {
        $this->salesChannel = $salesChannel;
    }

    /**
     * @inheritDoc
     */
    public function getAccountByUaid(?string $uaid = null): ?SalesChannel
    {
        return $this->salesChannel->where('uaid', $uaid)->first();
    }

    /**
     * @inheritDoc
     */
    public function getEmptySalesChannelModel(): SalesChannel
    {
        return $this->salesChannel->newInstance();
    }

    /**
     * @param int $salesChannelId
     */
    public function deleteSalesChannelById(int $salesChannelId): void
    {
        $this->salesChannel->destroy($salesChannelId);
    }

    /**
     * @param int $salesChannelId
     * @return SalesChannel|null
     */
    public function getSalesChannelById(int $salesChannelId): ?SalesChannel
    {
        return $this->salesChannel->find($salesChannelId);
    }

    public function getByCompanyId(int $companyId, ?int $platformId = null): Collection
    {
        $query = $this->salesChannel
            ->where('company_id', '=', $companyId);

        if (!is_null($platformId)) {
            $query->where('platform_id', '=', $platformId);
        }

        return $query->get();
    }

    /**
     * @inheritDoc
     */
    public function setAmazonRefreshTokenForSalesChannelId(int $salesChannelId, ?string $amazonRefreshToken = null): ?SalesChannel
    {
        $salesChannel = $this->getSalesChannelById($salesChannelId);
        if (is_null($salesChannel)) {
            return null;
        }
        $salesChannel->amazon_refresh_token = $amazonRefreshToken;
        $salesChannel->save();

        return $salesChannel;
    }

    /**
     * @inheritDoc
     */
    public function getSalesChannelsByUAIDs(array $uaids): Collection
    {
        return $this->salesChannel->whereIn('uaid', $uaids)->get();
    }

    public function getSalesChannelByUAID(string $uaid): ?SalesChannel
    {
        return $this->getSalesChannelsByUAIDs([$uaid])->first();
    }
}
