<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Contracts\Model;
use App\Core\Data\Models\IdentificationNumber;
use App\Core\Data\Repositories\Contracts\IdentificationNumberRepositoryContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class IdentificationNumberRepository implements IdentificationNumberRepositoryContract
{
    private IdentificationNumber $identificationNumber;

    public function __construct(IdentificationNumber $identificationNumber)
    {
        $this->identificationNumber = $identificationNumber;
    }

    public function getEmptyModel(): IdentificationNumber
    {
        return $this->identificationNumber->newInstance();
    }

    public function getById(int $id): ?IdentificationNumber
    {
        return $this->identificationNumber->find($id);
    }

    public function deleteById(int $id): void
    {
        $this->identificationNumber->destroy($id);
    }

    public function createNewIdentificationNumber(
        string $value,
        int $resourceId,
        string $resource,
        int $identificationNumberTypeId,
        ?int $countryId = null,
        ?string $startDate = null,
        ?string $note = null
    ): IdentificationNumber {
        $value = trim($value);
        $identificationNumber = $this->getEmptyModel();

        $identificationNumber->value = $value;
        $identificationNumber->resource_id = $resourceId;
        $identificationNumber->resource = $resource;
        $identificationNumber->country_id = $countryId;
        $identificationNumber->identification_number_type_id = $identificationNumberTypeId;
        $identificationNumber->start_date = $startDate;
        $identificationNumber->note = $note;

        $identificationNumber->save();

        return $identificationNumber;
    }

    public function updateNewIdentificationNumber(int $id, string $value, int $resourceId, string $resource, int $identificationNumberTypeId, ?int $countryId = null, ?string $startDate = null, ?string $note = null): IdentificationNumber
    {
        $value = trim($value);
        $identificationNumber = $this->getById($id);

        $identificationNumber->value = $value;
        $identificationNumber->resource_id = $resourceId;
        $identificationNumber->resource = $resource;
        $identificationNumber->country_id = $countryId;
        $identificationNumber->identification_number_type_id = $identificationNumberTypeId;
        $identificationNumber->start_date = $startDate;
        $identificationNumber->note = $note;

        $identificationNumber->save();

        return $identificationNumber;
    }

    public function findIdentificationNumberByValue(
        string $value,
        string $resource,
        int $resourceId,
        ?int $identificationNumberTypeId = null,
    ): EloquentCollection {
        $q = $this->identificationNumber
            ->where('resource', $resource)
            ->where('resource_id', $resourceId)
            ->where('value', $value);

        if ($identificationNumberTypeId !== null) {
            $q->where('identification_number_type_id', $identificationNumberTypeId);
        }

        return $q->get();
    }
}
