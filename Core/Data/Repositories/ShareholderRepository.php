<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\Shareholder;
use App\Core\Data\Repositories\Contracts\ShareholderRepositoryContract;


class ShareholderRepository implements ShareholderRepositoryContract
{
    /**
     * @var Shareholder
     */
    private $shareholder;

    public function __construct(Shareholder $shareholder)
    {
        $this->shareholder = $shareholder;
    }


    /**
     * @inheritDoc
     */
    public function getShareholderById(int $shareholderId): ?Shareholder
    {
        return $this->shareholder->find($shareholderId);
    }


    /**
     * @inheritDoc
     */
    public function getEmptyShareholderModel(): Shareholder
    {
        return $this->shareholder->newInstance();
    }

    /**
     * @param int $shareholderId
     */
    public function deleteShareholderById(int $shareholderId): void
    {
        $this->shareholder->destroy($shareholderId);
    }

}