<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\Contact;
use App\Core\Data\Repositories\Contracts\ContactRepositoryContract;
use Illuminate\Support\Collection;

class ContactRepository implements ContactRepositoryContract
{

    /**
     * @var Contact
     */
    private $contact;

    public function __construct(Contact $contact)
    {
        $this->contact = $contact;
    }

    /**
     * @inheritdoc
     */
    public function getAllContacts(): Collection
    {
        return $this->contact->all();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyContactModel(): Contact
    {
        return $this->contact->newInstance();
    }

    /**
     * @inheritdoc
     */
    public function getPersonContacts($personId): Collection
    {
        return $this->contact->where('person_id', $personId)->get();
    }

    /**
     * @inheritDoc
     */
    public function getContactById(int $contactId): ?Contact
    {
        return $this->contact->find($contactId);
    }

    /**
     * @param int $contactId
     */
    public function deleteContactById(int $contactId): void
    {
        $this->contact->destroy($contactId);
    }

    /**
     * @inheritDoc
     */
    public function getContactByCustomerId(int $customerId): Collection
    {
        return $this->contact->where('customer_id', $customerId)->get();
    }

    public function getContactBySupplierId(int $supplierId): Collection
    {
        return $this->contact->where('supplier_id', $supplierId)->get();
    }
}
