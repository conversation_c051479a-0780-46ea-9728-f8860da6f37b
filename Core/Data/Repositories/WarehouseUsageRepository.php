<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\RegistrationWarehouseUsage;
use App\Core\Data\Models\RegistrationWarehouseUsageType;
use App\Core\Data\Repositories\Contracts\WarehouseUsageRepositoryContract;
use Illuminate\Support\Collection;


class WarehouseUsageRepository implements WarehouseUsageRepositoryContract
{
    /**
     * @var RegistrationWarehouseUsage
     */
    private $warehouseUsage;

    /**
     * @var RegistrationWarehouseUsageType
     */
    private $warehouseType;

    public function __construct(
        RegistrationWarehouseUsage $warehouseUsage,
        RegistrationWarehouseUsageType $warehouseType
    ) {
        $this->warehouseUsage = $warehouseUsage;
        $this->warehouseType = $warehouseType;
    }


    /**
     * @inheritDoc
     */
    public function getWarehouseUsageById(int $warehouseUsageId): ?RegistrationWarehouseUsage
    {
        return $this->warehouseUsage->find($warehouseUsageId);
    }


    /**
     * @inheritDoc
     */
    public function getEmptyWarehouseUsageModel(): RegistrationWarehouseUsage
    {
        return $this->warehouseUsage->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function deleteCompanyWarehouseUsages(int $companyId): void
    {
        $this->warehouseUsage->where('company_id', $companyId)->delete();

        return;
    }

    /**
     * @inheritDoc
     */
    public function getAllWarehouseTypes(): Collection
    {
        return $this->warehouseType->get();
    }

}
