<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\PaymentRecipient;
use Illuminate\Support\Collection;
use App\Core\Data\Repositories\Contracts\PaymentRecipientsRepositoryContract;

class PaymentRecipientsRepository implements PaymentRecipientsRepositoryContract
{
    private PaymentRecipient $paymentRecipient;

    /**
     * @param PaymentRecipient $paymentRecipient
     */
    public function __construct(PaymentRecipient $paymentRecipient)
    {
        $this->paymentRecipient = $paymentRecipient;
    }

    /**
     * @inheritDoc
     */
    public function getPaymentRecipientById(int $id): ?PaymentRecipient
    {
        return $this->paymentRecipient->find($id);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyPaymentRecipientModel(): PaymentRecipient
    {
        return $this->paymentRecipient->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getPaymentRecipientByCountryIdAndCompanyId(int $countryId, int $companyId): Collection
    {
        return $this->paymentRecipient
            ->where('country_id', '=', $countryId)
            ->where('company_id', '=', $companyId)
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function deletePaymentRecipientById(int $paymentRecipientId): void
    {
        $paymentRecipient = $this->getPaymentRecipientById($paymentRecipientId);
        if (!is_null($paymentRecipient)) {
            $this->paymentRecipient->destroy($paymentRecipientId);
        }
    }
}
