<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\TaxPeriod;
use App\Core\Data\Models\TaxPeriodType;
use App\Core\Data\Repositories\Contracts\TaxPeriodRepositoryContract;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class TaxPeriodRepository implements TaxPeriodRepositoryContract
{
    private TaxPeriod $taxPeriod;

    public function __construct(TaxPeriod $taxPeriod)
    {
        $this->taxPeriod = $taxPeriod;
    }

    public function getAllTaxPeriods(): Collection
    {
        return $this->taxPeriod
            ->orderBy('tax_periods.id', 'ASC')
            ->get();
    }

    public function getMonthsTaxPeriods(): Collection
    {
        $months = [
            TaxPeriod::M1,
            TaxPeriod::M2,
            TaxPeriod::M3,
            TaxPeriod::M4,
            TaxPeriod::M5,
            TaxPeriod::M6,
            TaxPeriod::M7,
            TaxPeriod::M8,
            TaxPeriod::M9,
            TaxPeriod::M10,
            TaxPeriod::M11,
            TaxPeriod::M12,
        ];

        return $this->taxPeriod->whereIn('id', $months)
            ->orderBy('id')
            ->get();
    }

    public function getEmptyTaxPeriodModel(): TaxPeriod
    {
        return $this->taxPeriod->newInstance();
    }

    public function getTaxPeriodFromDatesAndTaxPeriodType(
        string $startDate,
        string $endDate,
        int $taxPeriodTypeId
    ): Collection {
        $startValue = Carbon::parse($startDate)->month;
        $endValue = Carbon::parse($endDate)->month;
        if ($taxPeriodTypeId === TaxPeriodType::Q) {
            $startValue = (int)($startValue / 3) + 1;
            $endValue = (int)($endValue / 3) + 1;
        }
        $range = range($startValue, $endValue);

        $query = $this->taxPeriod
            ->where('tax_period_type_id', '=', $taxPeriodTypeId);
        if ($taxPeriodTypeId !== TaxPeriodType::A) {
            $query->whereIn('value', $range);
        }

        return $query->get();
    }
}
