<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\FlatVatRate;
use App\Core\Data\Repositories\Contracts\FlatVatRateRepositoryContract;

class FlatVatRateRepository implements FlatVatRateRepositoryContract
{
    private FlatVatRate $flatVatRate;

    public function __construct(FlatVatRate $flatVatRate)
    {
        $this->flatVatRate = $flatVatRate;
    }

    public function getFlatVatRateById(int $flatVatRateId): ?FlatVatRate
    {
        return $this->flatVatRate->find($flatVatRateId);
    }

    public function getEmptyFlatVatRateModel(): FlatVatRate
    {
        return $this->flatVatRate->newInstance();
    }

    public function deleteFlatVatRateById(int $flatVatRateId): void
    {
        $this->flatVatRate->destroy($flatVatRateId);
    }
}
