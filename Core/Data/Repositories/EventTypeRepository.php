<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\EventType;
use App\Core\Data\Repositories\Contracts\EventTypeRepositoryContract;
use Illuminate\Support\Collection;

class EventTypeRepository implements EventTypeRepositoryContract
{
    private EventType $eventType;

    public function __construct(EventType $eventType)
    {
        $this->eventType = $eventType;
    }

    /**
     * @inheritDoc
     */
    public function getAllForAmazon(): Collection
    {
        return $this->eventType->whereNotNull('amazon_code')->get();
    }
}