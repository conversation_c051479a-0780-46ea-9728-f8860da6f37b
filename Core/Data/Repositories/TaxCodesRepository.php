<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\CountryItemTaxCode;
use App\Core\Data\Models\ItemCodeCategory;
use App\Core\Data\Models\ItemTaxCode;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\TaxationMethod;
use App\Core\Data\Repositories\Contracts\TaxCodesRepositoryContract;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TaxCodesRepository implements TaxCodesRepositoryContract
{
    private ItemTaxCode $taxCode;
    private ItemCodeCategory $itemCodeCategory;
    private ItemType $itemType;
    private TaxationMethod $taxationMethod;
    private CountryItemTaxCode $countryTaxCode;

    public function __construct(
        ItemTaxCode $taxCode,
        ItemCodeCategory $itemCodeCategory,
        ItemType $itemType,
        TaxationMethod $taxationMethod,
        CountryItemTaxCode $countryTaxCode
    ) {
        $this->taxCode = $taxCode;
        $this->itemCodeCategory = $itemCodeCategory;
        $this->itemType = $itemType;
        $this->taxationMethod = $taxationMethod;
        $this->countryTaxCode = $countryTaxCode;
    }

    public function getEmptyTaxCodeModel(): ItemTaxCode
    {
        return $this->taxCode->newInstance();
    }

    public function getEmptyCountryTaxCodeModel(): CountryItemTaxCode
    {
        return $this->countryTaxCode->newInstance();
    }

    public function getTaxCodeById(int $id): ItemTaxCode|null
    {
        return $this->taxCode->find($id);
    }

    /**
     * @inheritDoc
     */
    public function getAllTaxCodes(bool $paginate = true): Collection|LengthAwarePaginator
    {
        if ($paginate) {
            return $this->taxCode->orderBy('code')->paginate(300);
        } else {
            return $this->taxCode->all()->sortBy('code');
        }
    }

    public function storeUpdateTaxCode(
        string $code,
        int $itemCodeCategoryId,
        int $itemTypeId,
        int $taxationMethodId,
        ?string $description = null,
        ?int $id = null
    ): ItemTaxCode {
        $taxCode = $this->getEmptyTaxCodeModel();
        if (!is_null($id)) {
            $taxCode = $this->getTaxCodeById($id);
        }

        $code = str_replace(' ', '_', strtoupper(trim($code)));
        $taxCode->code = $code;
        $taxCode->item_code_category_id = $itemCodeCategoryId;
        $taxCode->item_type_id = $itemTypeId;
        $taxCode->taxation_method_id = $taxationMethodId;
        $taxCode->description = $description;
        $taxCode->save();

        return $taxCode;
    }

    public function destroyTaxCode(int $taxCodeId): void
    {
        $this->taxCode->find($taxCodeId)->delete();
    }

    public function getAllItemCodeCategories(): Collection
    {
        return $this->itemCodeCategory->orderBy('name')->get();
    }

    public function getAllItemTypes(): Collection
    {
        return $this->itemType->orderBy('name')->get();
    }

    public function getAllTaxationMethods(): Collection
    {
        return $this->taxationMethod->orderBy('name')->get();
    }
}
