<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\TaxPeriodType;
use App\Core\Data\Repositories\Contracts\TaxPeriodTypeRepositoryContract;
use Illuminate\Support\Collection;


class TaxPeriodTypeRepository implements TaxPeriodTypeRepositoryContract
{
    /**
     * @var TaxPeriodType
     */
    private $taxPeriodType;

    public function __construct(TaxPeriodType $taxPeriodType)
    {
        $this->taxPeriodType = $taxPeriodType;
    }


    /**
     * @inheritDoc
     */
    public function getAllTaxPeriodTypes(): Collection
    {
        return $this->taxPeriodType->all();
    }

}