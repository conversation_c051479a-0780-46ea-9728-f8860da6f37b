<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\BusinessModel;
use App\Core\Data\Repositories\Contracts\BusinessModelRepositoryContract;
use Illuminate\Support\Collection;

class BusinessModelRepository implements BusinessModelRepositoryContract
{
    private BusinessModel $businessModel;

    public function __construct(BusinessModel $businessModel)
    {
        $this->businessModel = $businessModel;
    }

    public function getAllBusinessModels(): Collection
    {
        return $this->businessModel->get();
    }
}
