<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\ContactType;
use Illuminate\Support\Collection;

interface ContactTypeRepositoryContract
{
    /**
     * @return Collection
     */
    public function getAllContactTypes(): Collection;

    /**
     * @param int $contactTypeId
     * @return ContactType|null
     */
    public function getContactTypeById(int $contactTypeId): ?ContactType;

    /**
     * @param array $contactTypeIds
     * @return Collection
     */
    public function getContactTypesByIds(array $contactTypeIds): Collection;

}
