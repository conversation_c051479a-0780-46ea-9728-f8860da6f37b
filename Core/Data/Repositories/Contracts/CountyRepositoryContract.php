<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\County;

interface CountyRepositoryContract
{

    /**
     * @param int $countyId
     * @return County|null
     */
    public function getCountyById(int $countyId): ?County;

    /**
     * @param string $countyName
     * @param string $countryId
     * @return County|null
     */
    public function getCountyByNameAndCountry(string $countyName, int $countryId): ?County;

    /**
     * @param int $countyId
     */
    public function deleteCountyById(int $countyId): void;

    /**
     * @return County
     */
    public function getEmptyCountyModel(): County;

    /**
     * @param string $name
     * @param int $countryId
     * @return County
     */
    public function createCountyIfNotExists(string $name, int $countryId): County;
}
