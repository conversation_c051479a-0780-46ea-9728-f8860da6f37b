<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\SampleFile;
use App\Core\Data\Models\SampleFileType;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface SampleFileRepositoryContract
{
    /**
     * @param int|null $countryId
     * @param int|null $sampleFileId
     * @param array|null $with
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(
        ?int $countryId = null,
        ?int $sampleFileId = null,
        ?array $with = null,
        int $perPage = 40
    ): LengthAwarePaginator;

    public function getSampleFileById(int $sampleFileId): ?SampleFile;

    public function deleteSampleFileById(int $sampleFileId): void;

    /**
     * @return SampleFile
     */
    public function getEmptySampleFileModel(): SampleFile;

    public function getSampleFileByTypeCountry(int $sampleFileTypeId, int $countryId): ?SampleFile;

    /**
     * @return Collection
     */
    public function getAllSampleFileTypes(): Collection;

    public function deleteSampleFileTypeById(int $sampleFileTypeId): void;

    /**
     * @return SampleFileType
     */
    public function getEmptySampleFileTypeModel(): SampleFileType;

    /**
     * @param int $sampleFileTypeId
     * @return SampleFileType|null
     */
    public function getSampleFileTypeById(int $sampleFileTypeId): ?SampleFileType;

    /**
     * @param string $search
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function searchSampleFile(string $search, ?int $perPage = 40): LengthAwarePaginator;

    /**
     * @param string $search
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function searchSampleFileTypes(string $search, ?int $perPage = 40): LengthAwarePaginator;
}
