<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyAccountant;

interface CompanyAccountantRepositoryContract
{
    /**
     * @return CompanyAccountant
     */
    public function getEmptyCompanyAccountantModel(): CompanyAccountant;

    /**
     * @param int|null $companyAccountantId
     * @return CompanyAccountant|null
     */
    public function getCompanyAccountantById(?int $companyAccountantId): ?CompanyAccountant;
}