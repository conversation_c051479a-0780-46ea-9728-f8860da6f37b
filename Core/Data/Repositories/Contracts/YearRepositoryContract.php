<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Year;
use Illuminate\Support\Collection;

interface YearRepositoryContract
{
    /**
     * @param string|null $orderBy
     * @param string|null $direction
     * @return Collection
     */
    public function getAllYears(?string $orderBy = 'value', ?string $direction = 'DESC'): Collection;

    /**
     * @return Year
     */
    public function getLatestYear(): Year;

    /**
     * @param int $yearId
     * @return Year|null
     */
    public function getYearById(int $yearId): ?Year;
}
