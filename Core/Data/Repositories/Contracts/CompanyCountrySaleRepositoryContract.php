<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyCountrySale;

interface CompanyCountrySaleRepositoryContract
{

    /**
     * @param int $companyCountrySaleId
     * @return CompanyCountrySale|null
     */
    public function getCompanyCountrySaleById(int $companyCountrySaleId): ?CompanyCountrySale;

    /**
     * @param int $companyCountrySaleId
     */
    public function deleteCompanyCountrySaleById(int $companyCountrySaleId): void;

    /**
     * @return CompanyCountrySale
     */
    public function getEmptyCompanyCountrySaleModel(): CompanyCountrySale;


}