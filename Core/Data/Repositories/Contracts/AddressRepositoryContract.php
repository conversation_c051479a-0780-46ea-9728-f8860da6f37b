<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Address;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface AddressRepositoryContract
{
    /**
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(int $perPage = 40): LengthAwarePaginator;

    /**
     * @param int $addressId
     * @return Address|null
     */
    public function getAddressById(int $addressId): ?Address;

    /**
     * @param int $addressId
     */
    public function deleteAddressById(int $addressId): void;

    /**
     * @return Address
     */
    public function getEmptyAddressModel(): Address;

    /**
     * @return Collection
     */
    public function getAllAddressTypes(): Collection;


}
