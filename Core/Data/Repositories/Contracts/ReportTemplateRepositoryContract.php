<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\ReportTemplate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface ReportTemplateRepositoryContract
{
    public function getAllReportTypes(): Collection;

    /**
     * @return ReportTemplate
     */
    public function getEmptyReportTemplateModel(): ReportTemplate;

    public function getReportTemplateForTypeOnDate(int $reportTypeId, string $dateFrom, string $interval): ?ReportTemplate;

    /**
     * @param int $reportId
     * @return ReportTemplate|null
     */
    public function getReportTemplateById(int $reportId): ?ReportTemplate;

    public function getAllReportTemplatesPaginate(
        int $resourceId,
        string $resourceClass,
        int $perPage = 40,
        bool $userIsAdministrative = false
    ): LengthAwarePaginator;

    public function deleteReportTemplateById(int $reportId): void;

    /**
     * @param int $reportId
     * @return void
     */
    public function deleteReportFieldsByReportTemplateId(int $reportId): void;

    public function insertReportTemplateFields(array $fields): void;
}
