<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\IdentificationNumber;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

interface IdentificationNumberRepositoryContract
{
    public function getEmptyModel(): IdentificationNumber;

    public function getById(int $id): ?IdentificationNumber;

    public function deleteById(int $id): void;

    public function createNewIdentificationNumber(
        string $value,
        int $resourceId,
        string $resource,
        int $identificationNumberTypeId,
        ?int $countryId = null,
        ?string $startDate = null,
        ?string $note = null
    ): IdentificationNumber;

    public function updateNewIdentificationNumber(
        int $id,
        string $value,
        int $resourceId,
        string $resource,
        int $identificationNumberTypeId,
        ?int $countryId = null,
        ?string $startDate = null,
        ?string $note = null
    ): IdentificationNumber;

    public function findIdentificationNumberByValue(
        string $value,
        string $resource,
        int $resourceId,
        ?int $identificationNumberTypeId = null,
    ): EloquentCollection;
}
