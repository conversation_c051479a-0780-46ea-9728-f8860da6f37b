<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Team;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface TeamRepositoryContract
{
    /**
     * @param int $perPage
     * @param array|null $with
     * @param int|null $institutionInstitutionTypeId
     * @param int|null $countryId
     * @param string|null $search
     * @param string|null $order
     * @param string|null $direction
     * @return LengthAwarePaginator
     */
    public function getAllTeamsPaginate(
        int $perPage = 40,
        ?array $with = null,
        ?int $institutionInstitutionTypeId = null,
        ?int $countryId = null,
        ?string $search = null,
        ?string $order = null,
        ?string $direction = null
    ): LengthAwarePaginator;

    /**
     * @param int $teamId
     * @return Team|null
     */
    public function getTeamById(int $teamId): ?Team;

    /**
     * @return Team
     */
    public function getEmptyTeamModel(): Team;

    /**
     * @param int $teamId
     */
    public function deleteTeamById(int $teamId): void;

    /**
     * @param array $teamUsers
     */
    public function insertTeamUsers(array $teamUsers): void;

    /**
     * @param array $teamUsersIds
     */
    public function deleteTeamUsersByIds(array $teamUsersIds): void;

    /**
     * @param int $userId
     */
    public function deleteTeamUsersByUserId(int $userId): void;

    /**
     * @param int $institutionInstitutionTypeId
     * @return Collection
     */
    public function getTeamsByInstitutionInstitutionTypeId(int $institutionInstitutionTypeId): Collection;
}
