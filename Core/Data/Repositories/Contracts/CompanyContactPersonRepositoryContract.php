<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyContactPerson;
use Illuminate\Support\Collection;

interface CompanyContactPersonRepositoryContract
{
    /**
     * @return Collection
     */
    public function getAllContactPersonTypes(): Collection;

    /**
     * @param int $companyContactPersonId
     */
    public function deleteCompanyContactPersonById(int $companyContactPersonId): void;

    /**
     * @param int $companyContactPersonId
     * @return CompanyContactPerson|null
     */
    public function getCompanyContactPersonById(int $companyContactPersonId): ?CompanyContactPerson;

    /**
     * @return CompanyContactPerson
     */
    public function getEmptyCompanyContactPersonModel(): CompanyContactPerson;
}
