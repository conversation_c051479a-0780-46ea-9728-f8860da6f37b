<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Company;
use App\Core\Data\Models\CompanyLegalRepresentative;
use App\Core\Data\Models\CompanyUserRole;
use App\Core\Data\Models\ImportGoodsReverseDocument;
use App\Core\Data\Models\InstitutionType;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface CompanyRepositoryContract
{
    public function getAll(
        ?array $with = null,
        ?array $companyStatusIds = null,
        ?int $partnerId = null
    ): ?Collection;

    public function getCompanyById(int $companyId): ?Company;

    public function getCompaniesByUserSearch(
        int $userId,
        ?string $search = null,
        ?array $companyIds = null,
        ?int $limit = null
    ): Collection;

    public function getCompaniesByUserSearchPaginate(
        int $userId,
        ?string $search = null,
        ?array $companyIds = null,
        ?int $perPage = null,
        ?array $with = [],
        ?array $companyStatusIds = null,
        ?int $partnerId = null,
        ?int $countryId = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): LengthAwarePaginator;

    public function getCompaniesForExportByUserSearch(
        int $userId,
        ?array $companyIds = null,
        ?array $with = [],
        ?array $companyStatusIds = null,
        ?int $partnerId = null
    ): Collection;

    public function getCompanyUserRoleByUserCompanyAndRoleId(int $userId, int $companyId, int $roleId): ?CompanyUserRole;

    public function getEmptyCompanyUserRoleModel(): CompanyUserRole;

    public function incrementCompanyUserSearch(int $companyId, int $userId): void;

    public function insertImportFromCountries(array $data): void;

    public function searchCompaniesBy(
        string $search,
        ?array $companyIds = null,
        ?int $perPage = 40
    ): ?LengthAwarePaginator;

    public function getEmptyCompanyModel(): Company;

    public function getAllCompaniesByPartnerId(int $institutionInstitutionTypeId, int $institutionTypeId = InstitutionType::TYPE_PARTNER): Collection;

    public function getCompaniesByIds(array $companyIds): ?Collection;

    public function deleteCompanyUserRoles(int $userId): void;

    public function getCompanyByPartnerCodeAndPartnerId(string $partnerCode, int $partnerInstitutionInstitutionTypeId): ?Company;

    public function getAllCompaniesIdsByAccountantInstitutionInstitutionTypeId(int $institutionInstitutionTypeId): array;

    public function insertCompanyUserRoles(array $roles): void;

    public function getImportGoodsReverseDocumentById(int $importGoodsReverseDocumentId): ?ImportGoodsReverseDocument;

    public function deleteImportGoodsReverseDocument(int $importGoodsReverseDocumentId): void;

    public function getEmptyImportGoodsReverseDocument(): ImportGoodsReverseDocument;

    public function createLegalRepresentative(int $companyId, int $personId): CompanyLegalRepresentative;

    public function getLegalRepresentative(int $companyId, int $personId): ?CompanyLegalRepresentative;

    public function deleteLegalRepresentative(int $companyId, int $personId): void;
}
