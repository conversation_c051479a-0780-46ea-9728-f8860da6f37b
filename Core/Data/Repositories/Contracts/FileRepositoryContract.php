<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\File;

interface FileRepositoryContract
{
    /**
     * @return File
     */
    public function getNewFileModel(): File;

    /**
     * @param int $fileId
     */
    public function deleteFileById(int $fileId): void;

    /**
     * @param int $fileId
     * @return File|null
     */
    public function getFileById(int $fileId): ?File;
}