<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CountryVatRate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface CountryVatRateRepositoryContract
{
    /**
     * @param int|null $countryId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllCountryVatRatesPaginate(?int $countryId, int $perPage = 40): LengthAwarePaginator;

    /**
     * @return CountryVatRate
     */
    public function getEmptyCountryVatRatesModel(): CountryVatRate;

    /**
     * @param int $countryVatRateId
     * @return CountryVatRate|null
     */
    public function getCountryVatRatesById(int $countryVatRateId): ?CountryVatRate;

    /**
     * @param int $countryVatRateId
     */
    public function deleteCountryVatRateById(int $countryVatRateId): void;
}
