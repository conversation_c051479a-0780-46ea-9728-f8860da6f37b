<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\TaxOffice;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface TaxOfficeRepositoryContract
{
    /**
     * @param int $taxOfficeId
     * @return TaxOffice|null
     */
    public function getTaxOfficeById(int $taxOfficeId): ?TaxOffice;

    /**
     * @param int $taxOfficeId
     */
    public function deleteTaxOfficeById(int $taxOfficeId): void;

    /**
     * @return TaxOffice
     */
    public function getEmptyTaxOfficeModel(): TaxOffice;

    /**
     * @return array
     */
    public function getOfficesInCountryIds(): array;

    /**
     * @param int $countryId
     * @return Collection
     */
    public function getOfficesForCountry(int $countryId): Collection;

    /**
     * @param int $companyId
     * @param int $countryId
     */
    public function deleteCompanyOfficeForCountry(int $companyId, int $countryId): void;

    /**
     * @param int $countryId
     * @param array|null $with
     * @param string|null $search
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function getAllOfficesInCountryPaginate(
        int $countryId,
        array $with = null,
        ?string $search = null,
        int $perPage = 40
    ): LengthAwarePaginator;

    /**
     * @param array $countryIds
     * @return Collection
     */
    public function getAllOfficesInCountries(array $countryIds): Collection;

    /**
     * @param string $search
     * @param int|null $countryId
     * @param int $limit
     * @return Collection
     */
    public function searchTaxOffices(string $search, ?int $countryId = null, int $limit = 10): Collection;


}