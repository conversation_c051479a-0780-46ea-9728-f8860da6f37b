<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\PostalCodeException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface PostalCodeRepositoryContract
{
    public function getPostalCodeExceptionsFromPostalCode(
        string $postalCode,
        int $postalCodeCountryId,
        int $perPage = 40
    ): LengthAwarePaginator;

    /**
     * @param int $postalCodeCountryId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPostalCodeExceptionsByCountryPaginate(int $postalCodeCountryId, int $perPage = 40): LengthAwarePaginator;

    /**
     * @param int $postalCodeExceptionId
     * @return PostalCodeException|null
     */
    public function getPostalCodeExceptionById(int $postalCodeExceptionId): ?PostalCodeException;

    /**
     * @param int $postalCodeExceptionId
     */
    public function deletePostalCodeExceptionById(int $postalCodeExceptionId): void;

    /**
     * @return PostalCodeException
     */
    public function getEmptyPostalCodeExceptionModel(): PostalCodeException;

    /**
     * @param string $postalCodeStartWith
     * @param int $postalCodeCountryId
     * @param string $place
     * @param int $vatCountryId
     * @param string $inVatCountryStartDate
     * @param string|null $inVatCountryEndDate
     * @param int|null $postalCodeExceptionId
     * @return PostalCodeException
     */
    public function storeUpdatePostalCodeException(
        string $postalCodeStartWith,
        int $postalCodeCountryId,
        string $place,
        int $vatCountryId,
        string $inVatCountryStartDate,
        ?string $inVatCountryEndDate = null,
        ?int $postalCodeExceptionId = null
    ): PostalCodeException;

    /**
     * @param int $postalCodeCountryId
     * @return Collection
     */
    public function getPostalCodeExceptionsByCountry(int $postalCodeCountryId): Collection;
}
