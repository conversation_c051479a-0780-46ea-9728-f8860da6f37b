<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\WarehouseOperator;

interface WarehouseOperatorRepositoryContract
{

    /**
     * @param int $warehouseOperatorId
     * @return WarehouseOperator|null
     */
    public function getWarehouseOperatorById(int $warehouseOperatorId): ?WarehouseOperator;

    /**
     * @param string $name
     * @return WarehouseOperator|null
     */
    public function getWarehouseOperatorByName(string $name): ?WarehouseOperator;

    /**
     * @param int $warehouseOperatorId
     */
    public function deleteWarehouseOperatorById(int $warehouseOperatorId): void;

    /**
     * @return WarehouseOperator
     */
    public function getEmptyWarehouseOperatorModel(): WarehouseOperator;

    /**
     * @param string $name
     * @return WarehouseOperator
     */
    public function createWarehouseOperatorIfNotExists(string $name): WarehouseOperator;
}
