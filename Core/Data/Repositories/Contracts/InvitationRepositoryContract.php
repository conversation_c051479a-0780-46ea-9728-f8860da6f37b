<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Invitation;
use Illuminate\Support\Collection;

interface InvitationRepositoryContract
{
    /**
     * @param int $invitationId
     * @return Invitation|null
     */
    public function getInvitationById(int $invitationId): ?Invitation;

    /**
     * @param string $invitationToken
     * @return Invitation|null
     */
    public function getInvitationByToken(string $invitationToken): ?Invitation;

    /**
     * @return Invitation
     */
    public function getEmptyInvitationModel(): Invitation;

    /**
     * @param array $invitationIds
     */
    public function deleteInvitationByIds(array $invitationIds): void;

    /**
     * @param string $email
     * @return Collection
     */
    public function getClientInvitationsByEmail(string $email): Collection;

    /**
     * @param string $email
     * @return Collection
     */
    public function getAccountantInvitationsByEmail(string $email): Collection;

    /**
     * @param int $companyId
     * @param string $email
     * @return Invitation|null
     */
    public function getClientInvitationByCompanyIdAndEmail(int $companyId, string $email): ?Invitation;

    /**
     * @param int $partnerId
     * @param string $email
     * @return Invitation|null
     */
    public function getAccountantInvitationByPartnerIdAndEmail(int $partnerId, string $email): ?Invitation;

    /**
     * @return Collection
     */
    public function getUnacceptedInvitations(): Collection;

    /**
     * @return Collection
     */
    public function getUnacceptedInvitationsForReminder(): Collection;
}