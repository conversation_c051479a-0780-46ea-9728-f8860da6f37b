<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\RegistrationProcess;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface RegistrationProcessRepositoryContract
{
    /**
     * @return Collection
     */
    public function getProcessStatuses(): Collection;

    /**
     * @param array $statusIds
     * @return Collection
     */
    public function getProcessStatusesByIds(array $statusIds): Collection;

    /**
     * @param int|null $statusId
     * @param int|null $perPage
     * @param array|null $companiesIds
     * @param array|null $countryIds
     * @param array|null $with
     * @return LengthAwarePaginator
     */
    public function getProcessesByStatus(
        ?int $statusId = null,
        ?int $perPage = 40,
        ?array $companiesIds = null,
        ?array $countryIds = null,
        ?array $with = null
    ): LengthAwarePaginator;

    /**
     * @param int|null $statusId
     * @param int|null $perPage
     * @param array|null $countryIds
     * @param array|null $companiesIds
     * @param array|null $companiesIdsPerCountries
     * @param array|null $with
     * @return LengthAwarePaginator
     */
    public function getProcessesByStatusForCompaniesPerCountries(
        ?int $statusId = null,
        ?int $perPage = 40,
        ?array $countryIds = null,
        ?array $companiesIds = null,
        ?array $companiesIdsPerCountries = null,
        ?array $with = null
    ): LengthAwarePaginator;

    /**
     * @param int $id
     * @return RegistrationProcess|null
     */
    public function getProcessById(int $id): ?RegistrationProcess;

    /**
     * @return RegistrationProcess
     */
    public function getEmptyRegistrationProcessModel(): RegistrationProcess;

    /**
     * @param int|null $countryId
     * @param array|null $companiesIds
     * @return Collection
     */
    public function getRegistrationProcessPerStatusCount(
        ?int $countryId = null,
        ?array $companiesIds = null
    ): Collection;

    /**
     * @param int|null $countryId
     * @param array|null $companiesIds
     * @param array|null $companiesIdsPerCountries
     * @return Collection
     */
    public function getRegistrationProcessPerStatusCountForCompaniesPerCountries(
        ?int $countryId = null,
        ?array $companiesIds = null,
        ?array $companiesIdsPerCountries = null
    ): Collection;

    public function setTerminationDate(array $registrationProcessIds, string $date): void;

    /**
     * @param int $companyId
     * @param int $countryId
     * @return RegistrationProcess
     */
    public function getRegistrationProcessByCompanyIdAndCountryId(
        int $companyId,
        int $countryId
    ): RegistrationProcess;
}
