<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\OnlineShop;

interface OnlineShopRepositoryContract
{

    /**
     * @param int $onlineShopId
     * @return OnlineShop|null
     */
    public function getOnlineShopById(int $onlineShopId): ?OnlineShop;

    /**
     * @param int $OnlineShopId
     */
    public function deleteOnlineShopById(int $onlineShopId): void;

    /**
     * @return OnlineShop
     */
    public function getEmptyOnlineShopModel(): OnlineShop;


}