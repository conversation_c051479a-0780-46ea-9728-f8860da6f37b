<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Currency;
use Illuminate\Support\Collection;

interface CurrencyRepositoryContract
{
    /**
     * @return Collection
     */
    public function getAllCurrencies(): Collection;

    /**
     * @return Currency
     */
    public function getEmptyCurrency(): Currency;

    /**
     * @param int $id
     * @return Currency|null
     */
    public function getCurrencyById(int $id): ?Currency;

    /**
     * Returns collection of currencies which will be safe to use inside evat_money() helper function
     *
     * @return Collection
     */
    public function getSafeEvatMoneyCurrencies(): Collection;
}
