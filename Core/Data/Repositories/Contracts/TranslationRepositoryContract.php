<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Translation;
use Illuminate\Database\Eloquent\Collection;

interface TranslationRepositoryContract
{
    public function deleteTranslationById(int $id): void;

    public function getAll(): Collection;

    public function getAllTranslationsByLocale(string $locale): Collection;

    public function getEmptyTranslationModel(): Translation;

    public function getTranslationById(int $id): ?Translation;

    public function getBaseTranslations(?string $search = null, int $limit = 100): Collection;

    public function getTranslationByKeyAndLocale(string $key, string $locale): ?Translation;

    public function insertTranslations(array $insert): void;
}
