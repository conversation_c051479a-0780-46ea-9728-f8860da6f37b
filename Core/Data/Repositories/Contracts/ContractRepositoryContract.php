<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Contract;
use Illuminate\Support\Collection;

interface ContractRepositoryContract
{
    public function getAllContracts(bool $paginate = false, ?int $institutionId = null, ?array $parameters = null);

    public function getEmptyContractModel(): Contract;

    public function getContractById(int $contractId): ?Contract;

    public function saveContract(array $contractParameters, int $institutionId): void;

    public function deleteContract(int $contractId): void;

    public function searchContractsByNameNumberCompany(string $search, int $institutionId, bool $isAdmin): Collection;
}
