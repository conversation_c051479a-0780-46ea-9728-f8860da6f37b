<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Shareholder;

interface ShareholderRepositoryContract
{
    /**
     * @param int $shareholderId
     * @return Shareholder|null
     */
    public function getShareholderById(int $shareholderId): ?Shareholder;


    /**
     * @return Shareholder
     */
    public function getEmptyShareholderModel(): Shareholder;

    /**
     * @param int $shareholderId
     */
    public function deleteShareholderById(int $shareholderId): void;

}