<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Person;

interface PersonRepositoryContract
{

    /**
     * @param int $personId
     * @return Person|null
     */
    public function getPersonById(int $personId): ?Person;


    /**
     * @param int $personId
     */
    public function deletePersonById(int $personId): void;

    /**
     * @return Person
     */
    public function getEmptyPersonModel(): Person;
}
