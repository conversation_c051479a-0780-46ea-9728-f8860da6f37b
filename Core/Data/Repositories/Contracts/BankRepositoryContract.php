<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Bank;

interface BankRepositoryContract
{

    /**
     * @param int $bankId
     * @return Bank|null
     */
    public function getBankById(int $bankId): ?Bank;


    /**
     * @return Bank
     */
    public function getEmptyBankModel(): Bank;

    /**
     * @param int $bankId
     */
    public function deleteBankById(int $bankId): void;


}
