<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\RegistrationWarehouseUsage;
use Illuminate\Support\Collection;

interface WarehouseUsageRepositoryContract
{

    /**
     * @param int $warehouseUsageId
     * @return RegistrationWarehouseUsage|null
     */
    public function getWarehouseUsageById(int $warehouseUsageId): ?RegistrationWarehouseUsage;


    /**
     * @return RegistrationWarehouseUsage
     */
    public function getEmptyWarehouseUsageModel(): RegistrationWarehouseUsage;

    /**
     * @param int $companyId
     * @return void
     */
    public function deleteCompanyWarehouseUsages(int $companyId): void;

    /**
     * @return Collection
     */
    public function getAllWarehouseTypes(): Collection;


}