<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyWarehouse;
use App\Core\Data\Models\Warehouse;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface WarehouseRepositoryContract
{

    /**
     * @param int $warehouseId
     * @return Warehouse|null
     */
    public function getWarehouseById(int $warehouseId): ?Warehouse;

    /**
     * @param string $warehouseName
     * @param int $countryId
     * @return Warehouse|null
     */
    public function getWarehouseByNameAndCountry(string $warehouseName, int $countryId): ?Warehouse;

    /**
     * @param int $warehouseId
     */
    public function deleteWarehouseById(int $warehouseId): void;

    /**
     * @return Warehouse
     */
    public function getEmptyWarehouseModel(): Warehouse;

    /**
     * @param string $name
     * @param int $countryId
     * @return Warehouse
     */
    public function createWarehouseIfNotExists(string $name, int $countryId): Warehouse;

    /**
     * @param bool|null $isConfirmed
     * @return Collection
     */
    public function getGlobalWarehouses(?bool $isConfirmed = true): Collection;

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getAllWarehousesByCompanyId(int $companyId): Collection;

    /**
     * @param int $proposeCompanyId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllProposeWarehousePaginateByCompanyId(int $proposeCompanyId, int $perPage = 40): LengthAwarePaginator;

    /**
     * @param int|null $companyId
     * @param int|null $warehouseId
     * @param int $perPage
     * @param string|null $search
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(
        ?int $companyId,
        ?int $warehouseId,
        int $perPage = 40,
        ?string $search = null
    ): LengthAwarePaginator;

    /**
     * @param int $companyWarehouseId
     * @return CompanyWarehouse|null
     */
    public function getCompanyWarehouseById(int $companyWarehouseId): ?CompanyWarehouse;

    /**
     * @param int $companyWarehouseId
     */
    public function deleteCompanyWarehouseById(int $companyWarehouseId): void;

    /**
     * @return CompanyWarehouse
     */
    public function getEmptyCompanyWarehouseModel(): CompanyWarehouse;

    /**
     * @param int $companyId
     * @param int $warehouseId
     * @return CompanyWarehouse|null
     */
    public function getCompanyWarehouseByCompanyAndWarehouseId(int $companyId, int $warehouseId): ?CompanyWarehouse;

    /**
     * @param int $companyId
     * @param int|null $companyWarehouseId
     * @param string|null $search
     * @param int|null $limit
     * @return Collection
     */
    public function getGlobalConfirmedWarehousesNotUsedByCompany(
        int $companyId,
        ?int $companyWarehouseId = null,
        ?string $search = null,
        ?int $limit = null
    ): Collection;

    /**
     * @param int|null $perPage
     * @param bool|null $proposed
     * @return LengthAwarePaginator
     */
    public function getAllGlobalWarehousesPaginate(?int $perPage = 40, ?bool $proposed = false): LengthAwarePaginator;

    /**
     * @param array $uids
     * @return Collection
     */
    public function getWarehousesByUids(array $uids): Collection;

    /**
     * @param string $search
     * @param int $companyId
     * @param int $limit
     * @return Collection
     */
    public function searchCompanyWarehouses(string $search, int $companyId, int $limit = 20): Collection;

    /**
     * @param string $search
     * @param int $limit
     * @return Collection
     */
    public function searchGlobalWarehouses(string $search, int $limit = 20): Collection;

    /**
     * @param string $search
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function searchWarehouseByNameOrUid(string $search, ?int $perPage = 40): LengthAwarePaginator;
}
