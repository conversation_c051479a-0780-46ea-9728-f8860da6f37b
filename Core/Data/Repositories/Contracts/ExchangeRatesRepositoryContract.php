<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\ExchangeRate;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface ExchangeRatesRepositoryContract
{
    public function getRatesForCurrency(int $currencyId, int $year): EloquentCollection;

    public function getRatesForDates(array $dates): Collection;

    public function getRatesBetweenDates(string $start, string $end): Collection;

    public function getLastDate(): string;

    public function getEmptyExchangeRateModel(): ExchangeRate;

    public function deleteAll(): void;
}
