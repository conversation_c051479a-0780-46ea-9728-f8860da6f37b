<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\OssNumber;
use Illuminate\Support\Collection;

interface OssNumberRepositoryContract
{
    public function getOssNumberById(int $ossNumberId): ?OssNumber;

    public function getEmptyOssNumberModel(): OssNumber;

    public function deleteOssNumberById(int $ossNumberId): void;

    public function getAllOssNumberTypes(): Collection;

    public function getEuCompanyOssNumberTypes(): Collection;

    public function getNonEuCompanyOssNumberTypes(): Collection;

    public function getMainOssNumber(int $companyId): ?OssNumber;

    public function getOssNumberByOssNumber(string $number): ?OssNumber;
}
