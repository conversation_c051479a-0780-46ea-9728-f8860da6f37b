<?php

namespace App\Core\Data\Repositories\Contracts;


use App\Core\Data\Models\Role;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface RoleRepositoryContract
{
    /**
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllRolesPaginate(int $perPage = 40): LengthAwarePaginator;

    /**
     * @return Collection
     */
    public function getAllRoles(): Collection;

    /**
     * @param int $roleId
     * @return Role|null
     */
    public function getRoleById(int $roleId): ?Role;

    /**
     * @return Role
     */
    public function getEmptyRoleModel(): Role;

    /**
     * @param int $roleId
     */
    public function deleteRoleById(int $roleId): void;

    public function getRolesForCompany(): EloquentCollection;
}
