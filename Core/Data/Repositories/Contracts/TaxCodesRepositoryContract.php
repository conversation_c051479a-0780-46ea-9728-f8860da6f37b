<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\ItemTaxCode;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface TaxCodesRepositoryContract
{
    /**
     * @param bool $paginate
     * @return Collection|LengthAwarePaginator
     */
    public function getAllTaxCodes(bool $paginate = true): Collection|LengthAwarePaginator;

    /**
     * @return ItemTaxCode
     */
    public function getEmptyTaxCodeModel(): ItemTaxCode;

    /**
     * @param int $id
     * @return ItemTaxCode|null
     */
    public function getTaxCodeById(int $id): ItemTaxCode|null;

    /**
     * @return Collection
     */
    public function getAllItemCodeCategories(): Collection;

    /**
     * @return Collection
     */
    public function getAllItemTypes(): Collection;

    /**
     * @return Collection
     */
    public function getAllTaxationMethods(): Collection;

    /**
     * @param int $taxCodeId
     * @return void
     */
    public function destroyTaxCode(int $taxCodeId): void;

    /**
     * @param string $code
     * @param int $itemCodeCategoryId
     * @param int $itemTypeId
     * @param int $taxationMethodId
     * @param string|null $description
     * @param int|null $id
     * @return ItemTaxCode
     */
    public function storeUpdateTaxCode(
        string $code,
        int $itemCodeCategoryId,
        int $itemTypeId,
        int $taxationMethodId,
        ?string $description = null,
        ?int $id = null
    ): ItemTaxCode;
}