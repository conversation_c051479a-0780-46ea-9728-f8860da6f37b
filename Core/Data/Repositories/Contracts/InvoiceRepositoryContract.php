<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceItem;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface InvoiceRepositoryContract
{
    /**
     * @param int $amazonReportId
     * @return void
     */
    public function deleteInvoicesByAmazonReportId(int $amazonReportId): void;

    public function getAllInvoiceSubtypes(): Collection;

    public function getAllInvoiceSubtypesByIds(array $invoiceSubtypesIds): Collection;

    public function getAllInvoicesPaginate(
        int $companyId,
        int $year,
        int $perPage = 40,
        ?int $invoiceStatusId = null,
        ?int $invoiceSubtypeId = null,
        ?array $with = null,
        ?string $search = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ): LengthAwarePaginator;

    public function getFirstSalesGroupedByCompanyIdCountryId(array $companiesIds): EloquentCollection;

    public function getInvoiceStatusesWithCountForCompany(
        int $companyId,
        int $year,
        ?int $invoiceSubtypeId = null,
        ?string $search = null
    ): EloquentCollection;

    public function getInvoicesDataForExport(
        int $companyId,
        ?int $invoiceSubtypeId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ):EloquentCollection;

    public function getInvoicesGroupedForEslReport(
        int $companyId,
        string $dateFrom,
        string $dateTo,
        array $subtypesIds
    ): EloquentCollection;

    public function getDistinctReportingCountriesByCompanyIdForPeriod(
        array $companiesIds,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?array $subtypesIds = null
    ): Collection;

    public function getInvoicesCountByCompaniesIds(array $companiesIds, int $limit = 10): Collection;

    public function getInvoicesMapperDataForVatReport(array $vatReportsIds, ?int $ossIssueCountryId = null, ?int $iossIssueCountryId = null): EloquentCollection;

    /**
     * @return Invoice
     */
    public function getEmptyInvoiceModel(): Invoice;

    /**
     * @return InvoiceItem
     */
    public function getEmptyInvoiceItemModel(): InvoiceItem;

    /**
     * @return Collection
     */
    public function getInvoiceTypes(): Collection;

    public function getInvoiceItemsForOssIossReport(
        int $companyId,
        string $startDate,
        string $endDate,
        ?array $invoiceItemTypeIds = null,
        ?array $invoiceSubtypeIds = null
    ): EloquentCollection;

    public function getMonthsWithInvoicesByCompanyIdForPeriod(int $companyId, string $dateFrom, string $dateTo): Collection;

    public function getYearsWithInvoicesByCompanyId(int $companyId): Collection;

    /**
     * @param Invoice $invoice
     * @return Invoice
     */
    public function storeInvoice(Invoice $invoice): Invoice;

    /**
     * @param Invoice $invoice
     * @param Collection $invoiceItems
     */
    public function storeInvoiceItems(Invoice $invoice, Collection $invoiceItems): void;

    /**
     * @param int $id
     * @return Invoice|null
     */
    public function getInvoiceById(int $id): ?Invoice;

    /**
     * @param int $invoiceId
     * @return mixed
     */
    public function deleteInvoiceById(int $invoiceId): void;

    public function getInvoiceReport(
        int $companyId,
        string $startDate,
        string $endDate,
        int $countryId
    ): Collection;

    /**
     * @return Collection
     */
    public function getAllInvoiceStatuses(): Collection;
}
