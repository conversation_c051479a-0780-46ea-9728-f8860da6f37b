<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CountryItemTaxCode;
use App\Core\Data\Models\Item;
use App\Core\Data\Models\ItemCategory;
use App\Core\Data\Models\ItemCommodityCodeType;
use App\Core\Data\Models\ItemItemIdentificationType;
use App\Core\Data\Models\ItemMarketplace;
use App\Core\Data\Models\ItemPurchasePrice;
use App\Core\Data\Models\ItemSalePrice;
use App\Core\Data\Models\ItemTaxCode;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface ItemRepositoryContract
{
    /**
     * @param int $amazonReportId
     * @return void
     */
    public function deleteNotConnectedItemsByAmazonReportId(int $amazonReportId): void;

    /**
     * @param int $itemId
     * @return Item|null
     */
    public function getItemById(int $itemId): ?Item;

    /**
     * @return Item
     */
    public function getEmptyItemModel(): Item;

    /**
     * @return Collection
     */
    public function getAllItemTaxCodes(): Collection;

    /**
     * @param int $countryId
     * @param int $perPage
     * @param string|null $search
     * @return LengthAwarePaginator
     */
    public function getAllCountryItemTaxCodesInCountryPaginate(
        int $countryId,
        int $perPage = 40,
        ?string $search = null
    ): LengthAwarePaginator;

    /**
     * @return Collection
     */
    public function getAllItemTypes(): Collection;

    /**
     * @return Collection
     */
    public function getAllItemCodeCategories(): Collection;

    /**
     * @return CountryItemTaxCode
     */
    public function getEmptyCountryItemTaxCodes(): CountryItemTaxCode;

    /**
     * @param int $countryItemTaxCodeId
     * @return CountryItemTaxCode|null
     */
    public function getCountryItemTaxCodeById(int $countryItemTaxCodeId): ?CountryItemTaxCode;

    /**
     * @param int $countryItemTaxCodeId
     */
    public function deleteCountryItemTaxCodeById(int $countryItemTaxCodeId): void;

    public function getCountryItemTaxCodeByCountryIdAndItemTaxCodeId(int $itemTaxCodeId, int $countryId, string $orderBy = 'from_data', string $direction = 'ASC'): Collection;

    /**
     * @return Collection
     */
    public function getAllItemIdentificationTypes(): Collection;

    /**
     * @return Collection
     */
    public function getAllCommodityCodes(): Collection;

    public function emptyItemCommodityCodeType(): ItemCommodityCodeType;

    /**
     * @param int $itemCommodityCodeTypeId
     * @return ItemCommodityCodeType|null
     */
    public function getItemCommodityCodeTypeById(int $itemCommodityCodeTypeId): ?ItemCommodityCodeType;

    /**
     * Returns left join item_item_identification_types on item_identification_types
     * Used for empty data set on create/edit item
     *
     * @return Collection
     */
    public function getAllAvailableItemItemIdentificationTypes(): Collection;

    /**
     * @return ItemItemIdentificationType
     */
    public function getEmptyItemItemIdentificationTypeModel(): ItemItemIdentificationType;

    /**
     * @param int $itemItemIdentificationTypeId
     * @return ItemItemIdentificationType|null
     */
    public function getItemItemIdentificationTypeById(int $itemItemIdentificationTypeId): ?ItemItemIdentificationType;

    /**
     * @param int $itemCommodityCodeTypeId
     */
    public function deleteItemCommodityCodeTypeById(int $itemCommodityCodeTypeId): void;

    /**
     * @return ItemPurchasePrice
     */
    public function getEmptyItemPurchasePrice(): ItemPurchasePrice;

    /**
     * @param int $itemPurchasePriceId
     * @return ItemPurchasePrice|null
     */
    public function getItemPurchasePriceById(int $itemPurchasePriceId): ?ItemPurchasePrice;

    /**
     * @return ItemSalePrice
     */
    public function getEmptyItemSalePrice(): ItemSalePrice;

    /**
     * @return ItemMarketplace
     */
    public function getEmptyItemMarketplace(): ItemMarketplace;

    /**
     * @param int $itemMarketplaceId
     * @return ItemMarketplace|null
     */
    public function getItemMarketplaceById(int $itemMarketplaceId): ?ItemMarketplace;

    /**
     * @param int $companyId
     * @param int|null $marketplaceId
     * @return Collection
     */
    public function getAllItemsByCompanyId(int $companyId, ?int $marketplaceId = null): Collection;

    /**
     * @param int $companyId
     * @param array|null $with
     * @param string|null $search
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function getAllItemsPaginateByCompanyId(
        int $companyId,
        ?array $with = null,
        ?string $search = null,
        ?int $perPage = null,
    ): LengthAwarePaginator;

    /**
     * @param int $itemSalePriceId
     * @return ItemSalePrice|null
     */
    public function getItemSalePriceById(int $itemSalePriceId): ?ItemSalePrice;

    /**
     * @param int $itemId
     */
    public function deleteItemById(int $itemId): void;

    public function getEmptyItemCategoryModel(): ItemCategory;

    public function getItemTaxCodeById(int $id): ?ItemTaxCode;

    public function getItemTaxCodesWithTaxRateForCountryOnDate(int $countryId, string $date, ?int $itemTypeId = null): EloquentCollection;

    public function getProductCategories(?int $id = null, ?string $search = null): Collection;

    public function getProductCategoryParents(int $productCategoryId): array;

    public function searchItemsPaginated(
        string $searchQuery,
        int $marketplaceId,
        int $invoiceCurrencyId,
        string $invoiceDate,
        int $companyId,
        ?int $itemTypeId = null
    ): LengthAwarePaginator;

    public function getItemsBySkusAndCompaniesIds(array $skus): Collection;

    public function getItemsByAsinsAndCompaniesIds(array $asins): Collection;

    public function deleteItemMarketplaceById(int $id, Item $item): ?bool;

    /**
     * @param int $taxCodeId
     * @param int|null $vatRateTypeId
     * @param string|null $vatFromDate
     * @param string|null $vatToDate
     * @return void
     */
    public function recreateTaxCodeForAllCountries(
        int $taxCodeId,
        ?int $vatRateTypeId = null,
        ?string $vatFromDate = null,
        ?string $vatToDate = null
    ): void;

    /**
     * @param int $countryId
     * @param int|null $itemTaxCodeId
     * @return Collection
     */
    public function getHistoryVatRate(int $countryId, ?int $itemTaxCodeId = null): Collection;
}
