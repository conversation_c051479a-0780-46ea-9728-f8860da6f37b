<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Store;
use Illuminate\Support\Collection;

interface StoreRepositoryContract
{
    /**
     * @param int $userId
     * @param string|null $key
     * @param string|null $channel
     * @return Collection
     */
    public function get(int $userId, ?string $key = null, ?string $channel = null): Collection;

    /**
     * @return Collection|null
     */
    public function getAllKeys(): ?Collection;

    /**
     * @return Store
     */
    public function getEmptyStoreModel(): Store;

    /**
     * @param int $storeId
     * @return Store|null
     */
    public function getStoreById(int $storeId): ?Store;

    /**
     * @param string $key
     * @param int $userId
     */
    public function deleteAllByKeyAndUser(string $key, int $userId): void;

    /**
     * @param int $storeId
     * @return void
     */
    public function deleteById(int $storeId): void;

    /**
     * @param string $channel
     * @param int $userId
     * @return void
     */
    public function deleteAllByChannelAndUser(string $channel, int $userId): void;
}
