<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\TaxPeriod;
use Illuminate\Support\Collection;

interface TaxPeriodRepositoryContract
{
    public function getAllTaxPeriods(): Collection;

    public function getMonthsTaxPeriods(): Collection;

    public function getEmptyTaxPeriodModel(): TaxPeriod;

    public function getTaxPeriodFromDatesAndTaxPeriodType(
        string $startDate,
        string $endDate,
        int $taxPeriodTypeId
    ): Collection;
}
