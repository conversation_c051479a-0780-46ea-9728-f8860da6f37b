<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\City;

interface CityRepositoryContract
{

    /**
     * @param int $cityId
     * @return City|null
     */
    public function getCityById(int $cityId): ?City;

    /**
     * @param string $cityName
     * @param int $countryId
     * @return City|null
     */
    public function getCityByNameAndCountry(string $cityName, int $countryId): ?City;

    /**
     * @param int $cityId
     */
    public function deleteCityById(int $cityId): void;

    /**
     * @return City
     */
    public function getEmptyCityModel(): City;

    /**
     * @param string $name
     * @param int $countryId
     * @return City
     */
    public function createCityIfNotExists(string $name, int $countryId): City;
}