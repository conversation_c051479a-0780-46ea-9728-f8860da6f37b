<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Supplier;
use App\Core\Data\Models\SupplierAddress;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface SupplierRepositoryContract
{
    public function deleteSuppliersIdentificationNumbersBySupplierId(int $supplierId): void;

    /**
     * @param int $supplierId
     * @return Supplier|null
     */
    public function getSupplierById(int $supplierId): ?Supplier;

    /**
     * @return Supplier
     */
    public function getEmptySupplierModel(): Supplier;

    /**
     * @return SupplierAddress
     */
    public function getEmptySupplierAddressModel(): SupplierAddress;

    /**
     * @param int $supplierAddressId
     * @return SupplierAddress|null
     */
    public function getSupplierAddressById(int $supplierAddressId): ?SupplierAddress;

    /**
     * @param int $supplierAddressId
     */
    public function deleteSupplierAddressById(int $supplierAddressId): void;

    /**
     * @param int|null $companyId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllSuppliersPaginate(
        ?int $companyId,
        int $perPage = 40
    ): LengthAwarePaginator;

    /**
     * @param int $supplierId
     * @return Collection
     */
    public function getSupplierAddressesBySupplierId(int $supplierId): Collection;

    /**
     * @param int $supplierId
     */
    public function deleteSupplierById(int $supplierId): void;

    public function getSuppliers(string $search, int $companyId): EloquentCollection;
}
