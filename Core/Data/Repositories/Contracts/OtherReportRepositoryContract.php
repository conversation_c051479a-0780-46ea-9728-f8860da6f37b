<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\OtherReport;
use App\Core\Data\Models\OtherReportStatus;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface OtherReportRepositoryContract
{
    /**
     * @return OtherReport
     */
    public function getEmptyOtherReportModel(): OtherReport;

    /**
     * @param int $companyId
     * @param int $statusId
     * @param int $uploadUserId
     * @param string|null $startDate
     * @param string|null $endDate
     * @param string|null $search
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getOtherReportsPaginate(
        int $companyId,
        int $statusId,
        int $uploadUserId,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $search = null,
        int $perPage = 40
    ): LengthAwarePaginator;

    /**
     * @param int $id
     * @return OtherReport|null
     */
    public function getOtherReportsById(int $id): ?OtherReport;

    /**
     * @return Collection<OtherReportStatus>
     */
    public function getAllOtherReportStatus(): Collection;

    /**
     * @param int $companyId
     * @param int $userId
     * @return Collection
     */
    public function getOtherReportsPerStatusCount(int $companyId, int $userId): Collection;
}
