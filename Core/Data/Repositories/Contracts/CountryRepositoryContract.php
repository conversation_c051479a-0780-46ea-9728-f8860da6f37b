<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\CountryCurrency;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface CountryRepositoryContract
{
    /**
     * @param array $ids
     * @return Collection
     */
    public function getAllCountriesByIds(array $ids): Collection;

    public function getAllInEu(bool $includeCommunities = false): Collection;

    /**
     * @return Collection
     */
    public function getAllNonEu(): Collection;

    /**
     * @param string $year
     * @return Collection
     */
    public function getAllInEuForYear(string $year): Collection;

    /**
     * @param string $year
     * @return Collection
     */
    public function getAllNonEuForYear(string $year): Collection;

    /**
     * @param string|null $year
     * @return Collection
     */
    public function getAllCountriesGroupedByEuMembership(string $year = null): Collection;

    /**
     * @param string|null $search
     * @return Collection|Country[]
     * @noinspection PhpDocSignatureInspection
     */
    public function getAllCountriesWhereDoingTax(?string $search = null): Collection;

    /**
     * @param int $userId
     * @param string|null $search
     * @param int|null $limit
     * @return Collection
     */
    public function getDoingTaxCountriesByUserSearch(int $userId, ?string $search = null, ?int $limit = null): Collection;

    /**
     * @param int $countryId
     * @return Country|null
     */
    public function getCountryById(int $countryId): ?Country;

    /**
     * @param string $countryCode
     * @return Country|null
     */
    public function getCountryByCode(string $countryCode): ?Country;

    public function getAllCountries(
        bool $showAll = false,
        bool $showEu = false,
        bool $showNonEu = false,
        ?string $search = null
    ): EloquentCollection;

    /**
     * @return Country
     */
    public function getEmptyCountryModel(): Country;

    /**
     * @return CountryCurrency
     */
    public function getEmptyCountryCurrencyModel(): CountryCurrency;

    /**
     * @param int $countryCurrencyId
     * @return CountryCurrency|null
     */
    public function getCountryCurrencyById(int $countryCurrencyId): ?CountryCurrency;

    /**
     * @param int $countryCurrencyId
     */
    public function deleteCountryCurrencyById(int $countryCurrencyId): void;

    /**
     * @param int $countryId
     * @param string $date
     * @return bool
     */
    public function isCountryInEu(int $countryId, string $date): bool;

    /**
     * @param string $search
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function searchCountryByNameOrCode(string $search, ?int $perPage = 40): LengthAwarePaginator;
}
