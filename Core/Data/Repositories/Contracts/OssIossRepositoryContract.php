<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\OssIossReport;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

interface OssIossRepositoryContract
{
    public function getNewOssIossReportModel(): OssIossReport;

    public function getOssReportsBetweenDates(int $companyId, string $dateFrom, string $dateTo): EloquentCollection;

    public function getIossReportsBetweenDates(int $companyId, string $dateFrom, string $dateTo): EloquentCollection;

    public function getOssReportByIdAndCompanyCheck($reportId, int $companyId): ?OssIossReport;

    public function getIossReportByIdAndCompanyCheck($reportId, int $companyId): ?OssIossReport;

    public function insertOssIossReports(array $insert): void;
}
