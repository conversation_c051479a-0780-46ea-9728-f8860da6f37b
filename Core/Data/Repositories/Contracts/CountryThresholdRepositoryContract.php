<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CountryThreshold;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface CountryThresholdRepositoryContract
{

    /**
     * @param int|null $countryId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(?int $countryId, int $perPage = 40): LengthAwarePaginator;

    /**
     * @return Collection
     */
    public function getAll(): Collection;

    /**
     * @return CountryThreshold
     */
    public function getEmptyCountryThresholdModel(): CountryThreshold;

    /**
     * @param int $countryThresholdId
     * @return CountryThreshold|null
     */
    public function getCountryThresholdById(int $countryThresholdId): ?CountryThreshold;

    /**
     * @param int $countryThresholdId
     */
    public function deleteCountryThresholdById(int $countryThresholdId): void;

    /**
     * @param int $yearId
     * @return Collection
     */
    public function getActiveCountryThresholds(int $yearId): Collection;

    public function getCountryThresholdByCountryAndYearId(int $countryId, int $yearId): ?CountryThreshold;

}
