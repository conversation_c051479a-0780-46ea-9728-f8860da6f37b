<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\IossNumber;
use Illuminate\Support\Collection;

interface IossNumberRepositoryContract
{
    public function getIossNumberById(int $iossNumberId): ?IossNumber;

    public function getEmptyIossNumberModel(): IossNumber;

    public function deleteIossNumberById(int $iossNumberId): void;

    public function getAllIossNumberTypes(): Collection;

    public function getEuCompanyIossNumberTypes(): Collection;

    public function getIossNumberByIossNumber(string $number): ?IossNumber;

    public function getNonEuCompanyIossNumberTypes(): Collection;

    public function getMainIossNumber(int $companyId): ?IossNumber;
}
