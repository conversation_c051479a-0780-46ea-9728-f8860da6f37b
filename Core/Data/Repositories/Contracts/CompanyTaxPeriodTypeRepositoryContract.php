<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyTaxPeriodType;
use Illuminate\Support\Collection;

interface CompanyTaxPeriodTypeRepositoryContract
{

    /**
     * @param int $companyTaxPeriodTypeId
     * @return CompanyTaxPeriodType|null
     */
    public function getCompanyTaxPeriodTypeById(int $companyTaxPeriodTypeId): ?CompanyTaxPeriodType;


    /**
     * @param int $companyTaxPeriodTypeId
     */
    public function deleteCompanyTaxPeriodTypeById(int $companyTaxPeriodTypeId): void;

    /**
     * @return CompanyTaxPeriodType
     */
    public function getEmptyCompanyTaxPeriodTypeModel(): CompanyTaxPeriodType;


    public function getCompanyTaxPeriodTypes(
        int $companyId,
        string $startDate
    ): Collection;
}
