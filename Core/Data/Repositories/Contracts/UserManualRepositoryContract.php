<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\UserManual;
use App\Core\Data\Models\UserManualMedia;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

interface UserManualRepositoryContract
{
    /**
     * @return UserManual
     */
    public function getEmptyUserManual(): UserManual;

    /**
     * @return UserManualMedia
     */
    public function getEmptyUserManualMedia(): UserManualMedia;

    /**
     * @param int $userManualMediaId
     * @return UserManualMedia|null
     */
    public function getUserManualMediaById(int $userManualMediaId): ?UserManualMedia;

    public function getUserManualByRouteName(string $routeName, ?string $urlParameterKey = null, ?string $urlParameterValue = null): EloquentCollection;

    /**
     * @param int $userManualId
     * @return UserManual|null
     */
    public function getUserManualById(int $userManualId): ?UserManual;

    /**
     * @param int $userManualMediaId
     * @return void
     */
    public function deleteUserManualMediaById(int $userManualMediaId): void;

    public function insertUserManuals(array $insert): void;
}
