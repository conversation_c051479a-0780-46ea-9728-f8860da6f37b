<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface UserRepositoryContract
{
    public function getUserByEmail(
        string $email,
        bool $includeAdministrative = false,
        bool $includeDeleted = false,
        bool $forgotPassword = false
    ): ?User;

    public function getUserByPasswordResetToken(string $token): ?User;

    public function getAllUsersPaginate(
        int $perPage = 40,
        ?array $with = null,
        ?int $institutionInstitutionTypeId = null,
        bool $includeAdministrative = false,
        ?int $roleId = null,
        ?string $search = null,
        ?string $orderBy = null,
        ?string $orderDirection = null
    ): LengthAwarePaginator;

    public function getUserById(int $userId, bool $includeAdministrative = false): ?User;

    public function getEmptyUserModel(): User;

    public function setUserHasToken(int $userId, bool $hasToken = false): User;

    public function searchUserByName(?string $search = null): ?Collection;

    public function getAllUsersForInstitution(int $institutionInstitutionTypeId, ?int $roleId = null): Collection;

    public function getCompaniesUsers(array $companyIds): Collection;

    public function getUserByEmailConfirmationToken(string $token): ?User;

    public function getUsersWithUnverifiedEmailForReminder(): Collection;

    public function getUserByLoginOnceToken(string $loginOnceToken): ?User;

    public function deleteUsersWithUnconfirmedEmail(): void;

    public function isUserLocked(string $username): bool;

    public function generatePasswordResetToken(User $user): User;

    public function lockAccount(string $username): User|null;

    public function unlockAccount(string $token): User|null;
}
