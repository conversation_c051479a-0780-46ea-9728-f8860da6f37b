<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\TaxScheme;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface TaxSchemesRepositoryContract
{
    /**
     * @param int|null $countryId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(?int $countryId, int $perPage = 40): LengthAwarePaginator;

    /**
     * @return TaxScheme
     */
    public function getEmptyTaxSchemeModel(): TaxScheme;

    /**
     * @param int $taxSchemeId
     * @return TaxScheme|null
     */
    public function getTaxSchemeById(int $taxSchemeId): ?TaxScheme;

    /**
     * @return Collection
     */
    public function getAllTaxSchemeGroups(): Collection;

    /**
     * @return Collection
     */
    public function getAllTaxSchemeNames(): Collection;

    /**
     * @param int $taxSchemeId
     * @return void
     */
    public function deleteTaxSchemeById(int $taxSchemeId): void;

    /**
     * @param int $countryId
     * @return Collection
     */
    public function getTaxSchemeByCountryId(int $countryId): Collection;

    /**
     * @param int $taxSchemeNameId
     * @param int $countryId
     * @return TaxScheme|null
     */
    public function getTaxSchemeByTaxSchemeNameId(int $taxSchemeNameId, int $countryId): ?TaxScheme;
}
