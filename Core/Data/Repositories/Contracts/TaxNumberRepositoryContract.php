<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\TaxNumber;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface TaxNumberRepositoryContract
{

    /**
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(int $perPage = 40): LengthAwarePaginator;

    /**
     * @param int $taxNumberId
     * @return TaxNumber|null
     */
    public function getTaxNumberById(int $taxNumberId): ?TaxNumber;

    /**
     * @param int $taxNumberId
     */
    public function deleteTaxNumberById(int $taxNumberId): void;

    /**
     * @return TaxNumber
     */
    public function getEmptyTaxNumberModel(): TaxNumber;

    /**
     * @param array $ids
     */
    public function deleteTaxNumbersByIds(array $ids): void;

    public function getTaxNumberByTaxNumber(string $number): ?TaxNumber;

    public function getTaxNumbersByIds(array $taxNumberIds): Collection;


}
