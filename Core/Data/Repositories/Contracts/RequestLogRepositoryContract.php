<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\RequestLog;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface RequestLogRepositoryContract
{
    /**
     * @return RequestLog
     */
    public function getNewRequestLogModel(): RequestLog;

    /**
     * @param string $time
     * @return Collection
     */
    public function getGetPostCount(string $time): Collection;

    /**
     * @param int $limit
     * @param string|null $dateFrom
     * @return Collection
     */
    public function getMostVisitedUrls(int $limit, ?string $dateFrom = null): Collection;

    /**
     * @param int $limit
     * @param string|null $dateFrom
     * @return Collection
     */
    public function getMostUsedAgents(int $limit, ?string $dateFrom = null): Collection;

    /**
     * No. of milliseconds
     *
     * @param string $date
     * @return float
     */
    public function getAverageRequestTime(string $date): float;

    /**
     * @param int $limit
     * @param string|null $dateFrom
     * @return Collection
     */
    public function getMostMemoryConsumingUrls(int $limit, ?string $dateFrom = null): Collection;

    /**
     * @param int $limit
     * @return Collection
     */
    public function getMostQueriesConsumingUrls(int $limit, ?string $dateFrom = null): Collection;

    /**
     * @param int $limit
     * @param string|null $dateFrom
     * @return Collection
     */
    public function getHardcoreUsers(int $limit, ?string $dateFrom = null): Collection;

    /**
     * @param string|null $dateFrom
     * @return int
     */
    public function getRequestsCount(?string $dateFrom = null): int;

    /**
     * @param int $limit
     * @param string|null $dateFrom
     * @return Collection
     */
    public function getLongestLastingUrls(int $limit, ?string $dateFrom = null): Collection;

    /**
     * @param string $dateFrom
     * @return int
     */
    public function getOnlineUsers(string $dateFrom): int;

    /**
     * @return LengthAwarePaginator
     */
    public function getRequestLogPaginated(): LengthAwarePaginator;
}
