<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\VatReport;
use Illuminate\Contracts\Pagination\LengthAwarePaginator as LengthAwarePaginatorContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Pagination\LengthAwarePaginator;

interface VatReportRepositoryContract
{
    public function getAllDistinctCountriesIdsByCompaniesIdsAndDates(array $companiesIds, string $dateFrom, string $dateTo): EloquentCollection;

    public function getAllVatReportsForCompanies(array $companiesIds, ?int $year = null): EloquentCollection;

    public function getAllVatReportsForCompaniesPaginate(
        array $companiesIds,
        int $year,
        int $statusId,
        ?int $companyId = null,
        ?int $countryId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?string $search = null,
        ?int $perPage = null
    ): LengthAwarePaginatorContract|LengthAwarePaginator;

    public function getNewVatReportModel(): VatReport;

    public function getVatReportForCompanyByCountryAndDates(int $companyId, int $countryId, string $dateFrom, string $dateTo): ?VatReport;

    public function getVatReportById(int $vatReportId): ?VatReport;

    public function getVatReportByIdAndCompanyCheck(int $vatReportId, array $companiesIds): ?VatReport;

    public function getVatReportsCountPerStatusByCompaniesIds(
        array $companiesIds,
        int $year,
        ?int $countryId = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): EloquentCollection;

    public function insertVatReports(array $toInsert): void;

    public function massUpdateVatReportDataAndStatus(array $data): void;
}
