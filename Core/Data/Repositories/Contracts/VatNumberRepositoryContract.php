<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\VatNumber;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

interface VatNumberRepositoryContract
{

    /**
     * @param int $companyId
     * @param int $countryId
     * @return bool
     */
    public function exist(int $companyId, int $countryId): bool;

    /**
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(int $perPage = 40): LengthAwarePaginator;

    /**
     * @return VatNumber
     */
    public function getEmptyVatNumberModel(): VatNumber;

    /**
     * @param int $vatNumberId
     * @return VatNumber|null
     */
    public function getVatNumberById(int $vatNumberId): ?VatNumber;

    /**
     * @param int $vatNumberId
     */
    public function deleteVatNumberById(int $vatNumberId): void;

    /**
     * @param array $ids
     */
    public function deleteVatNumbersByIds(array $ids): void;

    public function getVatNumberByVatNumber(string $vatNumber): ?VatNumber;

    /**
     * @param array $vatNumbers
     * @return EloquentCollection
     */
    public function getVatNumbersByNumbers(array $vatNumbers): EloquentCollection;
}
