<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\SalesChannel;
use Illuminate\Support\Collection;

interface SalesChannelRepositoryContract
{
    /**
     * @param string|null $uaid
     * @return SalesChannel|null
     */
    public function getAccountByUaid(?string $uaid = null): ?SalesChannel;

    /**
     * @return SalesChannel
     */
    public function getEmptySalesChannelModel(): SalesChannel;

    /**
     * @param int $salesChannelId
     * @return SalesChannel|null
     */
    public function getSalesChannelById(int $salesChannelId): ?SalesChannel;

    /**
     * @param int $salesChannelId
     */
    public function deleteSalesChannelById(int $salesChannelId): void;

    public function getByCompanyId(int $companyId, ?int $platformId = null): Collection;

    /**
     * @param array $uaids
     * @return Collection
     */
    public function getSalesChannelsByUAIDs(array $uaids): Collection;

    /**
     * @param string $uaid
     * @return SalesChannel|null
     */
    public function getSalesChannelByUAID(string $uaid): ?SalesChannel;

    /**
     * @param int $salesChannelId
     * @param string|null $amazonRefreshToken
     * @return SalesChannel|null
     */
    public function setAmazonRefreshTokenForSalesChannelId(int $salesChannelId, ?string $amazonRefreshToken = null): ?SalesChannel;
}
