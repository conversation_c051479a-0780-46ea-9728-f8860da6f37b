<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Institution;
use App\Core\Data\Models\InstitutionInstitutionType;
use App\Core\Data\Models\InstitutionTypeCountryPartner;
use App\Core\Data\Models\InstitutionBranch;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface InstitutionRepositoryContract
{
    public function searchInstitutions(
        string $search,
        ?array $institutionTypeIds = null,
        ?array $skipIds = null,
        ?array $institutionInstitutionTypeIds = null,
        ?int $limit = 40
    ): Collection;

    public function insertInstitutionInstitutionTypeUserRoles(array $roles): void;

    public function deleteInstitutionInstitutionTypeUserRole(int $userId): void;

    public function deleteInstitutionInstitutionTypeUserRolesByIds(array $institutionInstitutionTypeUserRolesIds): void;

    public function getAllPaginate(?int $institutionTypeId = null, int $perPage = 40): LengthAwarePaginator;

    public function getInstitutionById(int $institutionId): ?Institution;

    public function deleteInstitutionById(int $institutionId): void;

    public function getEmptyInstitutionModel(): Institution;

    public function getInstitutionTypesForInstitution(): Collection;

    public function getInstitutionInstitutionTypeByInstitutionIdAndInstitutionTypeId(int $institutionId, int $institutionTypeId): ?InstitutionInstitutionType;

    public function getEmptyInstitutionInstitutionTypeModel(): InstitutionInstitutionType;

    public function deleteInstitutionInstitutionTypeByInstitutionId(int $institutionId): void;

    public function insertInstitutionTypePartners(array $institutionTypePartners): void;

    public function getInstitutionTypePartnersByPartnerId(int $partnerId): Collection;

    public function getInstitutionInstitutionTypeById(int $institutionInstitutionTypeId): ?InstitutionInstitutionType;

    public function getInstitutionTypePartnersById(int $institutionTypePartnerId): ?InstitutionTypeCountryPartner;

    public function deleteInstitutionTypePartnersByIds(array $ids): void;

    public function getInstitutionTypePartnersByTypeByIds(int $institutionInstitutionTypeId): EloquentCollection;

    public function getInstitutionTypeAccountantByTypeByIds(int $institutionInstitutionTypeId): EloquentCollection;

    public function getEmptyInstitutionBranchModel(): InstitutionBranch;

    public function getInstitutionBranchById(int $institutionBranchId): ?InstitutionBranch;

    public function deleteInstitutionBranchById(int $institutionBranchId): void;

    public function getAllInstitutionsTypes(): Collection;

    public function searchInstitutionsByFullLegalNameOrAuthorizedPerson(string $search, ?int $perPage = 40): LengthAwarePaginator;

    public function getAllInstitutionInstitutionsTypes(): Collection;
}
