<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\AmazonReportRawData;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface AmazonReportRepositoryContract
{
    public function addMonthsToAmazonReportsQueue(array $rows): void;

    public function deleteAllAmazonApiQueueBySalesChannelIds(array $salesChannelsIds): void;

    public function deleteAmazonReportRawDataByAmazonReportId(int $amazonReportId): void;

    public function getAllReportsForSalesChannels(array $salesChannelsIds): Collection;

    public function getCountOfQueuedReportsByBatchId(string $batchId): int;

    public function getFailedAmazonReportRawDataPaginate(int $amazonReportId): LengthAwarePaginator;

    public function getNewAmazonReportModel(): AmazonReport;

    public function getReportById(int $reportId): ?AmazonReport;

    public function getQueuedReports(?int $limit = null): Collection;

    public function getReadReports(?int $limit = null): Collection;

    public function getAllResolvedReports(?int $limit = null): Collection;

    public function getAllSyncedReports(?int $limit = null): Collection;

    public function getAmazonReportRawDataInvoices(int $amazonReportId, int $amazonReportStatusId, int $cycle, int $limit): Collection;

    public function getRawAmazonReportRawDataByUniqueInvoiceKeys(int $amazonReportId, string $keys): array;

    public function getReportsByOriginalId(int $originalId): EloquentCollection;

    public function getReportsCountPerStatusForInstitution(
        string $startDate,
        string $endDate,
        ?int $institutionInstitutionTypeId = null,
        ?string $search = null,
        ?int $companyId = null,
        bool $checkCompaniesInstitutionsSalesChannels = true,
    ): EloquentCollection;

    public function getReportsForStatusForInstitution(
        array $reportStatusIds,
        string $startDate,
        string $endDate,
        ?int $institutionInstitutionTypeId = null,
        ?array $with = null,
        ?string $search = null,
        ?int $companyId = null,
        bool $checkCompaniesInstitutionsSalesChannels = true
    ): LengthAwarePaginator;

    public function getParsedCount(
        ?int $yearId = null,
        ?array $companiesIds = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $users = null
    ): int;

    public function getYearReportsPerStatusCount(int $userId, int $yearId): Collection;

    public function getAllReportStatuses(): Collection;

    public function getMonthsFromAmazonReportsQueue(int $limit, ?string $status = null): Collection;

    public function getAmazonReportRawDataById(int $amazonReportRawDataId): ?AmazonReportRawData;

    /**
     * @param array $statusIds
     * @return \Illuminate\Database\Eloquent\Collection<AmazonReport>
     */
    public function getAmazonReportsForStatus(array $statusIds): EloquentCollection;
}
