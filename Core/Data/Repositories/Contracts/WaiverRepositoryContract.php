<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Waiver;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface WaiverRepositoryContract
{

    /**
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(?int $companyId, ?int $countryId, int $perPage = 40): LengthAwarePaginator;

    /**
     * @param int $waiverId
     * @return Waiver|null
     */
    public function getWaiverById(int $waiverId): ?Waiver;

    /**
     * @param int $waiverId
     */
    public function deleteWaiverById(int $waiverId): void;

    /**
     * @return Waiver
     */
    public function getEmptyWaiverModel(): Waiver;


}