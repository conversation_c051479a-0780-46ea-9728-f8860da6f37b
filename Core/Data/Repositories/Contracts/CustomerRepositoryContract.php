<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Customer;
use App\Core\Data\Models\CustomerAddress;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface CustomerRepositoryContract
{
    public function deleteCustomerIdentificationNumbersByCustomerId(int $customerId): void;

    /**
     * @param int $customerId
     * @return Customer|null
     */
    public function getCustomerById(int $customerId): ?Customer;

    /**
     * @return Customer
     */
    public function getEmptyCustomerModel(): Customer;

    /**
     * @param int|null $companyId
     * @return Collection
     */
    public function getCustomersByCompanyId(?int $companyId): Collection;

    /**
     * @param string $search
     * @return Collection
     */
    public function getCustomers(string $search): Collection;

    /**
     * @return CustomerAddress
     */
    public function getEmptyCustomerAddressModel(): CustomerAddress;

    public function getCustomersByCompanyIdSearch(int $companyId, string $searchTerm): LengthAwarePaginator;

    /**
     * @param int $customerAddressId
     * @return CustomerAddress|null
     */
    public function getCustomerAddressById(int $customerAddressId): ?CustomerAddress;

    /**
     * @param int $customerAddressId
     */
    public function deleteCustomerAddressById(int $customerAddressId): void;

    /**
     * @param int|null $companyId
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllCustomersPaginate(
        ?int $companyId,
        int $perPage = 40
    ): LengthAwarePaginator;

    /**
     * @param int $customerId
     */
    public function deleteCustomerById(int $customerId): void;

    /**
     * @param int $customerId
     * @return Collection
     */
    public function getCustomersAddressesByCustomerId(int $customerId): Collection;
}
