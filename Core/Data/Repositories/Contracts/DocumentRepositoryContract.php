<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Document;
use App\Core\Data\Models\DocumentData;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;

interface DocumentRepositoryContract
{
    public function deleteAllDocumentDataByDocumentId(int $documentId): void;

    public function getDocumentsForCompany(
        int $companyId,
        ?int $countryId = null,
        ?array $categoriesIds = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $resource = null,
        ?int $resourceId = null,
        ?array $showAllDocumentsFromParentCategoriesIds = null,
        bool $showHidden = false
    ): EloquentCollection;

    public function getEmptyDocumentDataModel(): DocumentData;

    public function getEmptyDocumentModel(): Document;

    public function getDocumentById(int $documentId): ?Document;

    public function getAllDocumentDataFields(?bool $userInput = null): Collection;

    public function insertDocumentData(array $data): void;
}
