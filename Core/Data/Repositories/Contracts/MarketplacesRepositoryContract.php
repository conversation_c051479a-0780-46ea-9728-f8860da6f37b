<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Marketplace;
use App\Core\Data\Models\Platform;
use Illuminate\Support\Collection;

interface MarketplacesRepositoryContract
{
    public function getAllPlatformsForCompanyById(int $companyId): Collection;

    public function getAllForAmazon(): Collection;

    public function getAllPlatforms(): Collection;

    public function getEmptyMarketplaceModel(): Marketplace;

    public function getAllMarketplacesByCompanyById(int $companyId): Collection;

    public function getMarketplaceById(int $marketplaceId): Marketplace;

    public function getPlatformById(int $platformId): Platform;
}
