<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\DocumentCategory;
use App\Core\Data\Models\DocumentCategoryCountry;
use App\Core\Data\Models\DocumentCategoryOptions;
use App\Core\Data\Models\DocumentCategoryPlatform;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface DocumentCategoryRepositoryContract
{
    public function deleteDocumentCategory(int $categoryId): void;

    public function deleteDocumentCategoryPlatforms(array $documentCategoryPlatformsIds): void;

    public function deleteDocumentCategoryCountries(array $documentCategoryCountriesIds): void;

    public function getAllChildDocumentCategories(?int $parentCategoryId = null, ?string $search = null): Collection;

    public function getAllChildDocumentCategoriesSearch(string $search, int $limit = 100): LengthAwarePaginator;

    public function getAllDocumentCategoriesForCompanyCountryByCountries(int $companyCountryId, array $vatRegistrationCountriesIds, ?int $parentCategoryId = null): Collection;

    public function getAllDocumentCategoriesForPlatforms(array $platformsIds, ?int $parentCategoryId = null): Collection;

    public function getParentDocumentCategories(?array $ids = null, bool $showHidden = false): Collection;

    public function getDocumentCategoryById(int $id): ?DocumentCategory;

    public function getEmptyDocumentCategoryModel(): DocumentCategory;

    public function getEmptyDocumentCategoryOptionsModel(): DocumentCategoryOptions;

    public function getLatestSequence(?int $parentCategoryId = null): int;

    public function insertDocumentCategoryPlatforms(array $insert): void;

    public function insertDocumentCategoryCountries(array $insert): void;

    public function getEmptyDocumentCategoryCountryModel(): DocumentCategoryCountry;

    public function getEmptyDocumentCategoryPlatformModel(): DocumentCategoryPlatform;
}
