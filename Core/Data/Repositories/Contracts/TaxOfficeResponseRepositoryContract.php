<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\TaxOfficeResponseType;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

interface TaxOfficeResponseRepositoryContract
{
    /**
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function getAllPaginate(?int $perPage = 40): LengthAwarePaginator;

    /**
     * @return TaxOfficeResponseType
     */
    public function getNewTaxOfficeResponseTypeModel(): TaxOfficeResponseType;

    /**
     * @param int $taxOfficeResponseTypeId
     * @return TaxOfficeResponseType|null
     */
    public function getTaxOfficeResponseTypeById(int $taxOfficeResponseTypeId): ?TaxOfficeResponseType;
}