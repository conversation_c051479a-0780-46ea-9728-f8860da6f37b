<?php

namespace App\Core\Data\Repositories\Contracts;

use Illuminate\Support\Collection;
use App\Core\Data\Models\Contact;


interface ContactRepositoryContract
{
    /**
     * @return Collection
     */
    public function getAllContacts(): Collection;

    /**
     * @return Contact
     */
    public function getEmptyContactModel(): Contact;

    /**
     * @param int $personId
     * @return Collection
     */
    public function getPersonContacts($personId): Collection;

    /**
     * @param int $contactId
     * @return Contact|null
     */
    public function getContactById(int $contactId): ?Contact;

    /**
     * @param int $contactId
     */
    public function deleteContactById(int $contactId): void;

    /**
     * @param int $customerId
     * @return Collection
     */
    public function getContactByCustomerId(int $customerId): Collection;

    /**
     * @param int $supplierId
     * @return Collection
     */
    public function getContactBySupplierId(int $supplierId): Collection;

}
