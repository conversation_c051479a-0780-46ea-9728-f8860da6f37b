<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\ShopifyAccountInit;
use App\Core\Data\Models\ShopifyBulkFile;
use App\Core\Data\Models\ShopifyInstallRequest;
use App\Core\Data\Models\ShopifyMandatoryWebhookRequest;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface ShopifyRepositoryContract
{
    /**
     * @param int $shopifyBulkFileId
     * @return ShopifyBulkFile|null
     */
    public function getShopifyBulkFileById(int $shopifyBulkFileId): ?ShopifyBulkFile;

    /**
     * @return ShopifyBulkFile
     */
    public function getEmptyShopifyBulkFileModel(): ShopifyBulkFile;

    /**
     * @param int $shopifyBulkFileId
     */
    public function deleteShopifyBulkFileById(int $shopifyBulkFileId): void;

    /**
     * @return Collection
     */
    public function getAllCompletedBulkFilesReadyForDownload(): Collection;

    /**
     * @param int $bulkFileTypeId
     * @return Collection
     */
    public function getShopifyBulkFilesForFileParsing(int $bulkFileTypeId): Collection;

    /**
     * @param int $bulkFileTypeId
     * @return Collection
     */
    public function getShopifyBulkFilesForRawDataParsing(int $bulkFileTypeId): Collection;

    /**
     * @return ShopifyAccountInit
     */
    public function getEmptyShopifyAccountInitModel(): ShopifyAccountInit;

    /**
     * @param array|null $companyIds
     * @param int|null $yearId
     * @param int|null $perPage
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int|null $ecommerceAccountId
     * @param int|null $bulkFileStatusId
     * @return LengthAwarePaginator
     */
    public function getAllShopifyBulkOrdersFilesPaginate(
        ?array $companyIds = null,
        ?int $yearId = null,
        ?int $perPage = 40,
        ?string $startDate = null,
        ?string $endDate = null,
        ?int $ecommerceAccountId = null,
        ?int $bulkFileStatusId = null
    ): LengthAwarePaginator;

    /**
     * @return Collection
     */
    public function getAllBulkFileStatuses(): Collection;

    /**
     * @param string $bulkOperationId
     * @return ShopifyBulkFile|null
     */
    public function getShopifyBulkFileByBulkOperationId(string $bulkOperationId): ?ShopifyBulkFile;

    /**
     * @return Collection
     */
    public function getAllRequestedWithoutUrlBulkFiles(): Collection;

    /**
     * @param array|null $companyIds
     * @param string|null $startDate
     * @param string|null $endDate
     * @param array|null $userIds
     * @param array|null $bulkFileStatusIds
     * @return Collection
     */
    public function getOrderBulkFilesForExport(
        ?array $companyIds = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $userIds = null,
        ?array $bulkFileStatusIds = null
    ): Collection;

    /**
     * @param string $search
     * @param array|null $companyIds
     * @return Collection
     */
    public function getShopifyBulkOrdersFilesSalesChannels(
        string $search,
        ?array $companyIds = null
    ): Collection;

    /**
     * @return ShopifyMandatoryWebhookRequest
     */
    public function getEmptyShopifyMandatoryWebhookRequestModel(): ShopifyMandatoryWebhookRequest;

    /**
     * @param bool $paginate
     * @param string|null $receivedBeforeDate
     * @param string|null $receivedAfterDate
     * @param int|null $requestTypeId
     * @param bool|null $completed
     * @param int|null $perPage
     * @return LengthAwarePaginator|Collection
     */
    public function getAllShopifyMandatoryWebhookRequests(
        bool $paginate = true,
        ?string $receivedBeforeDate = null,
        ?string $receivedAfterDate = null,
        ?int $requestTypeId = null,
        ?bool $completed = null,
        ?int $perPage = 40
    ): LengthAwarePaginator|Collection;

    /**
     * @return Collection
     */
    public function getAllShopifyMandatoryWebhookRequestTypes(): Collection;

    /**
     * @param int $shopifyMandatoryWebhookRequestId
     * @return ShopifyMandatoryWebhookRequest|null
     */
    public function getShopifyMandatoryWebhookRequestById(int $shopifyMandatoryWebhookRequestId): ?ShopifyMandatoryWebhookRequest;

    /**
     * @param int $salesChannelId
     * @return ShopifyAccountInit|null
     */
    public function getShopifyAccountInitModelBySalesChannelId(int $salesChannelId): ?ShopifyAccountInit;

    /**
     * @param int $salesChannelId
     * @return ShopifyBulkFile|null
     */
    public function getShopifyBulkFileModelBySalesChannelId(int $salesChannelId): ?ShopifyBulkFile;

    /**
     * @return ShopifyInstallRequest
     */
    public function getEmptyShopifyInstallRequestModel(): ShopifyInstallRequest;


    /**
     * @param int $shopifyInstallRequestId
     * @param string $shopifyAccessToken
     * @return ShopifyInstallRequest|null
     */
    public function setShopifyAccessTokenForInstallRequestId(int $shopifyInstallRequestId, string $shopifyAccessToken): ?ShopifyInstallRequest;

    /**
     * @param string $shopName
     * @return ShopifyInstallRequest|null
     */
    public function getShopifyInstallRequestByShopName(string $shopName): ?ShopifyInstallRequest;

    /**
     * @param int $installRequestId
     * @return ShopifyInstallRequest|null
     */
    public function getShopifyInstallRequestById(int $installRequestId): ?ShopifyInstallRequest;

    /**
     * @param ShopifyMandatoryWebhookRequest $webhookRequest
     * @return string
     */
    public function getShopOwnerEmailAddress(ShopifyMandatoryWebhookRequest $webhookRequest): string;

    /**
     * @param ShopifyMandatoryWebhookRequest $webhookRequest
     * @return array
     */
    public function getCustomerShopifyData(ShopifyMandatoryWebhookRequest $webhookRequest): array;

    /**
     * @param ShopifyMandatoryWebhookRequest $webhookRequest
     * @return void
     */
    public function deleteShopifyCustomerData(ShopifyMandatoryWebhookRequest $webhookRequest): void;
}
