<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Brand;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface BrandRepositoryContract
{
    public function getAllBrands(): Collection;

    public function getAllPaginated(int $perPage = 40, ?string $search = null): LengthAwarePaginator;

    public function getBrandById(int $brandId, array|string $relationships = null): ?Brand;

    public function getEmptyBrandModel(): Brand;

    public function deleteBrandById(int $brandId): void;
}
