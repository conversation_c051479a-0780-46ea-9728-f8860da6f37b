<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyTaxPaymentData;

interface CompanyTaxPaymentRepositoryContract
{
    /**
     * @return CompanyTaxPaymentData
     */
    public function getEmptyCompanyTaxPaymentModel(): CompanyTaxPaymentData;

    /**
     * @param int $companyTaxPaymentId
     * @return CompanyTaxPaymentData|null
     */
    public function getCompanyTaxPaymentById(int $companyTaxPaymentId): ?CompanyTaxPaymentData;
}
