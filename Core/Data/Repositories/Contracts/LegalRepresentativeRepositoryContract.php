<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\CompanyLegalRepresentative;

interface LegalRepresentativeRepositoryContract
{
    /**
     * @return CompanyLegalRepresentative
     */
    public function getEmptyLegalRepresentativeModel(): CompanyLegalRepresentative;

    /**
     * @param int $legalRepresentativeId
     * @return CompanyLegalRepresentative|null
     */
    public function getLegalRepresentativeById(int $legalRepresentativeId): ?CompanyLegalRepresentative;

    /**
     * @param int $personId
     * @param int $companyId
     */
    public function getLegalRepresentativeByPersonAndCompanyIds(int $personId, int $companyId);
}
