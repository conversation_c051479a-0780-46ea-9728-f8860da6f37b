<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\DocumentTemplate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

interface DocumentTemplateRepositoryContract
{
    public function getAllDocumentTemplates(): EloquentCollection;

    public function getDocumentTemplateByCategoryIdForDate(int $documentCategoryId, string $date): ?DocumentTemplate;

    /**
     * @return DocumentTemplate
     */
    public function getEmptyDocumentTemplateModel(): DocumentTemplate;

    /**
     * @param int $documentTemplateId
     * @return DocumentTemplate|null
     */
    public function getDocumentTemplateById(int $documentTemplateId): ?DocumentTemplate;

    /**
     * @param int $perPage
     * @param array|null $with
     * @param string|null $search
     * @return LengthAwarePaginator
     */
    public function getAllDocumentTemplatePaginate(
        int $perPage = 40,
        ?array $with = null,
        ?string $search = null
    ): LengthAwarePaginator;

    public function deleteDocumentTemplateById(int $documentTemplateId): void;

    /**
     * @param int $documentTemplateId
     * @return void
     */
    public function deleteDocumentTemplateFieldsByDocumentTemplateId(int $documentTemplateId): void;

    public function insertDocumentTemplateFields(array $fields): void;
}
