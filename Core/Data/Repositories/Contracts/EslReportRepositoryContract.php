<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\EslReport;
use Illuminate\Database\Eloquent\Collection;

interface EslReportRepositoryContract
{
    public function getAllReportsForCompany(
        int $companyId,
        ?int $countryId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): Collection;

    public function getNewEslReportModel(): EslReport;

    public function getReportByIdAndCompanyCheck($reportId, int $companyId): ?EslReport;

    public function getReportForCompanyByCountryAndDates(int $companyId, int $countryId, string $dateFrom, string $dateTo): ?EslReport;

    public function getReportsByIds(array $reportsIds): Collection;

    public function insertReports(array $toInsert): void;

    public function massUpdateVatReportDataAndStatus(array $data): void;
}
