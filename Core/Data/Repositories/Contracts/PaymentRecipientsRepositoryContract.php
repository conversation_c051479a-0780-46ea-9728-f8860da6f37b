<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\PaymentRecipient;
use Illuminate\Support\Collection;

interface PaymentRecipientsRepositoryContract
{
    /**
     * @param int $id
     * @return PaymentRecipient|null
     */
    public function getPaymentRecipientById(int $id): ?PaymentRecipient;

    /**
     * @return PaymentRecipient
     */
    public function getEmptyPaymentRecipientModel(): PaymentRecipient;

    /**
     * @param int $countryId
     * @param int $companyId
     * @return Collection
     */
    public function getPaymentRecipientByCountryIdAndCompanyId(int $countryId, int $companyId): Collection;

    /**
     * @param int $paymentRecipientId
     * @return void
     */
    public function deletePaymentRecipientById(int $paymentRecipientId): void;
}
