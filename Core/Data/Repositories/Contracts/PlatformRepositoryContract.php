<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Platform;
use Illuminate\Support\Collection;

interface PlatformRepositoryContract
{
    /**
     * @param int $platformId
     * @return Platform|null
     */
    public function getPlatformById(int $platformId): ?Platform;

    /**
     * @return Platform
     */
    public function getEmptyPlatformModel(): Platform;

    /**
     * @return Collection
     */
    public function getAllPlatforms(): Collection;
}