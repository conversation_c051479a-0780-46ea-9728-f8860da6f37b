<?php

namespace App\Core\Data\Repositories\Contracts;

use App\Core\Data\Models\Notification;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface NotificationRepositoryContract
{
    public function deleteNotificationById(int $notificationId): void;

    /**
     * @return Notification
     */
    public function getNewNotificationModel(): Notification;

    /**
     * @param int $notificationId
     * @return Notification|null
     */
    public function getById(int $notificationId): ?Notification;

    /**
     * @param int $userId
     * @param bool|null $read
     * @return Collection
     */
    public function getAllByUserId(int $userId, ?bool $read = null): Collection;

    /**
     * @param array $notificationsIds
     * @param int $userId
     * @param array $columns
     */
    public function updateNotificationsByIdsForUser(array $notificationsIds, int $userId, array $columns): void;

    /**
     * @return Collection
     */
    public function getAll(): Collection;

    /**
     * @param int $perPage
     * @param string $type
     * @param int|null $forUser
     * @return LengthAwarePaginator
     */
    public function getAllPaginated(string $type, int $perPage = 20, ?int $forUser = null): LengthAwarePaginator;

    /**
     * @param array $notifications
     */
    public function insertNotifications(array $notifications): void;

    /**
     * @param Carbon $dateTime
     * @param bool|null $read
     */
    public function deleteNotificationsOlderThen(Carbon $dateTime, ?bool $read = null): void;
}
