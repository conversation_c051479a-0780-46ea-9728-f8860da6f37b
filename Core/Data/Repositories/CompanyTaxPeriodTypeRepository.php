<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\CompanyTaxPeriodType;
use App\Core\Data\Repositories\Contracts\CompanyTaxPeriodTypeRepositoryContract;
use Illuminate\Support\Collection;

class CompanyTaxPeriodTypeRepository implements CompanyTaxPeriodTypeRepositoryContract
{
    /**
     * @var companyTaxPeriodType
     */
    private $companyTaxPeriodType;

    public function __construct(CompanyTaxPeriodType $companyTaxPeriodType)
    {
        $this->companyTaxPeriodType = $companyTaxPeriodType;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyTaxPeriodTypeById(int $companyTaxPeriodTypeId): ?CompanyTaxPeriodType
    {
        return $this->companyTaxPeriodType->find($companyTaxPeriodTypeId);
    }

    /**
     * @param int $companyTaxPeriodTypeId
     */
    public function deleteCompanyTaxPeriodTypeById(int $companyTaxPeriodTypeId): void
    {
        $this->companyTaxPeriodType->destroy($companyTaxPeriodTypeId);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyCompanyTaxPeriodTypeModel(): CompanyTaxPeriodType
    {
        return $this->companyTaxPeriodType->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getCompanyTaxPeriodTypes(
        int $companyId,
        string $startDate
    ): Collection {
        return $this->companyTaxPeriodType
            ->where('company_id', '=', $companyId)
            ->where('start_date', '<=', $startDate)
            ->get();
    }

}
