<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\TaxSchemeGroup;
use App\Core\Data\Models\TaxSchemeName;
use App\Core\Data\Models\TaxScheme;
use App\Core\Data\Repositories\Contracts\TaxSchemesRepositoryContract;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TaxSchemesRepository implements TaxSchemesRepositoryContract
{
    private TaxScheme $taxScheme;
    private TaxSchemeGroup $taxSchemeGroup;
    private TaxSchemeName $taxSchemeName;

    /**
     * @param TaxScheme $taxScheme
     * @param TaxSchemeGroup $taxSchemeGroup
     * @param TaxSchemeName $taxSchemeName
     */
    public function __construct(
        TaxScheme $taxScheme,
        TaxSchemeGroup $taxSchemeGroup,
        TaxSchemeName $taxSchemeName
    ) {
        $this->taxScheme = $taxScheme;
        $this->taxSchemeGroup = $taxSchemeGroup;
        $this->taxSchemeName = $taxSchemeName;
    }

    /**
     * @inheritDoc
     */
    public function getAllPaginate(?int $countryId, int $perPage = 40): LengthAwarePaginator
    {
        $q = $this->taxScheme->with('country', 'taxSchemeGroup', 'taxSchemeName');
        if (!is_null($countryId)) {
            $q = $q->where('country_id', '=', $countryId);
        }
        $q = $q->orderBy('id');

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyTaxSchemeModel(): TaxScheme
    {
        return $this->taxScheme->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getTaxSchemeById(int $taxSchemeId): ?TaxScheme
    {
        return $this->taxScheme->find($taxSchemeId);
    }

    /**
     * @inheritDoc
     */
    public function getAllTaxSchemeGroups(): Collection
    {
        return $this->taxSchemeGroup->orderBy('id')->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllTaxSchemeNames(): Collection
    {
        return $this->taxSchemeName->orderBy('id')->get();
    }

    /**
     * @inheritDoc
     */
    public function deleteTaxSchemeById(int $taxSchemeId): void
    {
        $taxScheme = $this->getTaxSchemeById($taxSchemeId);
        if (!is_null($taxScheme)) {
            $this->taxScheme->destroy($taxSchemeId);
        }
    }

    /**
     * @inheritDoc
     */
    public function getTaxSchemeByCountryId(int $countryId): Collection
    {
        return $this->taxScheme->where('country_id', '=', $countryId)->get();
    }

    /**
     * @inheritDoc
     */
    public function getTaxSchemeByTaxSchemeNameId(int $taxSchemeNameId, int $countryId): ?TaxScheme
    {
        return $this->taxScheme
            ->where('country_id', '=', $countryId)
            ->where('tax_scheme_name_id', '=', $taxSchemeNameId)
            ->first();
    }
}
