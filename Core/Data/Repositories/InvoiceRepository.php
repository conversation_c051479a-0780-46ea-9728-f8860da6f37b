<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\AmazonReport;
use App\Core\Data\Models\Invoice;
use App\Core\Data\Models\InvoiceItem;
use App\Core\Data\Models\InvoiceStatus;
use App\Core\Data\Models\InvoiceSubtype;
use App\Core\Data\Models\InvoiceType;
use App\Core\Data\Models\ItemType;
use App\Core\Data\Models\TaxCollectionResponsibility;
use App\Core\Data\Models\TaxReportingScheme;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class InvoiceRepository implements InvoiceRepositoryContract
{
    private Invoice $invoice;
    private InvoiceType $invoiceType;
    private InvoiceItem $invoiceItem;
    private InvoiceStatus $invoiceStatus;
    private InvoiceSubtype $invoiceSubtype;

    public function __construct(
        Invoice $invoice,
        InvoiceType $invoiceType,
        InvoiceItem $invoiceItem,
        InvoiceStatus $invoiceStatus,
        InvoiceSubtype $invoiceSubtype
    ) {

        $this->invoice = $invoice;
        $this->invoiceType = $invoiceType;
        $this->invoiceItem = $invoiceItem;
        $this->invoiceStatus = $invoiceStatus;
        $this->invoiceSubtype = $invoiceSubtype;
    }

    public function getAllInvoicesPaginate(
        int $companyId,
        int $year,
        int $perPage = 40,
        ?int $invoiceStatusId = null,
        ?int $invoiceSubtypeId = null,
        ?array $with = null,
        ?string $search = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ): LengthAwarePaginator {
        if (!is_null($dateFrom)) {
            $dateFrom = Carbon::parse($dateFrom);
        }
        if (!is_null($dateTo)) {
            $dateTo = Carbon::parse($dateTo);
        }

        if (is_null($dateFrom) && is_null($dateTo)) {
            $dateFrom = Carbon::parse($year . '-01-01')->startOfYear();
            $dateTo = $dateFrom->clone()->endOfYear();
        }

        $q = $this->invoice
            ->select('invoices.*')
            ->where('invoices.company_id', '=', $companyId)
            ->leftJoin('countries AS vcc', 'invoices.tax_calculation_country_id', '=', 'vcc.id')
            ->leftJoin('countries AS trc', 'invoices.tax_reporting_country_id', '=', 'trc.id')
            ->leftJoin('countries AS wc', 'invoices.ship_from_country_id', '=', 'wc.id');

        if (!is_null($with)) {
            $q->with($with);
        }

        if (!is_null($invoiceStatusId)) {
            $q = $q->where('invoices.invoice_status_id', $invoiceStatusId);
        }

        if (!is_null($invoiceSubtypeId)) {
            $q = $q->where('invoices.invoice_subtype_id', $invoiceSubtypeId);
        }

        if (!is_null($dateFrom)) {
            $q = $q->where('invoices.invoice_date', '>=', $dateFrom->toDateString());
        }

        if (!is_null($dateTo)) {
            $q = $q->where('invoices.invoice_date', '<=', $dateTo->toDateString());
        }

        if (!is_null($search)) {
            $searchTerm = '%' . $search . '%';
            $q = $q->where(function (Builder $builder) use ($searchTerm) {
                $builder->where('invoice_number', 'ILIKE', $searchTerm)
                    ->orWhere('parsing_invoice_number', 'ILIKE', $searchTerm)
                    ->orWhere('ship_to_vat_number', 'ILIKE', $searchTerm)
                    ->orWhere('ship_from_vat_number', 'ILIKE', $searchTerm)
                    ->orWhere('ship_to_postal_code', 'ILIKE', $searchTerm)
                    ->orWhere('order_number', 'ILIKE', $searchTerm)
                    ->orWhere('resource', 'ILIKE', $searchTerm)
                    ->orWhere('vcc.name', 'ILIKE', $searchTerm)
                    ->orWhere('trc.name', 'ILIKE', $searchTerm)
                    ->orWhere('wc.name', 'ILIKE', $searchTerm);
            });
        }
        $q->orderBy('invoice_date', 'DESC');

        return $q->paginate($perPage);

    }

    public function getEmptyInvoiceModel(): Invoice
    {
        return $this->invoice->newInstance();
    }

    /**
     * @inheritdoc
     */
    public function getInvoiceTypes(): Collection
    {
        return $this->invoiceType
            ->orderBy('sequence')
            ->get();
    }

    public function getEmptyInvoiceItemModel(): InvoiceItem
    {
        return $this->invoiceItem->newInstance();
    }

    public function storeInvoice(Invoice $invoice): Invoice
    {
        $invoice = $invoice->unsetRelations();
        $invoice->save();

        return $invoice;
    }

    /**
     * @param \App\Core\Data\Models\Invoice $invoice
     * @param \Illuminate\Support\Collection<InvoiceItem> $invoiceItems
     * @return void
     */
    public function storeInvoiceItems(Invoice $invoice, Collection $invoiceItems): void
    {
        $insert = [];
        $fields = collect(InvoiceItem::getAllTableColumns())
            ->filter(function (string $field) {
                $skip = ['id'];

                return !in_array($field, $skip);
            })->toArray();

        $emptyFieldsValues = [
            'is_price_inserted_as_gross' => false
        ];

        foreach ($invoiceItems as $invoiceItem) {
            $invoiceItem->unsetRelations();
            $invoiceItem->setAppends([]);


            unset($invoiceItem->id);
            unset($invoiceItem->item_gross_price);

            $invoiceItem->invoice_id = $invoice->id;

            $invoiceItem = $invoiceItem->toArray();
            $invoiceItemForInsert = [];
            foreach ($fields as $field) {
                $value = $invoiceItem[$field] ?? null;
                if (is_null($value)) {
                    $value = $emptyFieldsValues[$field] ?? null;
                }

                $invoiceItemForInsert[$field] = $value;
            }

            $insert[] = $invoiceItemForInsert;
        }

        if (count($insert) > 0) {
            $this->invoiceItem->insert($insert);
        }
    }

    public function getInvoiceById(int $id): ?Invoice
    {
        return $this->invoice->find($id)?->load([
            'invoiceItems',
            'currency',
            'marketplace',
        ]);
    }

    public function deleteInvoiceById(int $invoiceId): void
    {
        $this->invoice->destroy($invoiceId);
    }

    public function getInvoiceReport(
        int $companyId,
        string $startDate,
        string $endDate,
        int $countryId
    ): Collection {
        $columns = [
            DB::raw('count(*) as count'),
            DB::raw('(sum(invoice_items.item_sales_price_net * qty) - COALESCE(sum(invoice_items.discount_net), 0)) as net_price'),
            DB::raw('(sum(invoice_items.item_sales_price_vat_amount * qty) - COALESCE(sum(invoice_items.discount_vat_amount), 0)) as vat_price'),
            DB::raw('sum(invoice_items.item_sales_price_net * qty) as net_price_solo'),
            DB::raw('sum(invoice_items.item_sales_price_vat_amount * qty) as vat_price_solo'),
            DB::raw('sum(invoice_items.discount_net) as discount_net'),
            DB::raw('sum(invoice_items.discount_vat_amount) as discount_vat_amount'),
            'invoice_items.vat_percent',
            'invoice_subtypes.type',
            'currencies.code as currency_code',
            'countries.name as vat_calc_country',
        ];

        return $this->invoice
            ->select($columns)
            ->join('invoice_items', 'invoices.id', '=', 'invoice_items.invoice_id')
            ->join('invoice_subtypes', 'invoices.invoice_subtype_id', '=', 'invoice_subtypes.id')
            ->join('currencies', 'invoices.sales_currency_id', '=', 'currencies.id')
            ->join('countries', 'invoices.tax_calculation_country_id', '=', 'countries.id')
            ->where('invoices.company_id', '=', $companyId)
            ->where('invoices.reporting_date', '>=', $startDate)
            ->where('invoices.reporting_date', '<=', $endDate)
            ->where('invoices.tax_reporting_country_id', '=', $countryId)
            ->groupBy([
                'invoice_subtypes.type',
                'invoice_items.vat_percent',
                'currencies.code',
                'countries.name',
            ])
            ->orderBy('countries.name')
            ->orderBy('invoice_subtypes.type')
            ->orderBy('invoice_items.vat_percent')
            ->get();
    }

    /**
     * @inheritDoc
     */
    public function getAllInvoiceStatuses(): Collection
    {
        return $this->invoiceStatus
            ->orderBy('sequence')
            ->get();
    }

    public function getInvoiceItemsForOssIossReport(
        int $companyId,
        string $startDate,
        string $endDate,
        ?array $invoiceItemTypeIds = null,
        ?array $invoiceSubtypeIds = null
    ): EloquentCollection {
        $columns = [
            'i.tax_calculation_country_id',
            'i.tax_reporting_country_id',
            'i.invoice_subtype_id',
            'i.sales_currency_id',
            'sales_currency.code as sales_currency_code',
            'invoice_subtypes.type as invoice_subtype_name',
            DB::raw('min("i"."tax_calculation_date") AS "tax_calculation_date"'),
            'i.ship_from_country_id',
            DB::raw('CASE WHEN "ii"."invoice_item_type_id" IN (' . ItemType::SHIPPING . ',' . ItemType::PACKAGING . ' ) THEN ' . ItemType::GOODS . ' ELSE "ii"."invoice_item_type_id" END AS "invoice_item_type_id"'),
            'ii.vat_percent',

            DB::raw('sum(("ii"."item_sales_price_net" * "ii"."qty") - coalesce(discount_net, 0)::float) AS "item_sales_price_net_sales_currency"'),
            DB::raw('sum(("ii"."item_sales_price_vat_amount" * "ii"."qty") - coalesce(discount_vat_amount, 0)::float) AS "item_sales_price_vat_amount_sales_currency"'),

            DB::raw('SUM(ii.item_sales_price_net * ii.qty / COALESCE(sales_currency_er.value, 1) - (COALESCE(discount_net, 0) / COALESCE(sales_currency_er.value, 1))::float)::float AS item_sales_price_net_eur'),
            DB::raw('SUM(ii.item_sales_price_vat_amount * ii.qty / COALESCE(sales_currency_er.value, 1) - (COALESCE(discount_vat_amount, 0) / COALESCE(sales_currency_er.value, 1))::float)::float AS item_sales_price_vat_amount_eur'),

            DB::raw('SUM(ii.item_sales_price_net * ii.qty * (COALESCE(tax_calculation_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)) - (COALESCE(discount_net, 0) * (COALESCE(tax_calculation_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)))::float)::float AS item_sales_price_net_tax_country_currency'),
            DB::raw('SUM(ii.item_sales_price_vat_amount * ii.qty * (COALESCE(tax_calculation_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)) - (COALESCE(discount_vat_amount, 0) * (COALESCE(tax_calculation_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)))::float)::float AS item_sales_price_vat_amount_tax_country_currency'),

            'ii.item_tax_code_id',
            'country_item_tax_codes.vat_rate_type_id'
        ];
        $query = $this->invoiceItem
            ->from('invoice_items as ii')
            ->select($columns)
            ->join('invoices as i', 'i.id', '=', 'ii.invoice_id')
            ->join('countries AS tax_calculation_countries', 'tax_calculation_countries.id', '=', 'i.tax_calculation_country_id')
            ->join('currencies AS sales_currency', 'i.sales_currency_id', '=', 'sales_currency.id')
            ->join('currencies AS tax_calculation_currency', 'tax_calculation_countries.currency_id', '=', 'tax_calculation_currency.id')
            ->leftJoin('exchange_rates AS sales_currency_er', function (JoinClause $join) {
                $join->on('sales_currency.id', '=', 'sales_currency_er.currency_id')
                    ->whereColumn('sales_currency_er.date', '=', 'i.invoice_date');
            })
            ->leftJoin('exchange_rates AS tax_calculation_currency_er', function (JoinClause $join) {
                $join->on('tax_calculation_currency.id', '=', 'tax_calculation_currency_er.currency_id')
                    ->whereColumn('tax_calculation_currency_er.date', '=', 'i.invoice_date');
            })
            ->join('invoice_subtypes', 'invoice_subtypes.id', '=', 'i.invoice_subtype_id')
            ->leftJoin('country_item_tax_codes', function (JoinClause $join) {
                $join->on('country_item_tax_codes.country_id', '=', 'i.tax_calculation_country_id')
                    ->whereColumn('country_item_tax_codes.item_tax_code_id', '=', 'ii.item_tax_code_id')
                    ->whereColumn('country_item_tax_codes.from_date', '<=', 'i.invoice_date')
                    ->where(function (JoinClause $query) {
                        $query->whereNull('country_item_tax_codes.to_date')
                            ->orWhereColumn('country_item_tax_codes.to_date', '>', 'i.invoice_date');
                    });
            })
            ->where('i.company_id', $companyId)
            ->where('i.invoice_status_id', InvoiceStatus::ISSUED)
            ->whereNotNull('tax_calculation_countries.in_eu_start_date')
            ->whereNull('tax_calculation_countries.in_eu_end_date')
            ->whereRaw('round("ii"."item_sales_price_net" * "ii"."qty", 2) != round((case when "ii"."discount_net" is null then 0 else "ii"."discount_net" end), 2)')
            ->whereBetween('i.invoice_date', [$startDate, $endDate]);

        if (!is_null($invoiceItemTypeIds)) {
            $query = $query->whereRaw('(CASE WHEN "ii"."invoice_item_type_id" IN (' . ItemType::SHIPPING . ',' . ItemType::PACKAGING . ' ) THEN ' . ItemType::GOODS . ' ELSE "ii"."invoice_item_type_id" END) IN (' . implode(', ', $invoiceItemTypeIds) . ')');
        }

        if (!is_null($invoiceSubtypeIds)) {
            $query = $query->whereIn('i.invoice_subtype_id', $invoiceSubtypeIds);
        }

        $groupBy = [
            'i.tax_calculation_country_id',
            'i.sales_currency_id',
            'sales_currency.code',
            DB::raw('CASE WHEN "ii"."invoice_item_type_id" IN (' . ItemType::SHIPPING . ',' . ItemType::PACKAGING . ' ) THEN ' . ItemType::GOODS . ' ELSE "ii"."invoice_item_type_id" END'),
            'ii.vat_percent',
            'tax_calculation_countries.id',
            'i.tax_reporting_country_id',
            'i.invoice_subtype_id',
            'invoice_subtypes.type',
            'i.ship_from_country_id',
            'discount_net',
            'ii.item_tax_code_id',
            'country_item_tax_codes.vat_rate_type_id'
        ];

        // Don't change order
        $query->groupBy($groupBy)->orderBy('tax_calculation_countries.code');

//        dd(eloquent_to_raw($query));

        return $query->get();
    }

    /**
     * @inheritDoc
     */
    public function deleteInvoicesByAmazonReportId(int $amazonReportId): void
    {
        DB::statement('ALTER TABLE invoices DISABLE TRIGGER ALL;');
        $this->invoiceItem
            ->whereIn('invoice_id', function (QueryBuilder $query) use ($amazonReportId) {
                $query->select('id')
                    ->from('invoices')
                    ->where('resource', AmazonReport::class)
                    ->where('resource_id', $amazonReportId);
            })->delete();

        $this->invoice
            ->where('resource', AmazonReport::class)
            ->where('resource_id', $amazonReportId)
            ->delete();
        DB::statement('ALTER TABLE invoices ENABLE TRIGGER ALL;');
    }

    public function getInvoicesMapperDataForVatReport(array $vatReportsIds, ?int $ossIssueCountryId = null, ?int $iossIssueCountryId = null): EloquentCollection
    {
        $columns = [
            'sub.vat_report_id AS vat_report_id',
            'sub.invoice_subtype_id AS invoice_subtype_id',
            'sub.company_id AS company_id',
            'sub.tax_calculation_country_id AS tax_calculation_country_id',
            'sub.invoice_item_type_id AS item_type_id',
            'sub.vat_rate_type_id AS vat_rate_type_id',
            'sub.vat_percent AS vat_percent',
            DB::raw('sum(sub.net) AS net'),
            DB::raw('sum(sub.net_discount) AS net_discount'),
            DB::raw('sum(sub.vat) AS vat'),
            DB::raw('sum(sub.vat_discount) AS vat_discount'),
            DB::raw('count(sub.cnt_invoices) AS cnt_invoices'),
            DB::raw('count(sub.cnt_items) AS cnt_items'),
        ];

        $qa = $this->invoice
            ->select($columns)
            ->from(function (QueryBuilder $builder) use ($vatReportsIds, $ossIssueCountryId, $iossIssueCountryId) {
                $whenPart = 'WHEN "i"."invoice_subtype_id" IN (' . InvoiceSubtype::EU_B2C_SALES_INVOICE . ', ' . InvoiceSubtype::CN_EU_B2C_SALES_INVOICE . ') ';
                $thenPart = ' THEN CASE WHEN i.invoice_subtype_id = ' . InvoiceSubtype::EU_B2C_SALES_INVOICE . ' THEN ' . InvoiceSubtype::DOMESTIC_SALES_INVOICE . ' ELSE ' . InvoiceSubtype::CN_DOMESTIC_SALES_INVOICE . ' END';

                $subtypeCase = [
                    'CASE',
                    $whenPart . 'AND i.tax_reporting_country_id != vr.country_id AND vr.country_id = i.tax_calculation_country_id AND i.tax_reporting_scheme_id = ' . TaxReportingScheme::AMAZON_REGULAR . ' AND i.tax_collection_responsibility_id = ' . TaxCollectionResponsibility::SELLER . ' ' . $thenPart,
                    $whenPart . 'AND i.tax_reporting_country_id = vr.country_id AND i.tax_reporting_country_id = i.ship_from_country_id' . '  AND i.tax_reporting_scheme_id = ' . TaxReportingScheme::AMAZON_REGULAR . ' AND i.tax_collection_responsibility_id = ' . TaxCollectionResponsibility::SELLER . ' ' . $thenPart,
                    $whenPart . 'AND i.tax_reporting_country_id = vr.country_id AND i.tax_reporting_country_id != i.ship_from_country_id' . '  AND i.tax_reporting_scheme_id = ' . TaxReportingScheme::AMAZON_REGULAR . ' AND i.tax_collection_responsibility_id = ' . TaxCollectionResponsibility::SELLER . ' ' . $thenPart,
                    'ELSE i.invoice_subtype_id END AS "invoice_subtype_id"'
                ];
                $subtypeCase = implode(' ', $subtypeCase);

                $columns = [
                    'vr.id AS vat_report_id',
                    DB::raw($subtypeCase),
                    'i.company_id AS company_id',
                    'i.tax_calculation_country_id AS tax_calculation_country_id',
                    DB::raw('CASE WHEN ii.invoice_item_type_id in (' . ItemType::SHIPPING . ', ' . ItemType::PACKAGING . ') THEN 1 ELSE ii.invoice_item_type_id END AS invoice_item_type_id'),
                    'vat_rate_types.id AS vat_rate_type_id',
                    DB::raw('CASE ' . $whenPart . 'AND i.tax_reporting_country_id != i.tax_calculation_country_id AND i.tax_reporting_country_id = vr.country_id THEN null ELSE ii.vat_percent END AS vat_percent'),
                    'i.parsing_invoice_number AS parsing_invoice_number',
                    DB::raw('SUM(ii.item_sales_price_net * ii.qty * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)) - (COALESCE(discount_net, 0) * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)))::float)::float AS net'),
                    DB::raw('SUM(COALESCE(ii.discount_net, 0) * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)))::float AS net_discount'),

                    DB::raw('(CASE ' . $whenPart . 'AND i.tax_reporting_country_id != i.tax_calculation_country_id AND i.tax_reporting_country_id = vr.country_id THEN 0.0 ELSE SUM((ii.item_sales_price_vat_amount * ii.qty * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1))) - (COALESCE(ii.discount_vat_amount, 0) * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1))))::float END)::float AS vat'),

                    DB::raw('sum(ii.discount_vat_amount) AS vat_discount'),
                    DB::raw('count(ii.invoice_id) AS cnt_invoices'),
                    DB::raw('count(ii.id) AS cnt_items'),
                ];

                $builder->select($columns)
                    ->from('invoices AS i')
                    ->join('vat_reports AS vr', function (JoinClause $join) use ($vatReportsIds) {
                        $join->on('vr.company_id', '=', 'i.company_id')
                            ->whereIn('vr.id', $vatReportsIds)
                            ->whereColumn(DB::raw('coalesce(i.reporting_date, i.invoice_date)'), '>=', 'vr.date_from')
                            ->whereColumn(DB::raw('coalesce(i.reporting_date, i.invoice_date)'), '<=', 'vr.date_to');
                    })
                    ->join('invoice_items AS ii', 'ii.invoice_id', '=', 'i.id')
                    ->join('country_item_tax_codes AS citc', function (JoinClause $join) {
                        $join->on('ii.item_tax_code_id', '=', 'citc.item_tax_code_id')
                            ->whereColumn('citc.country_id', 'vr.country_id')
                            ->whereColumn('citc.from_date', '<=', 'vr.date_from')
                            ->where(function (JoinClause $join) {
                                $join->whereNull('citc.to_date')->orWhereColumn('citc.to_date', '>=', 'vr.date_from');
                            });
                    })
                    ->join('vat_rate_types', 'citc.vat_rate_type_id', '=', 'vat_rate_types.id')
                    ->join('countries AS tax_reporting_country', 'vr.country_id', '=', 'tax_reporting_country.id')
                    ->join('currencies AS sales_currency', 'sales_currency.id', '=', 'i.sales_currency_id')
                    ->leftJoin('currencies AS tax_reporting_currency', 'tax_reporting_country.currency_id', '=', 'tax_reporting_currency.id')
                    ->leftJoin('exchange_rates AS sales_currency_er', function (JoinClause $join) {
                        $join->on('sales_currency.id', '=', 'sales_currency_er.currency_id')
                            ->whereColumn('sales_currency_er.date', 'i.invoice_date');
                    })
                    ->leftJoin('exchange_rates AS tax_reporting_currency_er', function (JoinClause $join) {
                        $join->on('tax_reporting_currency_er.currency_id', '=', 'tax_reporting_currency.id')
                            ->whereColumn('tax_reporting_currency_er.date', 'i.invoice_date');
                    })
                    ->where(function (QueryBuilder $query) use ($ossIssueCountryId, $iossIssueCountryId) {
                        $query->where('i.tax_collection_responsibility_id', '!=', TaxCollectionResponsibility::MARKETPLACE)
                            ->where(function (QueryBuilder $query) {
                                $bindings = [
                                    InvoiceSubtype::EU_B2C_SALES_INVOICE,
                                    InvoiceSubtype::CN_EU_B2C_SALES_INVOICE,
                                ];

                                $query->whereColumn('i.tax_reporting_country_id', 'vr.country_id')
                                    ->orWhereRaw('(case when ((i.invoice_subtype_id in (?, ?)) and (i.tax_reporting_country_id != "vr"."country_id")) then i.ship_from_country_id else "i"."tax_reporting_country_id" end) = "vr"."country_id"', $bindings);
                            })
                            ->where(function (QueryBuilder $query) use ($ossIssueCountryId) {
                                $types = [
                                    InvoiceSubtype::EU_B2C_SALES_INVOICE,
                                    InvoiceSubtype::CN_EU_B2C_SALES_INVOICE,
                                ];
                                $query->whereNotIn('i.invoice_subtype_id', $types)
                                    ->orWhereColumn('i.ship_from_country_id', 'vr.country_id');

                                if (!is_null($ossIssueCountryId)) {
                                    $query->orWhere('tax_reporting_country_id', '!=', $ossIssueCountryId);
                                }
                            })
                            ->where(function (QueryBuilder $query) use ($iossIssueCountryId) {
                                $types = [
                                    InvoiceSubtype::IOSS_EXPORT_INVOICE,
                                    InvoiceSubtype::CN_IOSS_EXPORT_INVOICE,
                                ];
                                $query->whereNotIn('i.invoice_subtype_id', $types)
                                    ->orWhereColumn('i.ship_from_country_id', 'vr.country_id');

                                if (!is_null($iossIssueCountryId)) {
                                    $query->orWhere('tax_reporting_country_id', '!=', $iossIssueCountryId);
                                }
                            });
                    })
                    ->where('i.invoice_status_id', '!=', InvoiceStatus::DRAFT)
                    ->groupBy(
                        'vr.id',
                        'invoice_subtype_id',
                        'tax_calculation_country_id',
                        'invoice_item_type_id',
                        'vat_rate_types.id',
                        'i.company_id',
                        'vat_percent',
                        'ii.invoice_id',
                        'parsing_invoice_number',
                        'i.tax_reporting_country_id',
                        'i.ship_from_country_id',
                        'i.tax_reporting_scheme_id',
                        'i.tax_collection_responsibility_id',
                    );
            }, 'sub')
            ->join('invoice_subtypes', 'sub.invoice_subtype_id', '=', 'invoice_subtypes.id')
            ->join('invoice_types', 'invoice_subtypes.invoice_type_id', '=', 'invoice_types.id')
            ->groupBy(
                'vat_report_id',
                'invoice_subtype_id',
                'tax_calculation_country_id',
                'item_type_id',
                'vat_rate_type_id',
                'company_id',
                'vat_percent',
                'invoice_types.sequence',
            )->orderBy('invoice_types.sequence');

//        dd(eloquent_to_raw($qa));

        return $qa->get();
    }

    public function getAllInvoiceSubtypesByIds(array $invoiceSubtypesIds): Collection
    {
        return $this->invoiceSubtype->whereIn('id', $invoiceSubtypesIds)->get();
    }

    public function getYearsWithInvoicesByCompanyId(int $companyId): Collection
    {
        return $this->invoice
            ->select([DB::raw('distinct extract(YEAR from invoice_date) as year')])
            ->where('company_id', $companyId)
            ->orderBy('year', 'DESC')
            ->get();
    }

    public function getMonthsWithInvoicesByCompanyIdForPeriod(int $companyId, string $dateFrom, string $dateTo): Collection
    {
        return $this->invoice
            ->select([DB::raw('distinct extract(MONTH from invoice_date) as month')])
            ->where('company_id', $companyId)
            ->where('invoice_date', '>=', $dateFrom)
            ->where('invoice_date', '<=', $dateTo)
            ->orderBy('month', 'DESC')
            ->get();
    }

    public function getDistinctReportingCountriesByCompanyIdForPeriod(
        array $companiesIds,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?array $subtypesIds = null
    ): Collection {
        $query = $this->invoice
            ->select(['tax_reporting_country_id', 'company_id'])
            ->distinct()
            ->where('invoice_status_id', InvoiceStatus::ISSUED)
            ->whereIn('company_id', $companiesIds)
            ->orderBy('tax_reporting_country_id');

        if (!is_null($dateFrom)) {
            $query->where(DB::raw('coalesce(reporting_date, invoice_date)'), '>=', $dateFrom);
        }

        if (!is_null($dateTo)) {
            $query->where(DB::raw('coalesce(reporting_date, invoice_date)'), '<=', $dateTo);
        }

        if (!is_null($subtypesIds) && count($subtypesIds) > 0) {
            $query->whereIn('invoice_subtype_id', $subtypesIds);
        }

        return $query->get();
    }

    public function getInvoicesCountByCompaniesIds(array $companiesIds, int $limit = 10): Collection
    {
        $select = [
            'company_id',
            DB::raw('count(id) as cnt')
        ];
        $q = $this->invoice->select($select);

        if (count($companiesIds) > 0) {
            $q->whereIn('company_id', $companiesIds);
        }

        return $q->groupBy('company_id')
            ->orderBy('cnt', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function getInvoicesGroupedForEslReport(
        int $companyId,
        string $dateFrom,
        string $dateTo,
        array $subtypesIds
    ): EloquentCollection {
        $columns = [
            'i.tax_reporting_country_id as tax_reporting_country_id',
            'i.bill_to_vat_number as buyer_vat_number',
            DB::raw('case when i.invoice_subtype_id in (' . InvoiceSubtype::CN_EU_B2B_SALES_INVOICE . ') then ' . InvoiceSubtype::CN_EU_B2B_SALES_INVOICE . ' else ' . InvoiceSubtype::EU_B2B_SALES_INVOICE . ' end as i_subtype_id'),
            DB::raw('case when ii.invoice_item_type_id in (' . ItemType::SERVICES . ') then ' . ItemType::SERVICES . ' else ' . ItemType::GOODS . ' end as item_type_id'),
            'tax_calculation_currency.code as tax_calculation_country_currency_code',
            'tax_reporting_currency.code as tax_reporting_country_currency_code',
            'sales_currency.code as sales_country_currency_code',
            DB::raw("extract(YEAR from coalesce(i.reporting_date, i.invoice_date)) || '-' || lpad(extract(MONTH from coalesce(i.reporting_date, i.invoice_date))::text, 2, '0') || '-01' as month_start"),

            DB::raw('SUM(ii.item_sales_price_net * ii.qty * (COALESCE(tax_calculation_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)) - (COALESCE(discount_net, 0) * (COALESCE(tax_calculation_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)))::float)::float AS net_in_tax_calculation_currency'),
            DB::raw('SUM(ii.item_sales_price_net * ii.qty * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)) - (COALESCE(discount_net, 0) * (COALESCE(tax_reporting_currency_er.value, 1) / COALESCE(sales_currency_er.value, 1)))::float)::float AS net_in_tax_reporting_currency'),
            DB::raw('SUM((ii.item_sales_price_net * ii.qty) - (COALESCE(discount_net, 0))::float)::float AS net_in_sales_currency'),
        ];
        $query = $this->invoice
            ->from('invoices AS i')
            ->select($columns)
            ->join('invoice_items AS ii', 'i.id', '=', 'ii.invoice_id')
            ->join('countries AS tax_calculation_country', 'tax_calculation_country.id', '=', 'i.tax_calculation_country_id')
            ->join('countries AS tax_reporting_country', 'i.tax_reporting_country_id', '=', 'tax_reporting_country.id')
            ->leftJoin('currencies AS tax_reporting_currency', 'tax_reporting_country.currency_id', '=', 'tax_reporting_currency.id')
            ->join('currencies AS sales_currency', 'i.sales_currency_id', '=', 'sales_currency.id')
            ->join('currencies AS tax_calculation_currency', 'tax_calculation_country.currency_id', '=', 'tax_calculation_currency.id')
            ->leftJoin('exchange_rates AS sales_currency_er', function (JoinClause $join) {
                $join->on('sales_currency.id', '=', 'sales_currency_er.currency_id')
                    ->whereColumn('sales_currency_er.date', '=', 'i.invoice_date');
            })
            ->leftJoin('exchange_rates AS tax_calculation_currency_er', function (JoinClause $join) {
                $join->on('tax_calculation_currency.id', '=', 'tax_calculation_currency_er.currency_id')
                    ->whereColumn('tax_calculation_currency_er.date', '=', 'i.invoice_date');
            })
            ->leftJoin('exchange_rates AS tax_reporting_currency_er', function (JoinClause $join) {
                $join->on('tax_reporting_currency_er.currency_id', '=', 'tax_reporting_currency.id')
                    ->whereColumn('tax_reporting_currency_er.date', 'i.invoice_date');
            })
            ->where('i.company_id', $companyId)
            ->where(DB::raw('coalesce(i.reporting_date, i.invoice_date)'), '>=', $dateFrom)
            ->where(DB::raw('coalesce(i.reporting_date, i.invoice_date)'), '<=', $dateTo)
            ->whereIn('i.invoice_subtype_id', $subtypesIds)
            ->where('i.invoice_status_id', '!=', InvoiceStatus::DRAFT)
            ->whereNotNull('i.bill_to_vat_number')
            ->groupBy('i.tax_reporting_country_id')
            ->groupBy('i_subtype_id')
            ->groupBy('i.bill_to_vat_number')
            ->groupBy('month_start')
            ->groupBy('item_type_id')
            ->groupBy('tax_calculation_country_currency_code')
            ->groupBy('sales_country_currency_code')
            ->groupBy('tax_reporting_country_currency_code')
            ->orderBy('i.tax_reporting_country_id')
            ->orderBy('month_start');

        return $query->get();
    }

    public function getFirstSalesGroupedByCompanyIdCountryId(array $companiesIds): EloquentCollection
    {
        return $this->invoice
            ->select([
                'company_id',
                'tax_reporting_country_id',
                'tax_calculation_country_id',
                'ship_from_country_id',
            ])
            ->selectRaw(
                'MIN("invoice_date") AS invoice_date'
            )
            ->whereIn('company_id', $companiesIds)
            ->groupBy('company_id')
            ->groupBy('tax_reporting_country_id')
            ->groupBy('tax_calculation_country_id')
            ->groupBy('ship_from_country_id')
            ->get();
    }

    public function getAllInvoiceSubtypes(): Collection
    {
        return $this->invoiceSubtype->orderBy('type')->get();
    }

    public function getInvoiceStatusesWithCountForCompany(
        int $companyId,
        int $year,
        ?int $invoiceSubtypeId = null,
        ?string $search = null
    ): EloquentCollection {
        $dateFrom = Carbon::parse($year . '-01-01')->startOfYear();
        $dateTo = $dateFrom->clone()->endOfYear();

        $q = $this->invoiceStatus->select('invoice_statuses.*')
            ->selectRaw('count("invoices"."id") AS count')
            ->leftJoin('invoices AS i', function (JoinClause $join) use ($companyId, $dateFrom, $dateTo, $invoiceSubtypeId) {
                $join->on('i.invoice_status_id', '=', 'invoice_statuses.id')
                    ->where('i.company_id', $companyId)
                    ->where('i.invoice_date', '>=', $dateFrom->toDateString())
                    ->where('i.invoice_date', '<=', $dateTo->toDateString());

                if (!is_null($invoiceSubtypeId)) {
                    $join->where('i.invoice_subtype_id', $invoiceSubtypeId);
                }
            })
            ->leftJoin('countries AS vcc', 'i.tax_calculation_country_id', '=', 'vcc.id')
            ->leftJoin('countries AS trc', 'i.tax_reporting_country_id', '=', 'trc.id')
            ->leftJoin('countries AS wc', 'i.ship_from_country_id', '=', 'wc.id')
            ->leftJoin('invoices', function (JoinClause $join) use ($search) {
                $join->on('invoices.id', '=', 'i.id');

                if (!is_null($search)) {
                    $join->where(function (JoinClause $query) use ($search) {
                        $searchTerm = '%' . $search . '%';
                        $query->where('vcc.name', 'ILIKE', $searchTerm)
                            ->orWhere('trc.name', 'ILIKE', $searchTerm)
                            ->orWhere('wc.name', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.invoice_number', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.parsing_invoice_number', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.ship_to_vat_number', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.ship_from_vat_number', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.ship_to_postal_code', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.order_number', 'ILIKE', $searchTerm)
                            ->orWhere('invoices.resource', 'ILIKE', $searchTerm)
                            ->orWhereNull('invoices.id');
                    });
                }
            });

        return $q->groupBy('invoice_statuses.id')
            ->orderBy('invoice_statuses.sequence')
            ->get();
    }

    public function getInvoicesDataForExport(
        int $companyId,
        ?int $invoiceSubtypeId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): EloquentCollection {

        $columns = [
            // INVOICES
            'invoices.id as id',
            'invoices.invoice_date',
            'invoices.invoice_number',
            'invoices.bill_to_name',
            'invoices.bill_to_address',
            'marketplaces.name as marketplace_name',
            'platforms.name as platform_name',
            'delivery_conditions.name as delivery_condition_name',
            'tax_reporting_schemes.name as tax_reporting_scheme_name',
            'tax_reporting_schemes.amazon_code as tax_reporting_scheme_code',
            'tax_collection_responsibilities.name as tax_collection_responsibilities_name',
            'tax_collection_responsibilities.amazon_code as tax_collection_responsibilities_code',
            'invoice_statuses.status',
            'invoices.ship_from_vat_number',
            'invoices.ship_to_vat_number',
            'invoices.ship_to_postal_code',
            'stc.name as ship_to_country',
            'vtc.name as tax_calculation_country',
            'trc.name as tax_reporting_country',
            'customer_types.type as customer_type',
            'invoices.sales_currency_id',
            'currencies.name as sales_currency',
            'currencies.code as sales_currency_code',
            'currencies.symbol as sales_currency_symbol',
            'invoice_subtypes.type as invoice_subtype',
            'invoice_types.type as invoice_type',
            'invoices.reporting_date',
            'invoices.tax_calculation_date',
            'sfc.name as ship_from_country',
            'invoices.company_id',
            'companies.full_legal_name as company_name',
            'companies.partner_code as company_partner_code',
            'invoices.order_number',
            'invoices.order_date',
            'bvnc.name as buyer_vat_number_country',
            'btc.name as bill_to_country',
            'invoices.bill_to_postal_code',
            'invoices.bill_to_vat_number',
            'invoices.ship_to_name',
            'invoices.ship_to_address',
            'invoices.description as invoice_description',
            'invoices.comment',

            // INVOICE ITEMS
            'invoice_items.id AS invoice_item_id',
            'invoice_items.item_description',
            'item_types.name as item_type',
            'invoice_items.qty',
            'invoice_items.vat_percent',
            'invoice_items.item_sales_price_net',
            'invoice_items.item_sales_price_vat_amount',
            'invoice_items.discount_net',
            'invoice_items.discount_vat_amount',
            'item_tax_codes.code as item_tax_code',
            'item_code_categories.name as item_tax_category',
            'invoice_items.discount_percentage',
        ];

        $q = $this->invoiceItem
            ->select($columns)
            ->join('invoices', 'invoices.id', '=', 'invoice_items.invoice_id')
            ->join('companies', 'invoices.company_id', '=', 'companies.id')
            ->leftJoin('marketplaces', 'marketplaces.id', '=', 'invoices.marketplace_id')
            ->leftJoin('platforms', 'platforms.id', '=', 'marketplaces.platform_id')
            ->leftJoin('delivery_conditions', 'delivery_conditions.id', '=', 'invoices.delivery_condition_id')
            ->leftJoin('tax_reporting_schemes', 'tax_reporting_schemes.id', '=', 'invoices.tax_reporting_scheme_id')
            ->leftJoin('tax_collection_responsibilities', 'tax_collection_responsibilities.id', '=', 'invoices.tax_collection_responsibility_id')
            ->leftJoin('invoice_statuses', 'invoices.invoice_status_id', '=', 'invoice_statuses.id')
            ->leftJoin('customer_types', 'customer_types.id', '=', 'invoices.customer_type_id')
            ->leftJoin('currencies', 'currencies.id', '=', 'invoices.sales_currency_id')
            ->leftJoin('countries as stc', 'invoices.ship_to_country_id', '=', 'stc.id')
            ->leftJoin('countries as vtc', 'invoices.tax_calculation_country_id', '=', 'vtc.id')
            ->leftJoin('countries as trc', 'invoices.tax_reporting_country_id', '=', 'trc.id')
            ->leftJoin('countries as sfc', 'invoices.ship_from_country_id', '=', 'sfc.id')
            ->leftJoin('countries as bvnc', 'invoices.buyer_vat_number_country_id', '=', 'bvnc.id')
            ->leftJoin('countries as btc', 'invoices.bill_to_country_id', '=', 'btc.id')
            ->leftJoin('invoice_subtypes', 'invoice_subtypes.id', '=', 'invoices.invoice_subtype_id')
            ->leftJoin('invoice_types', 'invoice_types.id', '=', 'invoice_subtypes.invoice_type_id')
            ->leftJoin('item_types', 'invoice_items.invoice_item_type_id', '=', 'item_types.id')
            ->leftJoin('item_tax_codes', 'invoice_items.item_tax_code_id', '=', 'item_tax_codes.id')
            ->leftJoin('item_code_categories', 'item_tax_codes.item_code_category_id', '=', 'item_code_categories.id')
            ->where('invoices.company_id', $companyId);

        if (!is_null($invoiceSubtypeId)) {
            $q->where('invoices.invoice_subtype_id', $invoiceSubtypeId);
        }

        if (!is_null($dateFrom)) {
            $q->where('invoices.invoice_date', '>=', $dateFrom);
        }

        if (!is_null($dateTo)) {
            $q->where('invoices.invoice_date', '<=', $dateTo);
        }

        return $q
            ->orderBy('invoices.invoice_date')
            ->orderBy('invoices.id')
            ->get();
    }
}
