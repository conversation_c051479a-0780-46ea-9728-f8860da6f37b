<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\Translation;
use App\Core\Data\Repositories\Contracts\TranslationRepositoryContract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class TranslationRepository implements TranslationRepositoryContract
{
    private Translation $translation;

    public function __construct(Translation $translation)
    {
        $this->translation = $translation;
    }

    public function getEmptyTranslationModel(): Translation
    {
        return $this->translation->newInstance();
    }

    public function getTranslationById(int $id): ?Translation
    {
        return $this->translation->find($id);
    }

    public function deleteTranslationById(int $id): void
    {
        $this->translation->destroy($id);
    }

    public function insertTranslations(array $insert): void
    {
        $this->translation->insert($insert);
    }

    public function getBaseTranslations(?string $search = null, int $limit = 100): Collection
    {
        if (!is_null($search)) {
            $search = trim($search);
        }

        $q = $this->translation
            ->where('locale', 'en')
            ->with(['rawConnectedTranslations', 'changeUser'])
            ->orderBy('key');

        if (!is_null($search)) {
            $q = $q->where(function ($query) use ($search) {
                return $query->where(DB::raw('key'), 'ilike', '%' . $search . '%')
                    ->orWhere(DB::raw('value'), 'ilike', '%' . $search . '%');
            });
        }

        return $q->limit($limit)->get();
    }

    public function getAll(array $columns = ['*']): Collection
    {
        return $this->translation->select($columns)->get();
    }

    public function getAllTranslationsByLocale(string $locale): Collection
    {
        return $this->translation
            ->orderBy('key')
            ->where('locale', $locale)
            ->get();
    }

    public function getTranslationByKeyAndLocale(string $key, string $locale): ?Translation
    {
        return $this->translation
            ->where('locale', $locale)
            ->where('key', $key)
            ->first();
    }
}
