<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\RegistrationProcess;
use App\Core\Data\Models\RegistrationStatus;
use App\Core\Data\Repositories\Contracts\RegistrationProcessRepositoryContract;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class RegistrationProcessRepository implements RegistrationProcessRepositoryContract
{

    /**
     * @var RegistrationStatus
     */
    private $registrationStatus;
    /**
     * @var RegistrationProcess
     */
    private $registrationProcess;

    /**
     * RegistrationSetupRepository constructor.
     *
     * @param RegistrationStatus $registrationStatus
     * @param RegistrationProcess $registrationProcess
     */
    public function __construct(
        RegistrationStatus $registrationStatus,
        RegistrationProcess $registrationProcess
    ) {
        $this->registrationStatus = $registrationStatus;
        $this->registrationProcess = $registrationProcess;
    }

    /**
     * @inheritDoc
     */
    public function getProcessStatuses(): Collection
    {
        return $this->registrationStatus->orderBy('id')->get();
    }

    public function getProcessStatusesByIds(array $statusIds): Collection
    {
        return $this->registrationStatus->whereIn('id', $statusIds)->get();
    }

    /**
     * @inheritDoc
     */
    public function getProcessesByStatus(
        ?int $statusId = null,
        ?int $perPage = 40,
        ?array $companiesIds = null,
        ?array $countryIds = null,
        ?array $with = null
    ): LengthAwarePaginator {
        $q = $this->registrationProcess->select();
        if (!is_null($statusId)) {
            $q->where('registration_status_id', '=', $statusId);
        }

        if (!is_null($with)) {
            $q->with($with);
        }

        if (!is_null($companiesIds)) {
            $q->whereIn('company_id', $companiesIds);
        }

        if (!is_null($countryIds)) {
            $q->whereIn('country_id', $countryIds);
        }

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getProcessesByStatusForCompaniesPerCountries(
        ?int $statusId = null,
        ?int $perPage = 40,
        ?array $countryIds = null,
        ?array $companiesIds = null,
        ?array $companiesIdsPerCountries = null,
        ?array $with = null
    ): LengthAwarePaginator {
        $countryIds = $countryIds ?? [];
        $companiesIds = $companiesIds ?? [];
        $companiesIdsPerCountries = $companiesIdsPerCountries ?? [];

        $select = [
            'registration_processes.*'
        ];
        $q = $this->registrationProcess->select($select);

        if (!is_null($statusId)) {
            $q->where('registration_status_id', '=', $statusId);
        }

        if (!is_null($with)) {
            $q->with($with);
        }

        if (count($companiesIdsPerCountries) > 0) {
            $raw = [];
            foreach ($companiesIdsPerCountries as $country) {
                foreach ($country['companiesIds'] as $companyId) {
                    $raw[] = '(' . $companyId . ', ' . $country['countryId'] . ')';
                }
            }
            if (count($raw) > 0) {
                $raw = implode(', ', $raw);
                $q->whereRaw('(registration_processes.company_id, registration_processes.country_id) IN (' . $raw . ')');
            }
        }

        if (count($companiesIds) > 0) {
            $q->whereIn('registration_processes.company_id', $companiesIds);
        }

        if (count($countryIds) > 0) {
            $q->whereIn('country_id', $countryIds);
        }

        return $q->paginate($perPage);
    }

    /**
     * @inheritDoc
     */
    public function getProcessById(int $id): ?RegistrationProcess
    {
        return $this->registrationProcess->find($id);
    }

    /**
     * @inheritDoc
     */
    public function getEmptyRegistrationProcessModel(): RegistrationProcess
    {
        return $this->registrationProcess->newInstance();
    }

    /**
     * @inheritdoc
     */
    public function getRegistrationProcessPerStatusCount(
        ?int $countryId = null,
        ?array $companiesIds = null
    ): Collection {
        $q = $this->registrationProcess
            ->select('registration_processes.registration_status_id as id',
                DB::raw('count(registration_processes.registration_status_id) as count'),
                'registration_statuses.status as tab')
            ->join('registration_statuses', 'registration_processes.registration_status_id', '=', 'registration_statuses.id');

        if (!is_null($countryId)) {
            $q->where('registration_processes.country_id', '=', $countryId);
        }

        if (!is_null($companiesIds)) {
            $q->whereIn('company_id', $companiesIds);
        }

        $q->groupBy('registration_processes.registration_status_id', 'registration_statuses.status');

        return $q->get();
    }

    /**
     * @inheritdoc
     */
    public function getRegistrationProcessPerStatusCountForCompaniesPerCountries(
        ?int $countryId = null,
        ?array $companiesIds = null,
        ?array $companiesIdsPerCountries = null
    ): Collection {
        $companiesIds = $companiesIds ?? [];
        $companiesIdsPerCountries = $companiesIdsPerCountries ?? [];

        $select = [
            'registration_processes.registration_status_id as id',
            DB::raw('count(registration_processes.registration_status_id) as count'),
            'registration_statuses.status as tab'
        ];
        $q = $this->registrationProcess
            ->select($select)
            ->join('registration_statuses', 'registration_processes.registration_status_id', '=', 'registration_statuses.id');

        if (!is_null($countryId)) {
            $q->where('registration_processes.country_id', '=', $countryId);
        }

        if (count($companiesIdsPerCountries) > 0) {
            $raw = [];
            foreach ($companiesIdsPerCountries as $country) {
                foreach ($country['companiesIds'] as $companyId) {
                    $raw[] = '(' . $companyId . ', ' . $country['countryId'] . ')';
                }
            }
            $raw = implode(', ', $raw);
            $q->whereRaw('(registration_processes.company_id, registration_processes.country_id) IN (' . $raw . ')');
        }

        if (count($companiesIds) > 0) {
            $q->whereIn('registration_processes.company_id', $companiesIds);
        }

        $q->groupBy('registration_processes.registration_status_id', 'registration_statuses.status');

        return $q->get();
    }


    /**
     * @inheritdoc
     */
    public function getRegistrationProcessStatusCountQuery(
        ?int $countryId = null,
        ?array $companiesIds = null
    ): Collection {
        $q = $this->registrationProcess
            ->select('registration_processes.registration_status_id as id',
                DB::raw('count(registration_processes.registration_status_id) as count'),
                'registration_statuses.status as tab')
            ->join('registration_statuses', 'registration_processes.registration_status_id', '=', 'registration_statuses.id');

        if (!is_null($countryId)) {
            $q->where('registration_processes.country_id', '=', $countryId);
        }

        if (!is_null($companiesIds)) {
            $q->whereIn('company_id', $companiesIds);
        }

        $q->groupBy('registration_processes.registration_status_id', 'registration_statuses.status');

        return $q->get();
    }

    public function setTerminationDate(array $registrationProcessIds, string $date): void
    {
        $this->registrationProcess
            ->whereIn('id', $registrationProcessIds)
            ->update(['termination_date' => $date]);
    }

    public function getRegistrationProcessByCompanyIdAndCountryId(
        int $companyId,
        int $countryId
    ): RegistrationProcess {
        return $this->registrationProcess
            ->where('company_id', '=', $companyId)
            ->where('country_id', '=', $countryId)
            ->first();
    }
}
