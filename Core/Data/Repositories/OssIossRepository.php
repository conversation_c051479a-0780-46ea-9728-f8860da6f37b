<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\OssIossReport;
use App\Core\Data\Repositories\Contracts\OssIossRepositoryContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class OssIossRepository implements OssIossRepositoryContract
{
    private OssIossReport $ossIossReport;

    public function __construct(OssIossReport $ossIossReport)
    {
        $this->ossIossReport = $ossIossReport;
    }

    public function getOssReportsBetweenDates(int $companyId, string $dateFrom, string $dateTo): EloquentCollection
    {
        return $this->ossIossReport
            ->whereIn('type', [OssIossReport::TYPE_OSS, OssIossReport::TYPE_VOES])
            ->where('company_id', $companyId)
            ->where('date_from', '>=', $dateFrom)
            ->where('date_to', '<=', $dateTo)
            ->orderBy('date_from')
            ->get();
    }

    public function getIossReportsBetweenDates(int $companyId, string $dateFrom, string $dateTo): EloquentCollection
    {
        return $this->ossIossReport
            ->whereIn('type', [OssIossReport::TYPE_IOSS])
            ->where('company_id', $companyId)
            ->where('date_from', '>=', $dateFrom)
            ->where('date_to', '<=', $dateTo)
            ->orderBy('date_from')
            ->get();
    }

    public function insertOssIossReports(array $insert): void
    {
        if (count($insert) < 1) {
            return;
        }

        $this->ossIossReport->insert($insert);
    }

    public function getNewOssIossReportModel(): OssIossReport
    {
        return $this->ossIossReport->newInstance();
    }

    public function getOssReportByIdAndCompanyCheck($reportId, int $companyId): ?OssIossReport
    {
        return $this->ossIossReport
            ->whereIn('type', [OssIossReport::TYPE_OSS, OssIossReport::TYPE_VOES])
            ->where('id', $reportId)
            ->where('company_id', $companyId)
            ->first();
    }

    public function getIossReportByIdAndCompanyCheck($reportId, int $companyId): ?OssIossReport
    {
        return $this->ossIossReport
            ->where('type', OssIossReport::TYPE_IOSS)
            ->where('id', $reportId)
            ->where('company_id', $companyId)
            ->first();
    }
}
