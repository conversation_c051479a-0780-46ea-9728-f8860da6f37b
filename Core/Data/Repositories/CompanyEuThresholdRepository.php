<?php

namespace App\Core\Data\Repositories;


use App\Core\Data\Models\CompanyEuThreshold;
use App\Core\Data\Repositories\Contracts\CompanyEuThresholdRepositoryContract;

class CompanyEuThresholdRepository implements CompanyEuThresholdRepositoryContract
{

    /**
     * @var CompanyEuThreshold
     */
    private $euThreshold;

    public function __construct(CompanyEuThreshold $euThreshold)
    {
        $this->euThreshold = $euThreshold;
    }


}
