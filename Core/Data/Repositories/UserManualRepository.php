<?php

namespace App\Core\Data\Repositories;

use App\Core\Data\Models\UserManual;
use App\Core\Data\Models\UserManualMedia;
use App\Core\Data\Repositories\Contracts\UserManualRepositoryContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class UserManualRepository implements UserManualRepositoryContract
{
    private UserManual $userManual;
    private UserManualMedia $userManualMedia;

    /**
     * @param UserManual $userManual
     * @param UserManualMedia $userManualMedia
     */
    public function __construct(UserManual $userManual, UserManualMedia $userManualMedia)
    {
        $this->userManual = $userManual;
        $this->userManualMedia = $userManualMedia;
    }

    /**
     * @inheritDoc
     */
    public function getEmptyUserManual(): UserManual
    {
        return $this->userManual->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getEmptyUserManualMedia(): UserManualMedia
    {
        return $this->userManualMedia->newInstance();
    }

    /**
     * @inheritDoc
     */
    public function getUserManualMediaById(int $userManualMediaId): ?UserManualMedia
    {
        return $this->userManualMedia->find($userManualMediaId);
    }

    public function getUserManualByRouteName(string $routeName, ?string $urlParameterKey = null, ?string $urlParameterValue = null): EloquentCollection
    {
        $q = $this->userManual->where('route_name', $routeName);

        if (!is_null($urlParameterKey)) {
            $q->where('url_param_key', $urlParameterKey);
        }

        if (!is_null($urlParameterValue)) {
            $q->where('url_param_value', $urlParameterValue);
        }

        return $q->get();
    }

    /**
     * @inheritDoc
     */
    public function getUserManualById(int $userManualId): ?UserManual
    {
        return $this->userManual->find($userManualId);
    }

    /**
     * @inheritDoc
     */
    public function deleteUserManualMediaById(int $userManualMediaId): void
    {
        $this->userManualMedia->destroy($userManualMediaId);
    }

    public function insertUserManuals(array $insert): void
    {
        $this->userManual->insert($insert);
    }
}
