<?php

namespace App\Core\Documents\DataTransfer;

use App\Core\Data\Models\DocumentDataField;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class DocumentDataStoreTransfer implements Arrayable, Jsonable
{
    private int $documentId;
    private int $documentDataFieldId;
    private mixed $rawData;
    private string $type;
    private bool $editable;

    public function __construct(
        int $documentId,
        int $documentDataFieldId,
        string $type,
        mixed $rawData,
        ?bool $editable = null
    ) {
        $this->documentId = $documentId;
        $this->documentDataFieldId = $documentDataFieldId;
        $this->type = $type;
        $this->rawData = $rawData;

        if (is_null($editable)) {
            $editable = true;
        }
        $this->editable = $editable;
    }

    /**
     * @return int
     */
    public function getDocumentId(): int
    {
        return $this->documentId;
    }

    /**
     * @return int
     */
    public function getDocumentDataFieldId(): int
    {
        return $this->documentDataFieldId;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @return mixed
     */
    public function getRawData(): mixed
    {
        return $this->rawData;
    }

    /**
     * @return string
     */
    public function getRawDataJson(): string
    {
        return evat_json_encode($this->getRawData());
    }

    /**
     * @return string|null
     */
    public function getData(): ?string
    {
        $data = $this->getRawData();
        if (is_null($data)) {
            return null;
        }

        if ($this->getType() === DocumentDataField::DATA_TYPE_BOOL) {
            $data = $data === true ? 'true' : 'false';
        }

        $jsonable = [
            DocumentDataField::DATA_TYPE_ARRAY,
            DocumentDataField::DATA_TYPE_OBJECT,
        ];

        if (in_array($this->getType(), $jsonable)) {
            $data = evat_json_encode($data);
        }

        return (string)$data;
    }

    public function hasEmptyValue(): bool
    {
        $data = $this->getRawData();
        if (is_array($data)) {
            // Check currency
            if ($this->getDocumentDataFieldId() === DocumentDataField::FIELD_AMOUNT) {
                $amount = $data['value'] ?? null;
                $currency = $data['currency']['id'] ?? null;

                return is_null($amount) || is_null($currency);
            }

            // Check array
            return count($data) < 1;
        }

        return is_null($data);
    }

    public function isEditable(): bool
    {
        return $this->editable;
    }

    public function toArray(): array
    {
        return [
            'documentId'          => $this->getDocumentId(),
            'documentDataFieldId' => $this->getDocumentDataFieldId(),
            'type'                => $this->getType(),
            'data'                => $this->getData(),
            'rawData'             => $this->getRawData(),
            'hasEmptyValue'       => $this->hasEmptyValue(),
        ];
    }

    public function toInsertArray(): array
    {
        return [
            'document_id'            => $this->getDocumentId(),
            'document_data_field_id' => $this->getDocumentDataFieldId(),
            'raw_value'              => $this->getRawDataJson(),
            'editable'               => $this->isEditable(),
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
