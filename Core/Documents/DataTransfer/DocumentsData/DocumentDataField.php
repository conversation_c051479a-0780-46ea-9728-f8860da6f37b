<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\DocumentData as DocumentDataModel;
use App\Core\Data\Models\DocumentDataField as DocumentDataFieldModel;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use App\Core\Data\Models\Currency as CurrencyModel;

class DocumentDataField implements Arrayable, Jsonable
{
    private DocumentDataFieldModel $documentDataField;
    private ?DocumentDataModel $documentData;

    private ?int $id = null;
    private int $sequence;
    private int $documentFieldId;
    private string $name;
    private string $fieldType;
    private string $key;
    private ?string $printValue = null;
    private bool $editable = true;
    private mixed $value = null;

    public function __construct(DocumentDataFieldModel $documentDataField, CommonData $commonData, ?DocumentDataModel $documentData = null)
    {
        $this->documentDataField = $documentDataField;
        $this->documentData = $documentData;

        $this->setCommonData($commonData);
    }

    /**
     * Common data is passed to this method and is called only in constructor - LARGE OBJECT
     *
     * @param \App\Core\Documents\DataTransfer\DocumentsData\CommonData $commonData
     * @return void
     */
    private function setCommonData(CommonData $commonData): void
    {
        $data = $this->documentData;
        $field = $this->documentDataField;
        $predefinedData = $commonData->getPredefinedData();

        // SET INIT DATA
        $this->id = $data?->id;
        $this->sequence = $field->sequence;
        $this->documentFieldId = $field->id;
        $this->name = _l($field->name);
        $this->fieldType = $field->data_type;
        $this->key = $field->key;
        $this->printValue = $data?->print_value;
        $this->editable = $data?->editable ?? true;
        $this->value = $value = $data?->data_value;

        // Resolve date fields
        if ($field->data_type === DocumentDataFieldModel::DATA_TYPE_DATE && !is_null($value)) {
            /**
             * @var \Carbon\Carbon $value
             */
            $this->value = $value->toDateString();
        }

        // Resolve amount field
        if ($field->id === DocumentDataFieldModel::FIELD_AMOUNT) {
            $currency = $value['currency'] ?? null;
            if (is_null($currency)) {
                $currency = $commonData->getCurrencies()->get(CurrencyModel::EUR);
                $currency = (new Currency($currency))->toArray();
            }
            $amount = $value['value'] ?? null;

            $amount = new Amount($amount, $currency);
            $this->value = $amount->toArray();
        }


        if ($field->id === DocumentDataFieldModel::FIELD_COUNTRY) {
            if ($predefinedData->hasCountry()) {
                $country = new Country($predefinedData->getCountry());
                $this->value = $country->toArray();
                $this->editable = $predefinedData->isCountryEditable();
            }
        }
    }

    public function toArray(): array
    {
        return [
            'id'              => $this->id,
            'sequence'        => $this->sequence,
            'documentFieldId' => $this->documentFieldId,
            'name'            => $this->name,
            'fieldType'       => $this->fieldType,
            'key'             => $this->key,
            'printValue'      => $this->printValue,
            'editable'        => $this->editable,
            'value'           => $this->value,
        ];
    }

    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }
}
