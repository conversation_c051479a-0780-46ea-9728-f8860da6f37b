<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class UpdateUser implements Arrayable, Jsonable
{
    private int $id;
    private string $email;
    private string $firstName;
    private string $lastName;
    private string $fullName;
    private bool $deleted;

    public function __construct(User $user)
    {
        $this->id = $user->id;
        $this->email = $user->email;
        $this->firstName = $user->first_name;
        $this->lastName = $user->last_name;
        $this->fullName = $user->full_name;
        $this->deleted = $user->deleted;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @return string
     */
    public function getFirstName(): string
    {
        return $this->firstName;
    }

    /**
     * @return string
     */
    public function getLastName(): string
    {
        return $this->lastName;
    }

    /**
     * @return string
     */
    public function getFullName(): string
    {
        return $this->fullName;
    }

    /**
     * @return bool
     */
    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function toArray()
    {
        return [
            'id'        => $this->getId(),
            'email'     => $this->getEmail(),
            'firstName' => $this->getFirstName(),
            'lastName'  => $this->getLastName(),
            'fullName'  => $this->getFullName(),
            'isDeleted' => $this->isDeleted(),
        ];
    }

    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }
}
