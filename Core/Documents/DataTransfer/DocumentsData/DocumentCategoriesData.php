<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class DocumentCategoriesData implements Arrayable, Jsonable
{
    private Collection $documentCategories;
    private CommonData $common;


    public function __construct(CommonData $common)
    {
        $this->documentCategories = collect();
        $this->common = $common;
    }

    public function addParentDocumentCategory(ParentDocumentCategory $parentDocumentCategory): void
    {
        $this->documentCategories->push($parentDocumentCategory);
    }

    public function getCommon(): CommonData
    {
        return $this->common;
    }

    public function getDocumentCategories(): Collection
    {
        return $this->documentCategories;
    }

    public function toArray(): array
    {
        return [
            'categories' => $this->getDocumentCategories()->toArray(),
            'common'     => $this->getCommon()->toArray()
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
