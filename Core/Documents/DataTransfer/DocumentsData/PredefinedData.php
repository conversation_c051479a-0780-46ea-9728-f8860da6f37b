<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\Country as CountryModel;
use Illuminate\Contracts\Support\Arrayable;

class PredefinedData implements Arrayable
{
    private ?CountryModel $country = null;
    private bool $isCountryEditable = false;

    public function getCountry(): ?CountryModel
    {
        return $this->country;
    }

    private function getCountryArray(): ?array
    {
        if (!$this->hasCountry()) {
            return null;
        }

        $country = new Country($this->getCountry());
        $country = $country->toArray();

        $country['isEditable'] = $this->isCountryEditable();

        return $country;
    }

    public function hasCountry(): bool
    {
        return !is_null($this->getCountry());
    }

    public function setCountry(?CountryModel $country): PredefinedData
    {
        $this->country = $country;

        return $this;
    }

    public function isCountryEditable(): bool
    {
        return $this->isCountryEditable;
    }

    public function setIsCountryEditable(bool $isCountryEditable): PredefinedData
    {
        $this->isCountryEditable = $isCountryEditable;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'country' => $this->getCountryArray()
        ];
    }
}
