<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Amount implements Arrayable, Jsonable
{
    private ?float $amount;
    private ?array $currency;

    public function __construct(?float $amount = null, ?array $currency = null)
    {
        $amount = $amount ?? 0.0;
        if ($amount === 0.0) {
            $amount = null;
        }

        if (empty($currency)) {
            $currency = [];
        }

        $this->amount = $amount;
        $this->currency = $currency;
    }

    public function toArray(): array
    {
        $amount = $this->amount;
        $currency = $this->currency;

        return [
            'value'    => $amount,
            'currency' => $currency,
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
