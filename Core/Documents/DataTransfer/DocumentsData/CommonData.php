<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\Country as CountryModel;
use App\Core\Data\Models\Currency as CurrencyModel;
use App\Core\Data\Models\DocumentDataField as DocumentDataFieldModel;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class CommonData implements Arrayable, Jsonable
{
    private Collection $fieldsTypes;
    private Collection $countries;
    private Collection $currencies;
    private array $meta = [];
    private PredefinedData $predefinedData;
    private ?VueComponentOptions $vueComponentOptions = null;

    public function setMeta(array $meta): CommonData
    {
        if (is_null($meta['filter'] ?? null)) {
            $meta['filter'] = [
                'search' => null,
                'type'   => null,
            ];
        }
        if (is_null($meta['parentCategory'] ?? null)) {
            $meta['parentCategory'] = null;
        }

        $this->meta = $meta;

        return $this;
    }

    public function setFieldsTypes(Collection $fieldsTypes): CommonData
    {
        $this->fieldsTypes = $fieldsTypes;

        return $this;
    }

    public function setCountries(Collection $countries): CommonData
    {
        $this->countries = $countries;

        return $this;
    }

    public function setCurrencies(Collection $currencies): CommonData
    {
        $this->currencies = $currencies;

        return $this;
    }

    public function setPredefinedData(PredefinedData $predefinedData): CommonData
    {
        $this->predefinedData = $predefinedData;

        return $this;
    }

    /**
     * @return \Illuminate\Support\Collection|\App\Core\Data\Models\DocumentDataField[]
     * @noinspection PhpDocSignatureInspection
     */
    public function getFieldsTypes(): Collection
    {
        return $this->fieldsTypes;
    }

    public function getFieldsTypesMapped(): Collection
    {
        return $this->fieldsTypes
            ->map(function (DocumentDataFieldModel $field) {
                return new DocumentDataField($field, $this);
            });
    }

    public function getCountries(): Collection
    {
        return $this->countries->keyBy('id');
    }

    public function getCurrencies(): Collection
    {
        return $this->currencies->keyBy('id');
    }

    public function getCountriesMapped(): Collection
    {
        return $this->getCountries()->map(function (CountryModel $country) {
            return new Country($country);
        })->values();
    }

    public function getCurrenciesMapped(): Collection
    {
        return $this->getCurrencies()->map(function (CurrencyModel $currency) {
            return new Currency($currency);
        })->values();
    }

    /**
     * @return array
     */
    public function getMeta(): array
    {
        return $this->meta;
    }

    /**
     * @return \App\Core\Documents\DataTransfer\DocumentsData\PredefinedData
     */
    public function getPredefinedData(): PredefinedData
    {
        return $this->predefinedData;
    }

    public function getVueComponentOptions(): VueComponentOptions
    {
        $vueComponentOptions = $this->vueComponentOptions;
        if (is_null($vueComponentOptions)) {
            $vueComponentOptions = new VueComponentOptions();
        }

        return $vueComponentOptions;
    }

    public function setVueComponentOptions(?VueComponentOptions $vueComponentOptions = null): CommonData
    {
        if (is_null($vueComponentOptions)) {
            $vueComponentOptions = new VueComponentOptions();
        }
        $this->vueComponentOptions = $vueComponentOptions;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'emptyFieldsData'  => $this->getFieldsTypesMapped()->toArray(),
            'countriesMapped'  => $this->getCountriesMapped()->toArray(),
            'currenciesMapped' => $this->getCurrenciesMapped()->toArray(),
            'predefinedData'   => $this->getPredefinedData()->toArray(),
            'meta'             => $this->getMeta(),
            'options'          => $this->getVueComponentOptions()->toArray()
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
