<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use Illuminate\Contracts\Support\Arrayable;

class VueComponentOptions implements Arrayable
{
    private array $routes = [];
    private bool $hiddenAllDocumentsSelector = false;
    private bool $hiddenSearch = false;
    private bool $hiddenFilter = false;
    private bool $hiddenUploadButton = false;
    private bool $hiddenCategorySampleFilesButton = false;
    private bool $hiddenCategoryRegenerateButton = false;
    private bool $hiddenCategoryUploadButton = false;
    private bool $hiddenDocumentEditButton = false;
    private bool $hiddenDocumentDeleteButton = false;

    public function getRoutes(): array
    {
        return $this->routes;
    }

    public function setRoutes(array $routes): VueComponentOptions
    {
        $this->routes = $routes;

        return $this;
    }

    public function isHiddenAllDocumentsSelector(): bool
    {
        return $this->hiddenAllDocumentsSelector;
    }

    public function setHiddenAllDocumentsSelector(bool $hiddenAllDocumentsSelector): VueComponentOptions
    {
        $this->hiddenAllDocumentsSelector = $hiddenAllDocumentsSelector;

        return $this;
    }

    public function isHiddenSearch(): bool
    {
        return $this->hiddenSearch;
    }

    public function setHiddenSearch(bool $hiddenSearch): VueComponentOptions
    {
        $this->hiddenSearch = $hiddenSearch;

        return $this;
    }

    public function isHiddenFilter(): bool
    {
        return $this->hiddenFilter;
    }

    public function setHiddenFilter(bool $hiddenFilter): VueComponentOptions
    {
        $this->hiddenFilter = $hiddenFilter;

        return $this;
    }

    public function isHiddenUploadButton(): bool
    {
        return $this->hiddenUploadButton;
    }

    public function setHiddenUploadButton(bool $hiddenUploadButton): VueComponentOptions
    {
        $this->hiddenUploadButton = $hiddenUploadButton;

        return $this;
    }

    public function isHiddenCategorySampleFilesButton(): bool
    {
        return $this->hiddenCategorySampleFilesButton;
    }

    public function setHiddenCategorySampleFilesButton(bool $hiddenCategorySampleFilesButton): VueComponentOptions
    {
        $this->hiddenCategorySampleFilesButton = $hiddenCategorySampleFilesButton;

        return $this;
    }

    public function isHiddenCategoryRegenerateButton(): bool
    {
        return $this->hiddenCategoryRegenerateButton;
    }

    public function setHiddenCategoryRegenerateButton(bool $hiddenCategoryRegenerateButton): VueComponentOptions
    {
        $this->hiddenCategoryRegenerateButton = $hiddenCategoryRegenerateButton;

        return $this;
    }

    public function isHiddenCategoryUploadButton(): bool
    {
        return $this->hiddenCategoryUploadButton;
    }

    public function setHiddenCategoryUploadButton(bool $hiddenCategoryUploadButton): VueComponentOptions
    {
        $this->hiddenCategoryUploadButton = $hiddenCategoryUploadButton;

        return $this;
    }

    public function isHiddenDocumentEditButton(): bool
    {
        return $this->hiddenDocumentEditButton;
    }

    public function setHiddenDocumentEditButton(bool $hiddenDocumentEditButton): VueComponentOptions
    {
        $this->hiddenDocumentEditButton = $hiddenDocumentEditButton;

        return $this;
    }

    public function isHiddenDocumentDeleteButton(): bool
    {
        return $this->hiddenDocumentDeleteButton;
    }

    public function setHiddenDocumentDeleteButton(bool $hiddenDocumentDeleteButton): VueComponentOptions
    {
        $this->hiddenDocumentDeleteButton = $hiddenDocumentDeleteButton;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'routes'                          => $this->getRoutes(),
            'hiddenAllDocumentsSelector'      => $this->isHiddenAllDocumentsSelector(),
            'hiddenSearch'                    => $this->isHiddenSearch(),
            'hiddenFilter'                    => $this->isHiddenFilter(),
            'hiddenUploadButton'              => $this->isHiddenUploadButton(),
            'hiddenCategorySampleFilesButton' => $this->isHiddenCategorySampleFilesButton(),
            'hiddenCategoryUploadButton'      => $this->isHiddenCategoryUploadButton(),
            'hiddenCategoryRegenerateButton'  => $this->isHiddenCategoryRegenerateButton(),
            'hiddenDocumentEditButton'        => $this->isHiddenDocumentEditButton(),
            'hiddenDocumentDeleteButton'      => $this->isHiddenDocumentDeleteButton(),
        ];
    }
}
