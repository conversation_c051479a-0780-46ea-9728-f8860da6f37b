<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\Currency as CurrencyModel;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Currency implements Arrayable, Jsonable
{
    private CurrencyModel $currency;

    public function __construct(CurrencyModel $currency)
    {
        $this->currency = $currency;
    }

    public function toArray(): array
    {
        return [
            'id'           => $this->currency->id,
            'name'         => $this->currency->name,
            'code'         => $this->currency->code,
            'symbol'       => $this->currency->symbol,
            'codeWithName' => $this->currency->code_with_name,
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
