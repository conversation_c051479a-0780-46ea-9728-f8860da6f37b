<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\SampleFile;
use App\Core\Data\Models\SampleFileType as SampleFileTypeModel;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class SampleFileType implements Arrayable, Jsonable
{
    private int $id;
    private string $name;
    private Collection $sampleFiles;

    public function __construct(SampleFileTypeModel $sampleFileType)
    {
        $this->id = $sampleFileType->id;
        $this->name = $sampleFileType->type;
        $this->sampleFiles = collect();

        foreach ($sampleFileType->sampleFiles as $sampleFile) {
            $this->addSampleFile($sampleFile);
        }
    }

    private function addSampleFile(SampleFile $sampleFile): void
    {
        $data = [
            'id'          => $sampleFile->id,
            'description' => $sampleFile->description,
            'name'        => $sampleFile->file->name,
            'fullName'    => $sampleFile->file->full_name,
            'path'        => $sampleFile->file->file_path,
            'extension'   => $sampleFile->file->extension,
            'mime'        => $sampleFile->file->mime,
            'size'        => $sampleFile->file->size,
            'humanSize'   => $sampleFile->file->human_size,
            'route'       => route('resource.download', $sampleFile->file->id),
        ];

        $this->sampleFiles->push($data);
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getSampleFiles(): Collection
    {
        return $this->sampleFiles;
    }

    public function toArray(): array
    {
        return [
            'id'          => $this->getId(),
            'name'        => $this->getName(),
            'sampleFiles' => $this->getSampleFiles()->toArray(),
        ];
    }

    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }
}
