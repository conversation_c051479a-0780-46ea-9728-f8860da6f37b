<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\DocumentCategory;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class ParentDocumentCategory implements Arrayable, Jsonable
{
    /**
     * @var \Illuminate\Support\Collection|\App\Core\Documents\DataTransfer\DocumentsData\ChildDocumentCategory[]
     */
    private Collection $childCategories;
    private int $id;
    private string $name;
    private ?string $tooltip;
    private int $sequence;
    private bool $generatable;
    private bool $uploadable;
    private bool $deletable;
    private int $documentsCount = 0;

    public function __construct(DocumentCategory $documentCategory)
    {
        $this->childCategories = collect();

        $this->id = $documentCategory->id;
        $this->name = $documentCategory->name;
        $this->sequence = $documentCategory->sequence;
        $this->generatable = $documentCategory->generatable;
        $this->uploadable = $documentCategory->uploadable;
        $this->deletable = $documentCategory->deletable;
        $this->tooltip = $documentCategory->tooltip;
    }

    /**
     * @return \Illuminate\Support\Collection|\App\Core\Documents\DataTransfer\DocumentsData\ChildDocumentCategory[]
     * @noinspection PhpDocSignatureInspection
     */
    public function getChildCategories(): Collection
    {
        return $this->childCategories;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return _l($this->name);
    }

    /**
     * @return int
     */
    public function getSequence(): int
    {
        return $this->sequence;
    }

    /**
     * @return bool
     */
    public function isGeneratable(): bool
    {
        return $this->generatable;
    }

    /**
     * @return bool
     */
    public function isUploadable(): bool
    {
        return $this->uploadable;
    }

    /**
     * @return bool
     */
    public function isDeletable(): bool
    {
        return $this->deletable;
    }

    /**
     * @param \App\Core\Documents\DataTransfer\DocumentsData\ChildDocumentCategory $childDocumentCategory
     * @return void
     */
    public function addChildCategory(ChildDocumentCategory $childDocumentCategory): void
    {
        if ($this->getId() !== $childDocumentCategory->getParentDocumentCategoryId()) {
            return;
        }

        $this->documentsCount = $this->documentsCount + $childDocumentCategory->getDocumentsCount();
        $this->childCategories->push($childDocumentCategory);
    }

    /**
     * @return bool
     */
    public function hasChildCategories(): bool
    {
        return $this->childCategories->count() > 0;
    }

    /**
     * @return int
     */
    public function getDocumentsCount(): int
    {
        return $this->documentsCount;
    }

    /**
     * @return string|null
     */
    public function getTooltip(): ?string
    {
        return $this->tooltip;
    }


    public function toArray(): array
    {
        return [
            'id'                            => $this->getId(),
            'name'                          => $this->getName(),
            'sequence'                      => $this->getSequence(),
            'tooltip'                       => $this->getTooltip(),
            'generatable'                   => $this->isGeneratable(),
            'uploadable'                    => $this->isUploadable(),
            'deletable'                     => $this->isDeletable(),
            'hasChildCategories'            => $this->hasChildCategories(),
            'childCategories'               => $this->getChildCategories()->toArray(),
            'childCategoriesDocumentsCount' => $this->getDocumentsCount(),
        ];
    }

    public function toJson($options = 0)
    {
        return evat_json_encode($this->toArray());
    }
}
