<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\Document as DocumentModel;
use App\Core\Data\Models\DocumentCategory;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class Document implements Arrayable, Jsonable
{
    private int $id;
    private int $fileId;
    private int $companyId;
    private Carbon $date;
    private Carbon $createdAt;
    private Carbon $updatedAt;
    private ?string $resource;
    private ?int $resourceId;
    private bool $uploaded;
    private bool $generated;
    private bool $editable;
    private bool $deletable;
    private string $name;
    private string $extension;
    private string $fullName;
    private string $size;
    private string $humanSize;
    private string $path;
    private string $mime;
    private string $route;
    private string $faIconClass;
    private UpdateUser $updateUser;
    private Collection $data;
    private ?ChildDocumentCategory $category = null;

    public function __construct(DocumentModel $document, CommonData $commonData)
    {
        $this->id = $document->id;
        $this->fileId = $document->file->id;
        $this->companyId = $document->company_id;
        $this->date = Carbon::parse($document->date);
        $this->createdAt = Carbon::parse($document->file->created_at);
        $this->updatedAt = Carbon::parse($document->updated_at);
        $this->resource = $document->resource;
        $this->resourceId = $document->resource_id;
        $this->uploaded = $document->uploaded;
        $this->generated = $document->generated;
        $this->editable = $document->editable ?? true;
        $this->deletable = $document->deletable ?? true;
        $this->name = $document->file->name;
        $this->extension = $document->file->extension;
        $this->fullName = $document->file->full_name;
        $this->size = $document->file->size;
        $this->humanSize = $document->file->human_size;
        $this->path = $document->file->file_path;
        $this->mime = $document->file->mime;
        $this->faIconClass = $document->file->fa_icon_class;
        $this->route = route('resource.show', $document->file->id);
        $this->updateUser = new UpdateUser($document->updateUser);
        $this->setData($document->data, $commonData);
        $this->setDocumentCategory($document->category);
    }

    private function setDocumentCategory(?DocumentCategory $category): void
    {
        if (is_null($category)) {
            return;
        }

        $this->category = new ChildDocumentCategory($category);
    }

    private function setData(Collection $data, CommonData $commonData): void
    {
        $this->data = collect();
        $data = $data->keyBy('document_data_field_id');
//dd($commonData->getFieldsTypes()->toArray());
        foreach ($commonData->getFieldsTypes() as $field) {
            /**
             * @var \App\Core\Data\Models\DocumentData|null $item
             */
            $item = $data->get($field->id);

            $this->data->push(new DocumentDataField($field, $commonData, $item));
        }
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getFileId(): int
    {
        return $this->fileId;
    }

    /**
     * @return \Carbon\Carbon
     */
    public function getDate(): Carbon
    {
        return $this->date;
    }

    /**
     * @return \Carbon\Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->createdAt;
    }

    /**
     * @return \Carbon\Carbon
     */
    public function getUpdatedAt(): Carbon
    {
        return $this->updatedAt;
    }

    /**
     * @return string|null
     */
    public function getResource(): ?string
    {
        return $this->resource;
    }

    /**
     * @return int|null
     */
    public function getResourceId(): ?int
    {
        return $this->resourceId;
    }

    /**
     * @return bool
     */
    public function isDeletable(): bool
    {
        return $this->deletable;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getExtension(): string
    {
        return $this->extension;
    }

    /**
     * @return string
     */
    public function getFullName(): string
    {
        return $this->fullName;
    }

    /**
     * @return string
     */
    public function getSize(): string
    {
        return $this->size;
    }

    /**
     * @return string
     */
    public function getHumanSize(): string
    {
        return $this->humanSize;
    }

    /**
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     * @return string
     */
    public function getMime(): string
    {
        return $this->mime;
    }

    /**
     * @return string
     */
    public function getRoute(): string
    {
        return $this->route;
    }

    /**
     * @return \App\Core\Documents\DataTransfer\DocumentsData\UpdateUser
     */
    public function getUpdateUser(): UpdateUser
    {
        return $this->updateUser;
    }

    /**
     * @return string
     */
    public function getFaIconClass(): string
    {
        return $this->faIconClass;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function getData(): Collection
    {
        return $this->data;
    }

    /**
     * @return bool
     */
    public function isUploaded(): bool
    {
        return $this->uploaded;
    }

    /**
     * @return bool
     */
    public function isGenerated(): bool
    {
        return $this->generated;
    }

    /**
     * @return bool
     */
    public function isEditable(): bool
    {
        return $this->editable;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return \App\Core\Documents\DataTransfer\DocumentsData\ChildDocumentCategory|null
     */
    public function getCategory(): ?ChildDocumentCategory
    {
        return $this->category;
    }

    public function toArray(): array
    {
        return [
            'id'                 => $this->getId(),
            'fileId'             => $this->getFileId(),
            'companyId'          => $this->getCompanyId(),
            'date'               => $this->getDate()->toDateString(),
            'dateLocale'         => $this->getDate()->format(_l_date_format('date')),
            'dateTimestamp'      => $this->getDate()->timestamp,
            'category'           => $this->getCategory()?->toArray(),
            'createdAt'          => $this->getCreatedAt()->toDateTimeString(),
            'createdAtLocale'    => $this->getCreatedAt()->format(_l_date_format('datetime')),
            'createdAtTimestamp' => $this->getCreatedAt()->timestamp,
            'updatedAt'          => $this->getUpdatedAt()->toDateTimeString(),
            'updatedAtLocale'    => $this->getUpdatedAt()->format(_l_date_format('datetime')),
            'updatedAtTimestamp' => $this->getUpdatedAt()->timestamp,
            'resource'           => $this->getResource(),
            'resourceId'         => $this->getResourceId(),
            'deletable'          => $this->isDeletable(),
            'generated'          => $this->isGenerated(),
            'uploaded'           => $this->isUploaded(),
            'editable'           => $this->isEditable(),
            'name'               => $this->getName(),
            'extension'          => $this->getExtension(),
            'fullName'           => $this->getFullName(),
            'size'               => $this->getSize(),
            'humanSize'          => $this->getHumanSize(),
            'path'               => $this->getPath(),
            'mime'               => $this->getMime(),
            'route'              => $this->getRoute(),
            'faIconClass'        => $this->getFaIconClass(),
            'updateUser'         => $this->getUpdateUser()->toArray(),
            'data'               => $this->getData()->toArray()
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
