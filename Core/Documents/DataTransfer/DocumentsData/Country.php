<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\Country as CountryModel;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Country implements Arrayable, Jsonable
{
    /**
     * @var \App\Core\Data\Models\Country
     */
    private CountryModel $country;

    public function __construct(CountryModel $country)
    {
        $this->country = $country;
    }

    public function toArray(): array
    {
        return [
            'id'   => $this->country->id,
            'name' => $this->country->name,
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
