<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use App\Core\Data\Models\Country;

class DocumentCountry implements Arrayable, Jsonable
{
    private Country $country;

    public function __construct(Country $country)
    {
        $this->country = $country;
    }

    public function toArray(): array
    {
        return [
            'id'   => $this->country->id,
            'name' => $this->country->name,
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }
}
