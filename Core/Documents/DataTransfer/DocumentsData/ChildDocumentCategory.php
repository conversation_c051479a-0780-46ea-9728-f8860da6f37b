<?php

namespace App\Core\Documents\DataTransfer\DocumentsData;

use App\Core\Data\Models\DocumentCategory;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use App\Core\Data\Models\Document as DocumentModel;
use App\Core\Data\Models\DocumentCategoryCountry as DocumentCategoryCountryModel;
use App\Core\Data\Models\Country as CountryModel;

class ChildDocumentCategory implements Arrayable, Jsonable
{
    /**
     * @var \Illuminate\Support\Collection|\App\Core\Documents\DataTransfer\DocumentsData\Document[]
     * @noinspection PhpDocFieldTypeMismatchInspection
     */
    private Collection $documents;
    private int $id;
    private int $parentDocumentCategoryId;
    private string $name;
    private int $sequence;
    private bool $generatable;
    private bool $uploadable;
    private bool $deletable;
    private Collection $categoryCountries;
    private static ?Collection $countries = null;
    private ?SampleFileType $sampleFileType = null;


    public function __construct(DocumentCategory $documentCategory)
    {
        if (is_null(self::$countries)) {
            self::$countries = CountryModel::orderBy('name')
                ->get()
                ->filter(function (CountryModel $country) {
                    return !in_array($country->id, CountryModel::GROUPED_IDS);
                });
        }

        $this->documents = collect();
        $this->id = $documentCategory->id;
        $this->parentDocumentCategoryId = $documentCategory->parent_category_id;
        $this->name = $documentCategory->name;
        $this->sequence = $documentCategory->sequence;
        $this->generatable = $documentCategory->generatable;
        $this->uploadable = $documentCategory->uploadable;
        $this->deletable = $documentCategory->deletable;
        if (!is_null($documentCategory->sampleFileType)) {
            $this->sampleFileType = new SampleFileType($documentCategory->sampleFileType);
        }

        $this->categoryCountries = $documentCategory->documentCategoryCountries
            ->merge($documentCategory->documentCategoryCompanyCountries);
    }

    /**
     * @return \Illuminate\Support\Collection|\App\Core\Documents\DataTransfer\DocumentsData\Document[]
     * @noinspection PhpDocSignatureInspection
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getParentDocumentCategoryId(): int
    {
        return $this->parentDocumentCategoryId;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return _l($this->name);
    }

    /**
     * @return int
     */
    public function getSequence(): int
    {
        return $this->sequence;
    }

    /**
     * @return bool
     */
    public function isGeneratable(): bool
    {
        return $this->generatable;
    }

    /**
     * @return bool
     */
    public function isUploadable(): bool
    {
        return $this->uploadable;
    }

    /**
     * @return bool
     */
    public function isDeletable(): bool
    {
        return $this->deletable;
    }

    /**
     * @return bool
     */
    public function hasDocuments(): bool
    {
        return $this->getDocumentsCount() > 0;
    }

    /**
     * @param \App\Core\Data\Models\Document $document
     * @param \App\Core\Documents\DataTransfer\DocumentsData\CommonData $commonData
     * @return void
     */
    public function addDocument(DocumentModel $document, CommonData $commonData): void
    {
        if ($document->document_category_id !== $this->getId()) {
            return;
        }

        $this->documents->push(new Document($document, $commonData));
    }

    /**
     * @return \App\Core\Documents\DataTransfer\DocumentsData\SampleFileType|null
     */
    public function getSampleFileType(): ?SampleFileType
    {
        return $this->sampleFileType;
    }

    /**
     * @return bool
     */
    public function hasSampleFileType(): bool
    {
        return !is_null($this->getSampleFileType());
    }

    /**
     * @return int
     */
    public function getDocumentsCount(): int
    {
        return $this->documents->count();
    }

    public function toArray(): array
    {
        return [
            'id'                       => $this->getId(),
            'parentDocumentCategoryId' => $this->getParentDocumentCategoryId(),
            'name'                     => $this->getName(),
            'sequence'                 => $this->getSequence(),
            'generatable'              => $this->isGeneratable(),
            'uploadable'               => $this->isUploadable(),
            'deletable'                => $this->isDeletable(),
            'hasSampleFileType'        => $this->hasSampleFileType(),
            'sampleFileType'           => $this->getSampleFileType()?->toArray(),
            'hasDocuments'             => $this->hasDocuments(),
            'documentsCount'           => $this->getDocumentsCount(),
            'documents'                => $this->getDocuments()->toArray(),
            'categoryCountries'        => $this->getCategoryCountries()->toArray()
        ];
    }

    public function toJson($options = 0): string
    {
        return evat_json_encode($this->toArray());
    }

    private function getCategoryCountries(): Collection
    {
        $allCountries = self::$countries;
        $categoryCountries = $this->categoryCountries
            ->keyBy('country_id')
            ->map(function (DocumentCategoryCountryModel $documentCategoryCountry) {
                return $documentCategoryCountry->country;
            });

        $countries = $categoryCountries;
        if ($categoryCountries->has(CountryModel::ALL)) {
            $countries = $allCountries;
        } elseif ($categoryCountries->has(CountryModel::EU)) {
            $countries = $allCountries->filter(function (CountryModel $country) {
                return $country->in_eu;
            });
        } elseif ($categoryCountries->has(CountryModel::NON_EU)) {
            $countries = $allCountries->filter(function (CountryModel $country) {
                return !$country->in_eu;
            });
        }

        return $countries->map(function (CountryModel $country) {
            return new DocumentCountry($country);
        })->values();
    }
}
