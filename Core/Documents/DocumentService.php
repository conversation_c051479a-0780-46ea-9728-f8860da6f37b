<?php

namespace App\Core\Documents;

use App\Core\Data\Models\Document;
use App\Core\Data\Models\DocumentCategory;
use App\Core\Data\Models\DocumentDataField;
use App\Core\Data\Repositories\Contracts\CompanyRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentCategoryRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentRepositoryContract;
use App\Core\Data\Repositories\Contracts\DocumentTemplateRepositoryContract;
use App\Core\Documents\Contracts\DocumentServiceContract;
use App\Core\Documents\DataTransfer\DocumentDataStoreTransfer;
use App\Core\Documents\DataTransfer\DocumentsData\ChildDocumentCategory;
use App\Core\Documents\DataTransfer\DocumentsData\CommonData;
use App\Core\Documents\DataTransfer\DocumentsData\Country;
use App\Core\Documents\DataTransfer\DocumentsData\DocumentCategoriesData;
use App\Core\Documents\DataTransfer\DocumentsData\ParentDocumentCategory;
use App\Core\Documents\DataTransfer\DocumentsData\PredefinedData;
use App\Core\Documents\DataTransfer\DocumentsData\VueComponentOptions;
use App\Core\File\Contracts\FileServiceContract;
use App\Core\Mappers\Mappers\Company\CompanyMapper;
use App\Core\Mappers\MappersData\CompanyMapperData\CompanyData;
use App\Core\PdfTemplates\Contracts\PdfTemplateServiceContract;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\UploadedFile;
use Throwable;

class DocumentService implements DocumentServiceContract
{
    private DocumentCategoryRepositoryContract $documentCategoryRepo;
    private DocumentRepositoryContract $documentRepo;
    private FileServiceContract $fileService;
    private CountryRepositoryContract $countryRepo;
    private CurrencyRepositoryContract $currencyRepo;
    private DocumentTemplateRepositoryContract $documentTemplateRepo;
    private CompanyRepositoryContract $companyRepo;
    private PdfTemplateServiceContract $pdfTemplateService;

    public function __construct(
        DocumentCategoryRepositoryContract $documentCategoryRepo,
        DocumentRepositoryContract $documentRepo,
        FileServiceContract $fileService,
        CountryRepositoryContract $countryRepo,
        CurrencyRepositoryContract $currencyRepo,
        DocumentTemplateRepositoryContract $documentTemplateRepo,
        CompanyRepositoryContract $companyRepo,
        PdfTemplateServiceContract $pdfTemplateService
    ) {
        $this->documentCategoryRepo = $documentCategoryRepo;
        $this->documentRepo = $documentRepo;
        $this->fileService = $fileService;
        $this->countryRepo = $countryRepo;
        $this->currencyRepo = $currencyRepo;
        $this->documentTemplateRepo = $documentTemplateRepo;
        $this->companyRepo = $companyRepo;
        $this->pdfTemplateService = $pdfTemplateService;
    }

    public function getDocumentsDataForDocumentsVueComponent(
        int $companyId,
        ?array $parentCategoriesIds = null,
        ?string $resource = null,
        ?int $resourceId = null,
        ?int $countryId = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $showAllDocumentsFromParentCategoriesIds = null,
        ?array $forceShowOnlyChildCategories = null,
        ?array $forceDontShowChildCategories = null,
        ?array $meta = null,
        ?PredefinedData $predefinedData = null,
        ?VueComponentOptions $vueComponentOptions = null
    ): DocumentCategoriesData {
        $parentCategoriesIds = array_filter($parentCategoriesIds, function ($parentCategoryId) {
            return $parentCategoryId !== DocumentCategory::CATEGORY_OTHER;
        });
        /**
         * @var \App\Core\Data\Models\DocumentCategory[]|\Illuminate\Support\Collection $parentCategories
         * @var \App\Core\Data\Models\DocumentCategory[] $childCategories
         * @var \App\Core\Data\Models\Document[] $documents
         */
        $parentCategories = $this->documentCategoryRepo
            ->getParentDocumentCategories($parentCategoriesIds)
            ->load(
                'childDocumentCategories.sampleFileType.sampleFiles.file.resourceModel',
                'childDocumentCategories.documentCategoryCountries.country',
                'childDocumentCategories.documentCategoryCompanyCountries.country',
                'childDocumentCategories.documentCategoryPlatforms',
            )
            ->keyBy('id');

        $childCategoriesIds = $parentCategories->pluck('childDocumentCategories')
            ->flatten()
            ->filter(function (DocumentCategory $category) use (
                $countryId,
                $showAllDocumentsFromParentCategoriesIds,
                $forceShowOnlyChildCategories,
                $forceDontShowChildCategories
            ) {
                if (is_array($forceShowOnlyChildCategories)) {
                    return in_array($category->id, $forceShowOnlyChildCategories);
                }

                $isValid = $category->isValidForVueComponent(
                    countryId: $countryId,
                    showAllDocumentsFromParentCategoriesIds: $showAllDocumentsFromParentCategoriesIds
                );

                if (is_array($forceDontShowChildCategories)) {
                    $isValid = $isValid && !in_array($category->id, $forceDontShowChildCategories);
                }

                return $isValid;
            })
            ->keyBy('id')
            ->keys()
            ->toArray();

        $documents = $this->documentRepo
            ->getDocumentsForCompany(
                companyId: $companyId,
                countryId: $countryId,
                categoriesIds: $childCategoriesIds,
                startDate: $startDate,
                endDate: $endDate,
                resource: $resource,
                resourceId: $resourceId,
                showAllDocumentsFromParentCategoriesIds: $showAllDocumentsFromParentCategoriesIds
            )
            ->load(
                'file.resourceModel',
                'updateUser',
                'data.field',
                'category.sampleFileType.sampleFiles.file',
                'category.documentCategoryCountries.country',
                'category.documentCategoryCompanyCountries.country',
                'category.documentCategoryPlatforms',
            )
            ->keyBy('id')
            ->groupBy('document_category_id');

        $fieldTypes = $this->documentRepo->getAllDocumentDataFields();
        $countries = $this->countryRepo->getAllCountries();
        $currencies = $this->currencyRepo->getAllCurrencies();
        $predefinedData = $predefinedData ?? new PredefinedData();

        $commonData = (new CommonData())
            ->setFieldsTypes($fieldTypes)
            ->setCountries($countries)
            ->setCurrencies($currencies)
            ->setPredefinedData($predefinedData)
            ->setMeta($meta ?? [])
            ->setVueComponentOptions($vueComponentOptions);

        $categories = new DocumentCategoriesData($commonData);
        foreach ($parentCategories as $parentDocumentCategory) {
            $category = new ParentDocumentCategory($parentDocumentCategory);
            foreach ($parentDocumentCategory->childDocumentCategories as $childDocumentCategory) {
                if (is_array($forceShowOnlyChildCategories) && !in_array($childDocumentCategory->id, $forceShowOnlyChildCategories)) {
                    continue;
                }

                if (is_array($forceDontShowChildCategories) && in_array($childDocumentCategory->id, $forceDontShowChildCategories)) {
                    continue;
                }

                if (!$childDocumentCategory->isValidForVueComponent(
                    countryId: $countryId,
                    showAllDocumentsFromParentCategoriesIds: $showAllDocumentsFromParentCategoriesIds
                )) {
                    continue;
                }

                $childCategory = new ChildDocumentCategory($childDocumentCategory);

                /**
                 * @var \App\Core\Data\Models\Document[] $childCategoryDocuments
                 */
                $childCategoryDocuments = $documents->get($childDocumentCategory->id, []);
                foreach ($childCategoryDocuments as $childCategoryDocument) {
                    if (is_null($childCategoryDocument->file)) {
                        $childCategoryDocument->delete();
                    } else {
                        $childCategory->addDocument($childCategoryDocument, $commonData);
                    }
                }

                $category->addChildCategory($childCategory);
            }

            $categories->addParentDocumentCategory($category);
        }

        return $categories;
    }

    public function deleteDocumentForCompany(int $documentId, int $companyId): void
    {
        $document = $this->documentRepo->getDocumentById($documentId);
        if (is_null($document) || $document->company_id !== $companyId) {
            return;
        }

        $file = $document->file;
        if (!is_null($file)) {
            $this->fileService->deleteFile($file);
        }

        $document->delete();
    }

    /**
     * @throws \Exception
     */
    public function updateDocument(int $documentId, int $categoryId, array $fields): Document
    {
        $document = $this->documentRepo->getDocumentById($documentId);
        $category = $this->documentCategoryRepo->getDocumentCategoryById($categoryId);
        if (is_null($document) || is_null($category)) {
            throw new Exception();
        }

        $fields = collect($fields)->map(function (array $field) use ($documentId) {
            return new DocumentDataStoreTransfer(
                $documentId,
                $field['documentFieldId'],
                $field['fieldType'],
                $field['value'],
                $field['editable'] ?? null,
            );
        })->filter(function (DocumentDataStoreTransfer $documentDataStoreTransfer) {
            // IMPORTANT -  while adding new field type, check DocumentDataStoreTransfer::hasEmptyValue
            return !$documentDataStoreTransfer->hasEmptyValue();
        });

        $insert = [];
        foreach ($fields as $field) {
            /**
             * @var DocumentDataStoreTransfer $field
             */
            $insert[] = $field->toInsertArray();
        }

        if (count($insert) > 0) {
            $this->documentRepo->deleteAllDocumentDataByDocumentId($document->id);
            $this->documentRepo->insertDocumentData($insert);
        }

        return $document;
    }

    /**
     * @throws \Throwable
     */
    public function uploadAndCreateDocument(
        UploadedFile $file,
        int $categoryId,
        array $fields,
        int $companyId,
        int $updateUserId,
        string $date,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true
    ): Document {
        $document = $this->createNewDocument(
            $categoryId,
            $companyId,
            $updateUserId,
            $date,
            $resourceId,
            $resource,
            $uploaded,
            $generated,
            $deletable,
            $editable
        );

        try {
            $this->fileService->storeUploadedFile($document, $file);
        } catch (Throwable $exception) {
            $document->delete();
            throw $exception;
        }

        return $this->updateDocument($document->id, $document->document_category_id, $fields);
    }

    /**
     * @throws \Throwable
     */
    public function regenerateDocument(
        int $categoryId,
        int $companyId,
        int $countryId,
        int $updateUserId,
        string $date,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true
    ): Document {
        $category = $this->documentCategoryRepo->getDocumentCategoryById($categoryId);
        $template = $this->documentTemplateRepo
            ->getDocumentTemplateByCategoryIdForDate($category->id ?? 0, $date)
            ?->load('file');
        if (is_null($template)) {
            throw new Exception('error.Document template not found', 854848);
        }

        $document = $this->createNewDocument(
            $categoryId,
            $companyId,
            $updateUserId,
            $date,
            $resourceId,
            $resource,
            $uploaded,
            $generated,
            $deletable,
            $editable
        );

        try {
            $data = null;
            if ($template->context === CompanyMapper::class) {
                $company = $this->companyRepo->getCompanyById($companyId);
                $country = $this->countryRepo->getCountryById($countryId);

                $data = new CompanyData($company, $country);
                $documentData = $this->documentRepo->getEmptyDocumentDataModel();

                $documentData->document_id = $document->id;
                $documentData->document_data_field_id = DocumentDataField::FIELD_COUNTRY;
                $documentData->editable = false;

                $countryDataField = new Country($country);
                $documentData->raw_value = $countryDataField->toJson();

                $documentData->save();
            }

            if (is_null($data)) {
                throw new Exception('DEV EXCEPTION - Data is null, maybe new context, please implement...');
            }

            $file = $this->fileService->storeFileToDatabase(
                $category->name,
                'application/pdf',
                'pdf',
                0,
                $document
            );

            $pdf = $this->pdfTemplateService->resolveFieldsAndGeneratePdf($template, $data);
            $this->fileService->copyFileToFilesystem($pdf->getTmpFile(), $file->file_path);
            $this->fileService->refreshDbFileSize($file);

            return $document->load('file');
        } catch (Throwable $exception) {
            $document->delete();
            throw $exception;
        }
    }

    private function createNewDocument(
        int $categoryId,
        int $companyId,
        int $updateUserId,
        string $date,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true
    ): Document {
        $document = $this->documentRepo->getEmptyDocumentModel();

        $document->document_category_id = $categoryId;
        $document->company_id = $companyId;
        $document->update_user_id = $updateUserId;
        $document->updated_at = Carbon::now()->toDateTimeString('microsecond');
        $document->date = $date;
        $document->resource_id = $resourceId;
        $document->resource = $resource;
        $document->deletable = $deletable;
        $document->editable = $editable;
        $document->uploaded = $uploaded;
        $document->generated = $generated;

        $document->save();

        return $document;
    }

    public function createDocumentFromData(
        string $data,
        string $nameWithExtension,
        int $categoryId,
        int $companyId,
        int $updateUserId,
        string $date,
        ?array $fields = null,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true
    ): Document {
        $document = $this->createNewDocument(
            $categoryId,
            $companyId,
            $updateUserId,
            $date,
            $resourceId,
            $resource,
            $uploaded,
            $generated,
            $deletable,
            $editable
        );

//        try {
        $file = $this->fileService->createFileFromData($data, $nameWithExtension, $document);

//        } catch (Throwable $exception) {
//            $document->delete();
//            throw $exception;
//        }

        $fields = $fields ?? [];

        /** @noinspection PhpUnhandledExceptionInspection */
        return $this->updateDocument(
            $document->id,
            $document->document_category_id,
            $fields
        )->load('file');
    }
}
