<?php

namespace App\Core\Documents;

use App\Core\Documents\Contracts\DocumentCategoryServiceContract;
use App\Core\Documents\Contracts\DocumentServiceContract;
use App\Core\System\Providers\CoreModuleServiceProvider;

class DocumentsServiceProvider extends CoreModuleServiceProvider
{
    protected function resolve(): void
    {
        $this->bind(DocumentCategoryServiceContract::class, DocumentCategoryService::class);
        $this->bind(DocumentServiceContract::class, DocumentService::class);
    }
}
