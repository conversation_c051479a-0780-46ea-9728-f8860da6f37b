<?php

namespace App\Core\Documents;

use App\Core\Data\Models\Country;
use App\Core\Data\Models\DocumentCategory;
use App\Core\Data\Models\Platform;
use App\Core\Data\Repositories\Contracts\DocumentCategoryRepositoryContract;
use App\Core\Documents\Contracts\DocumentCategoryServiceContract;

class DocumentCategoryService implements DocumentCategoryServiceContract
{
    private DocumentCategoryRepositoryContract $documentCategoryRepo;

    public function __construct(DocumentCategoryRepositoryContract $documentCategoryRepo)
    {
        $this->documentCategoryRepo = $documentCategoryRepo;
    }

    public function storeDocumentCategory(
        string $name,
        int $parentCategoryId,
        array $forCountries,
        array $forCompanyRegistrationCountries,
        array $forEcommercePlatforms,
        bool $generatable,
        bool $uploadable,
        bool $requiredOnRegistration,
        bool $isVisible,
        ?int $sampleFileTypeId = null,
        ?bool $amountInBalanceSheetAsCredit = null,
        ?int $documentCategoryId = null
    ): DocumentCategory {
        if (count($forCountries) < 1) {
            $forCountries = [Country::ALL];
        }
        if (count($forCompanyRegistrationCountries) < 1) {
            $forCompanyRegistrationCountries = [Country::ALL];
        }
        if (count($forEcommercePlatforms) < 1) {
            $forEcommercePlatforms = [Platform::PLATFORM_ALL];
        }

        $category = null;
        if (!is_null($documentCategoryId)) {
            $category = $this->documentCategoryRepo->getDocumentCategoryById($documentCategoryId);
        }

        if (is_null($category)) {
            $category = $this->documentCategoryRepo->getEmptyDocumentCategoryModel();
        }

        $category->load(
            'options',
            'documentCategoryCountries.country',
            'documentCategoryCompanyCountries.country',
            'documentCategoryPlatforms.platform',
            'sampleFileType',
            'parentDocumentCategory',
        );


        $sequence = $category->sequence;
        if (is_null($sequence)) {
            $sequence = $this->documentCategoryRepo->getLatestSequence($parentCategoryId) + DocumentCategory::SEQUENCE_INCREMENT;
        }

        $category->name = $name;
        $category->sequence = $sequence;
        $category->parent_category_id = $parentCategoryId;
        $category->sample_file_type_id = $sampleFileTypeId;
        $category->uploadable = $uploadable;
        $category->generatable = $generatable;
        $category->is_visible = $isVisible;

        $category->save();

        $this->syncDocumentCategoryPlatforms($category, $forEcommercePlatforms);
        $this->syncDocumentCategoryCountries($category, $forCountries);
        $this->syncDocumentCategoryCountries($category, $forCompanyRegistrationCountries, true);

        $options = $category->options;
        if (is_null($options)) {
            $options = $this->documentCategoryRepo->getEmptyDocumentCategoryOptionsModel();
        }

        $options->document_category_id = $category->id;
        $options->required_on_registration = $requiredOnRegistration;
        $options->amount_in_balance_sheet_as_credit = $amountInBalanceSheetAsCredit;
        $options->save();

        return $category->refresh();
    }

    private function syncDocumentCategoryCountries(DocumentCategory $documentCategory, array $countriesIds, bool $isCompanyCountry = false): void
    {
        $hasAll = count(array_filter($countriesIds, function (int $countryId) {
                return $countryId === Country::ALL;
            })) > 0;
        if ($hasAll) {
            $countriesIds = [Country::ALL];
        }

        $hasEu = count(array_filter($countriesIds, function (int $countryId) {
                return $countryId === Country::EU;
            })) > 0;
        if ($hasEu) {
            $countriesIds = [Country::EU];
        }

        if ($isCompanyCountry) {
            $existingCountries = $documentCategory->documentCategoryCompanyCountries;
        } else {
            $existingCountries = $documentCategory->documentCategoryCountries;
        }
        $existingCountries = $existingCountries->keyBy('country_id');

        $forInsert = [];
        foreach ($countriesIds as $countryId) {
            if (!$existingCountries->has($countryId)) {
                $forInsert[] = [
                    'country_id'           => $countryId,
                    'document_category_id' => $documentCategory->id,
                    'is_company_country'   => $isCompanyCountry,
                ];
            }
        }

        $forDelete = [];
        foreach ($existingCountries as $existingCountry) {
            if (!in_array($existingCountry->country_id, $countriesIds)) {
                $forDelete[] = $existingCountry->id;
            }
        }

        $this->documentCategoryRepo->deleteDocumentCategoryCountries($forDelete);
        $this->documentCategoryRepo->insertDocumentCategoryCountries($forInsert);
    }

    private function syncDocumentCategoryPlatforms(DocumentCategory $documentCategory, array $platformsIds): void
    {
        if (count($platformsIds) > 1) {
            $platformsIds = array_filter($platformsIds, function (int $platformId) {
                return $platformId !== Platform::PLATFORM_ALL;
            });
        }

        $existingPlatforms = $documentCategory->documentCategoryPlatforms->keyBy('platform_id');

        $forInsert = [];
        foreach ($platformsIds as $platformsId) {
            if (!$existingPlatforms->has($platformsId)) {
                $forInsert[] = [
                    'document_category_id' => $documentCategory->id,
                    'platform_id'          => $platformsId
                ];
            }
        }

        $forDelete = [];
        foreach ($existingPlatforms as $existingPlatform) {
            if (!in_array($existingPlatform->platform_id, $platformsIds)) {
                $forDelete[] = $existingPlatform->id;
            }
        }

        $this->documentCategoryRepo->deleteDocumentCategoryPlatforms($forDelete);
        $this->documentCategoryRepo->insertDocumentCategoryPlatforms($forInsert);
    }
}
