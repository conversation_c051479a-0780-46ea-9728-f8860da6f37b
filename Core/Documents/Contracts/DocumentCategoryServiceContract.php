<?php

namespace App\Core\Documents\Contracts;

use App\Core\Data\Models\DocumentCategory;

interface DocumentCategoryServiceContract
{
    public function storeDocumentCategory(
        string $name,
        int $parentCategoryId,
        array $forCountries,
        array $forCompanyRegistrationCountries,
        array $forEcommercePlatforms,
        bool $generatable,
        bool $uploadable,
        bool $requiredOnRegistration,
        bool $isVisible,
        ?int $sampleFileTypeId = null,
        ?bool $amountInBalanceSheetAsCredit = null,
        ?int $documentCategoryId = null
    ): DocumentCategory;
}
