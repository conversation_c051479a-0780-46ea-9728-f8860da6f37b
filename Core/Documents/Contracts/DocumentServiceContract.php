<?php

namespace App\Core\Documents\Contracts;

use App\Core\Data\Models\Document;
use App\Core\Documents\DataTransfer\DocumentsData\DocumentCategoriesData;
use App\Core\Documents\DataTransfer\DocumentsData\PredefinedData;
use App\Core\Documents\DataTransfer\DocumentsData\VueComponentOptions;
use Illuminate\Http\UploadedFile;

interface DocumentServiceContract
{
    public function deleteDocumentForCompany(int $documentId, int $companyId): void;

    public function getDocumentsDataForDocumentsVueComponent(
        int $companyId,
        ?array $parentCategoriesIds = null,
        ?string $resource = null,
        ?int $resourceId = null,
        ?int $countryId = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?array $showAllDocumentsFromParentCategoriesIds = null,
        ?array $forceShowOnlyChildCategories = null,
        ?array $forceDontShowChildCategories = null,
        ?array $meta = null,
        ?PredefinedData $predefinedData = null,
        ?VueComponentOptions $vueComponentOptions = null
    ): DocumentCategoriesData;

    public function regenerateDocument(
        int $categoryId,
        int $companyId,
        int $countryId,
        int $updateUserId,
        string $date,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true,
    ): Document;

    public function updateDocument(int $documentId, int $categoryId, array $fields): Document;

    public function uploadAndCreateDocument(
        UploadedFile $file,
        int $categoryId,
        array $fields,
        int $companyId,
        int $updateUserId,
        string $date,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true,
    ): Document;

    public function createDocumentFromData(
        string $data,
        string $nameWithExtension,
        int $categoryId,
        int $companyId,
        int $updateUserId,
        string $date,
        ?array $fields = null,
        ?int $resourceId = null,
        ?string $resource = null,
        bool $uploaded = true,
        bool $generated = false,
        bool $deletable = true,
        bool $editable = true
    ): Document;
}
