<?php

namespace App\Core\Addresses;

use App\Core\Data\Models\Address;
use App\Core\Data\Repositories\Contracts\AddressRepositoryContract;
use App\Core\Data\Repositories\Contracts\CityRepositoryContract;
use App\Core\Addresses\Contracts\AddressServiceContract;

class AddressService implements AddressServiceContract
{
    private AddressRepositoryContract $addressRepo;
    private CityRepositoryContract $cityRepo;

    public function __construct(AddressRepositoryContract $addressRepo, CityRepositoryContract $cityRepo)
    {
        $this->addressRepo = $addressRepo;
        $this->cityRepo = $cityRepo;
    }

    /**
     * @inheritDoc
     */
    public function storeAddress(
        string $street,
        string $cityName,
        string $postalCode,
        int $countryId,
        ?string $houseNumber = null,
        ?string $state = null,
        ?string $addition = null,
    ): Address {
        $city = $this->cityRepo->createCityIfNotExists($cityName, $countryId);

        $address = $this->addressRepo->getEmptyAddressModel();
        $address->street = $street;
        $address->house_number = $houseNumber;
        $address->addition = $addition;
        $address->city_id = $city->id;
        $address->postal_code = $postalCode;
        $address->state = $state;
        $address->country_id = $countryId;

        $address->save();

        return $address;
    }

    /**
     * @inheritDoc
     */
    public function storeUpdateAddress(
        string $street,
        ?string $houseNumber,
        ?string $addition,
        string $cityName,
        string $postalCode,
        ?string $state,
        int $countryId,
        ?int $addressId
    ): ?Address {

        $city = $this->cityRepo->createCityIfNotExists($cityName, $countryId);

        if (is_null($addressId)) {
            $address = $this->addressRepo->getEmptyAddressModel();
        } else {
            $address = $this->addressRepo->getAddressById($addressId);
            if (is_null($address)) {
                return null;
            }
        }

        $address->street = $street;
        $address->house_number = $houseNumber;
        $address->addition = $addition;
        $address->city_id = $city->id;
        $address->postal_code = $postalCode;
        $address->state = $state;
        $address->country_id = $countryId;

        $address->save();

        return $address;
    }

    /**
     * @inheritDoc
     */
    public function deleteAddressById(int $addressId): void
    {
        $this->addressRepo->deleteAddressById($addressId);
    }

    public function getAddressById(int $addressId): Address|null
    {
        return $this->addressRepo->getAddressById($addressId);
    }
}
