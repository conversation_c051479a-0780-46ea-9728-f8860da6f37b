<?php

namespace App\Core\Addresses\Contracts;

use App\Core\Data\Models\Address;

interface AddressServiceContract
{
    /**
     * @param string $street
     * @param string $cityName
     * @param string $postalCode
     * @param int $countryId
     * @param string|null $houseNumber
     * @param string|null $state
     * @param string|null $addition
     * @return Address
     */
    public function storeAddress(
        string $street,
        string $cityName,
        string $postalCode,
        int $countryId,
        ?string $houseNumber = null,
        ?string $state = null,
        ?string $addition = null,
    ): Address;

    /**
     * @param string $street
     * @param string|null $houseNumber
     * @param string|null $addition
     * @param string $cityName
     * @param string $postalCode
     * @param string|null $state
     * @param int $countryId
     * @param int|null $addressId
     * @return Address|null
     */
    public function storeUpdateAddress(
        string $street,
        ?string $houseNumber,
        ?string $addition,
        string $cityName,
        string $postalCode,
        ?string $state,
        int $countryId,
        ?int $addressId
    ): ?Address;

    /**
     * @param int $addressId
     */
    public function deleteAddressById(int $addressId): void;

    /**
     * @param int $addressId
     * @return Address|null
     */
    public function getAddressById(int $addressId): Address|null;
}
