<?php

namespace App\Api\Invoices\Controllers;

use App\Api\Common\Controllers\Controller;
use App\Api\Invoices\Transformers\Calculation\CalculationTransformer;
use App\Api\Invoices\Transformers\Calculation\InvoiceTransformer;
use App\Api\Invoices\Transformers\DeliveryConditionTransformer;
use App\Api\Invoices\Transformers\InvoiceStatusTransformer;
use App\Api\Invoices\Transformers\InvoiceTypeTransformer;
use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Data\Repositories\Contracts\DeliveryConditionRepositoryContract;
use App\Core\Data\Repositories\Contracts\InvoiceRepositoryContract;
use App\Core\Invoices\Contracts\InvoiceServiceContract;
use App\Core\Invoices\DataTransfer\InvoicesApi\CalculateInvoiceApiRequest;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class InvoiceController extends Controller
{
    private InvoiceRepositoryContract $invoiceRepo;
    private InvoiceServiceContract $invoiceService;

    public function __construct(
        InvoiceServiceContract $invoiceService,
        InvoiceRepositoryContract $invoiceRepo
    ) {
        $this->invoiceRepo = $invoiceRepo;
        $this->invoiceService = $invoiceService;
    }

    public function store(Request $request): JsonResponse
    {
        $request = new CalculateInvoiceApiRequest(
            $request->all(),
            $this->getApiConsumer()
        );

        $data = $this->invoiceService->calculateAndStoreInvoiceByApi($request);
        $data = $this->transform($data, new CalculationTransformer());

        return $this->successResponse($data->toArray());
    }

    /** @noinspection PhpUnused */
    public function changeStatus(int $invoiceId, Request $request): JsonResponse
    {
        $data = [
            'id' => $invoiceId
        ];

        $data = array_merge($data, $request->all());

        $invoice = $this->invoiceRepo->getInvoiceById($invoiceId);
        $rules = [
            'id'       => [
                'exists:invoices,id',
                function (string $attribute, mixed $value, Closure $fail) use ($invoice) {
                    if (!is_null($invoice)) {
                        $companyId = $invoice->company_id;
                        $apiConsumersCompaniesIds = $this->getApiConsumer()
                            ->load('companyApiConsumers')
                            ->companyApiConsumers
                            ->pluck('company_id')
                            ->toArray();

                        if (!in_array($companyId, $apiConsumersCompaniesIds)) {
                            $fail(1004);
                        }
                    }
                }
            ],
            'statusId' => 'required|integer|exists:invoice_statuses,id',
        ];
        $codes = [
            'id.required' => 1001,
            'id.integer'  => 1002,
            'id.exists'   => 1003,

            'statusId.required' => 1101,
            'statusId.integer'  => 1102,
            'statusId.exists'   => 1103,
        ];

        $messages = [
            1001 => 'Invoice id is required.',
            1002 => 'Invoice id must be an integer.',
            1003 => 'Invoice id does not exist.',
            1004 => 'Invoice company id does not match the API consumer company id.',

            1101 => 'Invoice status id is required.',
            1102 => 'Invoice status id must be an integer.',
            1103 => 'Invoice status does not exist.',
        ];

        $validator = Validator::make($data, $rules, $codes);
        if ($validator->fails()) {
            $codes = collect($validator->errors()->toArray())
                ->flatten()
                ->map(function (int $error) {
                    return $error;
                });

            $errors = $codes->map(function ($error) use ($messages) {
                return $messages[$error] ?? 'Unknown error';
            });

            /** @noinspection PhpUnhandledExceptionInspection */
            throw new ValidationApiException(
                errors: $errors->toArray(),
                statusCode: $codes->toArray(),
                meta: ['codes' => $messages],
            );
        }

        $invoice->invoice_status_id = (int)$data['statusId'];
        $invoice->save();

        $invoice = $invoice->transformData(new InvoiceTransformer());

        return $this->successResponse($invoice->toArray());
    }

    /** @noinspection PhpUnused */
    public function invoiceStatuses(): JsonResponse
    {
        $invoiceTypes = $this->invoiceRepo
            ->getAllInvoiceStatuses()
            ->transformData(new InvoiceStatusTransformer());

        return $this->successResponse($invoiceTypes->toArray());
    }

    /** @noinspection PhpUnused */
    public function invoiceTypes(): JsonResponse
    {
        $invoiceTypes = $this->invoiceRepo
            ->getInvoiceTypes()
            ->load('invoiceSubtypes')
            ->transformData(new InvoiceTypeTransformer());

        return $this->successResponse($invoiceTypes->toArray());
    }

    /** @noinspection PhpUnused */
    public function deliveryConditions(DeliveryConditionRepositoryContract $deliveryConditionRepo): JsonResponse
    {
        $deliveryConditions = $deliveryConditionRepo
            ->getAll()
            ->transformData(new DeliveryConditionTransformer());

        return $this->successResponse($deliveryConditions->toArray());
    }
}
