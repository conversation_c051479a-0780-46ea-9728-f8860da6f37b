<?php

use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'v1/invoices'], function () {
    Route::post('/calculate-and-store.json', 'InvoiceController@store')->name('api.v1.invoices.store');
    Route::patch('/{id}/change-status.json', 'InvoiceController@changeStatus')->name('api.v1.invoices.change-status');

    Route::get('/invoice-statuses.json', 'InvoiceController@invoiceStatuses')->name('api.v1.invoices.invoice-statuses');
    Route::get('/invoice-types.json', 'InvoiceController@invoiceTypes')->name('api.v1.invoices.invoice-types');
    Route::get('/delivery-conditions.json', 'InvoiceController@deliveryConditions')->name('api.v1.invoices.delivery-conditions');
});

