<?php

namespace App\Api\Invoices\Transformers\Calculation;

use App\Api\Invoices\Transformers\Calculation\Includes\CountryTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\CurrencyTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\CustomerTypeTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\DeliveryConditionTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\InvoiceItemTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\InvoiceStatusTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\InvoiceSubtypeTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\TaxCollectionResponsibilityTransformer;
use App\Api\Invoices\Transformers\Calculation\Includes\TaxReportingSchemeTransformer;
use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\Invoice;
use League\Fractal\Resource\Collection;
use League\Fractal\Resource\Item;

class InvoiceTransformer extends ApiTransformer
{
    protected array $defaultIncludes = [
        'items',
    ];

    protected array $availableIncludes = [
        'subtype',
        'status',
        'deliveryCondition',
        'taxReportingScheme',
        'taxCollectionResponsibility',
        'customerType',
        'salesCurrency',
        'taxCalculationCountry',
        'taxReportingCountry',
        'shipFromCountry',
        'billToCountry',
        'shipToCountry',
    ];

    private array $load = [
        'invoiceItems.taxCode.itemCodeCategory',
        'invoiceItems.taxCode.itemType',
        'invoiceItems.invoiceItemType',
        'invoiceSubtype',
        'status',
        'deliveryCondition',
        'taxReportingScheme',
        'taxCollectionResponsibility',
        'customerType',
        'currency',
        'taxCalculationCountry.currency',
        'taxReportingCountry.currency',
        'shipFromCountry.currency',
        'billToCountry.currency',
        'shipToCountry.currency',
    ];

    public function transform(Invoice $invoice): array
    {
        $invoice->loadMissing($this->load);

        return [
            'id'          => $invoice->id,
            'date'        => $invoice->invoice_date,
            'number'      => $invoice->invoice_number,
            'orderNumber' => $invoice->order_number,
            'createdAt'   => $invoice->created_at,

            'subtypeId'                     => $invoice->invoice_subtype_id,
            'statusId'                      => $invoice->invoice_status_id,
            'marketplaceId'                 => $invoice->marketplace_id,
            'companyId'                     => $invoice->company_id,
            'deliveryConditionId'           => $invoice->delivery_condition_id,
            'taxReportingSchemeId'          => $invoice->tax_reporting_scheme_id,
            'taxCollectionResponsibilityId' => $invoice->tax_collection_responsibility_id,
            'customerTypeId'                => $invoice->customer_type_id,
            'salesCurrencyId'               => $invoice->sales_currency_id,
            'taxCalculationCountryId'       => $invoice->tax_calculation_country_id,
            'taxReportingCountryId'         => $invoice->tax_reporting_country_id,

            'shipFromCountryId' => $invoice->ship_from_country_id,
            'shipFromVatNumber' => $invoice->ship_from_vat_number,
            'shipFromTaxNumber' => $invoice->ship_from_tax_number,

            'billToName'       => $invoice->bill_to_name,
            'billToCountryId'  => $invoice->bill_to_country_id,
            'billToAddress'    => $invoice->bill_to_address,
            'billToPostalCode' => $invoice->bill_to_postal_code,
            'billToVatNumber'  => $invoice->bill_to_vat_number,

            'shipToIsSameAsBillTo' => $invoice->shipping_is_same_as_bill_to,

            'shipToName'       => $invoice->ship_to_name,
            'shipToCountryId'  => $invoice->ship_to_country_id,
            'shipToAddress'    => $invoice->ship_to_address,
            'shipToPostalCode' => $invoice->ship_to_postal_code,
            'shipToVatNumber'  => $invoice->ship_to_vat_number,

            'totalWithoutShippingAndWrap' => round($invoice->total_without_shipping_and_wrap, 2, PHP_ROUND_HALF_EVEN),
            'total'                       => round($invoice->total, 2, PHP_ROUND_HALF_EVEN),
            'net'                         => round($invoice->total_net, 2, PHP_ROUND_HALF_EVEN),
            'vat'                         => round($invoice->total_vat, 2, PHP_ROUND_HALF_EVEN),
        ];
    }

    /** @noinspection PhpUnused */
    public function includeSubtype(Invoice $invoice): Item
    {
        return $this->item($invoice->invoiceSubtype, new InvoiceSubtypeTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeStatus(Invoice $invoice): Item
    {
        return $this->item($invoice->status, new InvoiceStatusTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeDeliveryCondition(Invoice $invoice): ?Item
    {
        $deliveryCondition = $invoice->deliveryCondition;
        if (is_null($deliveryCondition)) {
            return null;
        }

        return $this->item($deliveryCondition, new DeliveryConditionTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeTaxReportingScheme(Invoice $invoice): Item
    {
        return $this->item($invoice->taxReportingScheme, new TaxReportingSchemeTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeTaxCollectionResponsibility(Invoice $invoice): Item
    {
        return $this->item($invoice->taxCollectionResponsibility, new TaxCollectionResponsibilityTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeCustomerType(Invoice $invoice): ?Item
    {
        $customerType = $invoice->customerType;
        if (is_null($customerType)) {
            return null;
        }

        return $this->item($customerType, new CustomerTypeTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeSalesCurrency(Invoice $invoice): Item
    {
        return $this->item($invoice->currency, new CurrencyTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeTaxCalculationCountry(Invoice $invoice): ?Item
    {
        $country = $invoice->taxCalculationCountry;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeTaxReportingCountry(Invoice $invoice): ?Item
    {
        $country = $invoice->taxReportingCountry;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeShipFromCountry(Invoice $invoice): ?Item
    {
        $country = $invoice->shipFromCountry;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeBillToCountry(Invoice $invoice): ?Item
    {
        $country = $invoice->billToCountry;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeShipToCountry(Invoice $invoice): ?Item
    {
        $country = $invoice->shipToCountry;
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeItems(Invoice $invoice): Collection
    {
        return $this->collection($invoice->invoiceItems, new InvoiceItemTransformer());
    }
}

