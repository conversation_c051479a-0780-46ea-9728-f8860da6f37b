<?php

namespace App\Api\Invoices\Transformers\Calculation;

use App\Api\Invoices\Transformers\Calculation\Includes\InvoiceGoodsPropertiesTransformer;
use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Invoices\Contracts\DetectedPropertiesContract;
use League\Fractal\Resource\Item;

class DetectedTransformer extends ApiTransformer
{
    protected array $availableIncludes = [
        'invoiceGoodsProperties'
    ];

    public function transform(DetectedPropertiesContract $data): array
    {
        return [
            'taxCollectionResponsibilityId' => $data->getTaxCollectionResponsibilityId(),
            'taxCalculationCountryId'       => $data->getTaxCalculationCountryId(),
            'taxReportingSchemeId'          => $data->getTaxReportingSchemeId(),
            'customerTypeId'                => $data->getCustomerTypeId(),
            'invoiceSubtypeId'              => $data->getInvoiceSubtypeId(),
            'taxReportingCountryId'         => $data->getTaxReportingCountryId(),
            'invoiceGoodsProperties'        => null
        ];
    }

    /** @noinspection PhpUnused */
    public function includeInvoiceGoodsProperties(DetectedPropertiesContract $data): ?Item
    {
        $detect = $data->getInvoiceGoodsProperties();
        if (is_null($detect)) {
            return null;
        }

        return $this->item($detect, new InvoiceGoodsPropertiesTransformer());
    }
}
