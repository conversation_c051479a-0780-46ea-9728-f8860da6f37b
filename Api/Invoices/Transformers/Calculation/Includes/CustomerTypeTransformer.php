<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\CustomerType;

class CustomerTypeTransformer extends ApiTransformer
{
    public function transform(CustomerType $customerType): array
    {
        return [
            'id'   => $customerType->id,
            'name' => _l(text: $customerType->type, forceLocale: 'en'),
        ];
    }
}
