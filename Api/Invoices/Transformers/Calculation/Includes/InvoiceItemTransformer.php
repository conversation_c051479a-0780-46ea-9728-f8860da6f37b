<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\InvoiceItem;
use League\Fractal\Resource\Item;

class InvoiceItemTransformer extends ApiTransformer
{
    protected array $defaultIncludes = [
        'taxCode',
        'itemType',
    ];

    public function transform(InvoiceItem $item): array
    {
        return [
            'description' => $item->item_description,
            'qty'         => $item->qty,

            'vatPercent' => $this->round($item->vat_percent),
            'net'        => $this->round($item->item_sales_price_net),
            'gross'      => $this->round($item->item_gross_price),
            'vatAmount'  => $this->round($item->item_sales_price_vat_amount),

            'discountPercentage' => $this->round($item->discount_percentage),
            'discountNet'        => $this->round($item->discount_net),
            'discountGross'      => $this->round($item->discount_gross),
            'discountVatAmount'  => $this->round($item->discount_vat_amount),

//            'data' => $item->toArray(),
        ];
    }

    /** @noinspection PhpUnused */
    public function includeTaxCode(InvoiceItem $item): Item
    {
        return $this->item($item->taxCode, new TaxCodeTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeItemType(InvoiceItem $item): Item
    {
        return $this->item($item->invoiceItemType, new ItemTypeTransformer());
    }

    // HELPERS
    private function round(?float $value = null): ?float
    {
        if (is_null($value)) {
            return null;
        }

        return round($value, 2, PHP_ROUND_HALF_EVEN);
    }
}
