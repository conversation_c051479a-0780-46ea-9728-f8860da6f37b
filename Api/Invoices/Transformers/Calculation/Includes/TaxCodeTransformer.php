<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\ItemTaxCode;
use League\Fractal\Resource\Item;

class TaxCodeTransformer extends ApiTransformer
{
    protected array $defaultIncludes = [
        'itemCodeCategory',
    ];

    public function transform(ItemTaxCode $taxCode): array
    {
        return [
            'id'          => $taxCode->id,
            'description' => $taxCode->description,
            'code'        => $taxCode->code,
        ];
    }

    /** @noinspection PhpUnused */
    public function includeItemCodeCategory(ItemTaxCode $taxCode): Item
    {
        return $this->item($taxCode->itemCodeCategory, new ItemCodeCategoryTransformer());
    }
}
