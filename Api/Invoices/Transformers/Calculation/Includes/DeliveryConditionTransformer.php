<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\DeliveryCondition;

class DeliveryConditionTransformer extends APITransformer
{
    public function transform(DeliveryCondition $deliveryCondition): array
    {
        return [
            'id'   => $deliveryCondition->id,
            'name' => _l(text: $deliveryCondition->name, forceLocale: 'en'),
            'code' => $deliveryCondition->code,
        ];
    }
}
