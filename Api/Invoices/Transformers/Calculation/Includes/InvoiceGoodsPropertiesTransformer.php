<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Invoices\Contracts\InvoiceGoodsPropertiesContract;
use League\Fractal\Resource\Item;

class InvoiceGoodsPropertiesTransformer extends ApiTransformer
{
    protected array $defaultIncludes = [
        'companyCountry',
        'billToCountry',
        'shipToCountry',
        'shipFromCountry',
        'marketplaceOssCountry',
        'marketplaceIossRegistrationCountry',
        'companyOssRegistrationCountry',
        'companyIossRegistrationCountry',
    ];

    public function transform(InvoiceGoodsPropertiesContract $data): array
    {
        $data = $data->toArray();
        foreach ($this->defaultIncludes as $include) {
            $data[$include] = null;
        }

        return $data;
    }

    /** @noinspection PhpUnused */
    public function includeCompanyCountry(InvoiceGoodsPropertiesContract $data): Item
    {
        return $this->item($data->getCompanyCountry(), new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeBillToCountry(InvoiceGoodsPropertiesContract $data): Item
    {
        return $this->item($data->getBillToCountry(), new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeShipToCountry(InvoiceGoodsPropertiesContract $data): ?Item
    {
        $country = $data->getShipToCountry();
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeShipFromCountry(InvoiceGoodsPropertiesContract $data): Item
    {
        return $this->item($data->getShipFromCountry(), new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeMarketplaceOssCountry(InvoiceGoodsPropertiesContract $data): ?Item
    {
        $country = $data->getMarketplaceOssCountry();
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeMarketplaceIossRegistrationCountry(InvoiceGoodsPropertiesContract $data): ?Item
    {
        $country = $data->getMarketplaceIossRegistrationCountry();
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeCompanyOssRegistrationCountry(InvoiceGoodsPropertiesContract $data): ?Item
    {
        $country = $data->getCompanyOssRegistrationCountry();
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeCompanyIossRegistrationCountry(InvoiceGoodsPropertiesContract $data): ?Item
    {
        $country = $data->getCompanyIossRegistrationCountry();
        if (is_null($country)) {
            return null;
        }

        return $this->item($country, new CountryTransformer());
    }
}
