<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\TaxCollectionResponsibility;

class TaxCollectionResponsibilityTransformer extends ApiTransformer
{
    public function transform(TaxCollectionResponsibility $taxCollectionResponsibility): array
    {
        return [
            'id'          => $taxCollectionResponsibility->id,
            'name'        => _l(text: $taxCollectionResponsibility->name, forceLocale: 'en'),
            'description' => $taxCollectionResponsibility->note,
            'amazonCode'  => $taxCollectionResponsibility->amazon_code,
        ];
    }
}
