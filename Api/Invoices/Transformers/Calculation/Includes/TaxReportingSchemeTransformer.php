<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\TaxReportingScheme;

class TaxReportingSchemeTransformer extends ApiTransformer
{
    public function transform(TaxReportingScheme $taxReportingScheme): array
    {
        return [
            'id'          => $taxReportingScheme->id,
            'name'        => _l(text: $taxReportingScheme->name, forceLocale: 'en'),
            'amazonCode'  => $taxReportingScheme->amazon_code,
            'description' => trim($taxReportingScheme->description),
        ];
    }
}
