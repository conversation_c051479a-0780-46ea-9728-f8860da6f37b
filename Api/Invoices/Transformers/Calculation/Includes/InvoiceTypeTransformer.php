<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\InvoiceType;

class InvoiceTypeTransformer extends ApiTransformer
{
    public function transform(InvoiceType $invoiceType): array
    {
        return [
            'id'   => $invoiceType->id,
            'name' => $invoiceType->name,
        ];
    }
}
