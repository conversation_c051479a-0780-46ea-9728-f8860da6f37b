<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\ItemCodeCategory;

class ItemCodeCategoryTransformer extends ApiTransformer
{
    public function transform(ItemCodeCategory $itemCodeCategory): array
    {
        return [
            'id'   => $itemCodeCategory->id,
            'name' => _l(text: $itemCodeCategory->name, forceLocale: 'en'),
        ];
    }
}
