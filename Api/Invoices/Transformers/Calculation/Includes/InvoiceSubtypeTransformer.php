<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\InvoiceSubtype;
use League\Fractal\Resource\Item;

class InvoiceSubtypeTransformer extends APITransformer
{
    protected array $defaultIncludes = [
        'type',
    ];

    public function transform(InvoiceSubtype $invoiceSubtype): array
    {
        return [
            'id'   => $invoiceSubtype->id,
            'name' => $invoiceSubtype->name,
        ];
    }

    /** @noinspection PhpUnused */
    public function includeType(InvoiceSubtype $invoiceSubtype): Item
    {
        return $this->item($invoiceSubtype->invoiceType, new InvoiceTypeTransformer());
    }
}
