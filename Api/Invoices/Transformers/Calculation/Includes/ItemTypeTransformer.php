<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\ItemType;

class ItemTypeTransformer extends ApiTransformer
{
    public function transform(ItemType $itemType): array
    {
        return [
            'id'   => $itemType->id,
            'name' => _l(text: $itemType->name, forceLocale: 'en'),
        ];
    }
}
