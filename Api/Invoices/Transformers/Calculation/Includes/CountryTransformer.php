<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\Country;
use League\Fractal\Resource\Item;

class CountryTransformer extends ApiTransformer
{
    protected array $defaultIncludes = [
        'currency',
    ];

    public function transform(Country $country): array
    {
        $data = [
            'id'            => $country->id,
            'name'          => $country->name,
            'code'          => $country->code,
            'vatCode'       => $country->vat_code,
            'currencyId'    => $country->currency_id,
            'isCommunity'   => $country->is_community,
            'inEu'          => $country->in_eu,
            'inEuStartDate' => $country->in_eu_start_date,
            'inEuEndDate'   => $country->in_eu_end_date,
        ];
        if ($this->hasOptionalInclude('currency')) {
            $data['currency'] = null;
        }

        return $data;
    }

    public function includeCurrency(Country $country): ?Item
    {
        $currency = $country->currency;
        if (is_null($currency)) {
            return null;
        }

        return $this->item($country->currency, new CurrencyTransformer());
    }
}
