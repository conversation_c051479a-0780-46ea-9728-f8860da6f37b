<?php

namespace App\Api\Invoices\Transformers\Calculation\Includes;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\Currency;

class CurrencyTransformer extends ApiTransformer
{
    public function transform(Currency $currency): array
    {
        return [
            'id'     => $currency->id,
            'name'   => $currency->name,
            'code'   => $currency->code,
            'symbol' => $currency->symbol,
        ];
    }
}
