<?php

namespace App\Api\Invoices\Transformers\Calculation;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Invoices\DataTransfer\InvoicesApi\ApiCalculationData;
use League\Fractal\Resource\Item;

class CalculationTransformer extends ApiTransformer
{
    protected array $defaultIncludes = [
        'invoice',
        'detected',
    ];

    public function transform(ApiCalculationData $data): array
    {
        return [
            'invoice' => null,
            'detected' => null,
        ];
    }

    /** @noinspection PhpUnused */
    public function includeInvoice(ApiCalculationData $data): Item
    {
        return $this->item($data->getInvoice(), new InvoiceTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeDetected(ApiCalculationData $data): Item
    {
        return $this->item($data->getDetected(), new DetectedTransformer());
    }
}
