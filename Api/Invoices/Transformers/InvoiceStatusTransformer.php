<?php

namespace App\Api\Invoices\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\InvoiceStatus;

class InvoiceStatusTransformer extends ApiTransformer
{
    public function transform(InvoiceStatus $invoiceStatus): array
    {
        return [
            'id'    => $invoiceStatus->id,
            'name'  => _l(text: $invoiceStatus->status, forceLocale: 'en'),
            'index' => $invoiceStatus->key,
        ];
    }
}
