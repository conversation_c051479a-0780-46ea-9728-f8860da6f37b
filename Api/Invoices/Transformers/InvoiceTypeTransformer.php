<?php

namespace App\Api\Invoices\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\InvoiceType;
use League\Fractal\Resource\Collection;

class InvoiceTypeTransformer extends APITransformer
{
    protected array $availableIncludes = [
        'invoiceSubtypes'
    ];

    public function transform(InvoiceType $invoiceType): array
    {
        return [
            'id'   => $invoiceType->id,
            'name' => $invoiceType->name,
        ];
    }

    /** @noinspection PhpUnused */
    public function includeInvoiceSubtypes(InvoiceType $invoiceType): Collection
    {
        return $this->collection($invoiceType->invoiceSubtypes, new InvoiceSubtypeTransformer());
    }
}
