<?php

namespace App\Api\Invoices\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\DeliveryCondition;

class DeliveryConditionTransformer extends ApiTransformer
{
    public function transform(DeliveryCondition $deliveryCondition): array
    {
        return [
            'id'   => $deliveryCondition->id,
            'name' => _l($deliveryCondition->name),
            'code' => $deliveryCondition->code,
        ];
    }
}
