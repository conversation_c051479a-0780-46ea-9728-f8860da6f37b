<?php

use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'v1/identification-numbers'], function () {
    Route::get('/oss-types.json', 'IdentificationNumberController@ossTypes')->name('api.v1.identification-numbers.oss-types');
    Route::get('/ioss-types.json', 'IdentificationNumberController@iossTypes')->name('api.v1.identification-numbers.ioss-types');
    Route::get('/establishment-statues.json', 'IdentificationNumberController@vatNumberEstablishmentStatues')->name('api.v1.identification-numbers.vat-number-establishment-statues');
});

