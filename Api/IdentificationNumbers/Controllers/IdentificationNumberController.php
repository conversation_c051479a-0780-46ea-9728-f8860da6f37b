<?php /** @noinspection PhpUnused */

namespace App\Api\IdentificationNumbers\Controllers;

use App\Api\Common\Controllers\Controller;
use App\Api\IdentificationNumbers\Transformers\EstablishmentStatusTransformer;
use App\Api\IdentificationNumbers\Transformers\IossNumberTransformer;
use App\Api\IdentificationNumbers\Transformers\OssNumberTransformer;
use App\Core\Api\OpenApi\Schemas\IdentificationNumbers\EstablishmentStatusesScheme;
use App\Core\Api\OpenApi\Schemas\IdentificationNumbers\IossTypesScheme;
use App\Core\Api\OpenApi\Schemas\IdentificationNumbers\OssTypesScheme;
use App\Core\Data\Models\EstablishmentStatus;
use App\Core\Data\Repositories\Contracts\IossNumberRepositoryContract;
use App\Core\Data\Repositories\Contracts\OssNumberRepositoryContract;
use Illuminate\Http\JsonResponse;
use App\Core\Api\OpenApi\Others\Tag;
use App\Core\Api\OpenApi\Requests\Get;
use App\Core\Api\OpenApi\Responses\SuccessResponse;

#[Tag(name: 'Identification Numbers', description: 'Identification numbers API endpoints')]
class IdentificationNumberController extends Controller
{
    #[Get(
        path: '/identification-numbers/oss-types.json',
        summary: 'OSS types data',
        tags: ['Identification Numbers'],
        responses: [
            new SuccessResponse(
                description: 'OSS types data - array of objects',
                data: OssTypesScheme::class
            ),
        ]
    )]
    public function ossTypes(OssNumberRepositoryContract $ossNumberRepo): JsonResponse
    {
        $ossNumberTypes = $ossNumberRepo->getAllOssNumberTypes()
            ->transformData(new OssNumberTransformer());

        return $this->successResponse($ossNumberTypes->toArray());
    }

    #[Get(
        path: '/identification-numbers/ioss-types.json',
        summary: 'IOSS types data',
        tags: ['Identification Numbers'],
        responses: [
            new SuccessResponse(
                description: 'IOSS types data - array of objects',
                data: IossTypesScheme::class
            ),
        ]
    )]
    public function iossTypes(IossNumberRepositoryContract $iossNumberRepo): JsonResponse
    {
        $iossNumberTypes = $iossNumberRepo->getAllIossNumberTypes()
            ->transformData(new IossNumberTransformer());

        return $this->successResponse($iossNumberTypes->toArray());
    }

    #[Get(
        path: '/identification-numbers/establishment-statues.json',
        summary: 'Establishment statuses data',
        tags: ['Identification Numbers'],
        responses: [
            new SuccessResponse(
                description: 'Establishment statuses data - array of objects',
                data: EstablishmentStatusesScheme::class
            ),
        ]
    )]
    public function vatNumberEstablishmentStatues(): JsonResponse
    {
        $establishmentStatues = collect([
            new EstablishmentStatus(EstablishmentStatus::PERMANENT_ESTABLISHMENT),
            new EstablishmentStatus(EstablishmentStatus::OTHER_FACILITIES),
        ])->transformData(new EstablishmentStatusTransformer());

        return $this->successResponse($establishmentStatues->toArray());
    }
}
