<?php

namespace App\Api\IdentificationNumbers\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\OssNumberType;

class OssNumberTransformer extends ApiTransformer
{
    public function transform(OssNumberType $ossNumberType): array
    {
        return [
            'id'   => $ossNumberType->id,
            'name' => _l($ossNumberType->lang_key),
            'code' => $ossNumberType->code
        ];
    }
}
