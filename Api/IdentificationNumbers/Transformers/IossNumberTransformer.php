<?php

namespace App\Api\IdentificationNumbers\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\IossNumberType;
use App\Core\Data\Models\OssNumberType;

class IossNumberTransformer extends ApiTransformer
{
    public function transform(IossNumberType $iossNumberType): array
    {
        return [
            'id'   => $iossNumberType->id,
            'name' => _l($iossNumberType->lang_key),
            'code' => $iossNumberType->code
        ];
    }
}
