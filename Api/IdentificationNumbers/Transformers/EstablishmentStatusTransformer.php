<?php

namespace App\Api\IdentificationNumbers\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\EstablishmentStatus;

class EstablishmentStatusTransformer extends ApiTransformer
{
    public function transform(EstablishmentStatus $establishmentStatus): array
    {
        $description = strip_tags(_l($establishmentStatus->description));
        $description = str_replace('"', '`', $description);

        return [
            'id'          => $establishmentStatus->id,
            'name'        => _l($establishmentStatus->name),
            'description' => $description,
        ];
    }
}
