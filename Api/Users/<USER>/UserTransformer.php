<?php

namespace App\Api\Users\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\User;

class UserTransformer extends ApiTransformer
{
    public function transform(User $user): array
    {
        return [
            'id'        => $user->id,
            'email'     => $user->email,
            'firstName' => $user->first_name,
            'lastName'  => $user->last_name,
            'fullName'  => $user->full_name,
            'deleted'   => $user->deleted,
        ];
    }
}
