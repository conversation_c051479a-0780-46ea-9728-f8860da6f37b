<?php /** @noinspection PhpUnused */

namespace App\Api\Users\Controllers;

use App\Api\Common\Controllers\Controller;
use App\Api\Users\Transformers\UserTransformer;
use App\Core\Api\Exceptions\NotFoundApiException;
use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Api\OpenApi\Others\Body;
use App\Core\Api\OpenApi\Others\Tag;
use App\Core\Api\OpenApi\Parameters\QueryParameter;
use App\Core\Api\OpenApi\Requests\Get;
use App\Core\Api\OpenApi\Requests\Post;
use App\Core\Api\OpenApi\Requests\Schemas\SchemaString;
use App\Core\Api\OpenApi\Responses\ErrorResponse;
use App\Core\Api\OpenApi\Responses\SuccessResponse;
use App\Core\Api\OpenApi\Schemas\Users\CreateUserScheme;
use App\Core\Api\OpenApi\Schemas\Users\UserScheme;
use App\Core\Api\Responses\Helpers\HttpStatusCode;
use App\Core\Users\Contracts\UserServiceContract;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

#[Tag(name: 'Users', description: 'Users API endpoints')]
class UserController extends Controller
{
    private UserServiceContract $userService;

    public function __construct(UserServiceContract $userService)
    {
        $this->userService = $userService;
    }

    #[Post(
        path: '/users/store.json',
        summary: 'Create user if not exists',
        body: new Body(required: true, content: CreateUserScheme::class),
        tags: ['Users'],
        responses: [
            new SuccessResponse(description: 'User object', data: UserScheme::class),
            new ErrorResponse(
                response: HttpStatusCode::UNPROCESSABLE_ENTITY,
                description: 'Create user request validation errors',
                errors: [
                    'Email is required',
                    'Email is invalid',
                    'Email is already taken. Try to find user on "/v1/users/get-by-email.json" endpoint.',
                    'First name is required',
                    'Last name is required',
                    'Password must be at least 12 characters',
                    'Password is required',
                    'Password must contain at least one letter',
                    'Password must contain at least one uppercase letter, one lowercase letter',
                    'Password must contain at least one number',
                    'Password must contain at least one symbol',
                ],
                codes: [11, 12, 13, 21, 31, 41, 42, 43, 44, 45, 46, 47],
                meta: [
                    'codes' => [
                        '11' => 'Email is required.',
                        '12' => 'Email is invalid.',
                        '13' => 'Email is already taken. Try to find user on "/v1/users/get-by-email.json" endpoint.',
                        '21' => 'First name is required.',
                        '31' => 'Last name is required.',
                        '41' => 'Password must be at least 12 characters.',
                        '42' => 'Password is required.',
                        '43' => 'Password must contain at least one letter.',
                        '44' => 'Password must contain at least one uppercase letter, one lowercase letter.',
                        '45' => 'Password must contain at least one letter.',
                        '46' => 'Password must contain at least one number.',
                        '47' => 'Password must contain at least one symbol.',
                    ]
                ],
            )
        ]
    )]
    public function store(Request $request): JsonResponse
    {
        $email = $request->post('email');

        $findRoute = route('api.v1.users.get-by-email', [], false) . '?email=' . $email;
        $codes = [
            'email.required'     => 11,
            'email.email'        => 12,
            'email.unique'       => 13,
            'firstName.required' => 21,
            'lastName.required'  => 31,
            'password.min'       => 41,
            'password.required'  => 42,
            'password.string'    => 43,
            'password.mixed'     => 44,
            'password.letters'   => 45,
            'password.numbers'   => 46,
            'password.symbols'   => 47,
        ];
        $messages = [
            11 => 'Email is required.',
            12 => 'Email is invalid.',
            13 => 'Email is already taken. Try to find user on "' . $findRoute . '" endpoint.',
            21 => 'First name is required.',
            31 => 'Last name is required.',
            41 => 'Password must be at least 12 characters.',
            42 => 'Password is required.',
            43 => 'Password must contain at least one letter.',
            44 => 'Password must contain at least one uppercase letter, one lowercase letter.',
            45 => 'Password must contain at least one letter.',
            46 => 'Password must contain at least one number.',
            47 => 'Password must contain at least one symbol.',
        ];
        $validator = Validator::make(
            $request->all(),
            [
                'email'     => 'required|unique:users,email|email:rfc,dns',
                'password'  => [
                    'required',
                    Password::min(12)
                        ->mixedCase()
                        ->letters()
                        ->numbers()
                        ->symbols()
                ],
                'firstName' => 'required',
                'lastName'  => 'required',
            ],
            $codes
        );

        if ($validator->fails()) {
            $codes = collect($validator->errors()->toArray())
                ->flatten()
                ->map(function (int $error) {
                    return $error;
                });

            $errors = $codes->map(function ($error) use ($messages) {
                return $messages[$error] ?? +'Unknown error';
            });

            /** @noinspection PhpUnhandledExceptionInspection */
            throw new ValidationApiException(
                errors: $errors->toArray(),
                statusCode: $codes->toArray(),
                meta: ['codes' => $messages],
            );
        }

        $user = $this->userService->createBasicUser(
            firstName: $request->post('firstName'),
            lastName: $request->post('lastName'),
            email: $request->post('email'),
            password: $request->post('password'),
        )->transformData(new UserTransformer());

        return $this->successResponse($user->toArray());
    }

    #[Get(
        path: '/users/get-by-email.json',
        summary: 'Find user by email',
        parameters: [
            new QueryParameter(
                name: 'email',
                required: true,
                schema: new SchemaString(text: '<EMAIL>'),
            ),
        ],
        tags: ['Users'],
        responses: [
            new SuccessResponse(description: 'User object', data: UserScheme::class),
            new ErrorResponse(
                response: HttpStatusCode::UNPROCESSABLE_ENTITY,
                description: 'Email is required validation error',
                errors: [
                    'Email is required.',
                    'Email is invalid.',
                ],
                codes: [11, 12],
                meta: [
                    'codes' => [
                        '11' => 'Email is required.',
                        '12' => 'Email is invalid.',
                    ]
                ],
            )
        ]
    )]
    /** @noinspection PhpUnused */
    public function findByEmail(Request $request): JsonResponse
    {
        $codes = [
            'email.required' => 11,
            'email.email'    => 12,
        ];
        $messages = [
            11 => 'Email is required.',
            12 => 'Email is invalid.',
        ];
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|email:rfc,dns',
            ],
            $codes
        );
        if ($validator->fails()) {
            $codes = collect($validator->errors()->toArray())
                ->flatten()
                ->map(function (int $error) {
                    return $error;
                });

            $errors = $codes->map(function ($error) use ($messages) {
                return $messages[$error] ?? +'Unknown error';
            });

            /** @noinspection PhpUnhandledExceptionInspection */
            throw new ValidationApiException(
                errors: $errors->toArray(),
                statusCode: $codes->toArray(),
                meta: ['codes' => $messages],
            );
        }

        $email = $request->get('email');
        $user = $this->userService
            ->getUserByEmail($email)
            ?->transformData(new UserTransformer());

        if (is_null($user)) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new NotFoundApiException(errors: 'User not found.', data: ['email' => $email]);
        }

        return $this->successResponse($user->toArray());
    }
}
