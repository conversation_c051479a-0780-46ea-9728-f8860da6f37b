<?php

namespace App\Api\ExchangeRate\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\Currency;

class CurrencyTransformer extends ApiTransformer
{
    public function transform(Currency $currency): array
    {
        return [
            'id'     => $currency->id,
            'name'   => $currency->name,
            'symbol' => $currency->symbol,
            'code'   => $currency->code
        ];
    }
}
