<?php

namespace App\Api\ExchangeRate\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\ExchangeRate;
use League\Fractal\Resource\Item;

class ExchangeRateTransformer extends ApiTransformer
{
    protected array $availableIncludes = [
        'currency',
    ];

    public function transform(ExchangeRate $rate): array
    {
        $data = [
            'id'         => $rate->id,
            'date'       => $rate->date,
            'currencyId' => $rate->currency_id,
            'rate'       => $rate->value,
        ];

        if ($this->hasOptionalInclude('currency')) {
            $data['currency'] = null;
        }

        return $data;
    }

    public function includeCurrency(ExchangeRate $rate): Item
    {
        return $this->item($rate->currency, new CurrencyTransformer());
    }
}
