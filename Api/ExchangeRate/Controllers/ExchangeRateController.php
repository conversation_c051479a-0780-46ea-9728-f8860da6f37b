<?php /** @noinspection PhpUnused */

namespace App\Api\ExchangeRate\Controllers;

use App\Api\Common\Controllers\Controller;
use App\Api\ExchangeRate\Transformers\ExchangeRateTransformer;
use App\Core\Api\Exceptions\BadRequestApiException;
use App\Core\Api\Exceptions\NotFoundApiException;
use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Api\OpenApi\Parameters\UrlParameter;
use App\Core\Api\OpenApi\Schemas\ExchangeRates\ExchangeRateScheme;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\ExchangeRatesRepositoryContract;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Throwable;
use App\Core\Api\OpenApi\Others\Tag;
use App\Core\Api\OpenApi\Parameters\QueryParameter;
use App\Core\Api\OpenApi\Requests\Get;
use App\Core\Api\OpenApi\Requests\Schemas\SchemaString;
use App\Core\Api\OpenApi\Responses\ErrorResponse;
use App\Core\Api\OpenApi\Responses\SuccessResponse;
use App\Core\Api\Responses\Helpers\HttpStatusCode;

#[Tag(name: 'Exchange rates', description: 'Exchange rates API endpoints')]
class ExchangeRateController extends Controller
{
    private ExchangeRatesRepositoryContract $exchangeRatesRepo;

    public function __construct(ExchangeRatesRepositoryContract $exchangeRatesRepo)
    {
        $this->exchangeRatesRepo = $exchangeRatesRepo;
    }

    #[Get(
        path: '/exchange-rate/date.json',
        summary: 'Find exchange rates on date',
        parameters: [
            new QueryParameter(
                name: 'date',
                required: true,
                schema: new SchemaString(text: '2024-04-16', format: 'date'),
            ),
            new QueryParameter(
                name: 'with',
                required: false,
                schema: new SchemaString(text: 'currency'),
            ),
        ],
        tags: ['Exchange rates'],
        responses: [
            new SuccessResponse(
                description: 'Array of currency exchange rates on given date',
                data: ExchangeRateScheme::class,
                meta: [
                    'endpointDescription' => 'Exchange rate for date (ratio to EUR)'
                ]
            ),
            new ErrorResponse(
                response: HttpStatusCode::UNPROCESSABLE_ENTITY,
                description: 'Exchange rates validation errors',
                errors: [
                    'Date is required.',
                    'Date is in invalid format. Must be in format: YYYY-MM-DD',
                ],
                codes: [11, 12],
                meta: [
                    'codes' => [
                        '11' => 'Date is required.',
                        '12' => 'Date is in invalid format. Must be in format: YYYY-MM-DD',
                    ]
                ],
            )
        ]
    )]
    public function date(Request $request): JsonResponse
    {
        $codes = [
            'date.required'    => 11,
            'date.date_format' => 12,
        ];
        $messages = [
            11 => 'Date is required.',
            12 => 'Date is in invalid format. Must be in format: YYYY-MM-DD',
        ];
        $validator = Validator::make(
            $request->all(),
            [
                'date' => 'required|date_format:Y-m-d',
            ],
            $codes
        );

        if ($validator->fails()) {
            $codes = collect($validator->errors()->toArray())
                ->flatten()
                ->map(function (int $error) {
                    return $error;
                });

            $errors = $codes->map(function ($error) use ($messages) {
                return $messages[$error] ?? +'Unknown error';
            });

            /** @noinspection PhpUnhandledExceptionInspection */
            throw new ValidationApiException(
                errors: $errors->toArray(),
                statusCode: $codes->toArray(),
                meta: ['codes' => $messages],
            );
        }

        $date = $request->get('date');
        try {
            $date = Carbon::parse($date);
        } catch (Throwable) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new BadRequestApiException('Invalid date format');
        }

        $rates = $this->exchangeRatesRepo
            ->getRatesForDates([$date])
            ->transformData(new ExchangeRateTransformer());

        return $this->successResponse($rates->toArray(), ['endpointDescription' => 'Exchange rate for date (ratio to EUR)']);
    }

    #[Get(
        path: '/exchange-rate/{currencyId}/currency.json',
        summary: 'Find exchange rates for currency in year',
        parameters: [
            new UrlParameter(name: 'currencyId'),
            new QueryParameter(
                name: 'year',
                description: 'Year to get exchange rates for. If not provided, current year is used',
                required: false,
                schema: new SchemaString(text: '2022'),
            ),
            new QueryParameter(
                name: 'with',
                required: false,
                schema: new SchemaString(text: 'currency'),
            ),
        ],
        tags: ['Exchange rates'],
        responses: [
            new SuccessResponse(
                description: 'Array of currency exchange rates on given date',
                data: ExchangeRateScheme::class,
                meta: [
                    'endpointDescription' => 'Exchange rate for currency in year(optional, default: current year) (ratio to EUR)'
                ]
            ),
            new ErrorResponse(
                response: HttpStatusCode::NOT_FOUND,
                description: 'Exchange rates validation errors',
                errors: [
                    'Not found',
                    'Currency not found',
                ],
            )
        ]
    )]
    public function currency(int $currencyId, Request $request, CurrencyRepositoryContract $currencyRepo): JsonResponse
    {
        $year = $request->get('year', Carbon::now()->year);
        $currency = $currencyRepo->getCurrencyById($currencyId);
        if (is_null($currency)) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new NotFoundApiException('Currency not found');
        }
        $rates = $this->exchangeRatesRepo
            ->getRatesForCurrency($currencyId, $year)
            ->transformData(new ExchangeRateTransformer());

        return $this->successResponse($rates->toArray(), ['endpointDescription' => 'Exchange rate for currency in year(optional, default: current year) (ratio to EUR)']);
    }
}
