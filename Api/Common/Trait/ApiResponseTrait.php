<?php

namespace App\Api\Common\Trait;

use App\Core\Api\Responses\Contracts\ApiResponse;
use App\Core\Api\Responses\Helpers\HttpStatusCode;
use Illuminate\Http\JsonResponse;

trait ApiResponseTrait
{
    public function successResponse(
        array $data = [],
        array $meta = [],
        HttpStatusCode $httpStatusCode = HttpStatusCode::OK,
        ?int $statusCode = null,
        bool $loggable = false
    ): JsonResponse {
        return (new ApiResponse(
            httpStatusCode: $httpStatusCode,
            statusCode: $statusCode,
            data: $data,
            meta: $meta,
            loggable: $loggable
        ))->response();
    }
}
