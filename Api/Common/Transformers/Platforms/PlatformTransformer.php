<?php

namespace App\Api\Common\Transformers\Platforms;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\Platform;
use League\Fractal\Resource\Collection;
use League\Fractal\Resource\Item;

class PlatformTransformer extends ApiTransformer
{
    protected array $availableIncludes = [
        'platformType',
        'platformRegions',
    ];

    public function transform(Platform $platform): array
    {
        return [
            'id'             => $platform->id,
            'name'           => _l($platform->name),
            'platformTypeId' => $platform->platform_type_id,
        ];
    }

    /** @noinspection PhpUnused */
    public function includePlatformType(Platform $platform): Item
    {
        return $this->item($platform->platformType, new PlatformTypeTransformer());
    }

    /** @noinspection PhpUnused */
    public function includePlatformRegions(Platform $platform): Collection
    {
        return $this->collection($platform->platformRegions, new PlatformRegionTransformer());
    }
}
