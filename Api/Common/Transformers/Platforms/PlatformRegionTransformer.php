<?php

namespace App\Api\Common\Transformers\Platforms;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\PlatformRegion;

class PlatformRegionTransformer extends ApiTransformer
{
    public function transform(PlatformRegion $platformRegion): array
    {
        return [
            'id'   => $platformRegion->id,
            'name' => \Illuminate\Support\Str::remove('platforms.region.description.', _l($platformRegion->description)),
            'code' => $platformRegion->key,
        ];
    }
}
