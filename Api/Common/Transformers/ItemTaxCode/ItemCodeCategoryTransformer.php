<?php

namespace App\Api\Common\Transformers\ItemTaxCode;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\ItemCodeCategory;
use League\Fractal\Resource\Collection;

class ItemCodeCategoryTransformer extends ApiTransformer
{
    protected array $availableIncludes = [
        'itemTaxCodes',
    ];

    public function transform(ItemCodeCategory $itemCodeCategory): array
    {
        return [
            'id'   => $itemCodeCategory->id,
            'name' => _l($itemCodeCategory->name),
        ];
    }

    /** @noinspection PhpUnused */
    public function includeItemTaxCodes(ItemCodeCategory $itemCodeCategory): Collection
    {
        return $this->collection($itemCodeCategory->itemTaxCodes, new ItemTaxCodeTransformer());
    }
}
