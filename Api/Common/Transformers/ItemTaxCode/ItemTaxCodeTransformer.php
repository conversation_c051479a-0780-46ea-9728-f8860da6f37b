<?php

namespace App\Api\Common\Transformers\ItemTaxCode;

use App\Api\Common\Transformers\ItemTypeTransformer;
use App\Api\Common\Transformers\TaxationMethodTransformer;
use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\ItemTaxCode;
use League\Fractal\Resource\Item;

class ItemTaxCodeTransformer extends ApiTransformer
{
    protected array $availableIncludes = [
        'itemType',
        'taxationMethod',
    ];

    public function transform(ItemTaxCode $itemTaxCode): array
    {
        $data = [
            'id'                 => $itemTaxCode->id,
            'code'               => $itemTaxCode->code,
            'description'        => $itemTaxCode->description,
            'itemTypeId'         => $itemTaxCode->item_type_id,
            'taxationMethodId'   => $itemTaxCode->taxation_method_id,
        ];

        if ($this->hasOptionalInclude('itemType')) {
            $data['itemType'] = null;
        }
        if ($this->hasOptionalInclude('taxationMethod')) {
            $data['taxationMethod'] = null;
        }

        return $data;
    }

    /** @noinspection PhpUnused */
    public function includeItemCodeCategory(ItemTaxCode $itemTaxCode): ?Item
    {
        $itemCodeCategory = $itemTaxCode->itemCodeCategory;
        if (is_null($itemCodeCategory)) {
            return null;
        }

        return $this->item($itemCodeCategory, new ItemCodeCategoryTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeItemType(ItemTaxCode $itemTaxCode): ?Item
    {
        $itemType = $itemTaxCode->itemType;
        if (is_null($itemType)) {
            return null;
        }

        return $this->item($itemType, new ItemTypeTransformer());
    }

    /** @noinspection PhpUnused */
    public function includeTaxationMethod(ItemTaxCode $itemTaxCode): ?Item
    {
        $taxationMethod = $itemTaxCode->taxationMethod;
        if (is_null($taxationMethod)) {
            return null;
        }

        return $this->item($taxationMethod, new TaxationMethodTransformer());
    }
}
