<?php

namespace App\Api\Common\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\TaxationMethod;

class TaxationMethodTransformer extends ApiTransformer
{
    public function transform(TaxationMethod $taxationMethod): array
    {
        return [
            'id'   => $taxationMethod->id,
            'name' => _l($taxationMethod->name),
        ];
    }
}
