<?php

use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'v1'], function () {
    Route::get('ip.json', 'CommonController@ip')->name('api.v1.common.ip');
    Route::get('countries.json', 'CommonController@countries')->name('api.v1.common.countries');
    Route::get('currencies.json', 'CommonController@currencies')->name('api.v1.common.currencies');
    Route::get('tax-code-categories.json', 'CommonController@taxCodeCategories')->name('api.v1.common.tax-code-categories');
    Route::get('item-types.json', 'CommonController@itemTypes')->name('api.v1.common.item-types');
    Route::get('taxation-methods.json', 'CommonController@taxationMethods')->name('api.v1.common.taxation-methods');
    Route::get('platforms.json', 'CommonController@platforms')->name('api.v1.common.platforms');
    Route::get('business-types.json', 'CommonController@businessTypes')->name('api.v1.common.business-types');
});

