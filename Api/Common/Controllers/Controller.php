<?php

namespace App\Api\Common\Controllers;

use App\Api\Common\Trait\ApiResponseTrait;
use App\Core\Api\Contracts\ApiServiceContract;
use App\Core\Common\Common\Transformers\Contracts\TransformedObjectContract;
use App\Core\Data\Models\ApiConsumer;

class Controller
{
    use ApiResponseTrait;

    /**
     * Transforms data
     *
     * @param mixed $data
     * @param mixed $transformer
     * @return TransformedObjectContract
     */
    public function transform(mixed $data, mixed $transformer): TransformedObjectContract
    {
        return transform_data($data, $transformer);
    }

    protected function getApiConsumer(): ApiConsumer
    {
        /** @var ApiServiceContract $apiService */
        $apiService = resolve(ApiServiceContract::class);

        return $apiService->getApplicationByKey(request()->header('Application-Key', ''));
    }
}
