<?php

namespace App\Api\Common\Controllers;

use App\Api\Common\Transformers\BusinessTypeTransformer;
use App\Api\Common\Transformers\CountryTransformer;
use App\Api\Common\Transformers\CurrencyTransformer;
use App\Api\Common\Transformers\ItemTaxCode\ItemCodeCategoryTransformer;
use App\Api\Common\Transformers\ItemTypeTransformer;
use App\Api\Common\Transformers\Platforms\PlatformTransformer;
use App\Api\Common\Transformers\TaxationMethodTransformer;
use App\Core\Api\OpenApi\Schemas\Common\BusinessTypesScheme;
use App\Core\Api\OpenApi\Schemas\Common\CountriesScheme;
use App\Core\Api\OpenApi\Schemas\Common\CurrenciesScheme;
use App\Core\Api\OpenApi\Schemas\Common\IpAddressScheme;
use App\Core\Api\OpenApi\Schemas\Common\ItemCodeCategoriesScheme;
use App\Core\Api\OpenApi\Schemas\Common\ItemTypesScheme;
use App\Core\Api\OpenApi\Schemas\Common\PlatformsScheme;
use App\Core\Api\OpenApi\Schemas\Common\TaxationMethodsScheme;
use App\Core\Data\Repositories\Contracts\BusinessTypeRepositoryContract;
use App\Core\Data\Repositories\Contracts\CountryRepositoryContract;
use App\Core\Data\Repositories\Contracts\CurrencyRepositoryContract;
use App\Core\Data\Repositories\Contracts\ItemRepositoryContract;
use App\Core\Data\Repositories\Contracts\PlatformRepositoryContract;
use App\Core\Data\Repositories\Contracts\TaxCodesRepositoryContract;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Core\Api\OpenApi\Others\Tag;
use App\Core\Api\OpenApi\Parameters\QueryParameter;
use App\Core\Api\OpenApi\Requests\Get;
use App\Core\Api\OpenApi\Requests\Schemas\SchemaString;
use App\Core\Api\OpenApi\Responses\SuccessResponse;

#[Tag(name: 'Common', description: 'Common data API endpoints')]
class CommonController extends Controller
{
    #[Get(
        path: '/ip.json',
        summary: 'IP address check',
        tags: ['Common'],
        responses: [
            new SuccessResponse(
                description: 'IP address object',
                data: IpAddressScheme::class,
                meta: ['endpointDescription' => 'Yours IP address as seen by our server']
            ),
        ]
    )]
    public function ip(Request $request): JsonResponse
    {
        $data['ips'] = $request->ips();
        $meta['endpointDescription'] = 'Yours IP address as seen by our server';

        return $this->successResponse($data, $meta);
    }

    #[Get(
        path: '/countries.json',
        summary: 'Countries data',
        parameters: [
            new QueryParameter(
                name: 'with',
                description: 'Include additional relationships. Available relations: `currency`.',
                schema: new SchemaString()
            ),
        ],
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Countries data - array of objects', data: CountriesScheme::class),
        ]
    )]
    public function countries(CountryRepositoryContract $countryRepo): JsonResponse
    {
        $countries = $countryRepo->getAllCountries()
            ->load('currency')
            ->transformData(new CountryTransformer());

        return $this->successResponse($countries->toArray());
    }

    #[Get(
        path: '/currencies.json',
        summary: 'Currencies data',
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Currencies data - array of objects', data: CurrenciesScheme::class),
        ]
    )]
    public function currencies(CurrencyRepositoryContract $countryRepo): JsonResponse
    {
        $currencies = $countryRepo->getAllCurrencies()->transformData(new CurrencyTransformer());

        return $this->successResponse($currencies->toArray());
    }

    /** @noinspection PhpUnused */
    #[Get(
        path: '/tax-code-categories.json',
        summary: 'Tax code categories data',
        parameters: [
            new QueryParameter(
                name: 'with',
                description: 'Include additional relationships. Available relations: `itemTaxCodes`, `itemTaxCodes.itemType`, `itemTaxCodes.taxationMethod`.',
                schema: new SchemaString()
            ),
        ],
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Tax code categories data - array of objects', data: ItemCodeCategoriesScheme::class),
        ]
    )]
    public function taxCodeCategories(ItemRepositoryContract $itemRepo): JsonResponse
    {
        $itemCodeCategories = $itemRepo->getAllItemCodeCategories()
            ->load([
                'itemTaxCodes.itemType',
                'itemTaxCodes.taxationMethod',
            ])
            ->transformData(new ItemCodeCategoryTransformer());

        return $this->successResponse($itemCodeCategories->toArray());
    }

    #[Get(
        path: '/item-types.json',
        summary: 'Item types data',
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Item types data - array of objects', data: ItemTypesScheme::class),
        ]
    )]
    public function itemTypes(ItemRepositoryContract $itemRepo): JsonResponse
    {
        $itemTypes = $itemRepo->getAllItemTypes()->transformData(new ItemTypeTransformer());

        return $this->successResponse($itemTypes->toArray());
    }

    /** @noinspection PhpUnused */
    #[Get(
        path: '/taxation-methods.json',
        summary: 'Taxation methods data',
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Taxation methods data - array of objects', data: TaxationMethodsScheme::class),
        ]
    )]
    public function taxationMethods(TaxCodesRepositoryContract $taxCodesRepo): JsonResponse
    {
        $taxationMethods = $taxCodesRepo->getAllTaxationMethods()
            ->transformData(new TaxationMethodTransformer());

        return $this->successResponse($taxationMethods->toArray());
    }

    #[Get(
        path: '/platforms.json',
        summary: 'Platforms data',
        parameters: [
            new QueryParameter(
                name: 'with',
                description: 'Include additional relationships. Available relations: `platformType`.',
                schema: new SchemaString()
            ),
        ],
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Platforms data - array of objects', data: PlatformsScheme::class),
        ]
    )]
    public function platforms(PlatformRepositoryContract $platformRepo): JsonResponse
    {
        $platforms = $platformRepo
            ->getAllPlatforms()
            ->load(
                'platformType',
                'platformRegions',
            )
            ->transformData(new PlatformTransformer());

        return $this->successResponse($platforms->toArray());
    }

    /** @noinspection PhpUnused */
    #[Get(
        path: '/business-types.json',
        summary: 'Business types data',
        tags: ['Common'],
        responses: [
            new SuccessResponse(description: 'Business types data - array of objects', data: BusinessTypesScheme::class),
        ]
    )]
    public function businessTypes(BusinessTypeRepositoryContract $businessTypeRepo): JsonResponse
    {
        $businessTypes = $businessTypeRepo->getAllBusinessTypes()
            ->transformData(new BusinessTypeTransformer());

        return $this->successResponse($businessTypes->toArray());
    }
}
