<?php /** @noinspection PhpUnused */

namespace App\Api\Validation\Controllers;

use App\Api\Common\Controllers\Controller;
use App\Api\Validation\Transformers\VatNumberTransformer;
use App\Core\Api\Exceptions\UnprocessableEntityApiException;
use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Api\OpenApi\Others\Tag;
use App\Core\Api\OpenApi\Parameters\QueryParameter;
use App\Core\Api\OpenApi\Requests\Get;
use App\Core\Api\OpenApi\Requests\Schemas\SchemaString;
use App\Core\Api\OpenApi\Responses\ErrorResponse;
use App\Core\Api\OpenApi\Responses\SuccessResponse;
use App\Core\Api\OpenApi\Schemas\Validation\ValidateVatNumber;
use App\Core\Api\Responses\Helpers\HttpStatusCode;
use App\Core\VatNumbers\Api\Exceptions\VatNumberApiException;
use App\Core\VatNumbers\Api\VatNumberApi;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

#[Tag(name: 'Validation', description: 'VAT number API endpoints')]
class VatNumberController extends Controller
{
    #[Get(
        path: '/validate/vat-number.json',
        summary: 'Validate VAT number',
        parameters: [
            new QueryParameter(
                name: 'vatNumber',
                required: true,
                schema: new SchemaString(text: 'NL12345678901'),
            ),
            new QueryParameter(
                name: 'with',
                description: 'Include additional relationships. Available relations: `country`.',
                schema: new SchemaString()
            ),
        ],
        tags: ['Validation'],
        responses: [
            new SuccessResponse(description: 'VAT number validation object', data: ValidateVatNumber::class),
            new ErrorResponse(
                response: HttpStatusCode::UNPROCESSABLE_ENTITY,
                description: 'VAT number required validation error',
                errors: [
                    'VAT number is required.',
                ],
                codes: [11],
                meta: [
                    'codes' => [
                        '11' => 'VAT number is required.']
                ],
            ),
        ]
    )]
    public function validate(Request $request): JsonResponse
    {
        $vatNumber = $request->get('vatNumber', '');
        $codes = [
            'vatNumber.required' => 11,
        ];
        $messages = [
            11 => 'VAT number is required.',
        ];
        $validator = Validator::make(
            $request->all(),
            [
                'vatNumber' => 'required',
            ],
            $codes
        );

        if ($validator->fails()) {
            $codes = collect($validator->errors()->toArray())
                ->flatten()
                ->map(function (int $error) {
                    return $error;
                });

            $errors = $codes->map(function ($error) use ($messages) {
                return $messages[$error] ?? 'Unknown error';
            });

            /** @noinspection PhpUnhandledExceptionInspection */
            throw new ValidationApiException(
                errors: $errors->toArray(),
                statusCode: $codes->toArray(),
                meta: ['codes' => $messages],
            );
        }

        try {
            $validation = VatNumberApi::validateVatNumber($vatNumber);
        } catch (VatNumberApiException $e) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new UnprocessableEntityApiException(errors: $e->getMessage(), statusCode: $e->getCode());
        }

        $vatNumber = $this->transform($validation, new VatNumberTransformer());

        return $this->successResponse($vatNumber->toArray());
    }
}
