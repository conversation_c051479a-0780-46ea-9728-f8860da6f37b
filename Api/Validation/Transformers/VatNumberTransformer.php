<?php

namespace App\Api\Validation\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Common\Common\DataTransfer\DataTransferObject;
use League\Fractal\Resource\Item;

class VatNumberTransformer extends APITransformer
{
    protected array $availableIncludes = [
        'country'
    ];

    public function transform(DataTransferObject $vatNumber): array
    {
        return [
            'id'               => $vatNumber->id,
            'vatNumber'        => $vatNumber->vatNumber,
            'number'           => $vatNumber->number,
            'valid'            => $vatNumber->valid,
            'status'           => $vatNumber->status,
            'formatValid'      => $vatNumber->formatValid,
            'validated'        => $vatNumber->validated,
            'registerDate'     => $vatNumber->registerDate,
            'validationDate'   => $vatNumber->validationDate,
            'endDate'          => $vatNumber->endDate,
        ];
    }

    public function includeCountry(DataTransferObject $vatNumber): Item
    {
        return $this->item($vatNumber->country, new CountryTransformer());
    }
}
