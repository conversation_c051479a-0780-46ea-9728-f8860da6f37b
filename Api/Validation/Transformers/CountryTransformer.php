<?php

namespace App\Api\Validation\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Common\Common\DataTransfer\DataTransferObject;

class CountryTransformer extends APITransformer
{
    public function transform(DataTransferObject $country): array
    {
        return [
            'id'      => $country->id,
            'name'    => $country->name,
            'code'    => $country->code,
            'vatCode' => $country->vatCode,
        ];
    }
}
