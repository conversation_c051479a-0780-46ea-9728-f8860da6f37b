<?php

namespace App\Api\Companies\Controllers;

use App\Api\Common\Controllers\Controller;
use App\Api\Companies\Transformers\CreateCompanyTransformer;
use App\Api\Companies\Transformers\SalesChannelTransformer;
use App\Core\Api\Exceptions\ValidationApiException;
use App\Core\Api\OpenApi\Others\Body;
use App\Core\Api\OpenApi\Others\Tag;
use App\Core\Api\OpenApi\Requests\Post;
use App\Core\Api\OpenApi\Responses\ErrorResponse;
use App\Core\Api\OpenApi\Responses\SuccessResponse;
use App\Core\Api\OpenApi\Schemas\Companies\CompanyScheme;
use App\Core\Api\OpenApi\Schemas\Companies\FindOrCreateCompanyScheme;
use App\Core\Api\OpenApi\Schemas\Companies\SalesChannels\FindOrCreateSalesChannelScheme;
use App\Core\Api\OpenApi\Schemas\Companies\SalesChannels\SalesChannelScheme;
use App\Core\Api\Responses\Helpers\HttpStatusCode;
use App\Core\Companies\Contracts\CompanyServiceContract;
use App\Core\Companies\DataTransfer\CreateCompanyApiRequest;
use App\Core\Data\Models\Platform;
use App\Core\SalesChannels\Contracts\SalesChannelServiceContract;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

#[Tag(name: 'Companies', description: 'Companies API endpoints')]
class CompanyController extends Controller
{
    private CompanyServiceContract $companyService;
    private SalesChannelServiceContract $salesChannelService;

    public function __construct(
        CompanyServiceContract $companyService,
        SalesChannelServiceContract $salesChannelService
    ) {
        $this->companyService = $companyService;
        $this->salesChannelService = $salesChannelService;
    }

    #[Post(
        path: '/companies/find-or-store.json',
        summary: 'Find company or create new if not exists',
        body: new Body(required: true, content: FindOrCreateCompanyScheme::class),
        tags: ['Companies'],
        responses: [
            new SuccessResponse(description: 'Company object', data: CompanyScheme::class),
            new ErrorResponse(
                response: HttpStatusCode::UNPROCESSABLE_ENTITY,
                description: 'Create company request validation errors',
                errors: [
                    'Company identification numbers error.',
                    'Name is required.',
                    'Name must be a string.',
                    'Business type is required.',
                    'Business type does not exist.',
                    'Trade name must be a string.',
                    'Legal entity type is required.',
                    'Legal entity type must be a string.',
                    'Business activity is required.',
                    'Business activity must be a string.',
                    'Registration number is required.',
                    'Registration number must be a string.',
                    'Incorporation date is required.',
                    'Incorporation date must be a date.',
                    'Share capital value is required.',
                    'Share capital value must be a numeric.',
                    'Share capital currency is required.',
                    'Share capital currency does not exist.',

                    'E-mail is required.',
                    'E-mail is not a valid e-mail address.',
                    'Phone number is required.',
                    'Phone number must be a string.',

                    'Establishment address street is required.',
                    'Establishment address street must be a string.',
                    'Establishment address house number is required.',
                    'Establishment address addition must be a string.',
                    'Establishment address city is required.',
                    'Establishment address city must be a string.',
                    'Establishment address postal code is required.',
                    'Establishment address postal code must be a string.',
                    'Establishment address state must be a string.',
                    'Establishment address country is required.',
                    'Establishment address country does not exist.',

                    'VAT number country is required.',
                    'VAT number country does not exist.',
                    'VAT number is required.',
                    'VAT number must be a string.',
                    'VAT number register date is required.',
                    'VAT number register date must be a date.',
                    'VAT number establishment status is required.',
                    'VAT number establishment status does not exist.',

                    'Tax number country is required.',
                    'Tax number country does not exist.',
                    'Tax number is required.',
                    'Tax number must be a string.',
                    'Tax number register date is required.',
                    'Tax number register date must be a date.',

                    'OSS number country is required.',
                    'OSS number country does not exist.',
                    'OSS number is required.',
                    'OSS number must be a string.',
                    'OSS number register date is required.',
                    'OSS number register date must be a date.',
                    'OSS number type is required.',
                    'OSS number type does not exist.',

                    'IOSS number country is required.',
                    'IOSS number country does not exist.',
                    'IOSS number is required.',
                    'IOSS number must be a string.',
                    'IOSS number register date is required.',
                    'IOSS number register date must be a date.',
                    'IOSS number type is required.',
                    'IOSS number type does not exist.',
                ],
                codes: [
                    101,
                    1011,
                    1012,
                    1031,
                    1032,
                    1041,
                    1051,
                    1052,
                    1061,
                    1062,
                    1071,
                    1072,
                    1081,
                    1082,
                    1091,
                    1092,
                    1101,
                    1102,

                    2001,
                    2002,
                    2011,
                    2012,

                    3001,
                    3002,
                    3011,
                    3021,
                    3031,
                    3032,
                    3041,
                    3042,
                    3043,
                    3051,
                    3052,

                    4001,
                    4002,
                    4011,
                    4012,
                    4021,
                    4022,
                    4031,
                    4032,

                    5001,
                    5002,
                    5011,
                    5012,
                    5021,
                    5022,

                    6001,
                    6002,
                    6011,
                    6012,
                    6021,
                    6022,
                    6031,
                    6032,

                    7001,
                    7002,
                    7011,
                    7012,
                    7021,
                    7022,
                    7031,
                    7032,
                ],
                meta: [
                    'codes' => [
                        '101'  => 'Company identification numbers error.',
                        '1011' => 'Name is required.',
                        '1012' => 'Name must be a string.',
                        '1031' => 'Business type is required.',
                        '1032' => 'Business type does not exist.',
                        '1041' => 'Trade name must be a string.',
                        '1051' => 'Legal entity type is required.',
                        '1052' => 'Legal entity type must be a string.',
                        '1061' => 'Business activity is required.',
                        '1062' => 'Business activity must be a string.',
                        '1071' => 'Registration number is required.',
                        '1072' => 'Registration number must be a string.',
                        '1081' => 'Incorporation date is required.',
                        '1082' => 'Incorporation date must be a date.',
                        '1091' => 'Share capital value is required.',
                        '1092' => 'Share capital value must be a numeric.',
                        '1101' => 'Share capital currency is required.',
                        '1102' => 'Share capital currency does not exist.',

                        '2001' => 'E-mail is required.',
                        '2002' => 'E-mail is not a valid e-mail address.',
                        '2011' => 'Phone number is required.',
                        '2012' => 'Phone number must be a string.',

                        '3001' => 'Establishment address street is required.',
                        '3002' => 'Establishment address street must be a string.',
                        '3011' => 'Establishment address house number is required.',
                        '3021' => 'Establishment address addition must be a string.',
                        '3031' => 'Establishment address city is required.',
                        '3032' => 'Establishment address city must be a string.',
                        '3041' => 'Establishment address postal code is required.',
                        '3042' => 'Establishment address postal code must be a string.',
                        '3043' => 'Establishment address state must be a string.',
                        '3051' => 'Establishment address country is required.',
                        '3052' => 'Establishment address country does not exist.',

                        '4001' => 'VAT number country is required.',
                        '4002' => 'VAT number country does not exist.',
                        '4011' => 'VAT number is required.',
                        '4012' => 'VAT number must be a string.',
                        '4021' => 'VAT number register date is required.',
                        '4022' => 'VAT number register date must be a date.',
                        '4031' => 'VAT number establishment status is required.',
                        '4032' => 'VAT number establishment status does not exist.',

                        '5001' => 'Tax number country is required.',
                        '5002' => 'Tax number country does not exist.',
                        '5011' => 'Tax number is required.',
                        '5012' => 'Tax number must be a string.',
                        '5021' => 'Tax number register date is required.',
                        '5022' => 'Tax number register date must be a date.',

                        '6001' => 'OSS number country is required.',
                        '6002' => 'OSS number country does not exist.',
                        '6011' => 'OSS number is required.',
                        '6012' => 'OSS number must be a string.',
                        '6021' => 'OSS number register date is required.',
                        '6022' => 'OSS number register date must be a date.',
                        '6031' => 'OSS number type is required.',
                        '6032' => 'OSS number type does not exist.',

                        '7001' => 'IOSS number country is required.',
                        '7002' => 'IOSS number country does not exist.',
                        '7011' => 'IOSS number is required.',
                        '7012' => 'IOSS number must be a string.',
                        '7021' => 'IOSS number register date is required.',
                        '7022' => 'IOSS number register date must be a date.',
                        '7031' => 'IOSS number type is required.',
                        '7032' => 'IOSS number type does not exist.',
                    ],
                ],
            ),
        ]
    )]
    public function store(Request $request): JsonResponse
    {
        $request = new CreateCompanyApiRequest(
            $request->all(),
            $this->getApiConsumer()
        );

        $company = $this->companyService
            ->apiCompanyFactory($request)
            ->transformData(new CreateCompanyTransformer());

        return $this->successResponse($company->toArray());
    }

    /** @noinspection PhpUnused */
    #[Post(
        path: '/companies/sales-channels/find-or-store.json',
        summary: 'Find sales channel or create new if not exists',
        body: new Body(required: true, content: FindOrCreateSalesChannelScheme::class),
        tags: ['Companies', 'Sales channels'],
        responses: [
            new SuccessResponse(description: 'Sales channel object', data: SalesChannelScheme::class),
            new ErrorResponse(
                response: HttpStatusCode::UNPROCESSABLE_ENTITY,
                description: 'Create sales channel request validation errors',
                errors: [
                    'Company ID does not exist.',
                    'Company ID must be an integer.',
                    'Company does not exist.',
                    'Platform ID does not exist.',
                    'Platform ID must be an integer.',
                    'Platform does not exist.',
                    'Platform region ID is required if platform is Amazon (id: ' . Platform::PLATFORM_AMAZON . ').',
                    'Platform region ID must be an integer.',
                    'Platform region does not exist.',
                    'Name is required.',
                    'Name must be a string.',
                    'UAID is required.',
                    'UAID must be a string.',
                ],
                codes: [
                    11,
                    12,
                    13,
                    21,
                    22,
                    23,
                    31,
                    32,
                    33,
                    41,
                    42,
                    51,
                    52
                ],
                meta: [
                    'codes' => [
                        '11' => 'Company ID does not exist.',
                        '12' => 'Company ID must be an integer.',
                        '13' => 'Company does not exist.',
                        '21' => 'Platform ID does not exist.',
                        '22' => 'Platform ID must be an integer.',
                        '23' => 'Platform does not exist.',
                        '31' => 'Platform region ID is required if platform is Amazon (id: ' . Platform::PLATFORM_AMAZON . ').',
                        '32' => 'Platform region ID must be an integer.',
                        '33' => 'Platform region does not exist.',
                        '41' => 'Name is required.',
                        '42' => 'Name must be a string.',
                        '51' => 'UAID is required.',
                        '52' => 'UAID must be a string.',
                    ],
                ],
            ),
        ]
    )]
    public function storeSalesChannel(Request $request): JsonResponse
    {
        $apiConsumer = $this->getApiConsumer();
        $companyId = $request->post('companyId');
        $platformId = $request->post('platformId');
        $platformRegionId = $request->post('platformRegionId');
        $name = $request->post('name');
        $uaid = $request->post('uaid');

        $rules = [
            'companyId'        => 'required|integer|exists:companies,id',
            'platformId'       => 'required|integer|exists:platforms,id',
            'platformRegionId' => [
                Rule::requiredIf(function () use ($platformId) {
                    return $platformId === Platform::PLATFORM_AMAZON;
                }),
                'integer',
                'nullable',
                'exists:platform_regions,id',
            ],
            'name'             => 'required|string',
            'uaid'             => 'required|string',
            'marketplaceId'    => 'integer|exists:marketplaces,id',
        ];

        $codes = [
            'companyId.required'        => 11,
            'companyId.integer'         => 12,
            'companyId.exists'          => 13,
            'platformId.required'       => 21,
            'platformId.integer'        => 22,
            'platformId.exists'         => 23,
            'platformRegionId.required' => 31,
            'platformRegionId.integer'  => 32,
            'platformRegionId.exists'   => 33,
            'name.required'             => 41,
            'name.string'               => 42,
            'uaid.required'             => 51,
            'uaid.string'               => 52,
        ];

        $messages = [
            11 => 'Company ID does not exist.',
            12 => 'Company ID must be an integer.',
            13 => 'Company does not exist.',
            21 => 'Platform ID does not exist.',
            22 => 'Platform ID must be an integer.',
            23 => 'Platform does not exist.',
            31 => 'Platform region ID is required if platform is Amazon (id: ' . Platform::PLATFORM_AMAZON . ').',
            32 => 'Platform region ID must be an integer.',
            33 => 'Platform region does not exist.',
            41 => 'Name is required.',
            42 => 'Name must be a string.',
            51 => 'UAID is required.',
            52 => 'UAID must be a string.',
        ];

        $validator = Validator::make(
            $request->all(),
            $rules,
            $codes
        );

        if ($validator->fails()) {
            $codes = collect($validator->errors()->toArray())
                ->flatten()
                ->map(function (int $error) {
                    return $error;
                });

            $errors = $codes->map(function ($error) use ($messages) {
                return $messages[$error] ?? '+Unknown error';
            });

            /** @noinspection PhpUnhandledExceptionInspection */
            throw new ValidationApiException(
                errors: $errors->toArray(),
                statusCode: $codes->toArray(),
                meta: ['codes' => $messages],
            );
        }

        $salesChannel = $this->salesChannelService->getSalesChannelByUAID($uaid);
        if (is_null($salesChannel)) {
            $salesChannel = $this->salesChannelService
                ->storeUpdateSalesChannel(
                    companyId: $companyId,
                    name: $name,
                    platformId: $platformId,
                    uaid: $uaid,
                    platformRegionId: $platformRegionId,
                    marketplaceId: $apiConsumer->marketplace_id,
                );
        }

        $salesChannel = $salesChannel->transformData(new SalesChannelTransformer());

        return $this->successResponse($salesChannel->toArray());
    }
}
