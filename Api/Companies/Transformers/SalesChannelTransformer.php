<?php

namespace App\Api\Companies\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\SalesChannel;

class SalesChannelTransformer extends ApiTransformer
{
    public function transform(SalesChannel $salesChannel): array
    {
        return [
            'id'               => $salesChannel->id,
            'companyId'        => $salesChannel->company_id,
            'uaid'             => $salesChannel->uaid,
            'platformId'       => $salesChannel->platform_id,
            'name'             => $salesChannel->account_name,
            'platformRegionId' => $salesChannel->platform_region_id,
            'marketplaceId'    => $salesChannel->marketplace_id,
        ];
    }
}
