<?php

namespace App\Api\Companies\Transformers;

use App\Core\Api\Transformers\Contracts\ApiTransformer;
use App\Core\Data\Models\Company;

class CreateCompanyTransformer extends ApiTransformer
{
    public function transform(Company $company): array
    {
        return [
            'id'                 => $company->id,
            'name'               => $company->full_legal_name,
            'shortName'          => $company->short_name,
            'tradeName'          => $company->trade_name,
            'legalEntityType'    => $company->legal_type,
            'businessActivity'   => $company->business_activity,
            'registrationNumber' => $company->registration_number,
            'businessTypeId'     => $company->business_type_id,
            'incorporationDate'  => $company->incorporation_date,
        ];
    }
}
