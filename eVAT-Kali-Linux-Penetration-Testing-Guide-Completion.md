## 7.1 VM.2 Network Penetration Testing (continued)

```bash
    ssh)
      sshpass -p "$password" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 $username@$ip echo "Login successful" && echo "[!] Default SSH credentials work for $ip"
      ;;
    http|https)
      response=$(curl -s -o /dev/null -w "%{http_code}" -X POST -d "username=$username&password=$password" http://$ip:$port/login)
      if [ "$response" = "200" ] || [ "$response" = "302" ]; then
        echo "[!] Default HTTP credentials work for $ip:$port"
      fi
      ;;
    rdp)
      xfreerdp /u:$username /p:$password /v:$ip:$port +auth-only 2>/dev/null
      if [ $? -eq 0 ]; then
        echo "[!] Default RDP credentials work for $ip:$port"
      fi
      ;;
  esac
done < $SERVICE_LIST

# Test for local privilege escalation vectors
if [ -n "$(which sudo)" ]; then
  echo "[+] Testing for sudo misconfigurations"
  sudo -l
fi

# Test for locally accessible sensitive information
echo "[+] Searching for sensitive files"
find / -type f -name "*.conf" -o -name "*.config" -o -name "*.ini" -perm -o+r 2>/dev/null | grep -v '/proc/' > ~/evat-pentest/network/sensitive-files.txt

# Check for vulnerable software versions
echo "[+] Checking for vulnerable software versions"
dpkg -l | grep -E 'apache|nginx|mysql|postgres|ssh|openssh|php|python|java|tomcat|jboss|jenkins|docker' > ~/evat-pentest/network/installed-software.txt

# Report generation for VM.2 compliance
echo "[+] Generating VM.2 compliance report"
echo "VM.2 Penetration Testing Compliance Report" > ~/evat-pentest/reports/vm2-compliance-report.txt
echo "=======================================" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "Date: $(date)" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "Tester: $(whoami)@$(hostname)" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "1. Network Segmentation Findings" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "-----------------------------" >> ~/evat-pentest/reports/vm2-compliance-report.txt
cat ~/evat-pentest/network/open-ports-*.txt | sort | uniq >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "2. Default Credential Findings" >> ~/evat-pentest/reports/vm2-compliance-report.txt
echo "-----------------------------" >> ~/evat-pentest/reports/vm2-compliance-report.txt
grep "\[!\]" ~/evat-pentest/network/*.txt | sort | uniq >> ~/evat-pentest/reports/vm2-compliance-report.txt
EOL

chmod +x ~/evat-pentest/vm2-compliance-test.sh
```

### 7.2 CS.4 Cloud Services Penetration Testing

These tests specifically address the CS.4 requirement for penetration testing on cloud services:

```bash
# Create unified script for CS.4 testing
cat > ~/evat-pentest/cs4-compliance-test.sh << 'EOL'
#!/bin/bash

echo "Starting CS.4 Compliance Testing"
echo "================================"

# AWS tests for CS.4 compliance
if command -v aws &> /dev/null; then
  echo "[+] Running AWS compliance tests"
  
  # Create output directory
  mkdir -p ~/evat-pentest/cloud/aws
  
  # Test for public S3 buckets
  echo "[+] Testing S3 buckets for public access"
  aws s3api list-buckets --query "Buckets[].Name" --output text | tr '\t' '\n' > ~/evat-pentest/cloud/aws/all-buckets.txt
  
  for bucket in $(cat ~/evat-pentest/cloud/aws/all-buckets.txt); do
    # Check if bucket allows public access
    policy=$(aws s3api get-bucket-policy --bucket $bucket 2>/dev/null)
    acl=$(aws s3api get-bucket-acl --bucket $bucket 2>/dev/null)
    
    if echo "$policy" | grep -q "Principal\": \"\*\""; then
      echo "[!] Bucket $bucket has a policy with public access" | tee -a ~/evat-pentest/cloud/aws/public-buckets.txt
    fi
    
    if echo "$acl" | grep -q "AllUsers"; then
      echo "[!] Bucket $bucket has an ACL with public access" | tee -a ~/evat-pentest/cloud/aws/public-buckets.txt
    fi
    
    # Test for encryption
    encryption=$(aws s3api get-bucket-encryption --bucket $bucket 2>/dev/null)
    if [ $? -ne 0 ]; then
      echo "[!] Bucket $bucket does not have default encryption" | tee -a ~/evat-pentest/cloud/aws/unencrypted-buckets.txt
    fi
  done
  
  # Test for overly permissive security groups
  echo "[+] Testing EC2 security groups"
  aws ec2 describe-security-groups --output json > ~/evat-pentest/cloud/aws/security-groups.json
  
  # Find security groups that allow unrestricted access
  jq -r '.SecurityGroups[] | select(.IpPermissions[].IpRanges[].CidrIp == "0.0.0.0/0") | .GroupId + ": " + .GroupName' ~/evat-pentest/cloud/aws/security-groups.json > ~/evat-pentest/cloud/aws/open-security-groups.txt
  
  # Test for IAM users with passwords that have not been rotated
  echo "[+] Testing IAM password policies"
  aws iam get-account-password-policy --output json 2>/dev/null > ~/evat-pentest/cloud/aws/password-policy.json || echo "[!] No password policy set" | tee -a ~/evat-pentest/cloud/aws/iam-issues.txt
  
  # Get users with access keys
  aws iam list-users --output json > ~/evat-pentest/cloud/aws/iam-users.json
  
  for user in $(jq -r '.Users[].UserName' ~/evat-pentest/cloud/aws/iam-users.json); do
    # Get access keys for user
    aws iam list-access-keys --user-name $user --output json > ~/evat-pentest/cloud/aws/access-keys-$user.json
    
    # Check for active access keys older than 90 days
    jq -r '.AccessKeyMetadata[] | select(.Status == "Active") | select((.CreateDate | fromdate) < (now - 7776000)) | .AccessKeyId' ~/evat-pentest/cloud/aws/access-keys-$user.json | while read key; do
      echo "[!] User $user has active access key $key older than 90 days" | tee -a ~/evat-pentest/cloud/aws/old-access-keys.txt
    done
  done
  
  # Run ScoutSuite for comprehensive AWS assessment
  if command -v scout &> /dev/null; then
    echo "[+] Running ScoutSuite assessment"
    scout aws --report-dir ~/evat-pentest/cloud/aws/scoutsuite-report
  else
    echo "[!] ScoutSuite not installed, install with: pip install scoutsuite"
  fi
fi

# Azure tests for CS.4 compliance
if command -v az &> /dev/null; then
  echo "[+] Running Azure compliance tests"
  
  # Create output directory
  mkdir -p ~/evat-pentest/cloud/azure
  
  # Check if logged in
  az account show &>/dev/null
  if [ $? -ne 0 ]; then
    echo "[!] Not logged in to Azure. Please login first with 'az login'"
    exit 1
  fi
  
  # Get all resource groups
  az group list --output json > ~/evat-pentest/cloud/azure/resource-groups.json
  
  # For each resource group, check for:
  # 1. Storage accounts with public access
  # 2. VMs with public IPs
  # 3. Key vaults with unrestricted network access
  # 4. App services with FTP enabled
  
  for rg in $(jq -r '.[].name' ~/evat-pentest/cloud/azure/resource-groups.json); do
    echo "[+] Scanning resource group: $rg"
    
    # Check storage accounts
    az storage account list --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$rg-storage-accounts.json
    
    for sa in $(jq -r '.[].name' ~/evat-pentest/cloud/azure/$rg-storage-accounts.json); do
      # Check for public network access
      az storage account show --name $sa --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$sa-details.json
      
      network_rule=$(jq -r '.networkRuleSet.defaultAction' ~/evat-pentest/cloud/azure/$sa-details.json)
      if [ "$network_rule" == "Allow" ]; then
        echo "[!] Storage account $sa allows public network access" | tee -a ~/evat-pentest/cloud/azure/public-storage-accounts.txt
      fi
      
      # Check blob containers for public access
      key=$(az storage account keys list --account-name $sa --resource-group $rg --query '[0].value' -o tsv)
      az storage container list --account-name $sa --account-key $key --output json > ~/evat-pentest/cloud/azure/$sa-containers.json
      
      jq -r '.[] | select(.properties.publicAccess != null) | .name' ~/evat-pentest/cloud/azure/$sa-containers.json | while read container; do
        echo "[!] Container $container in storage account $sa has public access enabled" | tee -a ~/evat-pentest/cloud/azure/public-containers.txt
      done
    done
    
    # Check VMs for public IPs
    az vm list --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$rg-vms.json
    
    for vm in $(jq -r '.[].name' ~/evat-pentest/cloud/azure/$rg-vms.json); do
      az vm show -g $rg -n $vm --show-details --output json > ~/evat-pentest/cloud/azure/$vm-details.json
      
      public_ip=$(jq -r '.publicIps' ~/evat-pentest/cloud/azure/$vm-details.json)
      if [ "$public_ip" != "null" ] && [ "$public_ip" != "" ]; then
        echo "[!] VM $vm has public IP: $public_ip" | tee -a ~/evat-pentest/cloud/azure/public-vms.txt
      fi
    done
    
    # Check key vaults
    az keyvault list --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$rg-keyvaults.json
    
    for kv in $(jq -r '.[].name' ~/evat-pentest/cloud/azure/$rg-keyvaults.json); do
      az keyvault show --name $kv --output json > ~/evat-pentest/cloud/azure/$kv-details.json
      
      network_acls=$(jq -r '.properties.networkAcls.defaultAction' ~/evat-pentest/cloud/azure/$kv-details.json)
      if [ "$network_acls" == "Allow" ]; then
        echo "[!] Key vault $kv allows public network access" | tee -a ~/evat-pentest/cloud/azure/public-keyvaults.txt
      fi
    done
    
    # Check App Services
    az webapp list --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$rg-webapps.json
    
    for webapp in $(jq -r '.[].name' ~/evat-pentest/cloud/azure/$rg-webapps.json); do
      az webapp config show --name $webapp --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$webapp-config.json
      
      ftp_state=$(jq -r '.ftpsState' ~/evat-pentest/cloud/azure/$webapp-config.json)
      if [ "$ftp_state" != "Disabled" ]; then
        echo "[!] Web app $webapp has FTP enabled ($ftp_state)" | tee -a ~/evat-pentest/cloud/azure/ftp-enabled-webapps.txt
      fi
      
      http_only=$(jq -r '.httpsOnly' ~/evat-pentest/cloud/azure/$webapp-config.json)
      if [ "$http_only" != "true" ]; then
        echo "[!] Web app $webapp does not enforce HTTPS only" | tee -a ~/evat-pentest/cloud/azure/insecure-webapps.txt
      fi
    done
  done
  
  # Scan for Azure SQL databases with public endpoints
  az sql server list --output json > ~/evat-pentest/cloud/azure/sql-servers.json
  
  for server in $(jq -r '.[].name' ~/evat-pentest/cloud/azure/sql-servers.json); do
    rg=$(jq -r ".[] | select(.name == \"$server\") | .resourceGroup" ~/evat-pentest/cloud/azure/sql-servers.json)
    
    # Check firewall rules
    az sql server firewall-rule list --server $server --resource-group $rg --output json > ~/evat-pentest/cloud/azure/$server-firewall-rules.json
    
    # Look for overly permissive rules (0.0.0.0 - ***************)
    jq -r '.[] | select(.startIpAddress == "0.0.0.0" and .endIpAddress == "***************") | .name' ~/evat-pentest/cloud/azure/$server-firewall-rules.json | while read rule; do
      echo "[!] SQL server $server has a firewall rule ($rule) that allows all IPs" | tee -a ~/evat-pentest/cloud/azure/public-sql-servers.txt
    done
  done
fi

# Generate CS.4 compliance report
echo "[+] Generating CS.4 compliance report"
echo "CS.4 Cloud Services Penetration Testing Compliance Report" > ~/evat-pentest/reports/cs4-compliance-report.txt
echo "=====================================================" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "Date: $(date)" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "Tester: $(whoami)@$(hostname)" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt

if [ -d ~/evat-pentest/cloud/aws ]; then
  echo "1. AWS Findings" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "---------------" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  
  echo "1.1 Public S3 Buckets:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/aws/public-buckets.txt ]; then
    cat ~/evat-pentest/cloud/aws/public-buckets.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "1.2 Unencrypted S3 Buckets:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/aws/unencrypted-buckets.txt ]; then
    cat ~/evat-pentest/cloud/aws/unencrypted-buckets.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "1.3 Open Security Groups:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/aws/open-security-groups.txt ]; then
    cat ~/evat-pentest/cloud/aws/open-security-groups.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "1.4 IAM Issues:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/aws/iam-issues.txt ]; then
    cat ~/evat-pentest/cloud/aws/iam-issues.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  if [ -f ~/evat-pentest/cloud/aws/old-access-keys.txt ]; then
    cat ~/evat-pentest/cloud/aws/old-access-keys.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
fi

if [ -d ~/evat-pentest/cloud/azure ]; then
  echo "2. Azure Findings" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "-----------------" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  
  echo "2.1 Public Storage Accounts:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/public-storage-accounts.txt ]; then
    cat ~/evat-pentest/cloud/azure/public-storage-accounts.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "2.2 Public Blob Containers:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/public-containers.txt ]; then
    cat ~/evat-pentest/cloud/azure/public-containers.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "2.3 VMs with Public IPs:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/public-vms.txt ]; then
    cat ~/evat-pentest/cloud/azure/public-vms.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "2.4 Key Vaults with Public Access:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/public-keyvaults.txt ]; then
    cat ~/evat-pentest/cloud/azure/public-keyvaults.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "2.5 Web Apps with FTP Enabled:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/ftp-enabled-webapps.txt ]; then
    cat ~/evat-pentest/cloud/azure/ftp-enabled-webapps.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "2.6 Web Apps without HTTPS enforcement:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/insecure-webapps.txt ]; then
    cat ~/evat-pentest/cloud/azure/insecure-webapps.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
  
  echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "2.7 SQL Servers with Public Access:" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  if [ -f ~/evat-pentest/cloud/azure/public-sql-servers.txt ]; then
    cat ~/evat-pentest/cloud/azure/public-sql-servers.txt >> ~/evat-pentest/reports/cs4-compliance-report.txt
  else
    echo "None found." >> ~/evat-pentest/reports/cs4-compliance-report.txt
  fi
fi

echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "3. Remediation Recommendations" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "----------------------------" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "1. Ensure all S3 buckets and blob containers have public access blocked" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "2. Encrypt all cloud storage by default" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "3. Restrict security groups and firewall rules to specific IPs/CIDRs" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "4. Implement just-in-time access for VMs and databases" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "5. Rotate access keys and credentials regularly" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "6. Enable HTTPS-only for all web applications" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "7. Disable legacy protocols like FTP" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "8. Use private endpoints for Azure services wherever possible" >> ~/evat-pentest/reports/cs4-compliance-report.txt

echo "" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "4. Compliance Status" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "------------------" >> ~/evat-pentest/reports/cs4-compliance-report.txt
echo "Based on the findings, the current cloud services configuration:" >> ~/evat-pentest/reports/cs4-compliance-report.txt

# Count issues
aws_issues=0
azure_issues=0

if [ -d ~/evat-pentest/cloud/aws ]; then
  aws_issues=$(grep -l "\[!\]" ~/evat-pentest/cloud/aws/*.txt 2>/dev/null | wc -l)
fi

if [ -d ~/evat-pentest/cloud/azure ]; then
  azure_issues=$(grep -l "\[!\]" ~/evat-pentest/cloud/azure/*.txt 2>/dev/null | wc -l)
fi

total_issues=$((aws_issues + azure_issues))

if [ $total_issues -eq 0 ]; then
  echo "✅ COMPLIANT with CS.4 requirements" >> ~/evat-pentest/reports/cs4-compliance-report.txt
else
  echo "❌ NOT COMPLIANT with CS.4 requirements" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "Total issues found: $total_issues" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "AWS issues: $aws_issues" >> ~/evat-pentest/reports/cs4-compliance-report.txt
  echo "Azure issues: $azure_issues" >> ~/evat-pentest/reports/cs4-compliance-report.txt
fi
EOL

chmod +x ~/evat-pentest/cs4-compliance-test.sh
```

## 8. Running Tests and Collecting Evidence

```bash
# Create a master script to run all tests
cat > ~/evat-pentest/run-all-tests.sh << 'EOL'
#!/bin/bash

echo "eVAT Compliance Testing Suite"
echo "============================"
echo "Starting comprehensive penetration testing for VM.2 and CS.4 compliance"
echo ""

# Create directory structure
mkdir -p ~/evat-pentest/{network,web,cloud,api,evidence,reports}

# Step 1: Run the VM.2 compliance tests
echo "Step 1: Running VM.2 Network Penetration Tests"
echo "--------------------------------------------"
bash ~/evat-pentest/vm2-compliance-test.sh

# Step 2: Run the CS.4 compliance tests
echo "Step 2: Running CS.4 Cloud Services Penetration Tests"
echo "---------------------------------------------------"
bash ~/evat-pentest/cs4-compliance-test.sh

# Step 3: Run specialized web application tests
echo "Step 3: Running Web Application Tests"
echo "-----------------------------------"
python3 ~/evat-pentest/web/username-enum.py
python3 ~/evat-pentest/web/ssti-test.py
python3 ~/evat-pentest/web/idor-test.py

# Step 4: Run specialized API tests
echo "Step 4: Running API Security Tests"
echo "--------------------------------"
python3 ~/evat-pentest/api/jwt-test.py
bash ~/evat-pentest/api/extract-api-keys.sh

# Step 5: Collect and organize all evidence
echo "Step 5: Collecting Evidence"
echo "-------------------------"
bash ~/evat-pentest/collect-evidence.sh

# Step 6: Generate final compliance report
echo "Step 6: Generating Final Compliance Report"
echo "----------------------------------------"
echo "VM.2 and CS.4 Penetration Testing Compliance Report" > ~/evat-pentest/reports/final-compliance-report.md
echo "=================================================" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "## Overview" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "This report summarizes the results of penetration testing conducted for compliance with VM.2 (Network Penetration Testing) and CS.4 (Cloud Services Penetration Testing) requirements." >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "Testing conducted on: $(date)" >> ~/evat-pentest/reports/final-compliance-report.md
echo "Tester: $(whoami)@$(hostname)" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md

echo "## VM.2 Compliance Summary" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
vm2_issues=$(grep "\[!\]" ~/evat-pentest/reports/vm2-compliance-report.txt | wc -l)
if [ $vm2_issues -eq 0 ]; then
  echo "✅ **COMPLIANT** with VM.2 requirements" >> ~/evat-pentest/reports/final-compliance-report.md
else
  echo "❌ **NOT COMPLIANT** with VM.2 requirements" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "### VM.2 Issues Found:" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "" >> ~/evat-pentest/reports/final-compliance-report.md
  grep "\[!\]" ~/evat-pentest/reports/vm2-compliance-report.txt | sed 's/\[!\]/- /g' >> ~/evat-pentest/reports/final-compliance-report.md
fi

echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "## CS.4 Compliance Summary" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
cs4_issues=$(grep "\[!\]" ~/evat-pentest/reports/cs4-compliance-report.txt | wc -l)
if [ $cs4_issues -eq 0 ]; then
  echo "✅ **COMPLIANT** with CS.4 requirements" >> ~/evat-pentest/reports/final-compliance-report.md
else
  echo "❌ **NOT COMPLIANT** with CS.4 requirements" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "### CS.4 Issues Found:" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "" >> ~/evat-pentest/reports/final-compliance-report.md
  grep "\[!\]" ~/evat-pentest/reports/cs4-compliance-report.txt | sed 's/\[!\]/- /g' >> ~/evat-pentest/reports/final-compliance-report.md
fi

echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "## Overall Compliance Status" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
total_issues=$((vm2_issues + cs4_issues))
if [ $total_issues -eq 0
