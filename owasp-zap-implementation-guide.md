# OWASP ZAP Implementation Guide for eVAT Azure DevOps Pipeline

## Overview

This document provides a complete guide for implementing OWASP ZAP security scanning in your Azure DevOps pipeline. The implementation is designed to be robust and handle common issues that occur when running ZAP in CI/CD environments.

## Common Issues & Solutions

Based on the error previously encountered (`/home/<USER>/zap/zap.sh: No such file or directory`), here are key solutions:

1. **Correct Installation Path**: Ensure ZAP is installed to the expected path
2. **File Permissions**: Set proper execute permissions on the ZAP script
3. **Reliable Download Process**: Implement fallback mechanisms for downloads
4. **Path Verification**: Add checks to verify paths before execution

## Implementation Details

### 1. ZAP Installation Script

```yaml
- task: CmdLine@2
  displayName: 'Download and Configure OWASP ZAP'
  continueOnError: true
  inputs:
    script: |
      # Create dedicated ZAP directory with verified path
      ZAP_HOME="/home/<USER>/zap"
      mkdir -p $ZAP_HOME
      
      # Define multiple download URLs for resilience
      ZAP_VERSION="$(zapVersion)"
      PRIMARY_URL="https://github.com/zaproxy/zaproxy/releases/download/v$ZAP_VERSION/ZAP_${ZAP_VERSION}_Crossplatform.zip"
      BACKUP_URL="https://github.com/zaproxy/zaproxy/releases/download/v$ZAP_VERSION/ZAP_${ZAP_VERSION}_Linux.tar.gz"
      FALLBACK_URL="https://github.com/zaproxy/zaproxy/releases/latest/download/ZAP_Crossplatform.zip"
      
      # Multi-stage download with fallbacks
      echo "Downloading OWASP ZAP v$ZAP_VERSION from $PRIMARY_URL"
      wget -q $PRIMARY_URL -O /tmp/zap.zip || \
      wget -q $BACKUP_URL -O /tmp/zap.tgz || \
      wget -q $FALLBACK_URL -O /tmp/zap.zip
      
      # Check what we downloaded and extract accordingly
      if [ -f "/tmp/zap.zip" ] && [ -s "/tmp/zap.zip" ]; then
        echo "Extracting ZAP from zip archive"
        unzip -q /tmp/zap.zip -d /tmp/zap
        # Find the extracted ZAP directory
        ZAP_EXTRACTED=$(find /tmp/zap -name "zap.sh" -type f -exec dirname {} \; | head -n 1)
        # Copy files to the home directory
        cp -rf $ZAP_EXTRACTED/* $ZAP_HOME/
        rm -rf /tmp/zap.zip /tmp/zap
      elif [ -f "/tmp/zap.tgz" ] && [ -s "/tmp/zap.tgz" ]; then
        echo "Extracting ZAP from tar.gz archive"
        mkdir -p /tmp/zap
        tar -xzf /tmp/zap.tgz -C /tmp/zap
        # Find the extracted ZAP directory
        ZAP_EXTRACTED=$(find /tmp/zap -name "zap.sh" -type f -exec dirname {} \; | head -n 1)
        # Copy files to the home directory
        cp -rf $ZAP_EXTRACTED/* $ZAP_HOME/
        rm -rf /tmp/zap.tgz /tmp/zap
      else
        echo "ERROR: Failed to download ZAP. Creating placeholder files for pipeline continuity."
        mkdir -p $ZAP_HOME
        echo "#!/bin/bash" > $ZAP_HOME/zap.sh
        echo "echo 'ZAP was not installed correctly. This is a placeholder script.'" >> $ZAP_HOME/zap.sh
        echo "exit 1" >> $ZAP_HOME/zap.sh
      fi
      
      # Ensure execution permissions
      chmod +x $ZAP_HOME/zap.sh
      
      # Create a verification script to ensure we can run ZAP correctly
      echo "#!/bin/bash" > $ZAP_HOME/verify.sh
      echo "if [ -f \"$ZAP_HOME/zap.sh\" ]; then" >> $ZAP_HOME/verify.sh
      echo "  echo 'ZAP installation verified at $ZAP_HOME/zap.sh'" >> $ZAP_HOME/verify.sh
      echo "  # Test ZAP version command" >> $ZAP_HOME/verify.sh
      echo "  $ZAP_HOME/zap.sh -version" >> $ZAP_HOME/verify.sh
      echo "  exit \$?" >> $ZAP_HOME/verify.sh
      echo "else" >> $ZAP_HOME/verify.sh
      echo "  echo 'ERROR: ZAP not found at $ZAP_HOME/zap.sh'" >> $ZAP_HOME/verify.sh
      echo "  exit 1" >> $ZAP_HOME/verify.sh
      echo "fi" >> $ZAP_HOME/verify.sh
      chmod +x $ZAP_HOME/verify.sh
      
      # Run verification
      $ZAP_HOME/verify.sh
```

### 2. ZAP Baseline Scan (Quick Security Check)

```yaml
- task: CmdLine@2
  displayName: 'Run ZAP Baseline Scan'
  continueOnError: true
  inputs:
    script: |
      # Verify ZAP is installed correctly
      ZAP_HOME="/home/<USER>/zap"
      if [ ! -f "$ZAP_HOME/zap.sh" ]; then
        echo "ERROR: ZAP not found at $ZAP_HOME/zap.sh - Skipping scan"
        exit 0
      fi
      
      # Define report paths
      REPORTS_DIR="$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$(buildVersion)/zap-scan"
      mkdir -p $REPORTS_DIR
      
      # Run baseline scan (passive only, quick security check)
      echo "Running ZAP baseline scan against https://app.evat.com/"
      $ZAP_HOME/zap.sh -cmd -silent -quickurl https://app.evat.com/ \
        -quickprogress -quickout $REPORTS_DIR/baseline-report.html
      
      # Check if the scan completed successfully
      if [ -f "$REPORTS_DIR/baseline-report.html" ]; then
        echo "Baseline scan completed successfully"
        
        # Extract alerts for summary
        grep -o 'class="risk-[^"]*"' $REPORTS_DIR/baseline-report.html | \
          sort | uniq -c | sed 's/class="risk-//g' | sed 's/"//g' > $REPORTS_DIR/baseline-summary.txt
      else
        echo "Baseline scan failed - creating placeholder report"
        echo "<html><body><h1>ZAP Scan Failed</h1></body></html>" > $REPORTS_DIR/baseline-report.html
        echo "Scan failed to complete" > $REPORTS_DIR/baseline-summary.txt
      fi
```

### 3. Full Active Scan

```yaml
- task: CmdLine@2
  displayName: 'Run ZAP Full Active Scan'
  continueOnError: true
  inputs:
    script: |
      # Verify ZAP is installed correctly
      ZAP_HOME="/home/<USER>/zap"
      if [ ! -f "$ZAP_HOME/zap.sh" ]; then
        echo "ERROR: ZAP not found at $ZAP_HOME/zap.sh - Skipping scan"
        exit 0
      fi
      
      # Define report paths
      REPORTS_DIR="$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$(buildVersion)/zap-scan"
      mkdir -p $REPORTS_DIR
      
      # Start ZAP in daemon mode
      echo "Starting ZAP in daemon mode"
      $ZAP_HOME/zap.sh -daemon -host 127.0.0.1 -port 8090 \
        -config api.disablekey=true \
        -config scanner.attackOnStart=true \
        -config scanner.threadPerHost=5 &
      
      # Wait for ZAP to initialize
      sleep 30
      echo "ZAP daemon started"
      
      # Run full active scan
      echo "Running full active scan against https://app.evat.com/"
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 \
        -spider https://app.evat.com/ \
        -scan \
        -scanpolicy "Default Policy" \
        -quickout $REPORTS_DIR/active-scan-report.html \
        -quickprogress
      
      # Generate report in multiple formats
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 \
        -last_scan_report $REPORTS_DIR/active-scan-report.xml -format xml
      
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 \
        -last_scan_report $REPORTS_DIR/active-scan-report.json -format json
      
      # Extract high and critical alerts
      echo "Extracting high and critical alerts"
      grep -B 5 -A 20 'class="risk-high"' $REPORTS_DIR/active-scan-report.html > $REPORTS_DIR/high-alerts.html || true
      grep -B 5 -A 20 'class="risk-critical"' $REPORTS_DIR/active-scan-report.html > $REPORTS_DIR/critical-alerts.html || true
      
      # Shutdown ZAP
      echo "Shutting down ZAP daemon"
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 -shutdown
```

### 4. Authenticated Scanning

For testing authenticated areas of your application, use this script:

```yaml
- task: CmdLine@2
  displayName: 'Run ZAP Authenticated Scan'
  continueOnError: true
  env:
    # Store these in Azure DevOps variable groups with appropriate security
    LOGIN_URL: 'https://app.evat.com/login'
    TEST_USERNAME: '$(testUsername)'
    TEST_PASSWORD: '$(testPassword)'
  inputs:
    script: |
      # Verify ZAP is installed correctly
      ZAP_HOME="/home/<USER>/zap"
      if [ ! -f "$ZAP_HOME/zap.sh" ]; then
        echo "ERROR: ZAP not found at $ZAP_HOME/zap.sh - Skipping scan"
        exit 0
      fi
      
      # Create authentication script
      mkdir -p /tmp/zap-scripts
      cat > /tmp/zap-scripts/auth.js << 'EOL'
      // Login form handling script for ZAP
      function authenticate(helper, paramsValues, credentials) {
          print("Authenticating via JavaScript...");
          
          // Get the login page - this will make sure we have a valid session
          var loginUrl = paramsValues.get("LoginURL");
          var msg = helper.prepareMessage();
          helper.sendAndReceive(msg, false);
          
          // Extract any needed tokens
          var token = extractToken(msg);
          
          // Build the login request
          msg = helper.prepareMessage();
          var requestBody = "username=" + encodeURIComponent(credentials.getParam("username"))
                  + "&password=" + encodeURIComponent(credentials.getParam("password"))
                  + "&_token=" + token;
          
          msg.setRequestBody(requestBody);
          msg.getRequestHeader().setContentLength(requestBody.length);
          helper.sendAndReceive(msg, false);
          
          return msg.getResponseHeader().getStatusCode() === 302;
      }
      
      function extractToken(msg) {
          // Extract CSRF token from response - adjust based on your app
          var body = msg.getResponseBody().toString();
          var tokenRegex = /<input [^>]*name=["']_token["'] [^>]*value=["']([^"']+)["']/;
          var match = body.match(tokenRegex);
          return match ? match[1] : "";
      }
      
      function getRequiredParamsNames() {
          return ["LoginURL"];
      }
      
      function getOptionalParamsNames() {
          return [];
      }
      
      function getCredentialsParamsNames() {
          return ["username", "password"];
      }
      EOL
      
      # Start ZAP in daemon mode
      echo "Starting ZAP in daemon mode"
      $ZAP_HOME/zap.sh -daemon -host 127.0.0.1 -port 8090 \
        -config api.disablekey=true \
        -config scanner.attackOnStart=true &
      
      # Wait for ZAP to initialize
      sleep 30
      echo "ZAP daemon started"
      
      # Setup authentication
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 \
        -script /tmp/zap-scripts/auth.js \
        -ScriptParam LoginURL=$LOGIN_URL \
        -Username $TEST_USERNAME \
        -Password $TEST_PASSWORD \
        -Context "eVAT"
      
      # Run authenticated scan
      echo "Running authenticated scan"
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 \
        -user "eVAT User" \
        -spider https://app.evat.com/dashboard \
        -scan \
        -quickout $REPORTS_DIR/auth-scan-report.html \
        -quickprogress
      
      # Shutdown ZAP
      echo "Shutting down ZAP daemon"
      $ZAP_HOME/zap.sh -cmd -host 127.0.0.1 -port 8090 -shutdown
```

## Best Practices for OWASP ZAP in Azure DevOps

1. **Staged Security Testing:**
   - Run quick baseline scans early in the pipeline
   - Run full scans only on specific branches (master/main)
   - Run authenticated scans for protected functionality

2. **Performance Optimization:**
   - Use `-config scanner.threadPerHost=5` to balance speed and server load
   - Set appropriate timeouts with `-config scanner.maxScanDurationInMins=60`
   - Run only necessary scan rules with `-config scanner.maxRule=1000`

3. **Security Findings Management:**
   - Track security findings over time
   - Create security thresholds for build failures
   - Integrate with Azure DevOps work items for vulnerability tracking

4. **Report Management:**
   - Generate reports in multiple formats (HTML, XML, JSON)
   - Create trend analysis across builds
   - Generate executive summaries for management reporting

## Troubleshooting

### Common Issues and Solutions

1. **ZAP Not Found at Expected Path:**
   - Verify the installation directory
   - Check file permissions
   - Use `find` to locate zap.sh in the agent workspace

2. **Connection Timeout During Scans:**
   - Increase connection timeout with `-config connection.timeoutInSecs=120`
   - Reduce concurrent requests with `-config scanner.threadPerHost=3`

3. **Authenticated Scans Failing:**
   - Verify credentials are correctly passed
   - Check the authentication script logic
   - Review authentication logs for failure points

## Working with ZAP Results

### Interpreting Scan Results

ZAP categorizes findings by risk level:
- **Critical:** Immediate action required, high confidence in vulnerability
- **High:** Major security issues that should be fixed in the current sprint
- **Medium:** Security issues that should be scheduled for remediation
- **Low:** Minor security issues that should be considered for future sprints
- **Informational:** Best practice recommendations

### Acting on Findings

1. **Critical and High Findings:**
   - Create work items automatically from the pipeline
   - Assign to the responsible team member
   - Set due dates based on severity

2. **Medium and Low Findings:**
   - Aggregate and prioritize in security backlog
   - Address in planned security iterations

3. **False Positives:**
   - Document in a context file for future scan exclusion
   - Create a findings filter file to maintain clean reports

## Conclusion

By implementing OWASP ZAP in your Azure DevOps pipeline following this guide, you'll establish a reliable, automated security testing process that identifies vulnerabilities early in your development lifecycle.
