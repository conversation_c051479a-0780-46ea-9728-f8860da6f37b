trigger:
  batch: true
  branches:
    include:
    - master
variables:
- name: tempAppPath
  value: /opt/evat/temp/prodapp
- name: tempArchivePath
  value: /opt/evat/temp/archive
- name: appBasePath
  value: /var/www/prod-evat-test
- name: certPath
  value: /opt/evat/temp/cert/
- name: envPath
  value: /opt/evat/temp/env/
- name: cronBasePath
  value: /opt/evat/cron-test
- name: deployPath
  value: /opt/evat/app/app.evat.com/eVatApp/
- name: reportTimestamp
  value: $[pipeline.startTime]
- name: buildVersion
  value: $(Build.BuildNumber)
- name: reportBasePath
  value: $(Build.ArtifactStagingDirectory)/security-reports
- name: reportPath
  value: $(reportBasePath)/v$(buildVersion)_$(reportTimestamp)
- name: findingsHistoryPath
  value: $(Build.ArtifactStagingDirectory)/security-findings-history
- name: findingsVersion
  value: $(buildVersion).$(reportTimestamp)
- name: ADO_PAT
  value: $(System.AccessToken)
- name: zapVersion
  value: "2.14.0"
- name: dependencyCheckVersion
  value: "8.4.0"
- name: phpstanVersion
  value: "^2.0"
- name: larastanVersion
  value: "^2.0"
- name: enlightnVersion
  value: "^2.0"
- name: GITHUB_TOKEN
  value: $(GitHubToken)
- name: artifactsFolder
  value: 'eVAT_Pipeline_$(Build.BuildNumber)'
- group: ZapSettings
stages:
- stage: InitStage
  displayName: Starting deploy
  jobs:
  - job: BuildUbuntuImageJob
    displayName: Building image
    pool:
      vmImage: ubuntu-latest
  - job: PHPJob
    displayName: PHP
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      condition: false
      displayName: Setting PHP version
      inputs:
        repository: none
    - task: CmdLine@2
      inputs:
        script: |
          sudo update-alternatives --set php /usr/bin/php$(phpVersion)
          sudo update-alternatives --set phar /usr/bin/phar$(phpVersion)
          sudo update-alternatives --set phpdbg /usr/bin/phpdbg$(phpVersion)
          sudo update-alternatives --set php-cgi /usr/bin/php-cgi$(phpVersion)
          sudo update-alternatives --set phar.phar /usr/bin/phar.phar$(phpVersion)
          php -version
- stage: BuildApplicationStage
  displayName: Build
  dependsOn:
  - InitStage
  jobs:
  - job: ResolvingDependenciesJob
    displayName: Building application
    pool:
      vmImage: ubuntu-latest
    steps:
    - task: DownloadSecureFile@1
      name: pub
      displayName: Setting Elster's private repository certificate
      inputs:
        secureFile: 'id_rsa.pub'
    - task: DownloadSecureFile@1
      name: key
      displayName: Setting Elster's private repository public key
      inputs:
        secureFile: 'id_rsa'
    - task: DownloadSecureFile@1
      name: env
      displayName: Setting up .env secrets
      inputs:
        secureFile: '.env'
    - task: CmdLine@2
      displayName: 'Creating .ssh folder'
      inputs:
        script: mkdir ~/.ssh
    - task: CmdLine@2
      displayName: 'Copying .env file to working directory'
      inputs:
        script: cp $(env.secureFilePath) $(System.DefaultWorkingDirectory)
    - task: CmdLine@2
      displayName: 'Copying public key to working directory'
      inputs:
        script: cp $(pub.secureFilePath) ~/.ssh/
    - task: CmdLine@2
      displayName: 'Copying private key to working directory'
      inputs:
        script: cp $(key.secureFilePath) ~/.ssh/
    - task: CmdLine@2
      displayName: 'Changing permissions'
      inputs:
        script: chmod 600 ~/.ssh/id_rsa
    - task: CmdLine@2
      displayName: 'Running Composer install'
      inputs:
        script: composer install --ignore-platform-reqs
    - task: CmdLine@2
      displayName: 'Running Yarn install'
      inputs:
        script: yarn install
    - task: CmdLine@2
      displayName: 'Building assets'
      inputs:
        script: yarn prod
    - task: CmdLine@2
      displayName: 'Deleting node_modules'
      inputs:
        script: rm -R node_modules
    - task: CmdLine@2
      displayName: 'Clearing cache'
      inputs:
        script: php artisan cache:clear && php artisan route:clear && php artisan view:clear && php artisan config:clear
    - task: CmdLine@2
      displayName: 'Horizon install'
      inputs:
        script: php artisan horizon:publish
    - task: ArchiveFiles@2
      name: ArchiveFilesTask
      displayName: Archiving files
      inputs:
        rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
        replaceExistingArchive: true
    - task: CopyFilesOverSSH@0
      name: CopyingFilesTaskOverSSH
      displayName: Copying files over SSH to temp directory
      inputs:
        sshEndpoint: 'AzureProdSrv'
        sourceFolder: '$(Build.ArtifactStagingDirectory)/'
        contents: '**'
        targetFolder: '/opt/evat/temp/archive'
        readyTimeout: '20000'
    - task: SSH@0
      name: ResolvingTempDirectoryTask
      displayName: Resolving files in temp directory
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          rm -R $(tempAppPath)/
          mkdir $(tempAppPath)
          unzip $(tempArchivePath)/$(Build.BuildId).zip -d $(tempAppPath)/
          rm $(tempArchivePath)/$(Build.BuildId).zip
          cp $(envPath)/prodapp.env $(tempAppPath)/.env
        readyTimeout: '20000'
    - task: SSH@0
      displayName: Stop supervisor
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          ls
        readyTimeout: '20000'
- stage: StaticAnalysis
  displayName: 'Static Code Analysis'
  dependsOn:
  - BuildApplicationStage
  jobs:
  - job: EnsureSecurityFeedExists
    displayName: 'Setup Security Feed'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: CmdLine@2
      displayName: 'Initialize Security Findings History'
      inputs:
        script: |
          # Create directory for findings history
          mkdir -p $(findingsHistoryPath)

          # Initialize history files if they don't exist
          if [ ! -f "$(findingsHistoryPath)/phpstan_findings.txt" ]; then
            echo "Initializing PHPStan findings history file"
            touch $(findingsHistoryPath)/phpstan_findings.txt
          fi

          if [ ! -f "$(findingsHistoryPath)/enlightn_findings.txt" ]; then
            echo "Initializing Enlightn findings history file"
            touch $(findingsHistoryPath)/enlightn_findings.txt
          fi

          if [ ! -f "$(findingsHistoryPath)/zap_findings.txt" ]; then
            echo "Initializing ZAP findings history file"
            touch $(findingsHistoryPath)/zap_findings.txt
          fi

          echo "Created security findings history files in $(findingsHistoryPath)"

          # Just to be safe, publish findings as artifacts directly
          mkdir -p $(Build.ArtifactStagingDirectory)/security-findings-history
          cp $(findingsHistoryPath)/*.txt $(Build.ArtifactStagingDirectory)/security-findings-history/ || echo "No txt files to copy"
    - task: PowerShell@2
      displayName: 'Setup Security Feed (Using REST API with PAT)'
      continueOnError: true
      inputs:
        targetType: 'inline'
        script: |
          # Use REST API with the PAT you added
          $token = "$(AZ_DEVOPS_PAT)"

          # Fix URL format - use the correct API endpoint
          $organization = "https://feeds.dev.azure.com/eVAT"

          # API endpoint for feed creation - use the correct path
          $uri = "$organization/_apis/packaging/feeds?api-version=7.1-preview.1"

          # Base64 encode the Personal Access Token (PAT)
          $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$token"))

          # Create the authorization header
          $headers = @{
              Authorization = "Basic $base64AuthInfo"
              "Content-Type" = "application/json"
          }

          # Define the feed creation body
          $body = @{
              name = "SecurityFindings"
              description = "Feed for storing security findings history"
          } | ConvertTo-Json

          Write-Host "Attempting to create feed using PAT authentication..."
          Write-Host "Using API endpoint: $uri"

          try {
              # Try to create the feed
              $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body
              Write-Host "Security feed created successfully: $($response.name)"
          }
          catch {
              # Check if feed already exists
              if($_.Exception.Response.StatusCode -eq 409) {
                  Write-Host "Feed 'SecurityFindings' already exists. Continuing with existing feed."
              }
              else {
                  Write-Host "Error creating feed: $_"
                  Write-Host "StatusCode: $($_.Exception.Response.StatusCode)"
                  Write-Host "Continuing without feed creation. Findings history will be stored as build artifacts only."
              }
          }
    - task: PublishBuildArtifacts@1
      displayName: 'Publish Security Findings History'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/security-findings-history'
        artifactName: 'SecurityFindingsHistory'
      continueOnError: true
  - job: PHPAnalysis
    displayName: 'PHP Code Analysis'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: DownloadSecureFile@1
      name: sshKey
      displayName: 'Download SSH Private Key'
      inputs:
        secureFile: 'id_rsa'
    - task: CmdLine@2
      displayName: 'Setup SSH Authentication'
      inputs:
        script: |
          # Setup SSH directory and keys
          mkdir -p ~/.ssh
          cp $(sshKey.secureFilePath) ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

          # Add Azure DevOps to known hosts
          ssh-keyscan -t rsa ssh.dev.azure.com >> ~/.ssh/known_hosts

          # Configure Git to use SSH
          git config --global url."*********************:v3/".insteadOf "https://dev.azure.com/"

          # Test SSH connection and display SSH config for debugging
          echo "Testing SSH configuration:"
          ssh -T ********************* -o BatchMode=yes -v || echo "SSH authentication test completed"
    - task: CmdLine@2
      displayName: 'Install and Run Latest PHPStan'
      inputs:
        script: "# Define timestamp format without spaces or colons\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\nBUILD_VERSION=\"$(buildVersion)\"\n\n# Create a structured directory hierarchy for the consolidated artifact approach\n# Main artifact folder\nARTIFACT_FOLDER=\"$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$BUILD_VERSION\"\n\n# PHP Analysis folder structure\nPHP_ANALYSIS_FOLDER=\"$ARTIFACT_FOLDER/php-analysis\"\nPHPSTAN_FOLDER=\"$PHP_ANALYSIS_FOLDER/phpstan\"\n\n# Create all required directories\nmkdir -p $PHPSTAN_FOLDER/results\nmkdir -p $PHPSTAN_FOLDER/summaries\n\n# Keep backward compatibility with existing pipeline reporting\nREPORT_DIR=\"$(Build.ArtifactStagingDirectory)/security-reports/v$BUILD_VERSION_${TIMESTAMP}\"\nmkdir -p $REPORT_DIR/php-analysis/v$BUILD_VERSION\n\necho \"Attempting to install PHPStan and Larastan...\"\n\n# Update Composer and install dependencies\ncomposer self-update --2 || echo \"Composer self-update failed, continuing with existing version\"\n\n# Install without private repositories if only analyzing\nexport COMPOSER_DISCARD_CHANGES=true\n\n# Try composer install first to preserve current dependencies\ncomposer install --no-scripts --no-interaction || echo \"Initial composer install failed, proceeding with PHPStan installation only\"\n\n# Install PHPStan separately (without updating other dependencies)\n# Using PHPStan ^1.10 for compatibility with Larastan\ncomposer require --dev phpstan/phpstan:^1.10 --no-update\ncomposer require --dev larastan/larastan:^2.0 --no-update\ncomposer update phpstan/phpstan larastan/larastan --with-all-dependencies --no-interaction || echo \"PHPStan installation failed, will try to proceed with analysis\"\n\n# Get installed version for documentation\necho \"PHPStan version:\" \ncomposer show phpstan/phpstan | grep versions || echo \"PHPStan not installed\"\n\necho \"Larastan version:\" \ncomposer show larastan/larastan | grep versions || echo \"Larastan not installed\"\n\n# Create PHPStan config with latest features\ncat > phpstan.neon << 'EOL'\nincludes:\n    - ./vendor/larastan/larastan/extension.neon\nparameters:\n  level: 8\n  paths:\n  - app\nexcludePaths:\n  - vendor\n  - tests\ncheckMissingIterableValueType: false\nparallel:\n  maximumNumberOfProcesses: 4\nmemory-limit: 2G\nEOL\n\n# Create result file path with consistent format\nPHPSTAN_RESULT_FILE=\"$PHPSTAN_FOLDER/results/phpstan-results_${TIMESTAMP}.json\"\nPHPSTAN_SUMMARY_FILE=\"$PHPSTAN_FOLDER/summaries/phpstan-summary_${TIMESTAMP}.txt\"\n\n# Create standard paths for easier reference\nPHPSTAN_STANDARD_RESULT=\"$PHP_ANALYSIS_FOLDER/phpstan-results.json\"\nPHPSTAN_STANDARD_SUMMARY=\"$PHP_ANALYSIS_FOLDER/phpstan-summary.txt\"\n\n# Create placeholder report file if PHPStan not installed\nif [ ! -f \"vendor/bin/phpstan\" ]; then\n  echo \"PHPStan executable not found. Creating placeholder report.\"\n  echo '{\"totals\":{\"errors\":0,\"file_errors\":0},\"files\":[],\"errors\":[]}' > \"$PHPSTAN_RESULT_FILE\"\nelse\n  # Run PHPStan\n  echo \"Running PHPStan analysis...\"\n  vendor/bin/phpstan analyse --error-format=json > \"$PHPSTAN_RESULT_FILE\" || true\nfi\n\n# Create a text-based summary for easier reading\necho \"Generating PHPStan summary report...\"\necho \"PHPStan Analysis Summary - $(date)\" > \"$PHPSTAN_SUMMARY_FILE\"\necho \"===================================\" >> \"$PHPSTAN_SUMMARY_FILE\"\necho \"Build: $BUILD_VERSION\" >> \"$PHPSTAN_SUMMARY_FILE\"\necho \"Timestamp: $TIMESTAMP\" >> \"$PHPSTAN_SUMMARY_FILE\"\necho \"\" >> \"$PHPSTAN_SUMMARY_FILE\"\n\nif [ -f \"$PHPSTAN_RESULT_FILE\" ]; then\n  # Extract error count\n  ERROR_COUNT=$(grep -o '\"errors\":[0-9]*' \"$PHPSTAN_RESULT_FILE\" | head -1 | cut -d ':' -f2)\n  echo \"Total errors found: ${ERROR_COUNT:-Unknown}\" >> \"$PHPSTAN_SUMMARY_FILE\"\n  \n  # Extract first 10 errors for quick overview\n  echo -e \"\\nTop Issues:\" >> \"$PHPSTAN_SUMMARY_FILE\"\n  grep -o '\"message\":\"[^\"]*\"' \"$PHPSTAN_RESULT_FILE\" | head -10 | sed 's/\"message\":\"//g' | sed 's/\"//g' >> \"$PHPSTAN_SUMMARY_FILE\"\nelse\n  echo \"No PHPStan results found\" >> \"$PHPSTAN_SUMMARY_FILE\"\nfi\n\n# Copy to standard locations for easier reference\ncp \"$PHPSTAN_RESULT_FILE\" \"$PHPSTAN_STANDARD_RESULT\" || true\ncp \"$PHPSTAN_SUMMARY_FILE\" \"$PHPSTAN_STANDARD_SUMMARY\" || true\n\n# Maintain backward compatibility with existing pipeline\ncp \"$PHPSTAN_RESULT_FILE\" \"$REPORT_DIR/php-analysis/v$BUILD_VERSION/phpstan-results_${TIMESTAMP}.json\" || true\ncp \"$PHPSTAN_SUMMARY_FILE\" \"$REPORT_DIR/php-analysis/v$BUILD_VERSION/phpstan-summary.txt\" || true\ncp \"$PHPSTAN_RESULT_FILE\" \"$(Build.ArtifactStagingDirectory)/phpstan-results.json\" || true\ncp \"$PHPSTAN_SUMMARY_FILE\" \"$(Build.ArtifactStagingDirectory)/phpstan-summary.txt\" || true\n\necho \"PHPStan analysis completed. Files organized in: $PHP_ANALYSIS_FOLDER\"\n\n# List created files for verification\necho \"Created files:\"\nfind \"$ARTIFACT_FOLDER\" -type f | sort\n"
    - task: PublishBuildArtifacts@1
      displayName: 'Publish PHP Analysis Results'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$(buildVersion)/php-analysis'
        artifactName: '$(artifactsFolder)'
        artifactType: 'Container'
      continueOnError: true
  - job: FrontendAnalysis
    displayName: 'JavaScript/Vue Analysis'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: CmdLine@2
      displayName: 'Run Latest ESLint for Vue.js'
      inputs:
        script: "# Define timestamp format without spaces or colons\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\nBUILD_VERSION=\"$(buildVersion)\"\n\n# Create a structured directory hierarchy for the consolidated artifact approach\n# Main artifact folder\nARTIFACT_FOLDER=\"$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$BUILD_VERSION\"\n\n# Frontend Analysis folder structure\nFRONTEND_ANALYSIS_FOLDER=\"$ARTIFACT_FOLDER/frontend-analysis\"\nESLINT_FOLDER=\"$FRONTEND_ANALYSIS_FOLDER/eslint\"\n\n# Create all required directories\nmkdir -p $ESLINT_FOLDER/results\nmkdir -p $ESLINT_FOLDER/summaries\n\n# Keep backward compatibility with existing pipeline reporting\nREPORT_DIR=\"$(Build.ArtifactStagingDirectory)/security-reports/v$BUILD_VERSION_${TIMESTAMP}\"\nmkdir -p $REPORT_DIR/frontend-analysis/v$BUILD_VERSION\n\necho \"Installing ESLint and plugins...\"\n\n# Install latest ESLint and plugins\nnpm install --save-dev eslint@latest eslint-plugin-vue@latest eslint-plugin-security@latest\n\n# Log versions\necho \"ESLint version:\"\nnpx eslint --version\n\n# Create ESLint config\ncat > .eslintrc.js << 'EOL'\nmodule.exports = {\n  root: true,\n  env: { node: true, browser: true },\n  extends: ['plugin:vue/recommended', 'plugin:security/recommended'],\n  rules: {\n    'vue/multi-word-component-names': 'off',\n    'vue/max-attributes-per-line': 'off',\n    'security/detect-object-injection': 'warn',\n    'security/detect-unsafe-regex': 'warn',\n    'security/detect-eval-with-expression': 'error'\n  },\n  parserOptions: { ecmaVersion: 2022 }\n}\nEOL\n\n# Create result file path with consistent format\nESLINT_RESULT_FILE=\"$ESLINT_FOLDER/results/eslint-results_${TIMESTAMP}.json\"\nESLINT_SUMMARY_FILE=\"$ESLINT_FOLDER/summaries/eslint-summary_${TIMESTAMP}.txt\"\n\n# Create standard paths for easier reference\nESLINT_STANDARD_RESULT=\"$FRONTEND_ANALYSIS_FOLDER/eslint-results.json\"\nESLINT_STANDARD_SUMMARY=\"$FRONTEND_ANALYSIS_FOLDER/eslint-summary.txt\"\n\necho \"Running ESLint analysis...\"\n\n# Check if resources directory exists before running ESLint\nif [ -d \"resources\" ]; then\n  # Run ESLint and capture output to a file\n  npx eslint 'resources/**/*.{js,vue}' --format json > \"$ESLINT_RESULT_FILE\" || true\n  echo \"ESLint analysis completed.\"\nelse\n  # Create an empty report if resources directory doesn't exist\n  echo \"Resources directory not found. Creating empty report.\"\n  echo '{\"results\":[],\"errorCount\":0,\"warningCount\":0,\"fixableErrorCount\":0,\"fixableWarningCount\":0}' > \"$ESLINT_RESULT_FILE\"\nfi\n\n# Create a text-based summary for easier reading\necho \"Generating ESLint summary report...\"\necho \"ESLint Analysis Summary - $(date)\" > \"$ESLINT_SUMMARY_FILE\"\necho \"==================================\" >> \"$ESLINT_SUMMARY_FILE\"\necho \"Build: $BUILD_VERSION\" >> \"$ESLINT_SUMMARY_FILE\"\necho \"Timestamp: $TIMESTAMP\" >> \"$ESLINT_SUMMARY_FILE\"\necho \"\" >> \"$ESLINT_SUMMARY_FILE\"\n\nif [ -f \"$ESLINT_RESULT_FILE\" ]; then\n  # Extract error count\n  ERROR_COUNT=$(grep -o '\"errorCount\":[0-9]*' \"$ESLINT_RESULT_FILE\" | head -1 | cut -d ':' -f2)\n  WARNING_COUNT=$(grep -o '\"warningCount\":[0-9]*' \"$ESLINT_RESULT_FILE\" | head -1 | cut -d ':' -f2)\n  \n  echo \"Total errors found: ${ERROR_COUNT:-0}\" >> \"$ESLINT_SUMMARY_FILE\"\n  echo \"Total warnings found: ${WARNING_COUNT:-0}\" >> \"$ESLINT_SUMMARY_FILE\"\n  \n  # Extract first 10 messages for quick overview\n  echo -e \"\\nTop Issues:\" >> \"$ESLINT_SUMMARY_FILE\"\n  grep -o '\"message\":\"[^\"]*\"' \"$ESLINT_RESULT_FILE\" | head -10 | sed 's/\"message\":\"//g' | sed 's/\"//g' >> \"$ESLINT_SUMMARY_FILE\"\nelse\n  echo \"No ESLint results found\" >> \"$ESLINT_SUMMARY_FILE\"\nfi\n\n# Copy to standard locations for easier reference\ncp \"$ESLINT_RESULT_FILE\" \"$ESLINT_STANDARD_RESULT\" || true\ncp \"$ESLINT_SUMMARY_FILE\" \"$ESLINT_STANDARD_SUMMARY\" || true\n\n# Maintain backward compatibility with existing pipeline\ncp \"$ESLINT_RESULT_FILE\" \"$REPORT_DIR/frontend-analysis/v$BUILD_VERSION/eslint-results_${TIMESTAMP}.json\" || true\ncp \"$ESLINT_SUMMARY_FILE\" \"$REPORT_DIR/frontend-analysis/v$BUILD_VERSION/eslint-summary.txt\" || true\ncp \"$ESLINT_RESULT_FILE\" \"$(Build.ArtifactStagingDirectory)/eslint-results.json\" || true\ncp \"$ESLINT_SUMMARY_FILE\" \"$(Build.ArtifactStagingDirectory)/eslint-summary.txt\" || true\n\necho \"ESLint analysis completed. Files organized in: $FRONTEND_ANALYSIS_FOLDER\"\n\n# List created files for verification\necho \"Created files:\"\nfind \"$ARTIFACT_FOLDER/frontend-analysis\" -type f | sort\n"
    - task: PublishBuildArtifacts@1
      displayName: 'Publish Frontend Analysis Results'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$(buildVersion)/frontend-analysis'
        artifactName: '$(artifactsFolder)'
        artifactType: 'Container'
      continueOnError: true
  - job: DependencyCheck
    displayName: 'Dependencies Security Scan'
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: CmdLine@2
      displayName: 'Run Latest OWASP Dependency-Check'
      continueOnError: true
      inputs:
        script: "# Define timestamp format without spaces or colons\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\nBUILD_VERSION=\"$(buildVersion)\"\n\n# Create a structured directory hierarchy for the consolidated artifact approach\n# Main artifact folder\nARTIFACT_FOLDER=\"$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$BUILD_VERSION\"\n\n# Dependency check folder structure\nDEPENDENCY_CHECK_FOLDER=\"$ARTIFACT_FOLDER/dependency-check\"\n\n# Create all required directories\nmkdir -p $DEPENDENCY_CHECK_FOLDER/reports\nmkdir -p $DEPENDENCY_CHECK_FOLDER/summaries\n\n# Keep backward compatibility with existing pipeline reporting\nREPORT_DIR=\"$(Build.ArtifactStagingDirectory)/security-reports/v$BUILD_VERSION_${TIMESTAMP}\"\necho \"Creating report directory: $REPORT_DIR\"\nmkdir -p $REPORT_DIR/dependency-check/v$BUILD_VERSION\n\n# Download and install latest Dependency Check\necho \"Downloading and extracting OWASP Dependency Check v$(dependencyCheckVersion)...\"\nwget -q https://github.com/jeremylong/DependencyCheck/releases/download/v$(dependencyCheckVersion)/dependency-check-$(dependencyCheckVersion)-release.zip\nunzip -q dependency-check-$(dependencyCheckVersion)-release.zip\n\n# Set report file location with safe timestamp format\nREPORT_BASE=\"$REPORT_DIR/dependency-check/v$BUILD_VERSION/report_${TIMESTAMP}\"\n\n# Also set new consolidated report location\nCONSOLIDATED_REPORT_BASE=\"$DEPENDENCY_CHECK_FOLDER/reports/dependency-check-report_${TIMESTAMP}\"\nCONSOLIDATED_SUMMARY_FILE=\"$DEPENDENCY_CHECK_FOLDER/summaries/dependency-check-summary_${TIMESTAMP}.txt\"\n\n# Create standard paths for easier reference\nSTANDARD_HTML_REPORT=\"$DEPENDENCY_CHECK_FOLDER/dependency-check-report.html\"\nSTANDARD_JSON_REPORT=\"$DEPENDENCY_CHECK_FOLDER/dependency-check-report.json\"\nSTANDARD_XML_REPORT=\"$DEPENDENCY_CHECK_FOLDER/dependency-check-report.xml\"\nSTANDARD_SUMMARY=\"$DEPENDENCY_CHECK_FOLDER/dependency-check-summary.txt\"\n\necho \"Running OWASP Dependency Check scan...\"\n\n# Run Dependency Check with proper path formatting\nexport JAVA_OPTS=\"-Xmx3G\"\n./dependency-check/bin/dependency-check.sh \\\n    --scan composer.json composer.lock package.json package-lock.json \\\n    --enableExperimental \\\n    --project \"eVAT Application v$BUILD_VERSION\" \\\n    --out \"$REPORT_BASE\" \\\n    -f ALL\n\n# Improved report verification - check multiple locations\nif [ -f \"$REPORT_BASE.html\" ]; then\n  echo \"Found report at $REPORT_BASE.html\"\n  REPORT_FOUND=\"true\"\nelif [ -f \"${REPORT_BASE}-junit.xml\" ]; then\n  echo \"Found JUnit report at ${REPORT_BASE}-junit.xml\"\n  REPORT_FOUND=\"true\"\nelse\n  # Try to find the report using find command\n  echo \"Searching for generated reports...\"\n  REPORT_PATH=$(find \"$REPORT_DIR\" -name \"dependency-check-report.html\" | head -n 1)\n  \n  if [ -n \"$REPORT_PATH\" ]; then\n    echo \"Found report at $REPORT_PATH\"\n    REPORT_BASE=$(echo \"$REPORT_PATH\" | sed 's/\\.html$//')\n    REPORT_FOUND=\"true\"\n  else\n    REPORT_FOUND=\"false\"\n    echo \"Warning: Could not locate dependency check reports\"\n  fi\nfi\n\n# Copy reports if found\nif [ \"$REPORT_FOUND\" = \"true\" ]; then\n  echo \"Dependency Check completed successfully. Copying reports to consolidated artifact folder...\"\n  \n  # First, copy reports to the consolidated structure\n  # Copy HTML report\n  find \"$REPORT_DIR\" -name \"dependency-check*.html\" -exec cp {} \"$CONSOLIDATED_REPORT_BASE.html\" \\; || echo \"No HTML report found to copy\"\n  # Copy JSON report\n  find \"$REPORT_DIR\" -name \"dependency-check*.json\" -exec cp {} \"$CONSOLIDATED_REPORT_BASE.json\" \\; || echo \"No JSON report found to copy\"\n  # Copy XML report\n  find \"$REPORT_DIR\" -name \"dependency-check*.xml\" -exec cp {} \"$CONSOLIDATED_REPORT_BASE.xml\" \\; || echo \"No XML report found to copy\"\n  # Copy CSV report\n  find \"$REPORT_DIR\" -name \"dependency-check*.csv\" -exec cp {} \"$CONSOLIDATED_REPORT_BASE.csv\" \\; || echo \"No CSV report found to copy\"\n  # Copy SARIF report\n  find \"$REPORT_DIR\" -name \"dependency-check*.sarif\" -exec cp {} \"$CONSOLIDATED_REPORT_BASE.sarif\" \\; || echo \"No SARIF report found to copy\"\n  # Copy JUnit report\n  find \"$REPORT_DIR\" -name \"*-junit.xml\" -exec cp {} \"$CONSOLIDATED_REPORT_BASE-junit.xml\" \\; || echo \"No JUnit report found to copy\"\n  \n  # Copy to standard locations for easier reference\n  find \"$REPORT_DIR\" -name \"dependency-check*.html\" -exec cp {} \"$STANDARD_HTML_REPORT\" \\; || echo \"No HTML report found to copy\"\n  find \"$REPORT_DIR\" -name \"dependency-check*.json\" -exec cp {} \"$STANDARD_JSON_REPORT\" \\; || echo \"No JSON report found to copy\"\n  find \"$REPORT_DIR\" -name \"dependency-check*.xml\" -exec cp {} \"$STANDARD_XML_REPORT\" \\; || echo \"No XML report found to copy\"\n  \n  # Create a summary text file\n  echo \"OWASP Dependency Check Summary - $(date)\" > \"$CONSOLIDATED_SUMMARY_FILE\"\n  echo \"==================================\" >> \"$CONSOLIDATED_SUMMARY_FILE\"\n  echo \"Build: v$BUILD_VERSION\" >> \"$CONSOLIDATED_SUMMARY_FILE\"\n  echo \"Timestamp: ${TIMESTAMP}\" >> \"$CONSOLIDATED_SUMMARY_FILE\"\n  echo \"\" >> \"$CONSOLIDATED_SUMMARY_FILE\"\n  \n  # Extract vulnerability counts from an XML report (find it first)\n  XML_REPORT=$(find \"$REPORT_DIR\" -name \"dependency-check-report.xml\" | head -n 1)\n  if [ -n \"$XML_REPORT\" ]; then\n    echo \"Vulnerability Summary:\" >> \"$CONSOLIDATED_SUMMARY_FILE\"\n    grep -o '<vulnerabilities>[0-9]*</vulnerabilities>' \"$XML_REPORT\" | sed 's/<vulnerabilities>//g' | sed 's/<\\/vulnerabilities>//g' | awk '{print \"Total vulnerabilities: \" $1}' >> \"$CONSOLIDATED_SUMMARY_FILE\" || echo \"No vulnerability count found\"\n    grep -o '<severity>[^<]*</severity>' \"$XML_REPORT\" | sort | uniq -c | sed 's/<severity>//g' | sed 's/<\\/severity>//g' | awk '{print $2 \" severity: \" $1}' >> \"$CONSOLIDATED_SUMMARY_FILE\" || echo \"No severity data found\"\n  else\n    echo \"XML report not found for summary extraction\" >> \"$CONSOLIDATED_SUMMARY_FILE\"\n  fi\n  \n  # Copy summary to standard location\n  cp \"$CONSOLIDATED_SUMMARY_FILE\" \"$STANDARD_SUMMARY\" || true\n  \n  # Also maintain backward compatibility with existing pipeline\n  # Create copies with standard names for easier access to the original locations\n  find \"$REPORT_DIR\" -name \"dependency-check*.html\" -exec cp {} \"$(Build.ArtifactStagingDirectory)/dependency-check-report.html\" \\; || echo \"No HTML report found to copy\"\n  find \"$REPORT_DIR\" -name \"dependency-check*.json\" -exec cp {} \"$(Build.ArtifactStagingDirectory)/dependency-check-report.json\" \\; || echo \"No JSON report found to copy\"\n  find \"$REPORT_DIR\" -name \"dependency-check*.xml\" -exec cp {} \"$(Build.ArtifactStagingDirectory)/dependency-check-report.xml\" \\; || echo \"No XML report found to copy\"\n  find \"$REPORT_DIR\" -name \"dependency-check*.csv\" -exec cp {} \"$(Build.ArtifactStagingDirectory)/dependency-check-report.csv\" \\; || echo \"No CSV report found to copy\"\n  find \"$REPORT_DIR\" -name \"dependency-check*.sarif\" -exec cp {} \"$(Build.ArtifactStagingDirectory)/dependency-check-report.sarif\" \\; || echo \"No SARIF report found to copy\"\n  find \"$REPORT_DIR\" -name \"*-junit.xml\" -exec cp {} \"$(Build.ArtifactStagingDirectory)/dependency-check-junit.xml\" \\; || echo \"No JUnit report found to copy\"\n  \n  # Copy the consolidated summary to the old location\n  cp \"$CONSOLIDATED_SUMMARY_FILE\" \"$(Build.ArtifactStagingDirectory)/dependency-check-summary.txt\" || true\n  \n  echo \"Dependency check reports copied to consolidated artifact folder and original locations.\"\nelse\n  echo \"Warning: Dependency Check may have completed but reports were not found. Creating placeholder files...\"\n  \n  # Create placeholder files in the consolidated structure\n  echo \"<html><body><h1>Dependency Check Report Not Found</h1><p>The scan may have failed or reports were saved to an unexpected location.</p></body></html>\" > \"$STANDARD_HTML_REPORT\"\n  echo '{\"failed\": true, \"message\": \"Report not found\"}' > \"$STANDARD_JSON_REPORT\"\n  \n  # Create placeholder summary\n  echo \"OWASP Dependency Check Summary - $(date)\" > \"$STANDARD_SUMMARY\"\n  echo \"==================================\" >> \"$STANDARD_SUMMARY\"\n  echo \"Build: v$BUILD_VERSION\" >> \"$STANDARD_SUMMARY\"\n  echo \"Timestamp: ${TIMESTAMP}\" >> \"$STANDARD_SUMMARY\"\n  echo \"STATUS: REPORT NOT FOUND\" >> \"$STANDARD_SUMMARY\"\n  \n  # Also create placeholder files in original locations for backward compatibility\n  echo \"<html><body><h1>Dependency Check Report Not Found</h1><p>The scan may have failed or reports were saved to an unexpected location.</p></body></html>\" > \"$(Build.ArtifactStagingDirectory)/dependency-check-report.html\"\n  echo '{\"failed\": true, \"message\": \"Report not found\"}' > \"$(Build.ArtifactStagingDirectory)/dependency-check-report.json\"\n  cp \"$STANDARD_SUMMARY\" \"$(Build.ArtifactStagingDirectory)/dependency-check-summary.txt\"\nfi\n\necho \"Dependency Check step completed. Files organized in: $DEPENDENCY_CHECK_FOLDER\"\n\n# List created files for verification\necho \"Created files in consolidated structure:\"\nfind \"$DEPENDENCY_CHECK_FOLDER\" -type f | sort\n"
    - task: PublishBuildArtifacts@1
      displayName: 'Publish Dependency Check Results'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$(buildVersion)/dependency-check'
        artifactName: '$(artifactsFolder)'
        artifactType: 'Container'
      continueOnError: true
- stage: DAST
  displayName: 'DAST – Enhanced OWASP ZAP Security Scans'
  dependsOn:
  - StaticAnalysis
  jobs:
  - job: ZapScan
    displayName: 'Run OWASP ZAP Security Suite'
    pool:
      vmImage: 'ubuntu-latest'
    variables:
    - name: WORK_PATH
      value: $(Agent.BuildDirectory)/zap-output
    - name: ARTIFACT_FOLDER
      value: $(Build.ArtifactStagingDirectory)/eVAT_Pipeline_$(buildVersion)
    steps:
    - task: CmdLine@2
      displayName: 'Create Consolidated Artifact Structure'
      inputs:
        script: |
          # Define timestamp
          TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

          # Create main consolidated artifact folder
          mkdir -p $(ARTIFACT_FOLDER)

          # Create structured ZAP scan folders with separate scan type subfolders
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/reports
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/alerts
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/summaries
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/baseline
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/passive
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/api
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/authenticated
          mkdir -p $(ARTIFACT_FOLDER)/zap-scan/compliance
          # New folder for security trends
          mkdir -p $(ARTIFACT_FOLDER)/security-trends

          # Create work paths
          mkdir -p $(WORK_PATH)

          # Keep backward compatibility
          mkdir -p $(reportPath)/zap-scan/v$(buildVersion)
          mkdir -p $(findingsHistoryPath)
          mkdir -p $(Build.ArtifactStagingDirectory)/zap-scan

          # Initialize zap findings file
          if [ ! -f "$(findingsHistoryPath)/zap_findings.txt" ]; then
            touch $(findingsHistoryPath)/zap_findings.txt
          fi

          echo "Created directory structure:"
          find $(ARTIFACT_FOLDER) -type d | sort
      continueOnError: true
    - task: CmdLine@2
      displayName: 'Download and Extract Latest OWASP ZAP'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\necho \"Downloading ZAP v$(zapVersion)...\"\n\n# Try multiple download formats\nZAP_VERSION=\"$(zapVersion)\"\nDOWNLOAD_URL=\"https://github.com/zaproxy/zaproxy/releases/download/v$ZAP_VERSION/ZAP_${ZAP_VERSION}_Crossplatform.zip\"\n\n# Use GitHub token for authentication if available\nif [ -n \"$(GITHUB_TOKEN)\" ]; then\n  echo \"Using GitHub API with token authentication...\"\n  curl -H \"Authorization: token $(GITHUB_TOKEN)\" -L \"$DOWNLOAD_URL\" -o zap.zip\nelse\n  # Fallback to direct download without token\n  echo \"GITHUB_TOKEN not available, using direct download...\"\n  curl -L \"$DOWNLOAD_URL\" -o zap.zip\nfi\n\n# If first attempt fails, try alternative formats\nif [ ! -s zap.zip ]; then\n  echo \"First download attempt failed. Trying alternative formats...\"\n  \n  # Try without _Crossplatform suffix\n  curl -L \"https://github.com/zaproxy/zaproxy/releases/download/v$ZAP_VERSION/ZAP_${ZAP_VERSION}.zip\" -o zap.zip || \\\n  # Try latest release as last resort\n  curl -L \"https://github.com/zaproxy/zaproxy/releases/latest/download/ZAP_Crossplatform.zip\" -o zap.zip\nfi\n\n# Verify download success\nif [ ! -s zap.zip ]; then\n  echo \"All download attempts failed. Creating placeholder files.\"\n  \n  # Create placeholder summary file\n  ZAP_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/zap-summary_${TIMESTAMP}.txt\"\n  echo \"OWASP ZAP Scan Summary - $(date)\" > \"$ZAP_SUMMARY_FILE\"\n  echo \"============================\" >> \"$ZAP_SUMMARY_FILE\"\n  echo \"Build: v$(buildVersion)\" >> \"$ZAP_SUMMARY_FILE\"\n  echo \"ERROR: ZAP download failed. No scan was performed.\" >> \"$ZAP_SUMMARY_FILE\"\n  \n  # Create standard path reference\n  cp \"$ZAP_SUMMARY_FILE\" \"$(ARTIFACT_FOLDER)/zap-scan/zap-summary.txt\"\n  \n  # Create placeholder report\n  echo \"<html><body><h1>ZAP Scan Report Not Available</h1><p>ZAP download failed. No scan was performed.</p></body></html>\" > \"$(ARTIFACT_FOLDER)/zap-scan/reports/zap-report_${TIMESTAMP}.html\"\n  cp \"$(ARTIFACT_FOLDER)/zap-scan/reports/zap-report_${TIMESTAMP}.html\" \"$(ARTIFACT_FOLDER)/zap-scan/zap-report.html\"\n  \n  # Maintain backward compatibility\n  cp \"$(ARTIFACT_FOLDER)/zap-scan/zap-summary.txt\" \"$(Build.ArtifactStagingDirectory)/zap-scan/zap-summary.txt\"\n  cp \"$(ARTIFACT_FOLDER)/zap-scan/zap-report.html\" \"$(Build.ArtifactStagingDirectory)/zap-scan/zap-report.html\"\n  \n  echo \"Created placeholder ZAP files due to download failure.\"\n  exit 0\nfi\n\n# Extract ZAP\nunzip -q zap.zip -d $(HOME)/zap\nfind $(HOME)/zap -name \"zap.sh\" -type f -exec chmod +x {} \\;\necho \"ZAP extracted successfully to $(HOME)/zap\"\n\n# Install additional add-ons for enhanced scanning\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Installing additional ZAP add-ons for enhanced scanning...\"\n\n# Install add-ons for enhanced scanning capabilities\n$ZAP_SCRIPT -cmd -silent -addoninstall ascanrulesBeta\n$ZAP_SCRIPT -cmd -silent -addoninstall ascanrulesAlpha\n$ZAP_SCRIPT -cmd -silent -addoninstall pscanrulesBeta\n$ZAP_SCRIPT -cmd -silent -addoninstall pscanrulesAlpha\n$ZAP_SCRIPT -cmd -silent -addoninstall sqliplugin\n$ZAP_SCRIPT -cmd -silent -addoninstall domxss\n$ZAP_SCRIPT -cmd -silent -addoninstall scripts\n$ZAP_SCRIPT -cmd -silent -addoninstall alertFilters\n$ZAP_SCRIPT -cmd -silent -addoninstall oast\n$ZAP_SCRIPT -cmd -silent -addoninstall openapi\n$ZAP_SCRIPT -cmd -silent -addoninstall graphql\n$ZAP_SCRIPT -cmd -silent -addoninstall retire\n$ZAP_SCRIPT -cmd -silent -addoninstall wappalyzer\n\necho \"ZAP add-ons installed successfully.\"\n\n# Create config file with advanced settings including rate limiting protection\ncat > $(WORK_PATH)/zap-config.conf << EOL\nscanner.attackOnStart=true\nscanner.scanHeadersAllRequests=true\nscanner.threadPerHost=5\nspider.maxDuration=120\nspider.maxDepth=10\nspider.threadCount=5\n# Enhance scanning depth\nscanner.analysePerHost=true\najaxSpider.clickDefaultElems=true\najaxSpider.clickElemsOnce=true\najaxSpider.eventWait=5000\najaxSpider.maxCrawlDepth=10\najaxSpider.maxDuration=10\n# Rate limiting protection\nconnection.timeoutInSecs=60\nconnection.slowConnectionTimeoutInSecs=120\nconnection.requestTimeout=30\nhttpsender.corkscrewyPerHost=on\nhttpsender.corkscrewyRandomFactor=0.5\nEOL\n\necho \"ZAP configuration file created with rate limiting protection.\"\n"
    - task: CmdLine@2
      displayName: 'Run ZAP Baseline Scan'
      continueOnError: true
      env:
        SCAN_URL: 'https://app.evat.com/'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP script path\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Running ZAP v$(zapVersion) Baseline Scan from: $ZAP_SCRIPT\"\n\n# Define report paths in the consolidated structure\nBASELINE_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/baseline/baseline-report_${TIMESTAMP}.html\"\nBASELINE_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/baseline-summary_${TIMESTAMP}.txt\"\n\n# Standard paths\nBASELINE_STANDARD_REPORT=\"$(ARTIFACT_FOLDER)/zap-scan/baseline-report.html\"\nBASELINE_STANDARD_SUMMARY=\"$(ARTIFACT_FOLDER)/zap-scan/baseline-summary.txt\"\n\necho \"Running ZAP Baseline scan against: $(SCAN_URL)\"\n\n# Run Baseline scan - uses only passive scanning to find basic issues\n$ZAP_SCRIPT -cmd \\\n  -silent \\\n  -quickurl $(SCAN_URL) \\\n  -quickout \"$BASELINE_REPORT_FILE\" \\\n  -quickprogress\n\n# Create summary file\necho \"OWASP ZAP Baseline Scan Summary - $(date)\" > \"$BASELINE_SUMMARY_FILE\"\necho \"============================\" >> \"$BASELINE_SUMMARY_FILE\"\necho \"Build: v$(buildVersion)\" >> \"$BASELINE_SUMMARY_FILE\"\necho \"Target URL: $(SCAN_URL)\" >> \"$BASELINE_SUMMARY_FILE\"\necho \"Timestamp: ${TIMESTAMP}\" >> \"$BASELINE_SUMMARY_FILE\"\necho \"\" >> \"$BASELINE_SUMMARY_FILE\"\n\n# Count alerts by severity for the summary\nif [ -f \"$BASELINE_REPORT_FILE\" ]; then\n  echo \"Baseline scan completed successfully.\"\n  \n  # Copy to standard locations\n  cp \"$BASELINE_REPORT_FILE\" \"$BASELINE_STANDARD_REPORT\"\n  \n  # Extract alerts for the summary\n  echo \"Alert Summary:\" >> \"$BASELINE_SUMMARY_FILE\"\n  grep -o 'class=\"risk-[^\"]*\"' \"$BASELINE_REPORT_FILE\" | sort | uniq -c | \\\n  sed 's/class=\"risk-//g' | sed 's/\"//g' | \\\n  awk '{print $2 \" risk alerts: \" $1}' >> \"$BASELINE_SUMMARY_FILE\" || echo \"No alerts found\"\n  \n  # Copy summary to standard location\n  cp \"$BASELINE_SUMMARY_FILE\" \"$BASELINE_STANDARD_SUMMARY\"\nelse\n  echo \"Baseline scan failed to produce a report.\"\nfi\n"
    - task: CmdLine@2
      displayName: 'Run ZAP Full Active Scan with Spider'
      continueOnError: true
      env:
        SCAN_URL: 'https://app.evat.com/'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP script path\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Running ZAP v$(zapVersion) Full Active Scan from: $ZAP_SCRIPT\"\n\n# Define report paths in the consolidated structure\nZAP_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/reports/zap-report_${TIMESTAMP}.html\"\nZAP_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/zap-summary_${TIMESTAMP}.txt\"\n\n# Standard paths\nZAP_STANDARD_REPORT=\"$(ARTIFACT_FOLDER)/zap-scan/zap-report.html\"\nZAP_STANDARD_SUMMARY=\"$(ARTIFACT_FOLDER)/zap-scan/zap-summary.txt\"\n\n# Legacy path\nLEGACY_REPORT_FILE=\"$(WORK_PATH)/zap-report_${TIMESTAMP}.html\"\n\n# Run ZAP full scan with enhanced configuration\n$ZAP_SCRIPT -cmd \\\n  -silent \\\n  -config api.disablekey=true \\\n  -configfile $(WORK_PATH)/zap-config.conf \\\n  -spider $(SCAN_URL) \\\n  -ajaxSpider $(SCAN_URL) \\\n  -scan \\\n  -scanpolicy \"Default Policy\" \\\n  -quickout \"$LEGACY_REPORT_FILE\" \\\n  -quickprogress\n\n# Create summary file\necho \"OWASP ZAP Full Scan Summary - $(date)\" > \"$ZAP_SUMMARY_FILE\"\necho \"============================\" >> \"$ZAP_SUMMARY_FILE\"\necho \"Build: v$(buildVersion)\" >> \"$ZAP_SUMMARY_FILE\"\necho \"Target URL: $(SCAN_URL)\" >> \"$ZAP_SUMMARY_FILE\"\necho \"Timestamp: ${TIMESTAMP}\" >> \"$ZAP_SUMMARY_FILE\"\necho \"\" >> \"$ZAP_SUMMARY_FILE\"\n  \n# Copy reports to all required locations\nif [ -f \"$LEGACY_REPORT_FILE\" ]; then\n  # Copy to consolidated structure\n  cp \"$LEGACY_REPORT_FILE\" \"$ZAP_REPORT_FILE\"\n  cp \"$ZAP_REPORT_FILE\" \"$ZAP_STANDARD_REPORT\"\n  cp \"$ZAP_SUMMARY_FILE\" \"$ZAP_STANDARD_SUMMARY\"\n  \n  # Copy to legacy locations for backward compatibility\n  cp \"$LEGACY_REPORT_FILE\" \"$(reportPath)/zap-scan/v$(buildVersion)/\"\n  cp \"$LEGACY_REPORT_FILE\" \"$(Build.ArtifactStagingDirectory)/zap-scan/zap-report.html\"\n  cp \"$ZAP_SUMMARY_FILE\" \"$(Build.ArtifactStagingDirectory)/zap-scan/zap-summary.txt\"\n  \n  # Count alerts by severity for the summary\n  echo \"Alert Summary:\" >> \"$ZAP_SUMMARY_FILE\"\n  grep -o 'class=\"risk-[^\"]*\"' \"$ZAP_REPORT_FILE\" | sort | uniq -c | \\\n  sed 's/class=\"risk-//g' | sed 's/\"//g' | \\\n  awk '{print $2 \" risk alerts: \" $1}' >> \"$ZAP_SUMMARY_FILE\" || echo \"Could not extract alert counts\"\n  \n  # Update the standard summary with alert counts\n  cp \"$ZAP_SUMMARY_FILE\" \"$ZAP_STANDARD_SUMMARY\"\n  cp \"$ZAP_SUMMARY_FILE\" \"$(Build.ArtifactStagingDirectory)/zap-scan/zap-summary.txt\"\nelse\n  echo \"ZAP scan failed to produce a report.\"\nfi\n"
    - task: CmdLine@2
      displayName: 'Run ZAP Passive Scan with Analysis'
      continueOnError: true
      env:
        SCAN_URL: 'https://app.evat.com/'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP script path\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Running ZAP v$(zapVersion) Passive Scan from: $ZAP_SCRIPT\"\n\n# Define report paths in the consolidated structure\nPASSIVE_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/passive/passive-scan-report_${TIMESTAMP}.html\"\nPASSIVE_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/passive-scan-summary_${TIMESTAMP}.txt\"\n\n# Standard paths\nPASSIVE_STANDARD_REPORT=\"$(ARTIFACT_FOLDER)/zap-scan/passive-scan-report.html\"\nPASSIVE_STANDARD_SUMMARY=\"$(ARTIFACT_FOLDER)/zap-scan/passive-scan-summary.txt\"\n\necho \"Running ZAP Passive scan against: $(SCAN_URL)\"\n\n# Run Passive scan with retirement.js scanner\n$ZAP_SCRIPT -cmd \\\n  -silent \\\n  -quickurl $(SCAN_URL) \\\n  -pscan \\\n  -quickout \"$PASSIVE_REPORT_FILE\" \\\n  -quickprogress\n\n# Create summary file\necho \"OWASP ZAP Passive Scan Summary - $(date)\" > \"$PASSIVE_SUMMARY_FILE\"\necho \"============================\" >> \"$PASSIVE_SUMMARY_FILE\"\necho \"Build: v$(buildVersion)\" >> \"$PASSIVE_SUMMARY_FILE\"\necho \"Target URL: $(SCAN_URL)\" >> \"$PASSIVE_SUMMARY_FILE\"\necho \"Timestamp: ${TIMESTAMP}\" >> \"$PASSIVE_SUMMARY_FILE\"\necho \"\" >> \"$PASSIVE_SUMMARY_FILE\"\n\n# Count alerts by severity for the summary\nif [ -f \"$PASSIVE_REPORT_FILE\" ]; then\n  echo \"Passive scan completed successfully.\"\n  \n  # Copy to standard locations\n  cp \"$PASSIVE_REPORT_FILE\" \"$PASSIVE_STANDARD_REPORT\"\n  \n  # Extract alerts for the summary\n  echo \"Alert Summary:\" >> \"$PASSIVE_SUMMARY_FILE\"\n  grep -o 'class=\"risk-[^\"]*\"' \"$PASSIVE_REPORT_FILE\" | sort | uniq -c | \\\n  sed 's/class=\"risk-//g' | sed 's/\"//g' | \\\n  awk '{print $2 \" risk alerts: \" $1}' >> \"$PASSIVE_SUMMARY_FILE\" || echo \"No alerts found\"\n  \n  # Copy summary to standard location\n  cp \"$PASSIVE_SUMMARY_FILE\" \"$PASSIVE_STANDARD_SUMMARY\"\nelse\n  echo \"Passive scan failed to produce a report.\"\nfi\n"
    - task: CmdLine@2
      displayName: 'Run ZAP OWASP Top 10 Compliance Scan'
      continueOnError: true
      env:
        SCAN_URL: 'https://app.evat.com/'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP script path\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Running ZAP v$(zapVersion) OWASP Top 10 Compliance Scan from: $ZAP_SCRIPT\"\n\n# Define report paths in the consolidated structure\nCOMPLIANCE_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/compliance/owasp-top10-report_${TIMESTAMP}.html\"\nCOMPLIANCE_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/owasp-top10-summary_${TIMESTAMP}.txt\"\n\n# Standard paths\nCOMPLIANCE_STANDARD_REPORT=\"$(ARTIFACT_FOLDER)/zap-scan/owasp-top10-report.html\"\nCOMPLIANCE_STANDARD_SUMMARY=\"$(ARTIFACT_FOLDER)/zap-scan/owasp-top10-summary.txt\"\n\n# Create OWASP Top 10 specific context\ncat > $(WORK_PATH)/owasp-top10.context << EOL\n<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<configuration>\n    <context>\n        <name>OWASP-Top10</name>\n        <desc>OWASP Top 10 Context</desc>\n        <inscope>true</inscope>\n        <incregexes>https://app\\.evat\\.com.*</incregexes>\n    </context>\n</configuration>\nEOL\n\necho \"Running OWASP Top 10 compliance scan against: $(SCAN_URL)\"\n\n# Run OWASP Top 10 specific scan\n$ZAP_SCRIPT -cmd \\\n  -silent \\\n  -config api.disablekey=true \\\n  -contextfile $(WORK_PATH)/owasp-top10.context \\\n  -spider $(SCAN_URL) \\\n  -ajaxSpider $(SCAN_URL) \\\n  -scan \\\n  -scanpolicy \"Default Policy\" \\\n  -quickout \"$COMPLIANCE_REPORT_FILE\" \\\n  -quickprogress\n\n# Create summary file\necho \"OWASP ZAP Top 10 Compliance Scan Summary - $(date)\" > \"$COMPLIANCE_SUMMARY_FILE\"\necho \"=========================================\" >> \"$COMPLIANCE_SUMMARY_FILE\"\necho \"Build: v$(buildVersion)\" >> \"$COMPLIANCE_SUMMARY_FILE\"\necho \"Target URL: $(SCAN_URL)\" >> \"$COMPLIANCE_SUMMARY_FILE\"\necho \"Timestamp: ${TIMESTAMP}\" >> \"$COMPLIANCE_SUMMARY_FILE\"\necho \"\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n\n# Check if report was generated\nif [ -f \"$COMPLIANCE_REPORT_FILE\" ]; then\n  echo \"OWASP Top 10 compliance scan completed successfully.\"\n  \n  # Copy to standard locations\n  cp \"$COMPLIANCE_REPORT_FILE\" \"$COMPLIANCE_STANDARD_REPORT\"\n  \n  # Extract alerts and map to OWASP Top 10 categories\n  echo \"OWASP Top 10 Alert Summary:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  echo \"A01 - Broken Access Control:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  grep -B 5 -A 5 'Broken Access Control' \"$COMPLIANCE_REPORT_FILE\" | grep -o 'class=\"risk-[^\"]*\"' | sort | uniq -c >> \"$COMPLIANCE_SUMMARY_FILE\" || echo \"  No findings\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  \n  echo \"A02 - Cryptographic Failures:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  grep -B 5 -A 5 'Cryptographic' \"$COMPLIANCE_REPORT_FILE\" | grep -o 'class=\"risk-[^\"]*\"' | sort | uniq -c >> \"$COMPLIANCE_SUMMARY_FILE\" || echo \"  No findings\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  \n  echo \"A03 - Injection:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  grep -B 5 -A 5 'SQL Injection\\|XSS\\|Command Injection' \"$COMPLIANCE_REPORT_FILE\" | grep -o 'class=\"risk-[^\"]*\"' | sort | uniq -c >> \"$COMPLIANCE_SUMMARY_FILE\" || echo \"  No findings\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  \n  echo \"A04 - Insecure Design:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  grep -B 5 -A 5 'Insecure Design' \"$COMPLIANCE_REPORT_FILE\" | grep -o 'class=\"risk-[^\"]*\"' | sort | uniq -c >> \"$COMPLIANCE_SUMMARY_FILE\" || echo \"  No findings\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  \n  echo \"A05 - Security Misconfiguration:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  grep -B 5 -A 5 'Security Misconfiguration\\|Information Leak\\|Server Configuration' \"$COMPLIANCE_REPORT_FILE\" | grep -o 'class=\"risk-[^\"]*\"' | sort | uniq -c >> \"$COMPLIANCE_SUMMARY_FILE\" || echo \"  No findings\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  \n  echo \"All OWASP Categories - Overall Summary:\" >> \"$COMPLIANCE_SUMMARY_FILE\"\n  grep -o 'class=\"risk-[^\"]*\"' \"$COMPLIANCE_REPORT_FILE\" | sort | uniq -c | \\\n  sed 's/class=\"risk-//g' | sed 's/\"//g' | \\\n  awk '{print $2 \" risk alerts: \" $1}' >> \"$COMPLIANCE_SUMMARY_FILE\" || echo \"No alerts found\"\n  \n  # Copy summary to standard location\n  cp \"$COMPLIANCE_SUMMARY_FILE\" \"$COMPLIANCE_STANDARD_SUMMARY\"\nelse\n  echo \"OWASP Top 10 compliance scan failed to produce a report.\"\nfi\n"
    - task: CmdLine@2
      displayName: 'Run ZAP API Scan'
      continueOnError: true
      env:
        API_URL: 'https://api.evat.com/'
        OPENAPI_URL: 'https://api.evat.com/swagger/v1/swagger.json'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP script path\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Running ZAP v$(zapVersion) API Scan from: $ZAP_SCRIPT\"\n\n# Define report paths in the consolidated structure\nAPI_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/api/api-scan-report_${TIMESTAMP}.html\"\nAPI_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/api-scan-summary_${TIMESTAMP}.txt\"\n\n# Standard paths\nAPI_STANDARD_REPORT=\"$(ARTIFACT_FOLDER)/zap-scan/api-scan-report.html\"\nAPI_STANDARD_SUMMARY=\"$(ARTIFACT_FOLDER)/zap-scan/api-scan-summary.txt\"\n\n# Skip if API URL is not defined or accessible\nAPI_URL=\"${API_URL:-}\"\nOPENAPI_URL=\"${OPENAPI_URL:-}\"\n\nif [ -z \"$API_URL\" ] || [ -z \"$OPENAPI_URL\" ]; then\n  echo \"API URL or OpenAPI URL not defined, skipping API scan.\"\n  echo \"OWASP ZAP API Scan - $(date)\" > \"$API_SUMMARY_FILE\"\n  echo \"============================\" >> \"$API_SUMMARY_FILE\"\n  echo \"SKIPPED: API URL or OpenAPI URL not defined\" >> \"$API_SUMMARY_FILE\"\n  cp \"$API_SUMMARY_FILE\" \"$API_STANDARD_SUMMARY\"\n  exit 0\nfi\n\n# Check if API URL is accessible\nif ! curl --silent --head --fail \"$API_URL\" > /dev/null; then\n  echo \"API URL is not accessible, skipping API scan.\"\n  echo \"OWASP ZAP API Scan - $(date)\" > \"$API_SUMMARY_FILE\"\n  echo \"============================\" >> \"$API_SUMMARY_FILE\"\n  echo \"SKIPPED: API URL not accessible\" >> \"$API_SUMMARY_FILE\"\n  cp \"$API_SUMMARY_FILE\" \"$API_STANDARD_SUMMARY\"\n  exit 0\nfi\n\necho \"Running ZAP API scan against: $API_URL using OpenAPI definition at $OPENAPI_URL\"\n\n# Run API scan\n$ZAP_SCRIPT -cmd \\\n  -silent \\\n  -config api.disablekey=true \\\n  -configfile $(WORK_PATH)/zap-config.conf \\\n  -target $API_URL \\\n  -openapi $OPENAPI_URL \\\n  -quickout \"$API_REPORT_FILE\" \\\n  -quickprogress\n\n# Create summary file\necho \"OWASP ZAP API Scan Summary - $(date)\" > \"$API_SUMMARY_FILE\"\necho \"============================\" >> \"$API_SUMMARY_FILE\"\necho \"Build: v$(buildVersion)\" >> \"$API_SUMMARY_FILE\"\necho \"API URL: $API_URL\" >> \"$API_SUMMARY_FILE\"\necho \"OpenAPI URL: $OPENAPI_URL\" >> \"$API_SUMMARY_FILE\"\necho \"Timestamp: ${TIMESTAMP}\" >> \"$API_SUMMARY_FILE\"\necho \"\" >> \"$API_SUMMARY_FILE\"\n\n# Check if report was generated\nif [ -f \"$API_REPORT_FILE\" ]; then\n  echo \"API scan completed successfully.\"\n  \n  # Copy to standard locations\n  cp \"$API_REPORT_FILE\" \"$API_STANDARD_REPORT\"\n  \n  # Extract alerts for the summary\n  echo \"Alert Summary:\" >> \"$API_SUMMARY_FILE\"\n  grep -o 'class=\"risk-[^\"]*\"' \"$API_REPORT_FILE\" | sort | uniq -c | \\\n  sed 's/class=\"risk-//g' | sed 's/\"//g' | \\\n  awk '{print $2 \" risk alerts: \" $1}' >> \"$API_SUMMARY_FILE\" || echo \"No alerts found\"\n  \n  # Copy summary to standard location\n  cp \"$API_SUMMARY_FILE\" \"$API_STANDARD_SUMMARY\"\nelse\n  echo \"API scan failed to produce a report.\"\nfi\n"
    - task: CmdLine@2
      displayName: 'Run ZAP Authenticated Scan'
      continueOnError: true
      env:
        LOGIN_URL: 'https://app.evat.com/login'
        PROTECTED_URL: 'https://app.evat.com/dashboard'
        TEST_USERNAME: '$(testUsername)'
        TEST_PASSWORD: '$(testPassword)'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP script path\nZAP_SCRIPT=$(find $(HOME)/zap -name \"zap.sh\" -type f | head -n 1)\necho \"Running ZAP v$(zapVersion) Authenticated Scan from: $ZAP_SCRIPT\"\n\n# Define report paths in the consolidated structure\nAUTH_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/authenticated/auth-scan-report_${TIMESTAMP}.html\"\nAUTH_SUMMARY_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/summaries/auth-scan-summary_${TIMESTAMP}.txt\"\n\n# Standard paths\nAUTH_STANDARD_REPORT=\"$(ARTIFACT_FOLDER)/zap-scan/auth-scan-report.html\"\nAUTH_STANDARD_SUMMARY=\"$(ARTIFACT_FOLDER)/zap-scan/auth-scan-summary.txt\"\n\n# Skip if login credentials are not defined\nLOGIN_URL=\"${LOGIN_URL:-}\"\nPROTECTED_URL=\"${PROTECTED_URL:-}\"\nTEST_USERNAME=\"${TEST_USERNAME:-}\"\nTEST_PASSWORD=\"${TEST_PASSWORD:-}\"\n\nif [ -z \"$LOGIN_URL\" ] || [ -z \"$PROTECTED_URL\" ] || [ -z \"$TEST_USERNAME\" ] || [ -z \"$TEST_PASSWORD\" ]; then\n  echo \"Login credentials or URLs not defined, skipping authenticated scan.\"\n  echo \"OWASP ZAP Authenticated Scan - $(date)\" > \"$AUTH_SUMMARY_FILE\"\n  echo \"============================\" >> \"$AUTH_SUMMARY_FILE\"\n  echo \"SKIPPED: Login credentials or URLs not defined\" >> \"$AUTH_SUMMARY_FILE\"\n  cp \"$AUTH_SUMMARY_FILE\" \"$AUTH_STANDARD_SUMMARY\"\n  exit 0\nfi\n\n# Create auth script for ZAP\nmkdir -p $(WORK_PATH)/scripts\ncat > $(WORK_PATH)/scripts/auth.js << EOL\n// Login form handling script for ZAP\nvar HttpSender = Java.type('org.parosproxy.paros.network.HttpSender');\nvar HttpMessage = Java.type('org.parosproxy.paros.network.HttpMessage');\nvar URI = Java.type('org.apache.commons.httpclient.URI');\n\nfunction authenticate(helper, paramsValues, credentials) {\n    println(\"Authenticating via JavaScript script...\");\n    \n    // Prepare login request\n    var loginUri = new URI(\"${LOGIN_URL}\", false);\n    var requestBody = \"username=\" + encodeURIComponent(credentials.getParam(\"username\")) +\n                    \"&password=\" + encodeURIComponent(credentials.getParam(\"password\")) +\n                    \"&submit=Login\";\n    \n    // Send login request\n    var loginMsg = new HttpMessage(loginUri);\n    loginMsg.setRequestBody(requestBody);\n    loginMsg.getRequestHeader().setContentLength(requestBody.length);\n    loginMsg.getRequestHeader().setMethod(\"POST\");\n    loginMsg.getRequestHeader().setContentType(\"application/x-www-form-urlencoded\");\n    \n    // Send the message and get the response\n    var sender = new HttpSender(HttpSender.MANUAL_REQUEST_INITIATOR);\n    sender.sendAndReceive(loginMsg, true);\n    \n    // Check if authentication was successful\n    var response = loginMsg.getResponseHeader().toString() + loginMsg.getResponseBody().toString();\n    println(\"Authentication response received: \" + loginMsg.getResponseHeader().getStatusCode());\n    \n    // We consider 200 OK or 302 Found as potential successes\n    return (loginMsg.getResponseHeader().getStatusCode() == 200 || \n            loginMsg.getResponseHeader().getStatusCode() == 302);\n}\n\nfunction getRequiredParamsNames() {\n    return [\"username\", \"password\"];\n}\n\nfunction getOptionalParamsNames() {\n    return [];\n}\n\nfunction getCredentialsParamsNames() {\n    return [\"username\", \"password\"];\n}\nEOL\n\n# Create context file for ZAP\ncat > $(WORK_PATH)/context.context << EOL\n<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<configuration>\n    <context>\n        <name>eVAT</name>\n        <desc>eVAT Application Context</desc>\n        <inscope>true</inscope>\n        <incregexes>https://app\\.evat\\.com.*</incregexes>\n        <tech>\n            <include>Db.MySQL</include>\n            <include>Db</include>\n            <include>Language.PHP</include>\n            <include>OS.Linux</include>\n            <include>SCM.Git</include>\n            <include>WS.Apache</include>\n        </tech>\n        <authentication>\n            <type>2</type>\n            <strategy>1</strategy>\n            <loggedout>&lt;form.*login.*&gt;</loggedout>\n            <form>\n                <loginurl>${LOGIN_URL}</loginurl>\n                <loginbody>username={%username%}&amp;password={%password%}</loginbody>\n            </form>\n            <verification>\n                <regex>&lt;a.*logout.*&gt;</regex>\n            </verification>\n        </authentication>\n        <users>\n            <user>testuser</user>\n        </users>\n        <forceduser>testuser</forceduser>\n    </context>\n</configuration>\nEOL\n\necho \"Running ZAP authenticated scan against: $PROTECTED_URL with user: $TEST_USERNAME\"\n\n# Run authenticated scan\n$ZAP_SCRIPT -cmd \\\n  -silent \\\n  -config api.disablekey=true \\\n  -configfile $(WORK_PATH)/zap-config.conf \\\n  -contextfile $(WORK_PATH)/context.context \\\n  -script $(WORK_PATH)/scripts/auth.js \\\n  -user testuser \\\n  -username $TEST_USERNAME \\\n  -password $TEST_PASSWORD \\\n  -spider $PROTECTED_URL \\\n  -scan \\\n  -scanpolicy \"Default Policy\" \\\n  -quickout \"$AUTH_REPORT_FILE\" \\\n  -quickprogress\n\n# Create summary file\necho \"OWASP ZAP Authenticated Scan Summary - $(date)\" > \"$AUTH_SUMMARY_FILE\"\necho \"============================\" >> \"$AUTH_SUMMARY_FILE\"\necho \"Build: v$(buildVersion)\" >> \"$AUTH_SUMMARY_FILE\"\necho \"Target URL: $PROTECTED_URL\" >> \"$AUTH_SUMMARY_FILE\"\necho \"Timestamp: ${TIMESTAMP}\" >> \"$AUTH_SUMMARY_FILE\"\necho \"\" >> \"$AUTH_SUMMARY_FILE\"\n\n# Check if report was generated\nif [ -f \"$AUTH_REPORT_FILE\" ]; then\n  echo \"Authenticated scan completed successfully.\"\n  \n  # Copy to standard locations\n  cp \"$AUTH_REPORT_FILE\" \"$AUTH_STANDARD_REPORT\"\n  \n  # Extract alerts for the summary\n  echo \"Alert Summary:\" >> \"$AUTH_SUMMARY_FILE\"\n  grep -o 'class=\"risk-[^\"]*\"' \"$AUTH_REPORT_FILE\" | sort | uniq -c | \\\n  sed 's/class=\"risk-//g' | sed 's/\"//g' | \\\n  awk '{print $2 \" risk alerts: \" $1}' >> \"$AUTH_SUMMARY_FILE\" || echo \"No alerts found\"\n  \n  # Copy summary to standard location\n  cp \"$AUTH_SUMMARY_FILE\" \"$AUTH_STANDARD_SUMMARY\"\nelse\n  echo \"Authenticated scan failed to produce a report.\"\nfi\n"
    - task: CmdLine@2
      displayName: 'Generate Security Trend Analysis'
      continueOnError: true
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Get ZAP report file for analysis\nZAP_REPORT_FILE=\"$(ARTIFACT_FOLDER)/zap-scan/zap-report.html\"\n\n# Create trends folder\nmkdir -p $(ARTIFACT_FOLDER)/security-trends\n\n# Generate trends data from latest ZAP scan\nif [ -f \"$ZAP_REPORT_FILE\" ]; then\n  # Extract current alert counts\n  HIGH_COUNT=$(grep -o 'class=\"risk-high\"' \"$ZAP_REPORT_FILE\" | wc -l)\n  MEDIUM_COUNT=$(grep -o 'class=\"risk-medium\"' \"$ZAP_REPORT_FILE\" | wc -l)\n  LOW_COUNT=$(grep -o 'class=\"risk-low\"' \"$ZAP_REPORT_FILE\" | wc -l)\n  INFO_COUNT=$(grep -o 'class=\"risk-info\"' \"$ZAP_REPORT_FILE\" | wc -l)\n  \n  # Append to trends file with build version and timestamp\n  echo \"$(buildVersion),$(date +%s),$HIGH_COUNT,$MEDIUM_COUNT,$LOW_COUNT,$INFO_COUNT\" >> $(ARTIFACT_FOLDER)/security-trends/zap_trends.csv\n  \n  # If this is a new file, add header\n  if [ $(wc -l < $(ARTIFACT_FOLDER)/security-trends/zap_trends.csv) -eq 1 ]; then\n    sed -i '1i Build,Timestamp,High,Medium,Low,Info' $(ARTIFACT_FOLDER)/security-trends/zap_trends.csv\n  fi\n  \n  # Generate HTML trends visualization - fixed here-document\n  cat > $(ARTIFACT_FOLDER)/security-trends/security_trends.html << 'EOLMARKER'\n<!DOCTYPE html>\n<html>\n<head>\n    <title>eVAT Security Trends</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .chart-container { width: 80%; margin: 20px auto; }\n        .build-info { background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }\n    </style>\n</head>\n<body>\n    <h1>eVAT Security Trends</h1>\n    <div class=\"build-info\">\n        <strong>Current Build:</strong> BUILD_VERSION_PLACEHOLDER<br>\n        <strong>Generated:</strong> DATE_PLACEHOLDER<br>\n    </div>\n    \n    <div class=\"chart-container\">\n        <canvas id=\"trendsChart\"></canvas>\n    </div>\n    \n    <script>\n        // Load data from CSV\n        fetch('zap_trends.csv')\n          .then(response => response.text())\n          .then(data => {\n            const rows = data.trim().split('\\n');\n            const headers = rows[0].split(',');\n            const builds = [];\n            const highData = [];\n            const mediumData = [];\n            const lowData = [];\n            const infoData = [];\n            \n            for(let i=1; i<rows.length; i++) {\n              const cells = rows[i].split(',');\n              builds.push(cells[0]);\n              highData.push(parseInt(cells[2]));\n              mediumData.push(parseInt(cells[3]));\n              lowData.push(parseInt(cells[4]));\n              infoData.push(parseInt(cells[5]));\n            }\n            \n            // Create chart\n            const ctx = document.getElementById('trendsChart').getContext('2d');\n            const chart = new Chart(ctx, {\n              type: 'line',\n              data: {\n                labels: builds,\n                datasets: [\n                  {\n                    label: 'High Risk',\n                    data: highData,\n                    backgroundColor: 'rgba(255, 99, 132, 0.2)',\n                    borderColor: 'rgba(255, 99, 132, 1)',\n                    borderWidth: 2\n                  },\n                  {\n                    label: 'Medium Risk',\n                    data: mediumData,\n                    backgroundColor: 'rgba(255, 159, 64, 0.2)',\n                    borderColor: 'rgba(255, 159, 64, 1)',\n                    borderWidth: 2\n                  },\n                  {\n                    label: 'Low Risk',\n                    data: lowData,\n                    backgroundColor: 'rgba(255, 205, 86, 0.2)',\n                    borderColor: 'rgba(255, 205, 86, 1)',\n                    borderWidth: 2\n                  },\n                  {\n                    label: 'Info',\n                    data: infoData,\n                    backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                    borderColor: 'rgba(54, 162, 235, 1)',\n                    borderWidth: 2\n                  }\n                ]\n              },\n              options: {\n                responsive: true,\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    title: {\n                      display: true,\n                      text: 'Number of Issues'\n                    }\n                  },\n                  x: {\n                    title: {\n                      display: true,\n                      text: 'Build Version'\n                    }\n                  }\n                },\n                plugins: {\n                  title: {\n                    display: true,\n                    text: 'Security Issues Trend Over Time'\n                  }\n                }\n              }\n            });\n          });\n    </script>\n</body>\n</html>\nEOLMARKER\n\n  # Replace placeholders with actual values\n  sed -i \"s/BUILD_VERSION_PLACEHOLDER/$(buildVersion)/g\" $(ARTIFACT_FOLDER)/security-trends/security_trends.html\n  sed -i \"s/DATE_PLACEHOLDER/$(date)/g\" $(ARTIFACT_FOLDER)/security-trends/security_trends.html\n  \n  echo \"Security trends analysis generated at: $(ARTIFACT_FOLDER)/security-trends/security_trends.html\"\nelse\n  echo \"ZAP report not found, skipping trend analysis.\"\nfi\n\n- task: CmdLine@2\n  displayName: 'Extract and Process ZAP Findings'\n  continueOnError: true\n  inputs:\n    script: |\n      # Define timestamp\n      TIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n      \n      # Process ZAP results to find critical and high alerts\n      if [ -f \"$(ARTIFACT_FOLDER)/zap-scan/zap-report.html\" ]; then\n        echo \"Processing ZAP results for critical findings...\"\n        \n        # Extract alerts from the HTML report\n        HIGH_ALERTS=$(grep -B 5 -A 20 'class=\"risk-high\\\\|class=\"high\"' $(ARTIFACT_FOLDER)/zap-scan/zap-report.html || echo \"\")\n        CRITICAL_ALERTS=$(grep -B 5 -A 20 'class=\"risk-critical\\\\|class=\"critical\"' $(ARTIFACT_FOLDER)/zap-scan/zap-report.html || echo \"\")\n        \n        # Log findings to consolidated structure\n        if [ ! -z \"$HIGH_ALERTS\" ]; then\n          echo \"=== HIGH RISK ALERTS ===\" > \"$(ARTIFACT_FOLDER)/zap-scan/alerts/high-alerts_${TIMESTAMP}.txt\"\n          echo \"$HIGH_ALERTS\" >> \"$(ARTIFACT_FOLDER)/zap-scan/alerts/high-alerts_${TIMESTAMP}.txt\"\n          # Copy to standard location\n          cp \"$(ARTIFACT_FOLDER)/zap-scan/alerts/high-alerts_${TIMESTAMP}.txt\" \"$(ARTIFACT_FOLDER)/zap-scan/high-alerts.txt\"\n        fi\n        \n        if [ ! -z \"$CRITICAL_ALERTS\" ]; then\n          echo \"=== CRITICAL RISK ALERTS ===\" > \"$(ARTIFACT_FOLDER)/zap-scan/alerts/critical-alerts_${TIMESTAMP}.txt\"\n          echo \"$CRITICAL_ALERTS\" >> \"$(ARTIFACT_FOLDER)/zap-scan/alerts/critical-alerts_${TIMESTAMP}.txt\"\n          # Copy to standard location\n          cp \"$(ARTIFACT_FOLDER)/zap-scan/alerts/critical-alerts_${TIMESTAMP}.txt\" \"$(ARTIFACT_FOLDER)/zap-scan/critical-alerts.txt\"\n        fi\n        \n        # Log findings to original locations (backward compatibility)\n        if [ ! -z \"$HIGH_ALERTS\" ]; then\n          echo \"=== HIGH RISK ALERTS ===\" > $(WORK_PATH)/high_alerts.txt\n          echo \"$HIGH_ALERTS\" >> $(WORK_PATH)/high_alerts.txt\n          cp $(WORK_PATH)/high_alerts.txt $(reportPath)/zap-scan/v$(buildVersion)/\n          # Copy to the original location\n          cp \"$(ARTIFACT_FOLDER)/zap-scan/high-alerts.txt\" \"$(Build.ArtifactStagingDirectory)/zap-scan/high-alerts.txt\"\n        fi\n        \n        if [ ! -z \"$CRITICAL_ALERTS\" ]; then\n          echo \"=== CRITICAL RISK ALERTS ===\" > $(WORK_PATH)/critical_alerts.txt\n          echo \"$CRITICAL_ALERTS\" >> $(WORK_PATH)/critical_alerts.txt\n          cp $(WORK_PATH)/critical_alerts.txt $(reportPath)/zap-scan/v$(buildVersion)/\n          # Copy to the original location\n          cp \"$(ARTIFACT_FOLDER)/zap-scan/critical-alerts.txt\" \"$(Build.ArtifactStagingDirectory)/zap-scan/critical-alerts.txt\"\n        fi\n        \n        # Extract alert names for work item creation\n        ALERTS=$(echo \"$HIGH_ALERTS $CRITICAL_ALERTS\" | grep -o '<h3>[^<]*</h3>' | sed 's/<h3>//g' | sed 's/<\\/h3>//g' || echo \"\")\n        \n        echo \"$ALERTS\" | while read -r alert; do\n          if [ ! -z \"$alert\" ]; then\n            # Create a unique hash for this finding\n            FINDING_HASH=$(echo \"$alert\" | md5sum | awk '{print $1}')\n            \n            # Check if this finding has been reported before\n            if grep -q \"$FINDING_HASH\" \"$(findingsHistoryPath)/zap_findings.txt\"; then\n              echo \"Skipping already reported finding: $alert\"\n              continue\n            fi\n            \n            # Create work item for new finding\n            TITLE=\"ZAP - $alert\"\n            echo \"Creating work item for: $TITLE\"\n            \n            # Create work item using Azure CLI\n            WORK_ITEM_ID=$(az boards work-item create \\\n              --organization \"$(System.TeamFoundationCollectionUri)\" \\\n              --project \"$(System.TeamProject)\" \\\n              --type \"SECURITY VULNERABILITY\" \\\n              --title \"$TITLE\" \\\n              --assigned-to \"$(Build.RequestedFor)\" \\\n              --description \"<p><strong>Source:</strong> OWASP ZAP v$(zapVersion) Security Scan</p><p><strong>Details:</strong> $alert</p><p><strong>Found in build:</strong> $(buildVersion) on $(reportTimestamp)</p><p>See ZAP report for full details.</p>\" \\\n              --fields \"System.AreaPath=eVatApp;System.IterationPath=eVatApp/Future;Security_level=Critical\" \\\n              --output json | grep -o '\"id\": [0-9]*' | awk '{print $2}' || echo \"\")\n            \n            if [ ! -z \"$WORK_ITEM_ID\" ]; then\n              # Record this finding in our history\n              echo \"$FINDING_HASH|$TITLE|$WORK_ITEM_ID|$(date +%s)\" >> \"$(findingsHistoryPath)/zap_findings.txt\"\n              echo \"Created work item #$WORK_ITEM_ID for ZAP finding: $alert\"\n              \n              # Also add to our consolidated findings summary\n              mkdir -p \"$(ARTIFACT_FOLDER)/security-findings\"\n              echo \"$(date) - Work Item #$WORK_ITEM_ID - $TITLE\" >> \"$(ARTIFACT_FOLDER)/security-findings/zap-workitems.txt\"\n            fi\n          fi\n        done\n      fi\n"
    - task: CmdLine@2
      displayName: 'Create Security Dashboard'
      inputs:
        script: "# Define timestamp\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Create security dashboard directory\nmkdir -p $(ARTIFACT_FOLDER)/security-dashboard\n\n# Generate Dashboard HTML\ncat > $(ARTIFACT_FOLDER)/security-dashboard/zap-dashboard.html << EOL\n<!DOCTYPE html>\n<html>\n<head>\n    <title>eVAT ZAP Security Dashboard v$(buildVersion)</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n        h2 { color: #333; }\n        .build-info { background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }\n        a { color: #0078d4; text-decoration: none; }\n        a:hover { text-decoration: underline; }\n        .risk-high { color: #d83b01; font-weight: bold; }\n        .risk-medium { color: #ff8c00; }\n        .risk-low { color: #3b3a39; }\n        table { border-collapse: collapse; width: 100%; margin-top: 10px; }\n        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n        th { background-color: #f2f2f2; }\n        tr:nth-child(even) { background-color: #f9f9f9; }\n    </style>\n</head>\n<body>\n    <h1>eVAT OWASP ZAP Security Dashboard</h1>\n    <div class=\"build-info\">\n        <strong>Build:</strong> $(buildVersion)<br>\n        <strong>Generated:</strong> $(date)<br>\n        <strong>Build ID:</strong> $(Build.BuildId)\n    </div>\n    \n    <div class=\"section\">\n        <h2>Standard Scan Reports</h2>\n        <ul>\n            <li><a href=\"../zap-scan/zap-report.html\">Full Active Scan Report (HTML)</a></li>\n            <li><a href=\"../zap-scan/zap-summary.txt\">Full Scan Summary (TXT)</a></li>\n            <li><a href=\"../zap-scan/high-alerts.txt\">High Risk Alerts (TXT)</a></li>\n            <li><a href=\"../zap-scan/critical-alerts.txt\">Critical Risk Alerts (TXT)</a></li>\n        </ul>\n    </div>\n    \n    <div class=\"section\">\n        <h2>Best Practice Scans</h2>\n        <ul>\n            <li><a href=\"../zap-scan/baseline-report.html\">Baseline Scan (HTML)</a></li>\n            <li><a href=\"../zap-scan/baseline-summary.txt\">Baseline Summary (TXT)</a></li>\n            <li><a href=\"../zap-scan/passive-scan-report.html\">Passive Scan (HTML)</a></li>\n            <li><a href=\"../zap-scan/passive-scan-summary.txt\">Passive Scan Summary (TXT)</a></li>\n            <li><a href=\"../zap-scan/owasp-top10-report.html\">OWASP Top 10 Compliance Scan (HTML)</a></li>\n            <li><a href=\"../zap-scan/owasp-top10-summary.txt\">OWASP Top 10 Summary (TXT)</a></li>\n            <li><a href=\"../zap-scan/api-scan-report.html\">API Security Scan (HTML)</a></li>\n            <li><a href=\"../zap-scan/api-scan-summary.txt\">API Security Summary (TXT)</a></li>\n            <li><a href=\"../zap-scan/auth-scan-report.html\">Authenticated Scan (HTML)</a></li>\n            <li><a href=\"../zap-scan/auth-scan-summary.txt\">Authenticated Scan Summary (TXT)</a></li>\n        </ul>\n    </div>\n    \n    <div class=\"section\">\n        <h2>Security Trends</h2>\n        <ul>\n            <li><a href=\"../security-trends/security_trends.html\">Security Issue Trends Over Time</a></li>\n            <li><a href=\"../security-trends/zap_trends.csv\">Raw Trend Data (CSV)</a></li>\n        </ul>\n    </div>\n    \n    <div class=\"section\">\n        <h2>OWASP Top 10 Coverage</h2>\n        <table>\n            <tr>\n                <th>OWASP Top 10 Category</th>\n                <th>Scan Type</th>\n                <th>Coverage</th>\n            </tr>\n            <tr>\n                <td>A01:2021 - Broken Access Control</td>\n                <td>Active Scan, Authenticated Scan</td>\n                <td>✓</td>\n            </tr>\n            <tr>\n                <td>A02:2021 - Cryptographic Failures</td>\n                <td>Passive Scan, Compliance Scan</td>\n                <td>✓</td>\n            </tr>\n            <tr>\n                <td>A03:2021 - Injection</td>\n                <td>Active Scan, API Scan</td>\n                <td>✓</td>\n            </tr>\n            <tr>\n                <td>A04:2021 - Insecure Design</td>\n                <td>Compliance Scan</td>\n                <td>Partial</td>\n            </tr>\n            <tr>\n                <td>A05:2021 - Security Misconfiguration</td>\n                <td>Baseline Scan, Passive Scan</td>\n                <td>✓</td>\n            </tr>\n            <tr>\n                <td>A06:2021 - Vulnerable Components</td>\n                <td>Passive Scan (Retire.js)</td>\n                <td>✓</td>\n            </tr>\n            <tr>\n                <td>A07:2021 - Authentication Failures</td>\n                <td>Authenticated Scan</td>\n                <td>✓</td>\n            </tr>\n            <tr>\n                <td>A08:2021 - Software & Data Integrity</td>\n                <td>Passive Scan, API Scan</td>\n                <td>Partial</td>\n            </tr>\n            <tr>\n                <td>A09:2021 - Logging & Monitoring</td>\n                <td>Not covered by ZAP</td>\n                <td>-</td>\n            </tr>\n            <tr>\n                <td>A10:2021 - SSRF</td>\n                <td>Active Scan, API Scan</td>\n                <td>✓</td>\n            </tr>\n        </table>\n    </div>\n    \n    <div class=\"section\">\n        <h2>Historical Reports</h2>\n        <p>Access timestamped reports for this build:</p>\n        <ul>\n            <li><a href=\"../zap-scan/reports/\">Full Scan Reports</a></li>\n            <li><a href=\"../zap-scan/baseline/\">Baseline Scan Reports</a></li>\n            <li><a href=\"../zap-scan/compliance/\">Compliance Scan Reports</a></li>\n            <li><a href=\"../zap-scan/passive/\">Passive Scan Reports</a></li>\n            <li><a href=\"../zap-scan/api/\">API Scan Reports</a></li>\n            <li><a href=\"../zap-scan/authenticated/\">Authenticated Scan Reports</a></li>\n            <li><a href=\"../zap-scan/alerts/\">Alert Details</a></li>\n            <li><a href=\"../zap-scan/summaries/\">All Scan Summaries</a></li>\n        </ul>\n    </div>\n    \n    <div class=\"section\">\n        <h2>Work Items</h2>\n        <p>Security vulnerabilities automatically created as work items:</p>\n        <iframe src=\"../security-findings/zap-workitems.txt\" style=\"width:100%; height:200px; border:1px solid #ddd;\"></iframe>\n    </div>\n</body>\n</html>\nEOL\n\n# Create an index.html file at the root of the artifact folder for easy access\ncat > $(ARTIFACT_FOLDER)/index.html << EOL\n<!DOCTYPE html>\n<html>\n<head>\n    <title>eVAT Security Reports v$(buildVersion)</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n        h2 { color: #333; }\n        .build-info { background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }\n        a { color: #0078d4; text-decoration: none; }\n        a:hover { text-decoration: underline; }\n    </style>\n</head>\n<body>\n    <h1>eVAT Security Reports Dashboard</h1>\n    <div class=\"build-info\">\n        <strong>Build:</strong> $(buildVersion)<br>\n        <strong>Generated:</strong> $(date)<br>\n        <strong>Build ID:</strong> $(Build.BuildId)\n    </div>\n    \n    <div class=\"section\">\n        <h2>Security Dashboards</h2>\n        <ul>\n            <li><a href=\"security-dashboard/zap-dashboard.html\">OWASP ZAP Dashboard</a></li>\n            <li><a href=\"security-trends/security_trends.html\">Security Trends Analysis</a></li>\n        </ul>\n    </div>\n    \n    <div class=\"section\">\n        <h2>Security Reports by Category</h2>\n        <ul>\n            <li><a href=\"zap-scan/\">OWASP ZAP Scan Results</a></li>\n            <li><a href=\"dependency-check/\">Dependency Check Results</a></li>\n            <li><a href=\"php-analysis/\">PHP Analysis Results</a></li>\n            <li><a href=\"frontend-analysis/\">Frontend Analysis Results</a></li>\n        </ul>\n    </div>\n                  \n                  <div class=\"section\">\n                      <h2>Security Findings</h2>\n                      <ul>\n                          <li><a href=\"zap-scan/high-alerts.txt\">High Risk Alerts</a></li>\n                          <li><a href=\"zap-scan/critical-alerts.txt\">Critical Risk Alerts</a></li>\n                          <li><a href=\"security-findings/zap-workitems.txt\">Created Work Items</a></li>\n                      </ul>\n                  </div>\n              </body>\n              </html>\n              EOL\n              \n              echo \"ZAP Security dashboard created at: $(ARTIFACT_FOLDER)/security-dashboard/zap-dashboard.html\"\n              echo \"Main index created at: $(ARTIFACT_FOLDER)/index.html\"\n\n        - task: CmdLine@2\n          displayName: 'Convert ZAP Report to NUnit Format'\n          inputs:\n            script: |\n              # Create NUnit-compatible format for Azure DevOps test reporting\n              # Store in both the consolidated artifact folder and original location\n              \n              cat > $(ARTIFACT_FOLDER)/zap-scan/zap-nunit.xml << EOL\n              <?xml version=\"1.0\" encoding=\"utf-8\"?>\n              <test-results name=\"ZAPSecurityScan\" total=\"1\" failures=\"0\" not-run=\"0\" date=\"$(date +%Y-%m-%d)\" time=\"$(date +%H:%M:%S)\">\n                <test-suite name=\"ZAPSecurityTests\" success=\"true\" time=\"0\" asserts=\"0\">\n                  <results>\n                    <test-case name=\"OWASP.ZAP.SecurityScan\" executed=\"true\" success=\"true\" time=\"0\" asserts=\"0\">\n                      <description>\n                        ZAP Security Scan completed successfully. See the published artifacts for detailed results.\n                        Version: $(zapVersion)\n                        Timestamp: $(reportTimestamp)\n                        Build: $(buildVersion)\n                      </description>\n                    </test-case>\n                  </results>\n                </test-suite>\n              </test-results>\n              EOL\n              \n              # Copy to original location for backward compatibility\n              cp $(ARTIFACT_FOLDER)/zap-scan/zap-nunit.xml $(Build.ArtifactStagingDirectory)/zap-scan/zap-nunit.xml\n              \n        - task: PublishTestResults@2\n          displayName: 'Publish ZAP Test Results'\n          inputs:\n            testResultsFormat: 'NUnit'\n            testResultsFiles: '$(ARTIFACT_FOLDER)/zap-scan/zap-nunit.xml'\n            testRunTitle: 'ZAP Security Tests'\n            \n        # Create an empty file to mark the DAST stage as complete\n        - task: CmdLine@2\n          displayName: 'Mark DAST Stage Complete'\n          inputs:\n            script: |\n              echo \"DAST Stage completed successfully at $(date)\" > $(ARTIFACT_FOLDER)/zap-scan/dast-complete.txt\n              \n        # Publish artifacts in two ways for compatibility\n        - task: PublishBuildArtifacts@1\n          displayName: 'Publish Consolidated Artifacts'\n          inputs:\n            pathToPublish: '$(ARTIFACT_FOLDER)'\n            artifactName: 'eVAT_Pipeline_$(buildVersion)'\n            artifactType: 'Container'\n          continueOnError: true\n            \n        - task: PublishBuildArtifacts@1\n          displayName: 'Publish ZAP Reports (For Backward Compatibility)'\n          inputs:\n            pathToPublish: '$(Build.ArtifactStagingDirectory)/zap-scan'\n            artifactName: '$(artifactsFolder)'\n            artifactType: 'Container'\n          continueOnError: true\n"
- stage: DatabaseStage
  displayName: Database
  dependsOn:
  - DAST
  jobs:
  - job: RunningMigrationJob
    displayName: Running migration
    steps:
    - task: SSH@0
      displayName: Running migration
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          ls
        readyTimeout: '20000'
- stage: ProdApp2
  displayName: Deploying to Web 2 server
  dependsOn:
  - DatabaseStage
  jobs:
  - job: DeployWeb2Job
    displayName: Deploying eVAT Web 2
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      condition: false
      inputs:
        repository: none
    - task: SSH@0
      name: PurgeOldWeb2FilesTask
      displayName: Purging old files
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          sudo systemctl status nginx
          sudo rm -rf $(appBasePath)/app
          sudo rm -rf $(appBasePath)/artisan
          sudo rm -rf $(appBasePath)/bootstrap
          sudo rm -rf $(appBasePath)/config
          sudo rm -rf $(appBasePath)/public
          sudo rm -rf $(appBasePath)/resources
          sudo rm -rf $(appBasePath)/vendor
        readyTimeout: '20000'
    - task: SSH@0
      name: CopyWeb2FilesTask
      displayName: Copying new files
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          sudo rsync -av  --include='app/***' --include='bootstrap/***' --include='config/***' --include='public/***' --include='vendor/***' --include='resources/***' --include='artisan' --exclude="*" $(tempAppPath)/ $(appBasePath)/
          sudo rsync -av $(certPath)/ $(appBasePath)/resources/certificates/elster/
          sudo rsync -av $(tempAppPath)/storage/app/ $(appBasePath)/storage/app/
        readyTimeout: '20000'
    - task: SSH@0
      name: ChangingWeb2PermissionsTask
      displayName: Changing permissions
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          sudo chown -R www-data:www-data $(appBasePath)/
          sudo chmod -R 775 $(appBasePath)/storage
          sudo chmod -R 775 $(appBasePath)/public
          sudo chmod -R 775 $(appBasePath)/bootstrap/cache
          sudo chmod -R 775 $(appBasePath)/resources/certificates/elster
          sudo systemctl status nginx
        readyTimeout: '20000'
- stage: ProdCron1
  displayName: Deploying to Cron server 1
  dependsOn:
  - DatabaseStage
  - ProdApp2
  jobs:
  - job: DeployCron1Job
    displayName: Deploying to Cron server
    steps:
    - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
      condition: false
      inputs:
        repository: none
    - task: SSH@0
      name: StoppingCron1
      displayName: Stopping cron
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          ls
        readyTimeout: '20000'
    - task: SSH@0
      name: PurgeCron1OldFiles
      displayName: Purging old files
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          sudo rm -rf $(cronBasePath)/app
          sudo rm -rf $(cronBasePath)/artisan
          sudo rm -rf $(cronBasePath)/bootstrap
          sudo rm -rf $(cronBasePath)/config
          sudo rm -rf $(cronBasePath)/public
          sudo rm -rf $(cronBasePath)/resources
          sudo rm -rf $(cronBasePath)/vendor
        readyTimeout: '20000'
    - task: SSH@0
      name: SyncCron1FilesTask
      displayName: Syncing new files
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          sudo rsync -av  --include='app/***' --include='bootstrap/***' --include='config/***' --include='public/***' --include='vendor/***' --include='resources/***' --include='artisan' --exclude="*" $(tempAppPath)/ $(cronBasePath)/
          sudo rsync -av $(certPath)/ $(cronBasePath)/resources/certificates/elster/
        readyTimeout: '20000'
    - task: SSH@0
      name: ChmodCron1NewFilesTask
      displayName: Granting permission to new files
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          sudo chown -R www-data:www-data $(cronBasePath)/
          sudo chmod -R 775 $(cronBasePath)/storage
          sudo chmod -R 775 $(cronBasePath)/public
          sudo chmod -R 775 $(cronBasePath)/bootstrap/cache
          sudo chmod -R 775 $(cronBasePath)/resources/certificates/elster
        readyTimeout: '20000'
    - task: SSH@0
      name: StartingCron1
      displayName: Starting cron
      inputs:
        sshEndpoint: 'AzureProdSrv'
        runOptions: 'commands'
        commands: |
          date
          ls
        readyTimeout: '20000'

