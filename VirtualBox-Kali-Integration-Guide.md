# VirtualBox to Kali Linux Integration Guide

## Enabling Copy-Paste Between macOS and Kali Linux VM

To enable seamless copying and pasting between your macOS host and Kali Linux VM, follow these steps:

### Method 1: VirtualBox Guest Additions (Recommended)

1. **Install VirtualBox Guest Additions on Kali Linux**:

   First, make sure your Kali Linux VM is running and you're logged in. Then open a terminal and run:

   ```bash
   sudo apt update
   sudo apt install -y virtualbox-guest-x11
   ```

   This will install the necessary Guest Additions packages on Kali Linux.

2. **Enable Bidirectional Clipboard**:

   With the VM powered off:
   
   - Open VirtualBox Manager
   - Select your Kali Linux VM
   - Click on "Settings"
   - Go to "General" → "Advanced" tab
   - Set "Shared Clipboard" to "Bidirectional"
   - Click "OK" to save changes
   
   ![VirtualBox Settings](https://www.howtogeek.com/wp-content/uploads/2017/01/xsco_3.png.pagespeed.gp+jp+jw+pj+ws+js+rj+rp+rw+ri+cp+md.ic.39ecSD9n9W.png)

3. **Restart the VM** for changes to take effect.

4. **Test Copy-Paste**: 
   - Copy text from macOS (⌘+C)
   - Click inside your Kali Linux VM
   - Paste using Ctrl+Shift+V (in terminal) or Ctrl+V (in most applications)

### Method 2: Shared Folder Setup

If clipboard sharing doesn't work reliably, you can use shared folders:

1. **Create a folder on your Mac** to share with Kali (e.g., `/Users/<USER>/VM_Shared`)

2. **Configure the Shared Folder in VirtualBox**:
   - With the VM powered off
   - Go to VM Settings
   - Select "Shared Folders"
   - Click the "+" icon to add a new shared folder
   - Browse to your created folder
   - Check "Auto-mount" and "Make Permanent"
   - Click "OK"

3. **Access the Shared Folder in Kali**:
   
   After starting the VM, the shared folder should be accessible at `/media/sf_VM_Shared` (or similar name). If you can't access it, add your user to the `vboxsf` group:
   
   ```bash
   sudo usermod -aG vboxsf $(whoami)
   ```
   
   Then log out and log back in for changes to take effect.

4. **Use the Shared Folder** to transfer files between systems.

### Method 3: SSH with X Forwarding

Another option is to SSH into your Kali VM from your Mac terminal:

1. **Install and Start SSH Server on Kali**:
   ```bash
   sudo apt update
   sudo apt install -y openssh-server
   sudo systemctl enable ssh
   sudo systemctl start ssh
   ```

2. **Get the Kali VM's IP Address**:
   ```bash
   ip addr show
   ```
   Look for the inet address on your main interface (usually eth0 or enp0s3).

3. **SSH from Mac Terminal**:
   ```bash
   ssh kali@[VM_IP_ADDRESS]
   ```
   Replace [VM_IP_ADDRESS] with your VM's actual IP address.

4. **Use the Terminal** for running commands, with native copy-paste from your Mac.

## Transferring Your Testing Scripts to Kali

After setting up copy-paste or shared folders, here's how to transfer the testing scripts:

1. **Create the Directory Structure on Kali**:
   ```bash
   mkdir -p ~/evat-pentest/{network,web,cloud,api,evidence,reports}
   ```

2. **Transfer Each Script**:
   - Copy the script content from the guides
   - Paste into a new file in Kali using a text editor:
     ```bash
     nano ~/evat-pentest/vm2-compliance-test.sh
     ```
   - Paste the content (Ctrl+Shift+V in nano)
   - Save the file (Ctrl+O, then Enter, then Ctrl+X)

3. **Make Scripts Executable**:
   ```bash
   chmod +x ~/evat-pentest/*.sh
   ```

## Troubleshooting

If you're having issues with copy-paste functionality:

1. **Restart VirtualBox Guest Services**:
   ```bash
   sudo systemctl restart vboxadd-service
   ```

2. **Verify VBoxClient is Running**:
   ```bash
   ps aux | grep VBoxClient
   ```
   
   If not running, start it manually:
   ```bash
   VBoxClient --clipboard
   ```

3. **Update VirtualBox** to the latest version on your Mac.

4. **Update Kali Linux** within the VM:
   ```bash
   sudo apt update && sudo apt full-upgrade -y
   ```

## Next Steps After Setup

Once you can successfully copy-paste between your Mac and Kali VM, you can:

1. Copy the testing scripts from our guides into your Kali VM
2. Run the VM.2 and CS.4 compliance tests as outlined in the guides
3. Document your findings using the provided templates

For specific test commands, refer to the comprehensive guides we've already created:
- `eVAT-Kali-Linux-Penetration-Testing-Guide.md`
- `eVAT-Kali-Linux-Penetration-Testing-Guide-Completion.md`
- `eVAT-Kali-Linux-Penetration-Testing-Guide-Final.md`
