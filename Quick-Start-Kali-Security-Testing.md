# Quick Start: Kali Security Testing for eVAT Compliance

This guide provides the essential commands to quickly get started with security testing on your Kali Linux VM for VM.2 and CS.4 compliance.

## Prerequisites

- Kali Linux VM with VirtualBox Guest Additions installed
- Copy-paste functionality enabled between host and VM (see VirtualBox-Kali-Integration-Guide.md)
- Internet connectivity for tool updates

## 1. Initial Setup (Run these commands in your Kali terminal)

```bash
# Create directory structure
mkdir -p ~/evat-pentest/{network,web,cloud,api,evidence,reports}
cd ~/evat-pentest

# Update Kali and install any missing tools
sudo apt update && sudo apt install -y nmap dirb nikto sslscan gobuster hydra metasploit-framework zaproxy

# Create a basic scope file
cat > ~/evat-pentest/scope.yaml << 'EOL'
company: eVAT
date: "May 14, 2025"
targets:
  web_applications:
    - url: "https://app.evat.com"  # Replace with your actual target
      description: "Main eVAT application"
  network:
    ip_ranges:
      - "**********/24"  # Replace with your actual network range
  cloud:
    azure:
      subscription_id: "your-subscription-id" # Replace with actual ID
EOL
```

## 2. VM.2 Network Compliance Testing

```bash
# Create the VM.2 testing script
cat > ~/evat-pentest/vm2-test.sh << 'EOL'
#!/bin/bash
echo "Starting VM.2 Network Testing"
TARGET_RANGE="**********/24"  # Replace with your actual target

# Basic network discovery
echo "[+] Running network discovery scan"
sudo nmap -sn $TARGET_RANGE -oA ~/evat-pentest/network/discovery

# Detailed service scan
echo "[+] Running detailed service scan"
sudo nmap -sS -sV -O -p- -T4 --min-parallelism 50 $TARGET_RANGE -oA ~/evat-pentest/network/services

# Vulnerability scan
echo "[+] Running vulnerability scan"
sudo nmap -sS -sV -p- --script=vuln $TARGET_RANGE -oA ~/evat-pentest/network/vulnerabilities

echo "[+] Network scans completed"
EOL

# Make the script executable
chmod +x ~/evat-pentest/vm2-test.sh

# Run the VM.2 network testing script
./vm2-test.sh
```

## 3. CS.4 Cloud Compliance Testing

```bash
# Create the CS.4 testing script
cat > ~/evat-pentest/cs4-test.sh << 'EOL'
#!/bin/bash
echo "Starting CS.4 Cloud Testing"

# Azure scanning
if command -v az &> /dev/null; then
  echo "[+] Azure CLI found, running Azure tests"
  
  # Login to Azure (interactive)
  az login
  
  # Get subscription info
  echo "[+] Listing subscriptions"
  az account list --output table
  
  # Network Security Group rules check
  echo "[+] Checking NSG rules"
  az network nsg list --query "[].name" -o tsv | while read nsg; do
    echo "Checking NSG: $nsg"
    az network nsg rule list --nsg-name $nsg --query "[?access=='Allow' && direction=='Inbound' && sourceAddressPrefix=='*'].[name,destinationPortRange]" -o table
  done
  
  # Storage account check  
  echo "[+] Checking storage accounts"
  az storage account list --query "[].name" -o tsv | while read acct; do
    echo "Checking storage account: $acct"
    az storage account show --name $acct --query "networkRuleSet.defaultAction" -o tsv
  done
else
  echo "[-] Azure CLI not found. Install with: curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash"
fi

echo "[+] Cloud scans completed"
EOL

# Make the script executable
chmod +x ~/evat-pentest/cs4-test.sh

# Run the CS.4 cloud testing script
./cs4-test.sh
```

## 4. Web Application Testing with OWASP ZAP

```bash
# Create simple web app testing script
cat > ~/evat-pentest/webapp-test.sh << 'EOL'
#!/bin/bash
echo "Starting Web Application Testing"
TARGET_URL="https://app.evat.com"  # Replace with your target

# Start ZAP headless
echo "[+] Starting ZAP"
zap-cli start

# Spider the target
echo "[+] Spidering target: $TARGET_URL"
zap-cli spider $TARGET_URL

# Active scan
echo "[+] Running active scan on: $TARGET_URL"
zap-cli active-scan $TARGET_URL

# Generate report
echo "[+] Generating report"
zap-cli report -o ~/evat-pentest/web/zap-report.html -f html

# Stop ZAP
zap-cli shutdown

echo "[+] Web application testing completed"
EOL

# Make the script executable
chmod +x ~/evat-pentest/webapp-test.sh

# Install ZAP CLI if needed
pip install --user zapcli

# Run the web application testing script
./webapp-test.sh
```

## 5. Generate Compliance Report

```bash
# Create reporting script
cat > ~/evat-pentest/generate-report.sh << 'EOL'
#!/bin/bash
echo "Generating Compliance Report"
REPORT_DATE=$(date +"%Y-%m-%d")

# Create report directory
mkdir -p ~/evat-pentest/reports/$REPORT_DATE

# Start the report
cat > ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md << 'REPORT'
# eVAT Security Compliance Report
Date: $REPORT_DATE
Tester: $(whoami)@$(hostname)

## VM.2 Network Penetration Testing Results

### Key Findings

REPORT

# Add network scan findings
echo "### Network Scan Results" >> ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md
cat ~/evat-pentest/network/vulnerabilities.nmap >> ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md

# Add cloud findings
echo -e "\n## CS.4 Cloud Services Penetration Testing Results\n" >> ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md
echo "### Azure Findings" >> ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md
echo "See attached logs for details." >> ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md

# Add remediation recommendations
cat >> ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md << 'REMEDIATION'

## Remediation Plan

### Critical Vulnerabilities (5-day remediation)
- TBD based on findings

### High Vulnerabilities (30-day remediation)
- TBD based on findings

### Medium/Low Vulnerabilities
- TBD based on findings

## Compliance Status
- VM.2: [COMPLIANT/NON-COMPLIANT - TBD based on findings]
- CS.4: [COMPLIANT/NON-COMPLIANT - TBD based on findings]

## Evidence
All scan evidence is stored in ~/evat-pentest/evidence
REMEDIATION

echo "[+] Report generated: ~/evat-pentest/reports/$REPORT_DATE/compliance-report.md"
EOL

# Make the script executable
chmod +x ~/evat-pentest/generate-report.sh
```

## Next Steps

After running these basic tests:

1. Replace placeholder IP addresses and URLs with your actual targets
2. Review findings and document in the compliance report
3. Refer to the detailed guides for more comprehensive testing:
   - `eVAT-Kali-Linux-Penetration-Testing-Guide.md`
   - `eVAT-Kali-Linux-Penetration-Testing-Guide-Completion.md`
   - `eVAT-Kali-Linux-Penetration-Testing-Guide-Final.md`

## Important Notes

- Always ensure you have proper authorization before testing any systems
- Document all testing activities and findings thoroughly
- Ensure all remediation activities are tracked and verified
- Keep evidence secure for compliance auditing purposes
