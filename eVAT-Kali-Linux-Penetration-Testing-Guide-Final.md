## 8. Run-All-Tests Script (Continued)

```bash
total_issues=$((vm2_issues + cs4_issues))
if [ $total_issues -eq 0 ]; then
  echo "✅ **COMPLIANT** with all requirements" >> ~/evat-pentest/reports/final-compliance-report.md
else
  echo "❌ **NOT COMPLIANT** with all requirements" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "Total issues found: $total_issues" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "VM.2 issues: $vm2_issues" >> ~/evat-pentest/reports/final-compliance-report.md
  echo "CS.4 issues: $cs4_issues" >> ~/evat-pentest/reports/final-compliance-report.md
fi

echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "## Next Steps" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "1. Address all identified issues according to their severity" >> ~/evat-pentest/reports/final-compliance-report.md
echo "2. Implement a continuous security testing program" >> ~/evat-pentest/reports/final-compliance-report.md
echo "3. Schedule the next comprehensive penetration test for Q3 2025" >> ~/evat-pentest/reports/final-compliance-report.md
echo "4. Incorporate findings into the security training program" >> ~/evat-pentest/reports/final-compliance-report.md

echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "## Appendices" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "### Tools Used" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- OWASP ZAP" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- Nmap" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- Burp Suite" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- Metasploit Framework" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- AWS CLI" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- Azure CLI" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- ScoutSuite" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- Prowler" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- SSLScan" >> ~/evat-pentest/reports/final-compliance-report.md
echo "- Custom Python Scripts" >> ~/evat-pentest/reports/final-compliance-report.md

echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "### Evidence Files" >> ~/evat-pentest/reports/final-compliance-report.md
echo "" >> ~/evat-pentest/reports/final-compliance-report.md
echo "All evidence files are available in the $(pwd)/evat-pentest/evidence directory with checksums in checksums.txt." >> ~/evat-pentest/reports/final-compliance-report.md

echo "Testing completed and report generated: ~/evat-pentest/reports/final-compliance-report.md"
EOL

chmod +x ~/evat-pentest/run-all-tests.sh
```

## 9. Kali Linux Testing Workflow for Quarterly Assessments

To maintain compliance with VM.2 and CS.4 requirements, follow this quarterly testing workflow:

### 9.1 Pre-Testing Preparation

1. Update Kali Linux and all tools:
   ```bash
   sudo apt update && sudo apt full-upgrade -y
   ```

2. Update testing scope based on infrastructure changes:
   ```bash
   nano ~/evat-pentest/scope.yaml
   ```

3. Verify testing authorization:
   ```bash
   # Ensure you have a valid penetration testing authorization document
   # Review scope and boundaries before proceeding
   ```

### 9.2 Quarterly Testing Schedule

| Quarter | Focus | Target Date | Components |
|---------|-------|-------------|------------|
| Q3 2025 | Network Infrastructure | Aug 1-15, 2025 | Internal and external network, VPN, firewalls |
| Q4 2025 | Cloud Services | Nov 1-15, 2025 | AWS, Azure, containerized services |
| Q1 2026 | Application Security | Feb 1-15, 2026 | Web application, APIs, microservices |
| Q2 2026 | Comprehensive | May 1-15, 2026 | All components + new developments |

### 9.3 Post-Testing Activities

After each quarterly assessment:

1. Generate compliance report:
   ```bash
   bash ~/evat-pentest/collect-evidence.sh
   ```

2. Validate remediation of previous findings:
   ```bash
   # Create a script to verify previous findings
   cat > ~/evat-pentest/verify-remediation.sh << 'EOL'
   #!/bin/bash
   
   # Load previous findings
   previous_findings=~/evat-pentest/reports/previous_findings.json
   
   # For each finding, run a specific test to verify remediation
   jq -c '.[]' $previous_findings | while read -r finding; do
     id=$(echo $finding | jq -r '.id')
     test_command=$(echo $finding | jq -r '.verification_command')
     
     echo "Verifying remediation for finding $id"
     eval $test_command
     
     if [ $? -eq 0 ]; then
       echo "[✓] Finding $id appears to be remediated"
     else
       echo "[✗] Finding $id is NOT remediated"
     fi
   done
   EOL
   
   chmod +x ~/evat-pentest/verify-remediation.sh
   ```

3. Update the threat model:
   ```bash
   # Review and update threat model based on new findings
   nano ~/evat-pentest/threat-model.md
   ```

## 10. Critical Tests for Compliance Requirements

### 10.1 VM.2 Critical Tests

These tests focus on network penetration testing requirements that absolutely must be performed to meet VM.2 compliance:

```bash
# 1. Network segmentation testing (boundary testing)
# This test ensures proper separation between network segments
for source_segment in "**********/24" "**********/24" "***********/24"; do
  for target_segment in "**********/24" "**********/24" "***********/24"; do
    if [ "$source_segment" != "$target_segment" ]; then
      echo "Testing access from $source_segment to $target_segment"
      # Use a host in source_segment to scan target_segment
      # Document unexpected access between segments
    fi
  done
done

# 2. Privilege escalation testing
# This test checks if a standard user can gain administrative access
echo "Testing privilege escalation vectors"
sudo -l
find / -perm -4000 -type f 2>/dev/null
find / -perm -2000 -type f 2>/dev/null
find / -name "*.conf" -perm -o+r 2>/dev/null | xargs grep -l "password"

# 3. Default/weak credential testing
# This test checks for systems using default or weak credentials
echo "Testing for default credentials on critical systems"
hydra -L /usr/share/wordlists/metasploit/default_users.txt \
      -P /usr/share/wordlists/metasploit/default_passwords.txt \
      *********** ssh

# 4. Lateral movement testing
# This test verifies if a compromised system can be used to access other systems
echo "Testing lateral movement capabilities"
# From a compromised/authorized system:
crackmapexec smb **********/24 -u user -p password --shares
```

### 10.2 CS.4 Critical Tests

These tests focus on cloud services penetration testing requirements that absolutely must be performed to meet CS.4 compliance:

```bash
# 1. Public exposure testing
# This test identifies resources that are publicly accessible
echo "Testing for publicly exposed cloud resources"

# AWS S3 Public Buckets
aws s3api list-buckets --query "Buckets[].Name" --output text | while read bucket; do
  aws s3api get-bucket-policy --bucket $bucket 2>/dev/null | grep "Principal\": \"\*\"" && \
  echo "[!] $bucket has public access policy"
done

# Azure Storage Public Access
az storage account list --query "[].name" -o tsv | while read account; do
  az storage account show --name $account --query "networkRuleSet.defaultAction" -o tsv | grep "Allow" && \
  echo "[!] $account has public network access"
done

# 2. Identity and access management testing
# This test checks for overprivileged accounts and roles
echo "Testing for excessive IAM permissions"

# AWS IAM
aws iam list-users --query "Users[].UserName" --output text | while read user; do
  aws iam list-attached-user-policies --user-name $user --query "AttachedPolicies[?PolicyName=='AdministratorAccess'].PolicyName" --output text | grep -q "AdministratorAccess" && \
  echo "[!] $user has administrator access"
done

# Azure RBAC
az role assignment list --all --include-inherited --query "[?roleDefinitionName=='Owner' || roleDefinitionName=='Contributor'].[principalName, roleDefinitionName]" -o tsv

# 3. Encryption testing
# This test verifies proper encryption of data at rest and in transit
echo "Testing for unencrypted cloud resources"

# AWS EBS Volumes
aws ec2 describe-volumes --query "Volumes[?Encrypted==\`false\`].VolumeId" --output text

# Azure Disk Encryption
az vm encryption show --name test-vm --resource-group test-rg

# 4. Security misconfiguration testing
# This test checks for common security misconfigurations
echo "Testing for security misconfigurations"

# AWS Security Groups
aws ec2 describe-security-groups --query "SecurityGroups[?IpPermissions[?IpRanges[?CidrIp=='0.0.0.0/0' && FromPort==22]]].GroupId" --output text

# Azure NSG Rules
az network nsg list --query "[].name" -o tsv | while read nsg; do
  az network nsg rule list --nsg-name $nsg --query "[?sourceAddressPrefix=='*' && access=='Allow' && destinationPortRange=='22'].name" -o tsv
done
```

## 11. Report Templates

Use these templates to ensure consistent reporting for compliance purposes:

### 11.1 Finding Report Template

```markdown
# Security Finding Report

## Overview

- **Finding ID**: [UNIQUE-ID]
- **Severity**: [Critical|High|Medium|Low]
- **Compliance Requirement**: [VM.2|CS.4|Both]
- **Discovery Date**: [YYYY-MM-DD]
- **Affected Component**: [Component Name]

## Description

[Detailed description of the finding, including technical details and impact]

## Proof of Concept

[Steps to reproduce or evidence of the vulnerability]

```bash
# Example code or commands that demonstrate the vulnerability
```

## Impact

[Description of the potential impact if exploited]

## Remediation

[Detailed steps to remediate the issue]

```bash
# Example remediation commands or code
```

## Verification

[Steps to verify that the remediation was successful]

```bash
# Verification commands
```

## References

- [Relevant documentation]
- [CWE/CVE reference]
- [Industry best practices]
```

### 11.2 Executive Summary Template

```markdown
# Security Assessment Executive Summary

## Overview

This report summarizes the security assessment conducted for compliance with VM.2 (Network Penetration Testing) and CS.4 (Cloud Services Penetration Testing) requirements.

**Assessment Period**: [Start Date] to [End Date]
**Conducted By**: [Name/Team]

## Key Findings

### VM.2 Compliance Status

- **Status**: [COMPLIANT|NOT COMPLIANT]
- **Critical Issues**: [Count]
- **High Issues**: [Count]
- **Medium Issues**: [Count]
- **Low Issues**: [Count]

### CS.4 Compliance Status

- **Status**: [COMPLIANT|NOT COMPLIANT]
- **Critical Issues**: [Count]
- **High Issues**: [Count]
- **Medium Issues**: [Count]
- **Low Issues**: [Count]

## Risk Summary

[Visual representation of risks by category and severity]

## Recommendations

1. [Key recommendation 1]
2. [Key recommendation 2]
3. [Key recommendation 3]

## Next Assessment

The next assessment is scheduled for [Date] and will focus on [Focus Area].

## Attestation

This assessment was conducted in accordance with industry best practices and complies with the requirements specified in VM.2 and CS.4.

[Signature]
[Name]
[Position]
[Date]
```

## 12. Conclusion

This Kali Linux penetration testing guide provides a comprehensive methodology for conducting both VM.2 Network Penetration Testing and CS.4 Cloud Services Penetration Testing to meet compliance requirements. The guide includes:

1. **Complete testing scripts** for automating reconnaissance, vulnerability scanning, and exploitation
2. **Detailed reporting templates** to document findings and verify remediation
3. **Quarterly testing schedule** to maintain ongoing compliance
4. **Critical test cases** that must be performed to satisfy VM.2 and CS.4 requirements
5. **Evidence collection procedures** to maintain a verifiable audit trail

By following this guide, security testers can ensure their penetration testing activities adequately address the compliance requirements while identifying security vulnerabilities that automated tools might miss.

Remember that penetration testing is only one component of a comprehensive security program. Regular testing, combined with proper remediation, security training, and continuous monitoring, will help maintain a strong security posture and compliance with requirements over time.
