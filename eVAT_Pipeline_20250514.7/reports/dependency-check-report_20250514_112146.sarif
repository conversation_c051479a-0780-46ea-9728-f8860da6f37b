{"$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json", "version": "2.1.0", "runs": [{"tool": {"driver": {"name": "dependency-check", "version": "8.4.0", "informationUri": "https://jeremylong.github.io/DependencyCheck/", "rules": [], "properties": {"disclaimer": "Dependency-Check is an open source tool performing a best effort analysis of 3rd party dependencies; false positives and false negatives may exist in the analysis performed by the tool. Use of the tool and the reporting provided constitutes acceptance for use in an AS IS condition, and there are NO warranties, implied or otherwise, with regard to the analysis or its use. Any use of the tool and the reporting provided is at the user's risk. In no event shall the copyright holder or OWASP be held liable for any damages whatsoever arising out of or in connection with the use of this tool, the analysis performed, or the resulting report.", "nvd": "This report contains data retrieved from the National Vulnerability Database: https://nvd.nist.gov", "cisa": "This report may contain data retrieved from the CISA Known Exploited Vulnerability Catalog: https://www.cisa.gov/known-exploited-vulnerabilities-catalog", "npm": "This report may contain data retrieved from the the Github Advisory Database (via NPM Audit API): https://github.com/advisories/", "retirejs": "This report may contain data retrieved from the RetireJS community: https://retirejs.github.io/retire.js/", "ossindex": "This report may contain data retrieved from the Sonatype OSS Index: https://ossindex.sonatype.org", "CurrentEngineRelease": "12.1.0", "NVD CVE Checked": "2025-05-14T11:23:52", "NVD CVE Modified": "2025-05-14T10:00:04", "VersionCheckOn": "2025-05-14T11:23:57", "kev.checked": "1747221837"}}}, "artifacts": [], "results": []}]}