<?xml version="1.0"?><analysis xmlns="https://jeremylong.github.io/DependencyCheck/dependency-check.3.1.xsd"><scanInfo><engineVersion>8.4.0</engineVersion><dataSource><name>CurrentEngineRelease</name><timestamp>12.1.0</timestamp></dataSource><dataSource><name>NVD CVE Checked</name><timestamp>2025-05-14T11:23:52</timestamp></dataSource><dataSource><name>NVD CVE Modified</name><timestamp>2025-05-14T10:00:04</timestamp></dataSource><dataSource><name>VersionCheckOn</name><timestamp>2025-05-14T11:23:57</timestamp></dataSource><dataSource><name>kev.checked</name><timestamp>1747221837</timestamp></dataSource></scanInfo><projectInfo><name>eVAT Application v20250514.7</name><reportDate>2025-05-14T11:24:02.910720640Z</reportDate><credits>This report contains data retrieved from the National Vulnerability Database: https://nvd.nist.gov, Github Advisory Database (via NPM Audit API): https://github.com/advisories/, and the RetireJS community.</credits></projectInfo><dependencies></dependencies></analysis>