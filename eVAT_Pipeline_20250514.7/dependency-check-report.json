{"reportSchema": "1.1", "scanInfo": {"engineVersion": "8.4.0", "dataSource": [{"name": "CurrentEngineRelease", "timestamp": "12.1.0"}, {"name": "NVD CVE Checked", "timestamp": "2025-05-14T11:23:52"}, {"name": "NVD CVE Modified", "timestamp": "2025-05-14T10:00:04"}, {"name": "VersionCheckOn", "timestamp": "2025-05-14T11:23:57"}, {"name": "kev.checked", "timestamp": "1747221837"}]}, "projectInfo": {"name": "eVAT Application v20250514.7", "reportDate": "2025-05-14T11:24:02.910720640Z", "credits": {"NVD": "This report contains data retrieved from the National Vulnerability Database: https://nvd.nist.gov", "CISA": "This report may contain data retrieved from the CISA Known Exploited Vulnerability Catalog: https://www.cisa.gov/known-exploited-vulnerabilities-catalog", "NPM": "This report may contain data retrieved from the Github Advisory Database (via NPM Audit API): https://github.com/advisories/", "RETIREJS": "This report may contain data retrieved from the RetireJS community: https://retirejs.github.io/retire.js/", "OSSINDEX": "This report may contain data retrieved from the Sonatype OSS Index: https://ossindex.sonatype.org"}}, "dependencies": []}